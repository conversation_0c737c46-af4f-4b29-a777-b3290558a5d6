"""
Teeth chart service for the dentistry application.
"""
import json
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

class TeethChartService:
    """
    Service for managing dental teeth charts.
    """
    
    def initialize_teeth_chart(self):
        """
        Initialize a new teeth chart.
        
        Returns:
            dict: A new teeth chart structure
        """
        # Standard adult teeth chart (32 teeth)
        adult_teeth = {
            str(i): {
                "status": "normal",
                "procedures": [],
                "notes": ""
            } for i in range(1, 33)
        }
        
        # Standard child teeth chart (20 teeth)
        child_teeth = {
            str(i): {
                "status": "normal",
                "procedures": [],
                "notes": ""
            } for i in range(1, 21)
        }
        
        return {
            "adult": adult_teeth,
            "child": child_teeth,
            "last_updated": datetime.now().isoformat()
        }
    
    def update_tooth_status(self, teeth_chart, tooth_number, status, chart_type="adult", notes=None, procedure=None):
        """
        Update the status of a specific tooth.
        
        Args:
            teeth_chart: The teeth chart to update
            tooth_number: The number of the tooth
            status: The new status (normal, cavity, filling, crown, missing, etc.)
            chart_type: The chart type (adult or child)
            notes: Optional notes about the tooth
            procedure: Optional procedure information
            
        Returns:
            dict: The updated teeth chart
        """
        if not teeth_chart:
            teeth_chart = self.initialize_teeth_chart()
        
        tooth_key = str(tooth_number)
        
        # Ensure the chart type exists
        if chart_type not in teeth_chart:
            logger.warning(f"Chart type {chart_type} not found in teeth chart")
            return teeth_chart
        
        # Ensure the tooth exists in the chart
        if tooth_key not in teeth_chart[chart_type]:
            logger.warning(f"Tooth {tooth_number} not found in {chart_type} chart")
            return teeth_chart
        
        # Update the tooth status
        teeth_chart[chart_type][tooth_key]["status"] = status
        
        # Update notes if provided
        if notes:
            teeth_chart[chart_type][tooth_key]["notes"] = notes
        
        # Add procedure if provided
        if procedure:
            if "procedures" not in teeth_chart[chart_type][tooth_key]:
                teeth_chart[chart_type][tooth_key]["procedures"] = []
            
            procedure_entry = {
                "date": datetime.now().isoformat(),
                "type": procedure.get("type", "unknown"),
                "description": procedure.get("description", ""),
                "performed_by": procedure.get("performed_by", "")
            }
            
            teeth_chart[chart_type][tooth_key]["procedures"].append(procedure_entry)
        
        # Update the last updated timestamp
        teeth_chart["last_updated"] = datetime.now().isoformat()
        
        return teeth_chart
    
    def get_tooth_details(self, teeth_chart, tooth_number, chart_type="adult"):
        """
        Get details for a specific tooth.
        
        Args:
            teeth_chart: The teeth chart
            tooth_number: The number of the tooth
            chart_type: The chart type (adult or child)
            
        Returns:
            dict: The tooth details or None if not found
        """
        if not teeth_chart:
            return None
        
        tooth_key = str(tooth_number)
        
        # Ensure the chart type exists
        if chart_type not in teeth_chart:
            return None
        
        # Ensure the tooth exists in the chart
        if tooth_key not in teeth_chart[chart_type]:
            return None
        
        return teeth_chart[chart_type][tooth_key]
    
    def get_teeth_by_status(self, teeth_chart, status, chart_type="adult"):
        """
        Get all teeth with a specific status.
        
        Args:
            teeth_chart: The teeth chart
            status: The status to filter by
            chart_type: The chart type (adult or child)
            
        Returns:
            list: List of tooth numbers with the specified status
        """
        if not teeth_chart or chart_type not in teeth_chart:
            return []
        
        result = []
        for tooth_key, tooth_data in teeth_chart[chart_type].items():
            if tooth_data.get("status") == status:
                result.append(tooth_key)
        
        return result
    
    def get_teeth_needing_attention(self, teeth_chart, chart_type="adult"):
        """
        Get all teeth that need attention (not normal status).
        
        Args:
            teeth_chart: The teeth chart
            chart_type: The chart type (adult or child)
            
        Returns:
            dict: Dictionary mapping status to list of tooth numbers
        """
        if not teeth_chart or chart_type not in teeth_chart:
            return {}
        
        result = {}
        for tooth_key, tooth_data in teeth_chart[chart_type].items():
            status = tooth_data.get("status")
            if status != "normal":
                if status not in result:
                    result[status] = []
                result[status].append(tooth_key)
        
        return result
