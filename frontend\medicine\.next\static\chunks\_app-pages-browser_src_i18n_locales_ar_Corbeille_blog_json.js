"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_i18n_locales_ar_Corbeille_blog_json"],{

/***/ "(app-pages-browser)/./src/i18n/locales/ar/Corbeille/blog.json":
/*!*************************************************!*\
  !*** ./src/i18n/locales/ar/Corbeille/blog.json ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

module.exports = /*#__PURE__*/JSON.parse('{"blog":"blog AR","page-title":"مدونة","app-description":"تعد المنصة الصحة لMedecinSvp واجهة إعلامية إلكترونية لنشر معلومات صحية دقيقة ومدخلاً إلكترونياً لكل ما تقدمه المنصة من خدمات","keywords":"موقع وزارة الصحة ، اخبار الصحة ، اعلانات وزارة الصحة ، الاحداث الصحية ، احصائيات وزارة الصحة ، خدمات وزارة الصحة ، وزارة الصحة ، مركزصحي ، عدد الاصابات ، فحوصات ، الصحية"}');

/***/ })

}]);