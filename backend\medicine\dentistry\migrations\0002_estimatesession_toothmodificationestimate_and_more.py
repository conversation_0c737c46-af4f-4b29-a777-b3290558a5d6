# Generated by Django 4.2.7 on 2025-05-24 20:31

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('dentistry', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='EstimateSession',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('session_name', models.CharField(default='Session par défaut', max_length=200)),
                ('patient_id', models.CharField(blank=True, max_length=100, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('is_completed', models.BooleanField(default=False)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Estimate Session',
                'verbose_name_plural': 'Estimate Sessions',
                'ordering': ['-updated_at'],
            },
        ),
        migrations.CreateModel(
            name='ToothModificationEstimate',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('tooth_number', models.PositiveIntegerField(help_text='Numéro de la dent (1-32)')),
                ('svg_id', models.CharField(help_text="ID du SVG (ex: '1', '2', etc.)", max_length=10)),
                ('path_id', models.CharField(help_text="ID du path SVG (ex: '17', '18', etc.)", max_length=10)),
                ('modification_type', models.CharField(help_text="Type de modification (ex: 'cleaning', 'whitening', etc.)", max_length=50)),
                ('status', models.CharField(choices=[('not_applied', 'Non Appliqué'), ('planned', 'Planifié'), ('applied', 'Appliqué')], default='not_applied', max_length=20)),
                ('is_visible', models.BooleanField(default=False, help_text='Si le path est visible (non masqué)')),
                ('color', models.CharField(blank=True, help_text='Couleur de remplissage', max_length=7, null=True)),
                ('stroke', models.CharField(blank=True, help_text='Couleur du contour', max_length=7, null=True)),
                ('metadata', models.JSONField(blank=True, default=dict, help_text='Données supplémentaires (highlighted_paths, etc.)')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('session', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='modifications', to='dentistry.estimatesession')),
            ],
            options={
                'verbose_name': 'Tooth Modification Estimate',
                'verbose_name_plural': 'Tooth Modification Estimates',
                'ordering': ['-updated_at'],
            },
        ),
        migrations.CreateModel(
            name='EstimateTemplate',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True, null=True)),
                ('specialty', models.CharField(choices=[('esthetic', 'Dentisterie Esthétique'), ('prosthetic', 'Prothèses Thérapeutiques'), ('surgery', 'Chirurgie'), ('orthodontics', 'Orthopédie'), ('general', 'Général')], default='general', max_length=20)),
                ('template_data', models.JSONField(default=dict, help_text='Configuration des modifications par défaut')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('is_public', models.BooleanField(default=False, help_text='Visible par tous les utilisateurs')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Estimate Template',
                'verbose_name_plural': 'Estimate Templates',
                'ordering': ['specialty', 'name'],
            },
        ),
        migrations.CreateModel(
            name='EstimateStatistics',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('total_modifications', models.PositiveIntegerField(default=0)),
                ('applied_modifications', models.PositiveIntegerField(default=0)),
                ('planned_modifications', models.PositiveIntegerField(default=0)),
                ('esthetic_count', models.PositiveIntegerField(default=0)),
                ('prosthetic_count', models.PositiveIntegerField(default=0)),
                ('surgery_count', models.PositiveIntegerField(default=0)),
                ('orthodontics_count', models.PositiveIntegerField(default=0)),
                ('modified_teeth_count', models.PositiveIntegerField(default=0)),
                ('most_modified_tooth', models.PositiveIntegerField(blank=True, null=True)),
                ('session_duration_minutes', models.PositiveIntegerField(default=0)),
                ('last_activity', models.DateTimeField(auto_now=True)),
                ('calculated_at', models.DateTimeField(auto_now=True)),
                ('session', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='statistics', to='dentistry.estimatesession')),
            ],
            options={
                'verbose_name': 'Estimate Statistics',
                'verbose_name_plural': 'Estimate Statistics',
            },
        ),
        migrations.CreateModel(
            name='EstimateSessionHistory',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('action', models.CharField(choices=[('created', 'Créé'), ('modified', 'Modifié'), ('deleted', 'Supprimé'), ('completed', 'Terminé'), ('reset', 'Réinitialisé')], max_length=20)),
                ('description', models.TextField(blank=True, null=True)),
                ('previous_data', models.JSONField(blank=True, default=dict)),
                ('new_data', models.JSONField(blank=True, default=dict)),
                ('performed_at', models.DateTimeField(auto_now_add=True)),
                ('modification', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='dentistry.toothmodificationestimate')),
                ('performed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('session', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='history', to='dentistry.estimatesession')),
            ],
            options={
                'verbose_name': 'Estimate Session History',
                'verbose_name_plural': 'Estimate Session Histories',
                'ordering': ['-performed_at'],
            },
        ),
        migrations.CreateModel(
            name='DentistryWorkingHours',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_private', models.BooleanField(default=False, verbose_name='Is private')),
                ('privatized_by', models.UUIDField(blank=True, null=True, verbose_name='Privatized by')),
                ('owner', models.UUIDField(blank=True, null=True, verbose_name='Owner')),
                ('weekday', models.IntegerField(choices=[(0, 'Monday'), (1, 'Tuesday'), (2, 'Wednesday'), (3, 'Thursday'), (4, 'Friday'), (5, 'Saturday'), (6, 'Sunday')], verbose_name='Weekday')),
                ('is_working_day', models.BooleanField(default=True, verbose_name='Is Working Day')),
                ('morning_start', models.TimeField(blank=True, null=True, verbose_name='Morning Start Time')),
                ('morning_end', models.TimeField(blank=True, null=True, verbose_name='Morning End Time')),
                ('afternoon_start', models.TimeField(blank=True, null=True, verbose_name='Afternoon Start Time')),
                ('afternoon_end', models.TimeField(blank=True, null=True, verbose_name='Afternoon End Time')),
                ('lunch_break_start', models.TimeField(blank=True, null=True, verbose_name='Lunch Break Start')),
                ('lunch_break_end', models.TimeField(blank=True, null=True, verbose_name='Lunch Break End')),
                ('notes', models.TextField(blank=True, help_text='Special notes for this day', verbose_name='Notes')),
                ('doctor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='working_hours', to='dentistry.dentistrydoctor', verbose_name='Doctor')),
            ],
            options={
                'verbose_name': 'Dentistry Working Hours',
                'verbose_name_plural': 'Dentistry Working Hours',
                'ordering': ['weekday'],
            },
        ),
        migrations.CreateModel(
            name='DentistryVacationPeriod',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_private', models.BooleanField(default=False, verbose_name='Is private')),
                ('privatized_by', models.UUIDField(blank=True, null=True, verbose_name='Privatized by')),
                ('owner', models.UUIDField(blank=True, null=True, verbose_name='Owner')),
                ('title', models.CharField(max_length=200, verbose_name='Vacation Title')),
                ('start_date', models.DateField(verbose_name='Start Date')),
                ('end_date', models.DateField(verbose_name='End Date')),
                ('vacation_type', models.CharField(choices=[('summer', 'Summer Vacation'), ('winter', 'Winter Vacation'), ('personal', 'Personal Time'), ('conference', 'Conference'), ('training', 'Training'), ('other', 'Other')], default='personal', max_length=20, verbose_name='Vacation Type')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('is_approved', models.BooleanField(default=True, help_text='Whether this vacation period is approved', verbose_name='Is Approved')),
                ('affects_appointments', models.BooleanField(default=True, help_text='Whether this vacation blocks appointment booking', verbose_name='Affects Appointments')),
                ('doctor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='vacation_periods', to='dentistry.dentistrydoctor', verbose_name='Doctor')),
            ],
            options={
                'verbose_name': 'Dentistry Vacation Period',
                'verbose_name_plural': 'Dentistry Vacation Periods',
                'ordering': ['start_date'],
            },
        ),
        migrations.CreateModel(
            name='DentistryReview',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_private', models.BooleanField(default=False, verbose_name='Is private')),
                ('privatized_by', models.UUIDField(blank=True, null=True, verbose_name='Privatized by')),
                ('owner', models.UUIDField(blank=True, null=True, verbose_name='Owner')),
                ('patient_name', models.CharField(max_length=200, verbose_name='Patient Name')),
                ('patient_email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='Patient Email')),
                ('overall_rating', models.IntegerField(validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)], verbose_name='Overall Rating')),
                ('review_text', models.TextField(verbose_name='Review Text')),
                ('professionalism_rating', models.IntegerField(blank=True, null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)], verbose_name='Professionalism Rating')),
                ('communication_rating', models.IntegerField(blank=True, null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)], verbose_name='Communication Rating')),
                ('pain_management_rating', models.IntegerField(blank=True, null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)], verbose_name='Pain Management Rating')),
                ('cleanliness_rating', models.IntegerField(blank=True, null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)], verbose_name='Cleanliness Rating')),
                ('wait_time_rating', models.IntegerField(blank=True, null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)], verbose_name='Wait Time Rating')),
                ('treatment_received', models.CharField(help_text='Description of dental treatment received', max_length=200, verbose_name='Treatment Received')),
                ('visit_date', models.DateField(verbose_name='Visit Date')),
                ('would_recommend', models.BooleanField(default=True, help_text='Would recommend this dentist to others', verbose_name='Would Recommend')),
                ('is_verified', models.BooleanField(default=False, verbose_name='Is Verified')),
                ('verification_method', models.CharField(blank=True, choices=[('email', 'Email Verification'), ('phone', 'Phone Verification'), ('appointment', 'Appointment Verification'), ('manual', 'Manual Verification')], max_length=50, null=True, verbose_name='Verification Method')),
                ('is_featured', models.BooleanField(default=False, verbose_name='Is Featured')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('helpful_count', models.IntegerField(default=0, help_text='Number of users who found this review helpful', verbose_name='Helpful Count')),
                ('doctor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reviews', to='dentistry.dentistrydoctor', verbose_name='Doctor')),
            ],
            options={
                'verbose_name': 'Dentistry Review',
                'verbose_name_plural': 'Dentistry Reviews',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='DentistryPrivacySettings',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_private', models.BooleanField(default=False, verbose_name='Is private')),
                ('privatized_by', models.UUIDField(blank=True, null=True, verbose_name='Privatized by')),
                ('owner', models.UUIDField(blank=True, null=True, verbose_name='Owner')),
                ('show_profile_to_other_doctors', models.BooleanField(default=True, help_text='Allow other doctors to view your profile', verbose_name='Show Profile to Other Doctors')),
                ('show_profile_to_patients', models.BooleanField(default=True, help_text='Allow patients to view your public profile', verbose_name='Show Profile to Patients')),
                ('share_anonymized_data_for_research', models.BooleanField(default=False, help_text='Allow anonymized data to be used for research purposes', verbose_name='Share Anonymized Data for Research')),
                ('allow_patient_feedback', models.BooleanField(default=True, help_text='Allow patients to leave feedback and ratings', verbose_name='Allow Patient Feedback')),
                ('show_phone_number', models.BooleanField(default=True, help_text='Display phone number on public profile', verbose_name='Show Phone Number')),
                ('show_email_address', models.BooleanField(default=False, help_text='Display email address on public profile', verbose_name='Show Email Address')),
                ('require_patient_verification', models.BooleanField(default=False, help_text='Require patients to verify their identity before booking', verbose_name='Require Patient Verification')),
                ('patient_data_retention_months', models.PositiveIntegerField(default=60, help_text='How long to retain patient data after last visit', validators=[django.core.validators.MinValueValidator(12), django.core.validators.MaxValueValidator(120)], verbose_name='Patient Data Retention (Months)')),
                ('doctor', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='privacy_settings', to='dentistry.dentistrydoctor', verbose_name='Doctor')),
            ],
            options={
                'verbose_name': 'Dentistry Privacy Settings',
                'verbose_name_plural': 'Dentistry Privacy Settings',
            },
        ),
        migrations.CreateModel(
            name='DentistryNotificationSettings',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_private', models.BooleanField(default=False, verbose_name='Is private')),
                ('privatized_by', models.UUIDField(blank=True, null=True, verbose_name='Privatized by')),
                ('owner', models.UUIDField(blank=True, null=True, verbose_name='Owner')),
                ('email_notifications', models.BooleanField(default=True, help_text='Receive notifications via email', verbose_name='Email Notifications')),
                ('appointment_reminders', models.BooleanField(default=True, help_text='Receive reminders about upcoming appointments', verbose_name='Appointment Reminders')),
                ('new_appointment_notifications', models.BooleanField(default=True, help_text='Get notified when patients book new appointments', verbose_name='New Appointment Notifications')),
                ('cancellation_notifications', models.BooleanField(default=True, help_text='Get notified when appointments are cancelled', verbose_name='Cancellation Notifications')),
                ('sms_notifications', models.BooleanField(default=True, help_text='Receive notifications via SMS', verbose_name='SMS Notifications')),
                ('emergency_sms', models.BooleanField(default=True, help_text='Receive emergency notifications via SMS', verbose_name='Emergency SMS')),
                ('marketing_emails', models.BooleanField(default=False, help_text='Receive marketing and promotional emails', verbose_name='Marketing Emails')),
                ('newsletter_subscription', models.BooleanField(default=False, help_text='Subscribe to dental industry newsletters', verbose_name='Newsletter Subscription')),
                ('reminder_hours_before', models.PositiveIntegerField(default=24, help_text='Hours before appointment to send reminder', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(168)], verbose_name='Reminder Hours Before')),
                ('preferred_notification_method', models.CharField(choices=[('email', 'Email'), ('sms', 'SMS'), ('both', 'Both Email and SMS'), ('push', 'Push Notifications')], default='email', max_length=20, verbose_name='Preferred Notification Method')),
                ('doctor', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='notification_settings', to='dentistry.dentistrydoctor', verbose_name='Doctor')),
            ],
            options={
                'verbose_name': 'Dentistry Notification Settings',
                'verbose_name_plural': 'Dentistry Notification Settings',
            },
        ),
        migrations.CreateModel(
            name='DentistryLocationSettings',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_private', models.BooleanField(default=False, verbose_name='Is private')),
                ('privatized_by', models.UUIDField(blank=True, null=True, verbose_name='Privatized by')),
                ('owner', models.UUIDField(blank=True, null=True, verbose_name='Owner')),
                ('clinic_name', models.CharField(max_length=200, verbose_name='Clinic Name')),
                ('address_line_1', models.CharField(max_length=200, verbose_name='Address Line 1')),
                ('address_line_2', models.CharField(blank=True, max_length=200, verbose_name='Address Line 2')),
                ('city', models.CharField(max_length=100, verbose_name='City')),
                ('state_province', models.CharField(max_length=100, verbose_name='State/Province')),
                ('postal_code', models.CharField(max_length=20, verbose_name='Postal Code')),
                ('country', models.CharField(max_length=100, verbose_name='Country')),
                ('phone_number', models.CharField(max_length=20, verbose_name='Phone Number')),
                ('fax_number', models.CharField(blank=True, max_length=20, verbose_name='Fax Number')),
                ('email', models.EmailField(max_length=254, verbose_name='Email Address')),
                ('website', models.URLField(blank=True, verbose_name='Website')),
                ('latitude', models.DecimalField(blank=True, decimal_places=8, max_digits=10, null=True, verbose_name='Latitude')),
                ('longitude', models.DecimalField(blank=True, decimal_places=8, max_digits=11, null=True, verbose_name='Longitude')),
                ('show_on_map', models.BooleanField(default=True, verbose_name='Show on Map')),
                ('map_zoom_level', models.PositiveIntegerField(default=15, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(20)], verbose_name='Map Zoom Level')),
                ('parking_available', models.BooleanField(default=True, verbose_name='Parking Available')),
                ('parking_instructions', models.TextField(blank=True, verbose_name='Parking Instructions')),
                ('public_transport_info', models.TextField(blank=True, verbose_name='Public Transport Information')),
                ('accessibility_info', models.TextField(blank=True, verbose_name='Accessibility Information')),
                ('doctor', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='location_settings', to='dentistry.dentistrydoctor', verbose_name='Doctor')),
            ],
            options={
                'verbose_name': 'Dentistry Location Settings',
                'verbose_name_plural': 'Dentistry Location Settings',
            },
        ),
        migrations.CreateModel(
            name='DentistryHoliday',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_private', models.BooleanField(default=False, verbose_name='Is private')),
                ('privatized_by', models.UUIDField(blank=True, null=True, verbose_name='Privatized by')),
                ('owner', models.UUIDField(blank=True, null=True, verbose_name='Owner')),
                ('title', models.CharField(max_length=200, verbose_name='Holiday Title')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('holiday_type', models.CharField(choices=[('vacation', 'Vacation'), ('sick_leave', 'Sick Leave'), ('conference', 'Conference'), ('training', 'Training'), ('personal', 'Personal'), ('public_holiday', 'Public Holiday'), ('emergency', 'Emergency')], default='vacation', max_length=20, verbose_name='Holiday Type')),
                ('start_date', models.DateField(verbose_name='Start Date')),
                ('end_date', models.DateField(verbose_name='End Date')),
                ('is_all_day', models.BooleanField(default=True, verbose_name='All Day')),
                ('start_time', models.TimeField(blank=True, null=True, verbose_name='Start Time')),
                ('end_time', models.TimeField(blank=True, null=True, verbose_name='End Time')),
                ('is_recurring', models.BooleanField(default=False, verbose_name='Is Recurring')),
                ('recurrence_pattern', models.CharField(blank=True, choices=[('daily', 'Daily'), ('weekly', 'Weekly'), ('monthly', 'Monthly'), ('yearly', 'Yearly')], max_length=50, verbose_name='Recurrence Pattern')),
                ('affects_appointments', models.BooleanField(default=True, help_text='Whether this holiday blocks appointment booking', verbose_name='Affects Appointments')),
                ('doctor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='holidays', to='dentistry.dentistrydoctor', verbose_name='Doctor')),
            ],
            options={
                'verbose_name': 'Dentistry Holiday',
                'verbose_name_plural': 'Dentistry Holidays',
                'ordering': ['start_date'],
            },
        ),
        migrations.CreateModel(
            name='DentistryDoctorNote',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_private', models.BooleanField(default=False, verbose_name='Is private')),
                ('privatized_by', models.UUIDField(blank=True, null=True, verbose_name='Privatized by')),
                ('owner', models.UUIDField(blank=True, null=True, verbose_name='Owner')),
                ('title', models.CharField(max_length=200, verbose_name='Note Title')),
                ('content', models.TextField(verbose_name='Note Content')),
                ('note_type', models.CharField(choices=[('general', 'General Note'), ('performance', 'Performance Note'), ('training', 'Training Note'), ('complaint', 'Complaint Note'), ('achievement', 'Achievement Note'), ('reminder', 'Reminder Note')], default='general', max_length=50, verbose_name='Note Type')),
                ('priority', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('urgent', 'Urgent')], default='medium', max_length=20, verbose_name='Priority')),
                ('is_confidential', models.BooleanField(default=False, verbose_name='Is Confidential')),
                ('author_name', models.CharField(max_length=200, verbose_name='Author Name')),
                ('author_role', models.CharField(max_length=100, verbose_name='Author Role')),
                ('doctor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='internal_notes', to='dentistry.dentistrydoctor', verbose_name='Doctor')),
            ],
            options={
                'verbose_name': 'Dentistry Doctor Note',
                'verbose_name_plural': 'Dentistry Doctor Notes',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='DentistryComment',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_private', models.BooleanField(default=False, verbose_name='Is private')),
                ('privatized_by', models.UUIDField(blank=True, null=True, verbose_name='Privatized by')),
                ('owner', models.UUIDField(blank=True, null=True, verbose_name='Owner')),
                ('patient_name', models.CharField(max_length=200, verbose_name='Patient Name')),
                ('patient_email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='Patient Email')),
                ('patient_phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='Patient Phone')),
                ('content', models.TextField(verbose_name='Comment Content')),
                ('rating', models.IntegerField(blank=True, help_text='Rating from 1 to 5 stars', null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)], verbose_name='Rating')),
                ('likes', models.IntegerField(default=0, verbose_name='Likes Count')),
                ('treatment_type', models.CharField(blank=True, help_text='Type of dental treatment received', max_length=100, null=True, verbose_name='Treatment Type')),
                ('visit_date', models.DateField(blank=True, help_text='Date of the dental visit', null=True, verbose_name='Visit Date')),
                ('is_verified', models.BooleanField(default=False, help_text='Whether this comment is from a verified patient', verbose_name='Is Verified')),
                ('is_featured', models.BooleanField(default=False, help_text='Whether to feature this comment prominently', verbose_name='Is Featured')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='IP Address')),
                ('user_agent', models.TextField(blank=True, null=True, verbose_name='User Agent')),
                ('doctor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='comments', to='dentistry.dentistrydoctor', verbose_name='Doctor')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='replies', to='dentistry.dentistrycomment', verbose_name='Parent Comment')),
            ],
            options={
                'verbose_name': 'Dentistry Comment',
                'verbose_name_plural': 'Dentistry Comments',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='DentistryAppointmentSettings',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_private', models.BooleanField(default=False, verbose_name='Is private')),
                ('privatized_by', models.UUIDField(blank=True, null=True, verbose_name='Privatized by')),
                ('owner', models.UUIDField(blank=True, null=True, verbose_name='Owner')),
                ('allow_online_booking', models.BooleanField(default=True, help_text='Allow patients to book appointments online', verbose_name='Allow Online Booking')),
                ('advance_booking_days', models.PositiveIntegerField(default=30, help_text='How many days in advance patients can book appointments', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(365)], verbose_name='Advance Booking Days')),
                ('min_booking_notice_hours', models.PositiveIntegerField(default=24, help_text='Minimum hours notice required for booking', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(168)], verbose_name='Minimum Booking Notice (Hours)')),
                ('default_appointment_duration', models.PositiveIntegerField(default=30, validators=[django.core.validators.MinValueValidator(15), django.core.validators.MaxValueValidator(240)], verbose_name='Default Appointment Duration (Minutes)')),
                ('consultation_duration', models.PositiveIntegerField(default=30, validators=[django.core.validators.MinValueValidator(15), django.core.validators.MaxValueValidator(120)], verbose_name='Consultation Duration (Minutes)')),
                ('cleaning_duration', models.PositiveIntegerField(default=60, validators=[django.core.validators.MinValueValidator(30), django.core.validators.MaxValueValidator(120)], verbose_name='Cleaning Duration (Minutes)')),
                ('treatment_duration', models.PositiveIntegerField(default=90, validators=[django.core.validators.MinValueValidator(30), django.core.validators.MaxValueValidator(240)], verbose_name='Treatment Duration (Minutes)')),
                ('emergency_duration', models.PositiveIntegerField(default=45, validators=[django.core.validators.MinValueValidator(15), django.core.validators.MaxValueValidator(120)], verbose_name='Emergency Duration (Minutes)')),
                ('buffer_time_before', models.PositiveIntegerField(default=10, help_text='Buffer time before each appointment', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(60)], verbose_name='Buffer Time Before (Minutes)')),
                ('buffer_time_after', models.PositiveIntegerField(default=10, help_text='Buffer time after each appointment', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(60)], verbose_name='Buffer Time After (Minutes)')),
                ('allow_patient_cancellation', models.BooleanField(default=True, verbose_name='Allow Patient Cancellation')),
                ('cancellation_notice_hours', models.PositiveIntegerField(default=24, help_text='Minimum hours notice required for cancellation', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(168)], verbose_name='Cancellation Notice Hours')),
                ('send_appointment_reminders', models.BooleanField(default=True, verbose_name='Send Appointment Reminders')),
                ('reminder_hours_before', models.PositiveIntegerField(default=24, help_text='Hours before appointment to send reminder', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(168)], verbose_name='Reminder Hours Before')),
                ('reminder_methods', models.JSONField(default=list, help_text='List of reminder methods: email, sms, push', verbose_name='Reminder Methods')),
                ('doctor', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='appointment_settings', to='dentistry.dentistrydoctor', verbose_name='Doctor')),
            ],
            options={
                'verbose_name': 'Dentistry Appointment Settings',
                'verbose_name_plural': 'Dentistry Appointment Settings',
            },
        ),
        migrations.CreateModel(
            name='DentistryAppearanceSettings',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_private', models.BooleanField(default=False, verbose_name='Is private')),
                ('privatized_by', models.UUIDField(blank=True, null=True, verbose_name='Privatized by')),
                ('owner', models.UUIDField(blank=True, null=True, verbose_name='Owner')),
                ('theme', models.CharField(choices=[('light', 'Light'), ('dark', 'Dark'), ('auto', 'Auto (System)')], default='light', max_length=20, verbose_name='Theme')),
                ('language', models.CharField(choices=[('en', 'English'), ('fr', 'French'), ('es', 'Spanish'), ('de', 'German'), ('it', 'Italian'), ('ar', 'Arabic')], default='en', max_length=10, verbose_name='Language')),
                ('time_format', models.CharField(choices=[('12h', '12-hour (AM/PM)'), ('24h', '24-hour')], default='12h', max_length=10, verbose_name='Time Format')),
                ('date_format', models.CharField(choices=[('MM/DD/YYYY', 'MM/DD/YYYY'), ('DD/MM/YYYY', 'DD/MM/YYYY'), ('YYYY-MM-DD', 'YYYY-MM-DD')], default='MM/DD/YYYY', max_length=15, verbose_name='Date Format')),
                ('calendar_view', models.CharField(choices=[('day', 'Day'), ('week', 'Week'), ('month', 'Month'), ('agenda', 'Agenda')], default='month', max_length=10, verbose_name='Default Calendar View')),
                ('calendar_start_hour', models.PositiveIntegerField(default=8, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(23)], verbose_name='Calendar Start Hour')),
                ('calendar_end_hour', models.PositiveIntegerField(default=18, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(24)], verbose_name='Calendar End Hour')),
                ('doctor', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='appearance_settings', to='dentistry.dentistrydoctor', verbose_name='Doctor')),
            ],
            options={
                'verbose_name': 'Dentistry Appearance Settings',
                'verbose_name_plural': 'Dentistry Appearance Settings',
            },
        ),
        migrations.CreateModel(
            name='DentistryAccessibilitySettings',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_private', models.BooleanField(default=False, verbose_name='Is private')),
                ('privatized_by', models.UUIDField(blank=True, null=True, verbose_name='Privatized by')),
                ('owner', models.UUIDField(blank=True, null=True, verbose_name='Owner')),
                ('high_contrast', models.BooleanField(default=False, help_text='Increase contrast for better visibility', verbose_name='High Contrast')),
                ('large_text', models.BooleanField(default=False, help_text='Increase text size throughout the application', verbose_name='Large Text')),
                ('font_size', models.PositiveIntegerField(default=16, help_text='Base font size for the application', validators=[django.core.validators.MinValueValidator(10), django.core.validators.MaxValueValidator(22)], verbose_name='Font Size (px)')),
                ('screen_reader', models.BooleanField(default=False, help_text='Optimize for screen readers', verbose_name='Screen Reader Support')),
                ('color_blind_friendly', models.BooleanField(default=False, help_text='Use color blind friendly color schemes', verbose_name='Color Blind Friendly')),
                ('reduce_animations', models.BooleanField(default=False, help_text='Reduce or disable animations and transitions', verbose_name='Reduce Animations')),
                ('keyboard_navigation', models.BooleanField(default=False, help_text='Enable enhanced keyboard navigation features', verbose_name='Enhanced Keyboard Navigation')),
                ('doctor', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='accessibility_settings', to='dentistry.dentistrydoctor', verbose_name='Doctor')),
            ],
            options={
                'verbose_name': 'Dentistry Accessibility Settings',
                'verbose_name_plural': 'Dentistry Accessibility Settings',
            },
        ),
        migrations.AddIndex(
            model_name='toothmodificationestimate',
            index=models.Index(fields=['session', 'tooth_number'], name='dentistry_t_session_c07041_idx'),
        ),
        migrations.AddIndex(
            model_name='toothmodificationestimate',
            index=models.Index(fields=['session', 'modification_type'], name='dentistry_t_session_62e943_idx'),
        ),
        migrations.AddIndex(
            model_name='toothmodificationestimate',
            index=models.Index(fields=['session', 'status'], name='dentistry_t_session_cc484b_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='toothmodificationestimate',
            unique_together={('session', 'svg_id', 'path_id')},
        ),
        migrations.AlterUniqueTogether(
            name='dentistryworkinghours',
            unique_together={('doctor', 'weekday')},
        ),
        migrations.AddIndex(
            model_name='dentistryreview',
            index=models.Index(fields=['doctor', '-created_at'], name='dentistry_d_doctor__d67ea0_idx'),
        ),
        migrations.AddIndex(
            model_name='dentistryreview',
            index=models.Index(fields=['overall_rating', '-created_at'], name='dentistry_d_overall_18f2e3_idx'),
        ),
        migrations.AddIndex(
            model_name='dentistryreview',
            index=models.Index(fields=['is_verified', '-created_at'], name='dentistry_d_is_veri_f00af6_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='dentistryreview',
            unique_together={('doctor', 'patient_email', 'visit_date')},
        ),
        migrations.AddIndex(
            model_name='dentistrydoctornote',
            index=models.Index(fields=['doctor', '-created_at'], name='dentistry_d_doctor__e36a55_idx'),
        ),
        migrations.AddIndex(
            model_name='dentistrydoctornote',
            index=models.Index(fields=['note_type', '-created_at'], name='dentistry_d_note_ty_0f9a09_idx'),
        ),
        migrations.AddIndex(
            model_name='dentistrydoctornote',
            index=models.Index(fields=['priority', '-created_at'], name='dentistry_d_priorit_66c7b4_idx'),
        ),
        migrations.AddIndex(
            model_name='dentistrycomment',
            index=models.Index(fields=['doctor', '-created_at'], name='dentistry_d_doctor__6c39a0_idx'),
        ),
        migrations.AddIndex(
            model_name='dentistrycomment',
            index=models.Index(fields=['is_active', '-created_at'], name='dentistry_d_is_acti_a64eae_idx'),
        ),
        migrations.AddIndex(
            model_name='dentistrycomment',
            index=models.Index(fields=['rating', '-created_at'], name='dentistry_d_rating_589e86_idx'),
        ),
    ]
