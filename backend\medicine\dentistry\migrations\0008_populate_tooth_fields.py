# Generated manually for populating tooth fields

from django.db import migrations


def populate_tooth_fields(apps, schema_editor):
    """
    Populate the new tooth fields based on tooth_number for existing teeth.
    """
    Tooth = apps.get_model('dentistry', 'Tooth')
    
    def get_tooth_type(tooth_number):
        """Determine tooth type based on tooth number."""
        last_digit = tooth_number % 10
        
        if last_digit in [1, 2]:
            return 'incisor'
        elif last_digit == 3:
            return 'canine'
        elif last_digit in [4, 5]:
            return 'premolar'
        elif last_digit in [6, 7]:
            return 'molar'
        elif last_digit == 8:
            return 'wisdom'
        else:
            return 'other'

    def get_quadrant(tooth_number):
        """Determine quadrant based on tooth number."""
        first_digit = tooth_number // 10
        
        if first_digit == 1:
            return 'upper_right'
        elif first_digit == 2:
            return 'upper_left'
        elif first_digit == 3:
            return 'lower_left'
        elif first_digit == 4:
            return 'lower_right'
        else:
            return 'upper_right'  # Default fallback

    def get_position(tooth_number):
        """Determine position within quadrant based on tooth number."""
        return tooth_number % 10

    def get_is_permanent(tooth_number):
        """Determine if tooth is permanent based on tooth number."""
        first_digit = tooth_number // 10
        # Primary teeth have first digits 5, 6, 7, 8
        return first_digit not in [5, 6, 7, 8]

    # Get tooth name mapping
    TOOTH_NAMES = {
        11: 'Upper Right Central Incisor',
        12: 'Upper Right Lateral Incisor',
        13: 'Upper Right Canine',
        14: 'Upper Right First Premolar',
        15: 'Upper Right Second Premolar',
        16: 'Upper Right First Molar',
        17: 'Upper Right Second Molar',
        18: 'Upper Right Third Molar',
        21: 'Upper Left Central Incisor',
        22: 'Upper Left Lateral Incisor',
        23: 'Upper Left Canine',
        24: 'Upper Left First Premolar',
        25: 'Upper Left Second Premolar',
        26: 'Upper Left First Molar',
        27: 'Upper Left Second Molar',
        28: 'Upper Left Third Molar',
        31: 'Lower Left Central Incisor',
        32: 'Lower Left Lateral Incisor',
        33: 'Lower Left Canine',
        34: 'Lower Left First Premolar',
        35: 'Lower Left Second Premolar',
        36: 'Lower Left First Molar',
        37: 'Lower Left Second Molar',
        38: 'Lower Left Third Molar',
        41: 'Lower Right Central Incisor',
        42: 'Lower Right Lateral Incisor',
        43: 'Lower Right Canine',
        44: 'Lower Right First Premolar',
        45: 'Lower Right Second Premolar',
        46: 'Lower Right First Molar',
        47: 'Lower Right Second Molar',
        48: 'Lower Right Third Molar',
        # Primary teeth
        51: 'Upper Right Primary Central Incisor',
        52: 'Upper Right Primary Lateral Incisor',
        53: 'Upper Right Primary Canine',
        54: 'Upper Right Primary First Molar',
        55: 'Upper Right Primary Second Molar',
        61: 'Upper Left Primary Central Incisor',
        62: 'Upper Left Primary Lateral Incisor',
        63: 'Upper Left Primary Canine',
        64: 'Upper Left Primary First Molar',
        65: 'Upper Left Primary Second Molar',
        71: 'Lower Left Primary Central Incisor',
        72: 'Lower Left Primary Lateral Incisor',
        73: 'Lower Left Primary Canine',
        74: 'Lower Left Primary First Molar',
        75: 'Lower Left Primary Second Molar',
        81: 'Lower Right Primary Central Incisor',
        82: 'Lower Right Primary Lateral Incisor',
        83: 'Lower Right Primary Canine',
        84: 'Lower Right Primary First Molar',
        85: 'Lower Right Primary Second Molar',
    }

    # Update all existing teeth
    for tooth in Tooth.objects.all():
        tooth.name = TOOTH_NAMES.get(tooth.tooth_number, f'Tooth {tooth.tooth_number}')
        tooth.tooth_type = get_tooth_type(tooth.tooth_number)
        tooth.quadrant = get_quadrant(tooth.tooth_number)
        tooth.position = get_position(tooth.tooth_number)
        tooth.is_permanent = get_is_permanent(tooth.tooth_number)
        tooth.save()


def reverse_populate_tooth_fields(apps, schema_editor):
    """
    Reverse migration - clear the populated fields.
    """
    Tooth = apps.get_model('dentistry', 'Tooth')
    
    # Clear the new fields
    Tooth.objects.update(
        name='',
        tooth_type='other',
        quadrant='upper_right',
        position=1,
        is_permanent=True,
        description=None
    )


class Migration(migrations.Migration):

    dependencies = [
        ('dentistry', '0007_add_tooth_fields'),
    ]

    operations = [
        migrations.RunPython(
            populate_tooth_fields,
            reverse_populate_tooth_fields
        ),
    ]
