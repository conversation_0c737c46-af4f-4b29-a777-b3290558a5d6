"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/web/page",{

/***/ "(app-pages-browser)/./src/components/Appointments/overview/AjouterUnRendezVous.tsx":
/*!**********************************************************************!*\
  !*** ./src/components/Appointments/overview/AjouterUnRendezVous.tsx ***!
  \**********************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Text/Text.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Select/Select.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/NumberInput/NumberInput.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Button/Button.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Modal/Modal.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Group/Group.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Switch/Switch.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Menu/Menu.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Avatar/Avatar.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/TextInput/TextInput.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ActionIcon/ActionIcon.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Radio/Radio.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/InputBase/InputBase.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ColorPicker/ColorPicker.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Textarea/Textarea.mjs\");\n/* harmony import */ var simplebar_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! simplebar-react */ \"(app-pages-browser)/./node_modules/simplebar-react/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCheck_IconColorPicker_IconHexagonPlusFilled_tabler_icons_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=IconCheck,IconColorPicker,IconHexagonPlusFilled!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconHexagonPlusFilled.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCheck_IconColorPicker_IconHexagonPlusFilled_tabler_icons_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=IconCheck,IconColorPicker,IconHexagonPlusFilled!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconColorPicker.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCheck_IconColorPicker_IconHexagonPlusFilled_tabler_icons_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=IconCheck,IconColorPicker,IconHexagonPlusFilled!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconCheck.mjs\");\n/* harmony import */ var _barrel_optimize_names_FaCalendarPlus_FaMicrophone_FaMicrophoneLines_FaUserDoctor_react_icons_fa6__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=FaCalendarPlus,FaMicrophone,FaMicrophoneLines,FaUserDoctor!=!react-icons/fa6 */ \"(app-pages-browser)/./node_modules/react-icons/fa6/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_RiUserFollowLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=RiUserFollowLine!=!react-icons/ri */ \"(app-pages-browser)/./node_modules/react-icons/ri/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_LiaAddressCardSolid_LiaBirthdayCakeSolid_react_icons_lia__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=LiaAddressCardSolid,LiaBirthdayCakeSolid!=!react-icons/lia */ \"(app-pages-browser)/./node_modules/react-icons/lia/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_TbNumber_react_icons_tb__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=TbNumber!=!react-icons/tb */ \"(app-pages-browser)/./node_modules/react-icons/tb/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FiPhone_react_icons_fi__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=FiPhone!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_CiAt_react_icons_ci__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=CiAt!=!react-icons/ci */ \"(app-pages-browser)/./node_modules/react-icons/ci/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_MdOutlineBedroomChild_MdOutlineContentPasteSearch_MdOutlineSocialDistance_react_icons_md__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=MdOutlineBedroomChild,MdOutlineContentPasteSearch,MdOutlineSocialDistance!=!react-icons/md */ \"(app-pages-browser)/./node_modules/react-icons/md/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ListPlus_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=ListPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/list-plus.js\");\n/* harmony import */ var react_imask__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-imask */ \"(app-pages-browser)/./node_modules/react-imask/esm/index.js\");\n/* harmony import */ var _mantine_notifications__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @mantine/notifications */ \"(app-pages-browser)/./node_modules/@mantine/notifications/esm/notifications.store.mjs\");\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! moment */ \"(app-pages-browser)/./node_modules/moment/moment.js\");\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(moment__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst RendezVousSelector = (param)=>{\n    let { onClose } = param;\n    _s();\n    const [selectedPeriod, setSelectedPeriod] = react__WEBPACK_IMPORTED_MODULE_1___default().useState('15days');\n    const [, setStartDate] = react__WEBPACK_IMPORTED_MODULE_1___default().useState('12/06/2025');\n    const [duration, setDuration] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(30);\n    const [numberOfDays, setNumberOfDays] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(3);\n    const [selectedSlots, setSelectedSlots] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(new Set());\n    // Générer les créneaux horaires\n    const generateTimeSlots = ()=>{\n        const slots = [];\n        const startHour = 8;\n        const endHour = 14;\n        for(let hour = startHour; hour < endHour; hour++){\n            for(let minute = 0; minute < 60; minute += 30){\n                const startTime = \"\".concat(hour.toString().padStart(2, '0'), \":\").concat(minute.toString().padStart(2, '0'));\n                const endMinute = minute + 30;\n                const endHour = endMinute >= 60 ? hour + 1 : hour;\n                const adjustedEndMinute = endMinute >= 60 ? endMinute - 60 : endMinute;\n                const endTime = \"\".concat(endHour.toString().padStart(2, '0'), \":\").concat(adjustedEndMinute.toString().padStart(2, '0'));\n                slots.push({\n                    id: \"\".concat(hour, \"-\").concat(minute),\n                    startTime,\n                    endTime\n                });\n            }\n        }\n        return slots;\n    };\n    // Calculer la date selon la période sélectionnée\n    const getDateForPeriod = ()=>{\n        switch(selectedPeriod){\n            case '15days':\n                return '12 juin 2025';\n            case '1month':\n                return '26 juin 2025';\n            case '3months':\n                return '10 juillet 2025';\n            default:\n                return '12 juin 2025';\n        }\n    };\n    // Calculer la date de début selon la période\n    const getStartDateForPeriod = ()=>{\n        switch(selectedPeriod){\n            case '15days':\n                return '12/06/2025';\n            case '1month':\n                return '25/06/2025';\n            case '3months':\n                return '10/07/2025';\n            default:\n                return '12/06/2025';\n        }\n    };\n    // Calculer la date formatée selon la période\n    const getFormattedDateForPeriod = ()=>{\n        switch(selectedPeriod){\n            case '15days':\n                return '12/06/2025';\n            case '1month':\n                return '26/06/2025';\n            case '3months':\n                return '10/07/2025';\n            default:\n                return '12/06/2025';\n        }\n    };\n    const timeSlots = generateTimeSlots();\n    const handleSlotToggle = (slotId)=>{\n        const newSelectedSlots = new Set(selectedSlots);\n        if (newSelectedSlots.has(slotId)) {\n            newSelectedSlots.delete(slotId);\n        } else {\n            newSelectedSlots.add(slotId);\n        }\n        setSelectedSlots(newSelectedSlots);\n    };\n    const isValidateEnabled = selectedSlots.size > 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-12 gap-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"col-span-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 bg-gray-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                        size: \"sm\",\n                                        fw: 500,\n                                        mb: \"xs\",\n                                        children: \"\\xc0 partir de\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                        value: getStartDateForPeriod(),\n                                        onChange: (value)=>setStartDate(value || ''),\n                                        data: [\n                                            {\n                                                value: '12/06/2025',\n                                                label: '12/06/2025'\n                                            },\n                                            {\n                                                value: '25/06/2025',\n                                                label: '25/06/2025'\n                                            },\n                                            {\n                                                value: '10/07/2025',\n                                                label: '10/07/2025'\n                                            },\n                                            {\n                                                value: '10/09/2025',\n                                                label: '10/09/2025'\n                                            }\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                        size: \"sm\",\n                                        fw: 500,\n                                        mb: \"xs\",\n                                        children: \"Dur\\xe9e (min)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.NumberInput, {\n                                        value: duration,\n                                        onChange: (value)=>setDuration(Number(value)),\n                                        min: 15,\n                                        max: 120,\n                                        step: 15\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                        size: \"sm\",\n                                        fw: 500,\n                                        mb: \"xs\",\n                                        children: \"Nbre des jours\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.NumberInput, {\n                                        value: numberOfDays,\n                                        onChange: (value)=>setNumberOfDays(Number(value)),\n                                        min: 1,\n                                        max: 30\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                lineNumber: 228,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"col-span-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                    size: \"lg\",\n                                    fw: 600,\n                                    children: getDateForPeriod()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                    size: \"sm\",\n                                    color: \"dimmed\",\n                                    children: \"24\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2 max-h-96 overflow-y-auto\",\n                            children: timeSlots.map((slot)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between p-2 hover:bg-gray-50 rounded\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                    size: \"sm\",\n                                                    children: \"le\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                    size: \"sm\",\n                                                    color: \"blue\",\n                                                    fw: 500,\n                                                    children: getFormattedDateForPeriod()\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                    size: \"sm\",\n                                                    children: \"de\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                    size: \"sm\",\n                                                    color: \"red\",\n                                                    fw: 500,\n                                                    children: slot.startTime\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                    lineNumber: 285,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                    size: \"sm\",\n                                                    children: \"\\xe0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                    lineNumber: 288,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                    size: \"sm\",\n                                                    color: \"green\",\n                                                    fw: 500,\n                                                    children: slot.endTime\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: selectedSlots.has(slot.id),\n                                            onChange: ()=>handleSlotToggle(slot.id),\n                                            className: \"form-checkbox h-4 w-4 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, slot.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                            lineNumber: 276,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center mt-6 pt-4 border-t\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                            variant: selectedPeriod === '15days' ? 'filled' : 'outline',\n                                            onClick: ()=>setSelectedPeriod('15days'),\n                                            size: \"sm\",\n                                            children: \"15 jours\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                            variant: selectedPeriod === '1month' ? 'filled' : 'outline',\n                                            onClick: ()=>setSelectedPeriod('1month'),\n                                            size: \"sm\",\n                                            children: \"1 Mois\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                            variant: selectedPeriod === '3months' ? 'filled' : 'outline',\n                                            onClick: ()=>setSelectedPeriod('3months'),\n                                            size: \"sm\",\n                                            children: \"3 Mois\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                            color: isValidateEnabled ? 'blue' : 'gray',\n                                            disabled: !isValidateEnabled,\n                                            onClick: ()=>{\n                                                // Logique de validation ici\n                                                onClose();\n                                            },\n                                            children: \"Valider\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                            variant: \"outline\",\n                                            color: \"red\",\n                                            onClick: onClose,\n                                            children: \"Annuler\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                    lineNumber: 328,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                            lineNumber: 303,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                    lineNumber: 270,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                lineNumber: 269,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n        lineNumber: 227,\n        columnNumber: 5\n    }, undefined);\n};\n_s(RendezVousSelector, \"JA7kajyxwsY0NPzKL2AkP/6ymxQ=\");\n_c = RendezVousSelector;\nconst AjouterUnRendezVous = (props)=>{\n    const { opened, onClose, appointmentForm, handleSubmit, eventTitle, setEventTitle, titleOptions, setTitleOptions, newTitle, setNewTitle, patientName, setPatientName, patientlastName, setPatientlastName, openListDesPatient, eventDateDeNaissance, handleDateChange, eventAge, genderOption, handleOptionChange, eventEtatCivil, setEventEtatCivil, eventCin, setEventCin, address, setAddress, eventTelephone, setEventTelephone, email, setEmail, patientdoctor, setPatientDocteur, patientsocialSecurity, setSocialSecurity, consultationTypes, setConsultationTypes, patienttypeConsultation, setPatientTypeConsultation, setEventType, searchValue, setSearchValue, dureeDeLexamen, getEventTypeColor, newConsultationType, setNewConsultationType, newConsultationColor, setNewConsultationColor, ColorPickeropened, openedColorPicker, closeColorPicker, changeEndValue, setChangeEndValue, setDureeDeLexamen, eventAganda, setEventAganda, agendaTypes, setAgendaTypes, newAgendaType, setNewAgendaType, isWaitingList, eventDate, setEventDate, eventTime, setEventTime, eventConsultation, openListRendezVous, ListRendezVousOpened, closeListRendezVous, patientcomment, setPatientcomment, patientnotes, setPatientNotes, patientcommentairelistedattente, setPatientCommentairelistedattente, eventResourceId, setEventResourceId, eventType, checkedAppelvideo, handleAppelvideoChange, checkedRappelSms, handleRappelSmsChange, checkedRappelEmail, handleRappelEmailChange, currentPatient, waitingList, setWaitingList, setPatientModalOpen } = props;\n    var _eventAge_toString;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Modal.Root, {\n        opened: opened,\n        onClose: onClose,\n        size: \"70%\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Modal.Overlay, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                lineNumber: 451,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Modal.Content, {\n                className: \"overflow-y-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Modal.Header, {\n                        style: {\n                            height: '60px',\n                            background: \"#3799CE\",\n                            padding: \"11px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Modal.Title, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                        fw: 600,\n                                        c: \"var(--mantine-color-white)\",\n                                        className: \"mb-2 flex gap-2 text-lg font-semibold leading-none tracking-tight\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                width: \"1em\",\n                                                height: \"1em\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M21 7.5V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h3.5M16 2v4M8 2v4m-5 4h5m9.5 7.5L16 16.3V14\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 469,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                            cx: 16,\n                                                            cy: 16,\n                                                            r: 6\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 470,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                    lineNumber: 462,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                lineNumber: 456,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Ajouter un rendez-vous\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                        lineNumber: 455,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground text-sm text-[var(--mantine-color-white)]\",\n                                        children: \"Remplissez les d\\xe9tails ci-dessous pour ajouter un nouvel \\xe9v\\xe9nement.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                        lineNumber: 475,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                lineNumber: 454,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Group, {\n                                justify: \"flex-end\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Switch, {\n                                        defaultChecked: true,\n                                        color: \"teal\",\n                                        size: \"xs\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                        lineNumber: 480,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground text-sm text-[var(--mantine-color-white)]\",\n                                        children: \"Pause\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                        lineNumber: 485,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Modal.CloseButton, {\n                                        className: \"mantine-focus-always\",\n                                        style: {\n                                            color: \"white\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                        lineNumber: 488,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                lineNumber: 479,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                        lineNumber: 453,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Modal.Body, {\n                        style: {\n                            padding: '0px'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-2 pl-4 h-[600px]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(simplebar_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"simplebar-scrollable-y h-[calc(100%)]\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"pr-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: (e)=>{\n                                            e.preventDefault();\n                                            handleSubmit(appointmentForm.values);\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid gap-3 py-2 pr-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-4 mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                            value: eventTitle,\n                                                            onChange: (value)=>setEventTitle(value !== null && value !== void 0 ? value : \"\"),\n                                                            placeholder: \"Titre\",\n                                                            data: titleOptions,\n                                                            className: \"w-full\",\n                                                            leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarPlus_FaMicrophone_FaMicrophoneLines_FaUserDoctor_react_icons_fa6__WEBPACK_IMPORTED_MODULE_12__.FaUserDoctor, {\n                                                                size: 16\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                lineNumber: 506,\n                                                                columnNumber: 38\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 500,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Menu, {\n                                                            width: 200,\n                                                            shadow: \"md\",\n                                                            closeOnItemClick: false,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Menu.Target, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Avatar, {\n                                                                        color: \"#4BA3D3\",\n                                                                        radius: \"sm\",\n                                                                        h: 36,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCheck_IconColorPicker_IconHexagonPlusFilled_tabler_icons_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                            size: 20\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                            lineNumber: 511,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 510,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 509,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Menu.Dropdown, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.TextInput, {\n                                                                        leftSectionPointerEvents: \"none\",\n                                                                        leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarPlus_FaMicrophone_FaMicrophoneLines_FaUserDoctor_react_icons_fa6__WEBPACK_IMPORTED_MODULE_12__.FaUserDoctor, {\n                                                                            size: 16\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                            lineNumber: 517,\n                                                                            columnNumber: 42\n                                                                        }, void 0),\n                                                                        placeholder: \"Ajouter des titres\",\n                                                                        value: newTitle,\n                                                                        onChange: (e)=>setNewTitle(e.target.value),\n                                                                        rightSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.ActionIcon, {\n                                                                            size: \"sm\",\n                                                                            onClick: ()=>{\n                                                                                if (newTitle.trim()) {\n                                                                                    const newTitleOption = {\n                                                                                        value: newTitle,\n                                                                                        label: newTitle\n                                                                                    };\n                                                                                    setTitleOptions([\n                                                                                        ...titleOptions,\n                                                                                        newTitleOption\n                                                                                    ]);\n                                                                                    setEventTitle(newTitle);\n                                                                                    setNewTitle(\"\");\n                                                                                    _mantine_notifications__WEBPACK_IMPORTED_MODULE_18__.notifications.show({\n                                                                                        title: 'Titre ajouté',\n                                                                                        message: '\"'.concat(newTitle, '\" a \\xe9t\\xe9 ajout\\xe9 \\xe0 la liste des titres'),\n                                                                                        color: 'green',\n                                                                                        autoClose: 2000\n                                                                                    });\n                                                                                }\n                                                                            },\n                                                                            disabled: !newTitle.trim(),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarPlus_FaMicrophone_FaMicrophoneLines_FaUserDoctor_react_icons_fa6__WEBPACK_IMPORTED_MODULE_12__.FaCalendarPlus, {\n                                                                                size: 16\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                lineNumber: 540,\n                                                                                columnNumber: 33\n                                                                            }, void 0)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                            lineNumber: 522,\n                                                                            columnNumber: 31\n                                                                        }, void 0),\n                                                                        onKeyDown: (e)=>{\n                                                                            if (e.key === 'Enter' && newTitle.trim()) {\n                                                                                const newTitleOption = {\n                                                                                    value: newTitle,\n                                                                                    label: newTitle\n                                                                                };\n                                                                                setTitleOptions([\n                                                                                    ...titleOptions,\n                                                                                    newTitleOption\n                                                                                ]);\n                                                                                setEventTitle(newTitle);\n                                                                                setNewTitle(\"\");\n                                                                                _mantine_notifications__WEBPACK_IMPORTED_MODULE_18__.notifications.show({\n                                                                                    title: 'Titre ajouté',\n                                                                                    message: '\"'.concat(newTitle, '\" a \\xe9t\\xe9 ajout\\xe9 \\xe0 la liste des titres'),\n                                                                                    color: 'green',\n                                                                                    autoClose: 2000\n                                                                                });\n                                                                            }\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 515,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 514,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 508,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.TextInput, {\n                                                            id: \"event-nom\",\n                                                            placeholder: \"Nom *\",\n                                                            type: \"text\",\n                                                            value: patientName,\n                                                            onChange: (e)=>setPatientName(e.target.value),\n                                                            required: true,\n                                                            className: \"input input-bordered w-full\",\n                                                            leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiUserFollowLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_19__.RiUserFollowLine, {\n                                                                size: 16\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                lineNumber: 568,\n                                                                columnNumber: 38\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 560,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.TextInput, {\n                                                            id: \"event-prenom\",\n                                                            placeholder: \"Pr\\xe9nom *\",\n                                                            type: \"text\",\n                                                            value: patientlastName,\n                                                            onChange: (e)=>setPatientlastName(e.target.value),\n                                                            required: true,\n                                                            className: \"input input-bordered w-full\",\n                                                            leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiUserFollowLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_19__.RiUserFollowLine, {\n                                                                size: 16\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                lineNumber: 578,\n                                                                columnNumber: 38\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 570,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Avatar, {\n                                                            color: \"#4BA3D3\",\n                                                            radius: \"sm\",\n                                                            h: 36,\n                                                            onClick: openListDesPatient,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MdOutlineBedroomChild_MdOutlineContentPasteSearch_MdOutlineSocialDistance_react_icons_md__WEBPACK_IMPORTED_MODULE_20__.MdOutlineContentPasteSearch, {\n                                                                size: 20\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                lineNumber: 581,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 580,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                    lineNumber: 499,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-4 mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.TextInput, {\n                                                            type: \"date\",\n                                                            placeholder: \"Date de Naissance...\",\n                                                            id: \"event-dateDeNaissance\",\n                                                            value: eventDateDeNaissance,\n                                                            onChange: handleDateChange,\n                                                            required: true,\n                                                            className: \"input input-bordered max-w-[278px] w-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 587,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.TextInput, {\n                                                            type: \"text\",\n                                                            id: \"event-age\",\n                                                            value: (_eventAge_toString = eventAge === null || eventAge === void 0 ? void 0 : eventAge.toString()) !== null && _eventAge_toString !== void 0 ? _eventAge_toString : \"\",\n                                                            placeholder: eventAge !== null ? eventAge.toString() : \"Veuillez entrer votre date de naissance\",\n                                                            readOnly: true,\n                                                            className: \"input input-bordered max-w-[278px] w-full\",\n                                                            leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LiaAddressCardSolid_LiaBirthdayCakeSolid_react_icons_lia__WEBPACK_IMPORTED_MODULE_21__.LiaBirthdayCakeSolid, {\n                                                                size: 16\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                lineNumber: 607,\n                                                                columnNumber: 38\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 596,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Radio.Group, {\n                                                                value: genderOption,\n                                                                onChange: handleOptionChange,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Radio, {\n                                                                            value: \"Homme\",\n                                                                            label: \"Homme\"\n                                                                        }, \"homme\", false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                            lineNumber: 615,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Radio, {\n                                                                            value: \"Femme\",\n                                                                            label: \"Femme\"\n                                                                        }, \"femme\", false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                            lineNumber: 616,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Radio, {\n                                                                            value: \"Enfant\",\n                                                                            label: \"Enfant\"\n                                                                        }, \"enfant\", false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                            lineNumber: 617,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 614,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                lineNumber: 610,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 609,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                    lineNumber: 586,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                            value: eventEtatCivil,\n                                                            onChange: (value)=>setEventEtatCivil(value !== null && value !== void 0 ? value : \"\"),\n                                                            placeholder: \"\\xc9tat civil\",\n                                                            data: [\n                                                                {\n                                                                    value: \"Célibataire\",\n                                                                    label: \"Célibataire\"\n                                                                },\n                                                                {\n                                                                    value: \"Marié(e)\",\n                                                                    label: \"Marié(e)\"\n                                                                },\n                                                                {\n                                                                    value: \"Divorcé(e)\",\n                                                                    label: \"Divorcé(e)\"\n                                                                },\n                                                                {\n                                                                    value: \"Veuf(ve)\",\n                                                                    label: \"Veuf(ve)\"\n                                                                },\n                                                                {\n                                                                    value: \"Autre chose\",\n                                                                    label: \"Autre chose\"\n                                                                }\n                                                            ],\n                                                            className: \"select w-full max-w-xs\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 625,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.TextInput, {\n                                                            placeholder: \"CIN\",\n                                                            disabled: genderOption === 'Enfant',\n                                                            value: eventCin,\n                                                            onChange: (e)=>setEventCin(e.target.value),\n                                                            styles: {\n                                                                input: {\n                                                                    backgroundColor: genderOption === 'Enfant' ? '#f5f5f5' : undefined,\n                                                                    color: genderOption === 'Enfant' ? '#999' : undefined\n                                                                }\n                                                            },\n                                                            className: \"input input-bordered mb-2 w-full\",\n                                                            leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TbNumber_react_icons_tb__WEBPACK_IMPORTED_MODULE_23__.TbNumber, {\n                                                                size: 16\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                lineNumber: 650,\n                                                                columnNumber: 38\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 638,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.TextInput, {\n                                                            id: \"Adresse\",\n                                                            placeholder: \"Adress\\xe9 par\",\n                                                            value: address,\n                                                            onChange: (e)=>setAddress(e.target.value),\n                                                            className: \"input input-bordered mb-2 w-full\",\n                                                            leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LiaAddressCardSolid_LiaBirthdayCakeSolid_react_icons_lia__WEBPACK_IMPORTED_MODULE_21__.LiaAddressCardSolid, {\n                                                                size: 16\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                lineNumber: 658,\n                                                                columnNumber: 38\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 652,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                    lineNumber: 624,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.InputBase, {\n                                                            id: \"T\\xe9l\\xe9phone\",\n                                                            component: react_imask__WEBPACK_IMPORTED_MODULE_2__.IMaskInput,\n                                                            mask: \"00-00-00-00-00\",\n                                                            placeholder: \"T\\xe9l\\xe9phone\",\n                                                            value: eventTelephone,\n                                                            onAccept: (value)=>setEventTelephone(value),\n                                                            unmask: true,\n                                                            className: \"input input-bordered mb-2 w-full\",\n                                                            leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiPhone_react_icons_fi__WEBPACK_IMPORTED_MODULE_25__.FiPhone, {\n                                                                size: 16\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                lineNumber: 673,\n                                                                columnNumber: 38\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 664,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.TextInput, {\n                                                            id: \"Email\",\n                                                            placeholder: \"Email\",\n                                                            value: email,\n                                                            onChange: (e)=>setEmail(e.target.value),\n                                                            className: \"input input-bordered mb-2 w-full\",\n                                                            leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CiAt_react_icons_ci__WEBPACK_IMPORTED_MODULE_26__.CiAt, {\n                                                                size: 16\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                lineNumber: 681,\n                                                                columnNumber: 38\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 675,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                    lineNumber: 663,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                            value: patientdoctor,\n                                                            onChange: (value)=>setPatientDocteur(value !== null && value !== void 0 ? value : \"\"),\n                                                            placeholder: \"Docteur\",\n                                                            data: [\n                                                                {\n                                                                    value: \"Docteur\",\n                                                                    label: \"Docteur\"\n                                                                },\n                                                                {\n                                                                    value: \"dr.Kader\",\n                                                                    label: \"dr.Kader\"\n                                                                },\n                                                                {\n                                                                    value: \"dr.Kaders\",\n                                                                    label: \"dr.Kaders\"\n                                                                }\n                                                            ],\n                                                            className: \"w-full\",\n                                                            leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarPlus_FaMicrophone_FaMicrophoneLines_FaUserDoctor_react_icons_fa6__WEBPACK_IMPORTED_MODULE_12__.FaUserDoctor, {\n                                                                size: 16\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                lineNumber: 697,\n                                                                columnNumber: 38\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 687,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                            value: patientsocialSecurity || 'Aucune',\n                                                            onChange: (value)=>setSocialSecurity(value || 'Aucune'),\n                                                            placeholder: \"S\\xe9curit\\xe9 sociale\",\n                                                            data: [\n                                                                {\n                                                                    value: \"Aucune\",\n                                                                    label: \"Aucune\"\n                                                                },\n                                                                {\n                                                                    value: \"CNSS\",\n                                                                    label: \"CNSS\"\n                                                                },\n                                                                {\n                                                                    value: \"AMO\",\n                                                                    label: \"AMO\"\n                                                                }\n                                                            ],\n                                                            className: \"w-full\",\n                                                            leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MdOutlineBedroomChild_MdOutlineContentPasteSearch_MdOutlineSocialDistance_react_icons_md__WEBPACK_IMPORTED_MODULE_20__.MdOutlineSocialDistance, {\n                                                                size: 16\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                lineNumber: 709,\n                                                                columnNumber: 38\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 699,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                    lineNumber: 686,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-4 mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                            label: \"Type de consultation\",\n                                                            placeholder: \"Rechercher ou saisir...\",\n                                                            data: consultationTypes,\n                                                            value: patienttypeConsultation,\n                                                            onChange: (value)=>{\n                                                                setPatientTypeConsultation(value !== null && value !== void 0 ? value : \"\");\n                                                                const selectedLabel = [\n                                                                    {\n                                                                        value: \"Visite de malade\",\n                                                                        eventType: \"visit\"\n                                                                    },\n                                                                    {\n                                                                        value: \"Visitor Counter\",\n                                                                        eventType: \"visitor-counter\"\n                                                                    },\n                                                                    {\n                                                                        value: \"Completed\",\n                                                                        eventType: \"completed\"\n                                                                    }\n                                                                ].find((item)=>item.value === value);\n                                                                if (selectedLabel) {\n                                                                    setEventType(selectedLabel.eventType);\n                                                                }\n                                                            },\n                                                            searchable: true,\n                                                            searchValue: searchValue,\n                                                            onSearchChange: setSearchValue,\n                                                            clearable: true,\n                                                            maxDropdownHeight: 280,\n                                                            rightSectionWidth: 70,\n                                                            required: true,\n                                                            rightSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-[#4CAF50] text-white px-2 py-1 rounded text-xs\",\n                                                                children: dureeDeLexamen\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                lineNumber: 740,\n                                                                columnNumber: 27\n                                                            }, void 0),\n                                                            allowDeselect: true,\n                                                            className: \"w-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 715,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Menu, {\n                                                            width: 260,\n                                                            shadow: \"md\",\n                                                            closeOnItemClick: false,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Menu.Target, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Avatar, {\n                                                                        color: \"#4BA3D3\",\n                                                                        radius: \"sm\",\n                                                                        h: 36,\n                                                                        mt: \"24\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCheck_IconColorPicker_IconHexagonPlusFilled_tabler_icons_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                            size: 20\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                            lineNumber: 748,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 747,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 746,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Menu.Dropdown, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.TextInput, {\n                                                                                leftSectionPointerEvents: \"none\",\n                                                                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarPlus_FaMicrophone_FaMicrophoneLines_FaUserDoctor_react_icons_fa6__WEBPACK_IMPORTED_MODULE_12__.FaUserDoctor, {\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                    lineNumber: 755,\n                                                                                    columnNumber: 44\n                                                                                }, void 0),\n                                                                                placeholder: \"Ajouter des Consultation\",\n                                                                                value: newConsultationType,\n                                                                                onChange: (e)=>setNewConsultationType(e.target.value),\n                                                                                rightSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.ActionIcon, {\n                                                                                    size: \"sm\",\n                                                                                    onClick: ()=>{\n                                                                                        if (newConsultationType.trim()) {\n                                                                                            const newType = {\n                                                                                                value: newConsultationType,\n                                                                                                label: newConsultationType,\n                                                                                                duration: dureeDeLexamen || \"15 min\"\n                                                                                            };\n                                                                                            setConsultationTypes([\n                                                                                                ...consultationTypes,\n                                                                                                newType\n                                                                                            ]);\n                                                                                            setPatientTypeConsultation(newConsultationType);\n                                                                                            setNewConsultationType(\"\");\n                                                                                            _mantine_notifications__WEBPACK_IMPORTED_MODULE_18__.notifications.show({\n                                                                                                title: 'Type de consultation ajouté',\n                                                                                                message: '\"'.concat(newConsultationType, '\" a \\xe9t\\xe9 ajout\\xe9'),\n                                                                                                color: 'green',\n                                                                                                autoClose: 2000\n                                                                                            });\n                                                                                        }\n                                                                                    },\n                                                                                    disabled: !newConsultationType.trim(),\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarPlus_FaMicrophone_FaMicrophoneLines_FaUserDoctor_react_icons_fa6__WEBPACK_IMPORTED_MODULE_12__.FaCalendarPlus, {\n                                                                                        size: 16\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                        lineNumber: 782,\n                                                                                        columnNumber: 35\n                                                                                    }, void 0)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                    lineNumber: 760,\n                                                                                    columnNumber: 33\n                                                                                }, void 0),\n                                                                                onKeyDown: (e)=>{\n                                                                                    if (e.key === 'Enter' && newConsultationType.trim()) {\n                                                                                        const newType = {\n                                                                                            value: newConsultationType,\n                                                                                            label: newConsultationType,\n                                                                                            duration: dureeDeLexamen || \"15 min\"\n                                                                                        };\n                                                                                        setConsultationTypes([\n                                                                                            ...consultationTypes,\n                                                                                            newType\n                                                                                        ]);\n                                                                                        setPatientTypeConsultation(newConsultationType);\n                                                                                        setNewConsultationType(\"\");\n                                                                                        _mantine_notifications__WEBPACK_IMPORTED_MODULE_18__.notifications.show({\n                                                                                            title: 'Type de consultation ajouté',\n                                                                                            message: '\"'.concat(newConsultationType, '\" a \\xe9t\\xe9 ajout\\xe9'),\n                                                                                            color: 'green',\n                                                                                            autoClose: 2000\n                                                                                        });\n                                                                                    }\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                lineNumber: 753,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Avatar, {\n                                                                                color: newConsultationColor,\n                                                                                radius: \"sm\",\n                                                                                ml: 4,\n                                                                                h: 36,\n                                                                                onClick: openedColorPicker,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                    fill: \"none\",\n                                                                                    viewBox: \"0 0 200 200\",\n                                                                                    style: {\n                                                                                        width: \"26px\",\n                                                                                        height: \"26px\"\n                                                                                    },\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        fill: \"#FF5178\",\n                                                                                        d: \"M100 0a100 100 0 00-50 13.398l30 51.961A40 40 0 01100 60V0z\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                        lineNumber: 814,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                    lineNumber: 811,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                lineNumber: 804,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 752,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 751,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 745,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Modal, {\n                                                            opened: ColorPickeropened,\n                                                            onClose: closeColorPicker,\n                                                            size: \"auto\",\n                                                            yOffset: \"18vh\",\n                                                            xOffset: 30,\n                                                            withCloseButton: false,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_27__.ColorPicker, {\n                                                                    defaultValue: newConsultationColor,\n                                                                    value: newConsultationColor,\n                                                                    onChange: setNewConsultationColor,\n                                                                    onChangeEnd: setChangeEndValue,\n                                                                    format: \"hex\",\n                                                                    swatches: [\n                                                                        '#2e2e2e',\n                                                                        '#868e96',\n                                                                        '#fa5252',\n                                                                        '#e64980',\n                                                                        '#be4bdb',\n                                                                        '#7950f2',\n                                                                        '#4c6ef5',\n                                                                        '#228be6',\n                                                                        '#15aabf',\n                                                                        '#12b886',\n                                                                        '#40c057',\n                                                                        '#82c91e',\n                                                                        '#fab005',\n                                                                        '#fd7e14'\n                                                                    ]\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 821,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Group, {\n                                                                    justify: \"center\",\n                                                                    mt: 8,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                                        variant: \"filled\",\n                                                                        w: \"100%\",\n                                                                        color: \"\".concat(newConsultationColor),\n                                                                        leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCheck_IconColorPicker_IconHexagonPlusFilled_tabler_icons_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                            stroke: 1,\n                                                                            size: 18\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                            lineNumber: 834,\n                                                                            columnNumber: 42\n                                                                        }, void 0),\n                                                                        onClick: ()=>{\n                                                                            setNewConsultationColor(changeEndValue);\n                                                                            closeColorPicker();\n                                                                        },\n                                                                        children: \"S\\xe9lectionner cette couleur\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 830,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 829,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 820,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                            label: \"Dur\\xe9e\",\n                                                            value: dureeDeLexamen,\n                                                            onChange: (value)=>setDureeDeLexamen(value !== null && value !== void 0 ? value : \"\"),\n                                                            placeholder: \"15 min\",\n                                                            data: [\n                                                                \"10 min\",\n                                                                \"15 min\",\n                                                                \"20 min\",\n                                                                \"25 min\",\n                                                                \"30 min\",\n                                                                \"35 min\",\n                                                                \"40 min\",\n                                                                \"45 min\"\n                                                            ],\n                                                            className: \"select w-full max-w-xs\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 845,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                            label: \"Agenda\",\n                                                            value: eventAganda,\n                                                            onChange: (value)=>setEventAganda(value !== null && value !== void 0 ? value : \"\"),\n                                                            placeholder: \"Ajouter des Agenda\",\n                                                            data: agendaTypes,\n                                                            className: \"w-full\",\n                                                            leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarPlus_FaMicrophone_FaMicrophoneLines_FaUserDoctor_react_icons_fa6__WEBPACK_IMPORTED_MODULE_12__.FaUserDoctor, {\n                                                                size: 16\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                lineNumber: 860,\n                                                                columnNumber: 38\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 853,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Menu, {\n                                                            width: 200,\n                                                            shadow: \"md\",\n                                                            closeOnItemClick: false,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Menu.Target, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Avatar, {\n                                                                        color: \"#4BA3D3\",\n                                                                        radius: \"sm\",\n                                                                        h: 36,\n                                                                        mt: \"24\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCheck_IconColorPicker_IconHexagonPlusFilled_tabler_icons_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                            size: 20\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                            lineNumber: 865,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 864,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 863,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Menu.Dropdown, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.TextInput, {\n                                                                        leftSectionPointerEvents: \"none\",\n                                                                        leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarPlus_FaMicrophone_FaMicrophoneLines_FaUserDoctor_react_icons_fa6__WEBPACK_IMPORTED_MODULE_12__.FaUserDoctor, {\n                                                                            size: 16\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                            lineNumber: 871,\n                                                                            columnNumber: 42\n                                                                        }, void 0),\n                                                                        placeholder: \"Ajouter des Agenda\",\n                                                                        value: newAgendaType,\n                                                                        onChange: (e)=>setNewAgendaType(e.target.value),\n                                                                        rightSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.ActionIcon, {\n                                                                            size: \"sm\",\n                                                                            onClick: ()=>{\n                                                                                if (newAgendaType.trim()) {\n                                                                                    const newAgendaOption = {\n                                                                                        value: newAgendaType,\n                                                                                        label: newAgendaType\n                                                                                    };\n                                                                                    setAgendaTypes([\n                                                                                        ...agendaTypes,\n                                                                                        newAgendaOption\n                                                                                    ]);\n                                                                                    setEventAganda(newAgendaType);\n                                                                                    setNewAgendaType(\"\");\n                                                                                    _mantine_notifications__WEBPACK_IMPORTED_MODULE_18__.notifications.show({\n                                                                                        title: 'Agenda ajouté',\n                                                                                        message: '\"'.concat(newAgendaType, '\" a \\xe9t\\xe9 ajout\\xe9 \\xe0 la liste des agendas'),\n                                                                                        color: 'green',\n                                                                                        autoClose: 2000\n                                                                                    });\n                                                                                }\n                                                                            },\n                                                                            disabled: !newAgendaType.trim(),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarPlus_FaMicrophone_FaMicrophoneLines_FaUserDoctor_react_icons_fa6__WEBPACK_IMPORTED_MODULE_12__.FaCalendarPlus, {\n                                                                                size: 16\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                lineNumber: 894,\n                                                                                columnNumber: 33\n                                                                            }, void 0)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                            lineNumber: 876,\n                                                                            columnNumber: 31\n                                                                        }, void 0),\n                                                                        onKeyDown: (e)=>{\n                                                                            if (e.key === 'Enter' && newAgendaType.trim()) {\n                                                                                const newAgendaOption = {\n                                                                                    value: newAgendaType,\n                                                                                    label: newAgendaType\n                                                                                };\n                                                                                setAgendaTypes([\n                                                                                    ...agendaTypes,\n                                                                                    newAgendaOption\n                                                                                ]);\n                                                                                setEventAganda(newAgendaType);\n                                                                                setNewAgendaType(\"\");\n                                                                                _mantine_notifications__WEBPACK_IMPORTED_MODULE_18__.notifications.show({\n                                                                                    title: 'Agenda ajouté',\n                                                                                    message: '\"'.concat(newAgendaType, '\" a \\xe9t\\xe9 ajout\\xe9 \\xe0 la liste des agendas'),\n                                                                                    color: 'green',\n                                                                                    autoClose: 2000\n                                                                                });\n                                                                            }\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 869,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 868,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 862,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                    lineNumber: 714,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                !isWaitingList && !appointmentForm.values.addToWaitingList && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mx-auto flex gap-4 mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                            size: \"12px\",\n                                                            className: \"label\",\n                                                            style: {\n                                                                marginTop: \"10px\"\n                                                            },\n                                                            children: \"Date du RDV\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 919,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.TextInput, {\n                                                            id: \"event-date\",\n                                                            type: \"date\",\n                                                            value: eventDate,\n                                                            onChange: (e)=>setEventDate(e.target.value),\n                                                            className: \"input input-bordered mb-2 w-64 max-w-64\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 920,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                            size: \"12px\",\n                                                            className: \"label\",\n                                                            style: {\n                                                                marginTop: \"10px\"\n                                                            },\n                                                            children: \"De*\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 927,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.TextInput, {\n                                                            id: \"event-time\",\n                                                            type: \"time\",\n                                                            value: eventTime,\n                                                            onChange: (e)=>setEventTime(e.target.value),\n                                                            className: \"input input-bordered mb-2 w-64 max-w-64\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 928,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                            size: \"12px\",\n                                                            className: \"label\",\n                                                            style: {\n                                                                marginTop: \"10px\"\n                                                            },\n                                                            children: \"\\xe0*\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 935,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.TextInput, {\n                                                            id: \"event-time-end\",\n                                                            type: \"text\",\n                                                            placeholder: eventTime !== null ? moment__WEBPACK_IMPORTED_MODULE_3___default()(eventTime, \"HH:mm\").add(parseInt(eventConsultation), \"minutes\").format(\"HH:mm\") : \"Please enter your date of birth\",\n                                                            readOnly: true,\n                                                            className: \"input input-bordered mb-2 w-40\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 936,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Avatar, {\n                                                            color: \"#4BA3D3\",\n                                                            radius: \"sm\",\n                                                            h: 36,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ListPlus_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                                size: 30,\n                                                                className: \"text-[#3799CE] cursor-pointer\",\n                                                                onClick: openListRendezVous\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                lineNumber: 950,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 949,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Modal, {\n                                                            opened: ListRendezVousOpened,\n                                                            onClose: closeListRendezVous,\n                                                            size: \"xl\",\n                                                            centered: true,\n                                                            withCloseButton: false,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RendezVousSelector, {\n                                                                onClose: closeListRendezVous\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                lineNumber: 962,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 955,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                    lineNumber: 918,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-4 mb-2 -mt-2 pr-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_30__.Textarea, {\n                                                            id: \"event-Commentaire\",\n                                                            value: patientcomment,\n                                                            onChange: (event)=>{\n                                                                var _event_currentTarget_value;\n                                                                return setPatientcomment((_event_currentTarget_value = event.currentTarget.value) !== null && _event_currentTarget_value !== void 0 ? _event_currentTarget_value : \"\");\n                                                            },\n                                                            placeholder: \"Commentaire ...\",\n                                                            className: \"w-full\",\n                                                            rightSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarPlus_FaMicrophone_FaMicrophoneLines_FaUserDoctor_react_icons_fa6__WEBPACK_IMPORTED_MODULE_12__.FaMicrophoneLines, {\n                                                                size: 18\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                lineNumber: 975,\n                                                                columnNumber: 39\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 969,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_30__.Textarea, {\n                                                            id: \"event-Notes\",\n                                                            value: patientnotes,\n                                                            onChange: (event)=>{\n                                                                var _event_currentTarget_value;\n                                                                return setPatientNotes((_event_currentTarget_value = event.currentTarget.value) !== null && _event_currentTarget_value !== void 0 ? _event_currentTarget_value : \"\");\n                                                            },\n                                                            placeholder: \"Notes ...\",\n                                                            className: \"w-full\",\n                                                            rightSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarPlus_FaMicrophone_FaMicrophoneLines_FaUserDoctor_react_icons_fa6__WEBPACK_IMPORTED_MODULE_12__.FaMicrophoneLines, {\n                                                                size: 18\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                lineNumber: 983,\n                                                                columnNumber: 39\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 977,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_30__.Textarea, {\n                                                            id: \"event-Commentairelistedattente\",\n                                                            value: patientcommentairelistedattente,\n                                                            onChange: (event)=>{\n                                                                var _event_currentTarget_value;\n                                                                return setPatientCommentairelistedattente((_event_currentTarget_value = event.currentTarget.value) !== null && _event_currentTarget_value !== void 0 ? _event_currentTarget_value : \"\");\n                                                            },\n                                                            placeholder: \"Commentaire (liste d'attente)...\",\n                                                            className: \"w-full\",\n                                                            rightSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarPlus_FaMicrophone_FaMicrophoneLines_FaUserDoctor_react_icons_fa6__WEBPACK_IMPORTED_MODULE_12__.FaMicrophone, {\n                                                                size: 18\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                lineNumber: 991,\n                                                                columnNumber: 39\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 985,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                    lineNumber: 968,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-base-100 px-[4px] pt-[8px]\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"text-daisy flex flex-wrap gap-x-4 gap-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-1\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                                    value: eventResourceId ? eventResourceId.toString() : \"\",\n                                                                    onChange: (value)=>{\n                                                                        setEventResourceId(Number(value) || 1);\n                                                                    },\n                                                                    name: \"resourceId\",\n                                                                    placeholder: \"Room\",\n                                                                    data: [\n                                                                        {\n                                                                            value: \"1\",\n                                                                            label: \"Room A\"\n                                                                        },\n                                                                        {\n                                                                            value: \"2\",\n                                                                            label: \"Room B\"\n                                                                        }\n                                                                    ],\n                                                                    required: true,\n                                                                    className: \"select w-full max-w-xs\",\n                                                                    leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MdOutlineBedroomChild_MdOutlineContentPasteSearch_MdOutlineSocialDistance_react_icons_md__WEBPACK_IMPORTED_MODULE_20__.MdOutlineBedroomChild, {\n                                                                        size: 16\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 1012,\n                                                                        columnNumber: 42\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 999,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                lineNumber: 998,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        id: \"visit\",\n                                                                        type: \"radio\",\n                                                                        name: \"eventType\",\n                                                                        value: \"visit\",\n                                                                        className: \"peer hidden\",\n                                                                        checked: eventType === \"visit\",\n                                                                        onChange: (e)=>setEventType(e.target.value)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 1017,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"visit\",\n                                                                        className: \"\".concat(eventType === \"visit\" ? \"peer-checked:text-[#34D1BF]\" : \"text-[var(--mantine-color-dark-0)]\", \" inline-flex h-10 w-full items-center justify-center whitespace-nowrap rounded-md px-4 py-2 text-sm font-medium uppercase\"),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            className: \"flex items-center gap-2 text-xs uppercase\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"disk-teal relative z-10 block h-[12px] w-[12px] rounded-full border-2 border-white\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                    lineNumber: 1035,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                \"Visite de malade\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                            lineNumber: 1034,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 1026,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                lineNumber: 1016,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        id: \"visitor-counter\",\n                                                                        type: \"radio\",\n                                                                        name: \"eventType\",\n                                                                        value: \"visitor-counter\",\n                                                                        className: \"peer hidden\",\n                                                                        checked: eventType === \"visitor-counter\",\n                                                                        onChange: (e)=>setEventType(e.target.value)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 1042,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"visitor-counter\",\n                                                                        className: \"\".concat(eventType === \"visitor-counter\" ? \"peer-checked:text-[#F17105]\" : \"text-[var(--mantine-color-dark-0)]\", \" inline-flex h-10 w-full items-center justify-center whitespace-nowrap rounded-md px-4 py-2 text-sm font-medium uppercase\"),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            className: \"flex items-center gap-2 text-xs uppercase\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"disk-orange relative z-10 block h-[12px] w-[12px] rounded-full border-2 border-white\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                    lineNumber: 1060,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                \"Visitor Counter\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                            lineNumber: 1059,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 1051,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                lineNumber: 1041,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        id: \"completed\",\n                                                                        type: \"radio\",\n                                                                        name: \"eventType\",\n                                                                        value: \"completed\",\n                                                                        className: \"peer hidden\",\n                                                                        checked: eventType === \"completed\",\n                                                                        onChange: (e)=>setEventType(e.target.value)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 1067,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"completed\",\n                                                                        className: \"\".concat(eventType === \"completed\" ? \"peer-checked:text-[#3799CE]\" : \"text-[var(--mantine-color-dark-0)]\", \" inline-flex h-10 w-full items-center justify-center whitespace-nowrap px-4 py-2 text-sm font-medium uppercase\"),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            className: \"flex items-center gap-2 text-xs uppercase\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"disk-azure relative z-10 block h-[12px] w-[12px] rounded-full border-2 border-white\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                    lineNumber: 1085,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                \"Completed\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                            lineNumber: 1084,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 1076,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                lineNumber: 1066,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        id: \"diagnosis\",\n                                                                        type: \"radio\",\n                                                                        name: \"eventType\",\n                                                                        value: \"diagnosis\",\n                                                                        checked: eventType === \"diagnosis\",\n                                                                        className: \"peer hidden\",\n                                                                        onChange: (e)=>setEventType(e.target.value)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 1092,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"diagnosis\",\n                                                                        className: \"\".concat(eventType === \"diagnosis\" ? \"peer-checked:text-[#F3124E]\" : \"text-[var(--mantine-color-dark-0)]\", \" inline-flex h-10 w-full items-center justify-center whitespace-nowrap px-4 py-2 text-sm font-medium uppercase\"),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            className: \"flex items-center gap-2 text-xs uppercase\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"disk-red relative z-10 block h-[12px] w-[12px] rounded-full border-2 border-white\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                    lineNumber: 1110,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                \"Re-diagnose\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                            lineNumber: 1109,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 1101,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                lineNumber: 1091,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                        lineNumber: 997,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                    lineNumber: 996,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 pr-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Group, {\n                                                            gap: \"xs\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Switch, {\n                                                                    color: \"teal\",\n                                                                    size: \"xs\",\n                                                                    label: \"Add to Waiting List\",\n                                                                    checked: appointmentForm.values.addToWaitingList,\n                                                                    onChange: (event)=>{\n                                                                        appointmentForm.setFieldValue('addToWaitingList', event.currentTarget.checked);\n                                                                        appointmentForm.setFieldValue('removeFromCalendar', event.currentTarget.checked);\n                                                                    },\n                                                                    thumbIcon: appointmentForm.values.addToWaitingList ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCheck_IconColorPicker_IconHexagonPlusFilled_tabler_icons_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                                        size: 12,\n                                                                        color: \"var(--mantine-color-teal-6)\",\n                                                                        stroke: 3\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 1132,\n                                                                        columnNumber: 31\n                                                                    }, void 0) : null\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 1121,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Switch, {\n                                                                    checked: checkedAppelvideo,\n                                                                    onChange: handleAppelvideoChange,\n                                                                    color: \"teal\",\n                                                                    size: \"xs\",\n                                                                    label: \"Appel video\",\n                                                                    thumbIcon: checkedAppelvideo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCheck_IconColorPicker_IconHexagonPlusFilled_tabler_icons_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                                        size: 12,\n                                                                        color: \"var(--mantine-color-teal-6)\",\n                                                                        stroke: 3\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 1147,\n                                                                        columnNumber: 31\n                                                                    }, void 0) : null\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 1139,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Switch, {\n                                                                    checked: checkedRappelSms,\n                                                                    onChange: handleRappelSmsChange,\n                                                                    color: \"teal\",\n                                                                    size: \"xs\",\n                                                                    label: \"Rappel Sms\",\n                                                                    thumbIcon: checkedRappelSms ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCheck_IconColorPicker_IconHexagonPlusFilled_tabler_icons_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                                        size: 12,\n                                                                        color: \"var(--mantine-color-teal-6)\",\n                                                                        stroke: 3\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 1161,\n                                                                        columnNumber: 31\n                                                                    }, void 0) : null\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 1153,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Switch, {\n                                                                    checked: checkedRappelEmail,\n                                                                    onChange: handleRappelEmailChange,\n                                                                    color: \"teal\",\n                                                                    size: \"xs\",\n                                                                    label: \"Rappel e-mail\",\n                                                                    thumbIcon: checkedRappelEmail ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCheck_IconColorPicker_IconHexagonPlusFilled_tabler_icons_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                                        size: 12,\n                                                                        color: \"var(--mantine-color-teal-6)\",\n                                                                        stroke: 3\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 1175,\n                                                                        columnNumber: 31\n                                                                    }, void 0) : null\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 1167,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 1120,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                            type: \"submit\",\n                                                            className: \"btn mb-2 bg-[#03A684] text-[var(--mantine-Button-label-MB)] hover:bg-[#03A684]/90\",\n                                                            onClick: ()=>{\n                                                                onClose();\n                                                            },\n                                                            children: currentPatient ? \"Enregistrer\" : \"Ajouter\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 1183,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        currentPatient && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                            color: \"red\",\n                                                            onClick: ()=>{\n                                                                if (currentPatient) {\n                                                                    setWaitingList(waitingList.filter((p)=>p.id !== currentPatient.id));\n                                                                    setPatientModalOpen(false);\n                                                                }\n                                                            },\n                                                            className: \"btn mb-2 bg-[#F3124E] text-[var(--mantine-Button-label-MB)] hover:bg-[#F3124E]/90\",\n                                                            children: \"Supprimer\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 1193,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                            onClick: ()=>{\n                                                                onClose();\n                                                            },\n                                                            className: \"btn mb-2 bg-[#F5A524] text-[var(--mantine-Button-label-MB)] hover:bg-[#F5A524]/90\",\n                                                            children: \"Annuler\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 1206,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                    lineNumber: 1119,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                            lineNumber: 497,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                        lineNumber: 496,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                    lineNumber: 495,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                lineNumber: 494,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                            lineNumber: 493,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                        lineNumber: 492,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                lineNumber: 452,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n        lineNumber: 446,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = AjouterUnRendezVous;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AjouterUnRendezVous);\nvar _c, _c1;\n$RefreshReg$(_c, \"RendezVousSelector\");\n$RefreshReg$(_c1, \"AjouterUnRendezVous\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Appointments/overview/AjouterUnRendezVous.tsx\n"));

/***/ })

});