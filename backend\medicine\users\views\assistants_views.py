from rest_framework import generics, permissions, status
from rest_framework.response import Response
from django.contrib.auth import get_user_model
from ..models import User
from .user_serializers import UserSerializer, AssistantSerializer

class AssistantListView(generics.ListAPIView):
    """
    View to list all assistants.
    """
    serializer_class = AssistantSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """
        Filter queryset to only include assistants.
        """
        # Get the assigned_doctor parameter from the request
        assigned_doctor = self.request.query_params.get('assigned_doctor')

        # Base queryset for assistants
        queryset = User.objects.filter(user_type='assistant')

        # Log the request for debugging
        print(f"AssistantListView: Request from user {self.request.user.email} (type: {self.request.user.user_type})")
        print(f"AssistantListView: assigned_doctor param = {assigned_doctor}")

        # If assigned_doctor is provided, filter by it
        if assigned_doctor:
            print(f"AssistantListView: Filtering by assigned_doctor_id={assigned_doctor}")
            queryset = queryset.filter(assigned_doctor_id=assigned_doctor)
        # If the current user is a doctor, only show their assistants
        elif self.request.user.user_type == 'doctor':
            print(f"AssistantListView: Filtering by current doctor {self.request.user.id}")
            queryset = queryset.filter(assigned_doctor=self.request.user)

        # Log the result count
        print(f"AssistantListView: Found {queryset.count()} assistants")

        return queryset

class AssistantCreateView(generics.CreateAPIView):
    """
    View to create a new assistant.
    """
    serializer_class = AssistantSerializer
    permission_classes = [permissions.IsAuthenticated]

    def perform_create(self, serializer):
        """
        Set the user_type to 'assistant' and assigned_doctor to the current user if they are a doctor.
        Also check subscription limits before creating the assistant.
        """
        # Get the current user
        user = self.request.user

        # Check if the current user is a doctor
        if user.user_type == 'doctor':
            # Check subscription limits before creating the assistant
            from subscriptions.models import DoctorSubscription
            from rest_framework.exceptions import ValidationError

            try:
                subscription = DoctorSubscription.objects.get(doctor=user, status='active')

                # Count current assistants
                current_assistants = User.objects.filter(
                    assigned_doctor=user,
                    user_type='assistant'
                ).count()

                # Check if the doctor can create more assistants
                if current_assistants >= subscription.package.max_assistants:
                    raise ValidationError({
                        'detail': f'You have reached the maximum number of assistants ({subscription.package.max_assistants}) '
                                f'allowed by your subscription plan. Please upgrade your plan to add more assistants.'
                    })

                # Create the assistant with the doctor as the assigned_doctor
                assistant = serializer.save(user_type='assistant', assigned_doctor=user)

                # Update the subscription assistant count
                subscription.current_assistant_count = current_assistants + 1
                subscription.save(update_fields=['current_assistant_count'])

            except DoctorSubscription.DoesNotExist:
                raise ValidationError({
                    'detail': 'No active subscription found. Please contact support to activate your subscription.'
                })
        else:
            # If the current user is not a doctor, just set the user_type to 'assistant'
            serializer.save(user_type='assistant')

class AssistantDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    View to retrieve, update, or delete an assistant.
    """
    serializer_class = AssistantSerializer
    permission_classes = [permissions.IsAuthenticated]


    lookup_field = 'pk'

    def get_queryset(self):
        """
        Filter queryset to only include assistants.
        """
        # Get the current user
        user = self.request.user

        # Log the request for debugging
        print(f"AssistantDetailView.get_queryset: Request from user {user.email} (type: {user.user_type})")

        # Base queryset for assistants
        queryset = User.objects.filter(user_type='assistant')

        # If the current user is a doctor, only allow access to their assistants
        if user.user_type == 'doctor':
            print(f"AssistantDetailView.get_queryset: Filtering by current doctor {user.id}")
            queryset = queryset.filter(assigned_doctor=user)
        elif user.is_staff or user.is_superuser:
            print(f"AssistantDetailView.get_queryset: User is staff/superuser, showing all assistants")
            # Admin users can see all assistants
            pass
        else:
            # For other user types, only show assistants assigned to them
            # This is a fallback and might not be needed in most cases
            print(f"AssistantDetailView.get_queryset: Non-doctor user, filtering by assigned_doctor={user.id}")
            queryset = queryset.filter(assigned_doctor=user)

        # Log the result count
        print(f"AssistantDetailView.get_queryset: Found {queryset.count()} assistants")

        return queryset

    def update(self, request, *args, **kwargs):
        """
        Update an assistant.
        """
        # Log the request for debugging
        print(f"AssistantDetailView.update: Request from {request.user.email} (type: {request.user.user_type})")
        print(f"AssistantDetailView.update: Request method = {request.method}")
        print(f"AssistantDetailView.update: Request data = {request.data}")

        # Get the instance
        instance = self.get_object()
        print(f"AssistantDetailView.update: Updating assistant {instance.id} ({instance.email})")

        # Ensure the user_type remains 'assistant'
        if 'user_type' in request.data and request.data['user_type'] != 'assistant':
            print(f"AssistantDetailView.update: Correcting user_type from {request.data['user_type']} to 'assistant'")
            request.data['user_type'] = 'assistant'

        # Handle status conversion
        if 'status' in request.data:
            status_value = request.data.pop('status')
            print(f"AssistantDetailView.update: Converting status '{status_value}' to is_active/is_pending flags")
            if status_value == 'active':
                request.data['is_active'] = True
                request.data['is_pending'] = False
            elif status_value == 'inactive':
                request.data['is_active'] = False
                request.data['is_pending'] = False
            elif status_value == 'pending':
                request.data['is_active'] = False
                request.data['is_pending'] = True

        # Handle password update
        if 'password' in request.data and request.data['password']:
            # Check if password and password2 match
            if 'password2' in request.data and request.data['password'] != request.data['password2']:
                return Response(
                    {"password": "Password fields didn't match."},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Set the password directly on the instance
            password = request.data.pop('password')
            # Remove password2 field as it's not needed for user update
            request.data.pop('password2', None)

            # Set the new password
            instance.set_password(password)
            instance.save(update_fields=['password'])

            # Log the password update
            print(f"AssistantDetailView.update: Set new password for assistant {instance.email}")

        # Log the request data for debugging
        print(f"AssistantDetailView.update: Final request data for {instance.email}:", request.data)

        # Check if there's a profile image in the request
        if 'profile_image' in request.data and request.data['profile_image']:
            print(f"AssistantDetailView.update: Profile image found in request for assistant {instance.email}")
        else:
            print(f"AssistantDetailView.update: No profile image in request for assistant {instance.email}")

        # Call the parent update method
        response = super().update(request, *args, **kwargs)

        # Log the updated instance
        updated_instance = self.get_object()
        print(f"AssistantDetailView.update: Updated assistant {updated_instance.email}")
        # Use the serializer to get the profile_image_url
        serializer = self.get_serializer(updated_instance)
        profile_image_url = serializer.data.get('profile_image_url')
        print(f"AssistantDetailView.update: New profile_image_url: {profile_image_url}")
        print(f"AssistantDetailView.update: Update successful, response status = {response.status_code}")

        return response

    def destroy(self, request, *args, **kwargs):
        """
        Delete an assistant.
        """
        # Get the instance
        instance = self.get_object()

        # Check if the current user is authorized to delete this assistant
        user = request.user
        if user.user_type == 'doctor' and instance.assigned_doctor != user:
            return Response(
                {"detail": "You do not have permission to delete this assistant."},
                status=status.HTTP_403_FORBIDDEN
            )

        # Log the deletion
        print(f"Deleting assistant: {instance.email} (ID: {instance.id})")

        # Call the parent destroy method
        return super().destroy(request, *args, **kwargs)
