"""
Authentication views for the dentistry application.
"""
from rest_framework import viewsets, permissions, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from django.contrib.auth import get_user_model
from dentistry.models import DentistryRole, DentistryStaffProfile

User = get_user_model()
from dentistry.serializers import (
    UserSerializer, DentistryRoleSerializer,
    DentistryStaffProfileSerializer, DentistryStaffProfileCreateSerializer
)

class DentistryRoleViewSet(viewsets.ModelViewSet):
    """
    API endpoint for dentistry roles.
    """
    queryset = DentistryRole.objects.all()
    serializer_class = DentistryRoleSerializer
    permission_classes = [permissions.IsAuthenticated, permissions.IsAdminUser]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'created_at']

class DentistryStaffProfileViewSet(viewsets.ModelViewSet):
    """
    API endpoint for dentistry staff profiles.
    """
    queryset = DentistryStaffProfile.objects.all()
    serializer_class = DentistryStaffProfileSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['role', 'is_active', 'job_title']
    search_fields = ['user__first_name', 'user__last_name', 'job_title', 'specialization', 'bio']
    ordering_fields = ['user__last_name', 'user__first_name', 'hire_date', 'created_at']

    def get_serializer_class(self):
        """
        Return different serializers based on the action.
        """
        if self.action == 'create':
            return DentistryStaffProfileCreateSerializer
        return DentistryStaffProfileSerializer

    def get_permissions(self):
        """
        Only staff can create, update or delete staff profiles.
        """
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            return [permissions.IsAuthenticated(), permissions.IsAdminUser()]
        return [permissions.IsAuthenticated()]

    @action(detail=False, methods=['get'])
    def dentists(self, request):
        """
        Get all dentists.
        """
        dentists = self.queryset.filter(job_title__icontains='dentist') | self.queryset.filter(job_title__icontains='doctor')

        page = self.paginate_queryset(dentists)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(dentists, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def hygienists(self, request):
        """
        Get all hygienists.
        """
        hygienists = self.queryset.filter(job_title__icontains='hygienist')

        page = self.paginate_queryset(hygienists)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(hygienists, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def assistants(self, request):
        """
        Get all assistants.
        """
        assistants = self.queryset.filter(job_title__icontains='assistant')

        page = self.paginate_queryset(assistants)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(assistants, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def deactivate(self, request, pk=None):
        """
        Deactivate a staff member.
        """
        staff_profile = self.get_object()

        if not staff_profile.is_active:
            return Response(
                {"detail": "Staff member is already inactive."},
                status=status.HTTP_400_BAD_REQUEST
            )

        staff_profile.is_active = False
        staff_profile.save()

        # Also deactivate the user account
        user = staff_profile.user
        user.is_active = False
        user.save()

        serializer = self.get_serializer(staff_profile)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def activate(self, request, pk=None):
        """
        Activate a staff member.
        """
        staff_profile = self.get_object()

        if staff_profile.is_active:
            return Response(
                {"detail": "Staff member is already active."},
                status=status.HTTP_400_BAD_REQUEST
            )

        staff_profile.is_active = True
        staff_profile.save()

        # Also activate the user account
        user = staff_profile.user
        user.is_active = True
        user.save()

        serializer = self.get_serializer(staff_profile)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def schedule(self, request, pk=None):
        """
        Get the schedule for a staff member.
        """
        staff_profile = self.get_object()

        # In a real implementation, this would fetch appointments and other schedule data
        # For now, we'll just return the working days and hours

        return Response({
            'staff_member': {
                'id': staff_profile.id,
                'name': staff_profile.full_name,
                'job_title': staff_profile.job_title
            },
            'working_days': staff_profile.working_days,
            'working_hours': staff_profile.working_hours
        })
