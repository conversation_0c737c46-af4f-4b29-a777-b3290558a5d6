from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from users.models import DoctorLicense
from users.utils import LicenseGenerator

# Try to import subscription models
try:
    from subscriptions.models import SubscriptionPackage, DoctorSubscription
    HAS_SUBSCRIPTIONS = True
except ImportError:
    HAS_SUBSCRIPTIONS = False

User = get_user_model()

class Command(BaseCommand):
    help = 'Fix doctor licenses and subscriptions'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS("=== Checking doctor licenses ==="))
        
        # Get all doctors
        doctors = User.objects.filter(user_type='doctor')
        self.stdout.write(self.style.SUCCESS(f"Found {doctors.count()} doctors in the system"))
        
        # Check which doctors have licenses
        doctors_with_licenses = []
        doctors_without_licenses = []
        
        for doctor in doctors:
            try:
                license = DoctorLicense.objects.get(user=doctor)
                doctors_with_licenses.append((doctor, license))
            except DoctorLicense.DoesNotExist:
                doctors_without_licenses.append(doctor)
        
        self.stdout.write(self.style.SUCCESS(f"- {len(doctors_with_licenses)} doctors have licenses"))
        self.stdout.write(self.style.SUCCESS(f"- {len(doctors_without_licenses)} doctors don't have licenses"))
        
        # Create licenses for doctors without one
        for doctor in doctors_without_licenses:
            self.stdout.write(self.style.SUCCESS(f"\nCreating license for doctor: {doctor.email} (ID: {doctor.id})"))
            
            # Generate a secure license number
            license_number, raw_data = LicenseGenerator.generate_license(
                user_id=doctor.id,
                specialization=getattr(doctor, 'specialization', ''),
                expiry_days=365  # Default to 1 year
            )
            
            # Generate an activation code
            activation_code = LicenseGenerator.generate_activation_code()
            
            # Create a license record
            license = DoctorLicense.objects.create(
                user=doctor,
                license_number=license_number,
                status='pending',
                license_data=raw_data,
                issue_date=timezone.now(),
                expiry_date=timezone.now() + timezone.timedelta(days=365),
                activation_code=activation_code
            )
            
            self.stdout.write(self.style.SUCCESS(f"Created license: {license.license_number}"))
            
            # Update the doctor's license_number field if it exists
            if hasattr(doctor, 'license_number'):
                doctor.license_number = license_number
                doctor.save()
                self.stdout.write(self.style.SUCCESS(f"Updated doctor's license_number field"))
        
        # Fix subscriptions if available
        if HAS_SUBSCRIPTIONS:
            self.fix_doctor_subscriptions()
        else:
            self.stdout.write(self.style.SUCCESS("\nSkipping subscription fixes (subscription models not available)"))
        
        # Print summary
        self.stdout.write(self.style.SUCCESS("\n=== Summary ==="))
        self.stdout.write(self.style.SUCCESS(f"Total doctors: {User.objects.filter(user_type='doctor').count()}"))
        self.stdout.write(self.style.SUCCESS(f"Total licenses: {DoctorLicense.objects.count()}"))
        
        if HAS_SUBSCRIPTIONS:
            self.stdout.write(self.style.SUCCESS(f"Total active subscriptions: {DoctorSubscription.objects.filter(status='active').count()}"))
        
        self.stdout.write(self.style.SUCCESS("\nDone! Please refresh the admin page to see the updated counts."))
    
    def fix_doctor_subscriptions(self):
        """
        Check and fix doctor subscriptions:
        1. Find all doctors with licenses
        2. Ensure each doctor has a subscription
        3. Create subscriptions for doctors without one
        """
        self.stdout.write(self.style.SUCCESS("\n=== Checking doctor subscriptions ==="))
        
        # Get all doctor licenses
        licenses = DoctorLicense.objects.all()
        self.stdout.write(self.style.SUCCESS(f"Found {licenses.count()} doctor licenses"))
        
        # Check which doctors have subscriptions
        doctors_with_subscriptions = []
        doctors_without_subscriptions = []
        
        for license in licenses:
            try:
                subscription = DoctorSubscription.objects.filter(doctor=license.user, status='active').first()
                if subscription:
                    doctors_with_subscriptions.append((license.user, subscription))
                else:
                    doctors_without_subscriptions.append(license.user)
            except Exception as e:
                self.stdout.write(self.style.ERROR(f"Error checking subscription for {license.user.email}: {e}"))
                doctors_without_subscriptions.append(license.user)
        
        self.stdout.write(self.style.SUCCESS(f"- {len(doctors_with_subscriptions)} doctors have active subscriptions"))
        self.stdout.write(self.style.SUCCESS(f"- {len(doctors_without_subscriptions)} doctors don't have active subscriptions"))
        
        # Create subscriptions for doctors without one
        if doctors_without_subscriptions:
            # Get or create a default package
            try:
                default_package = SubscriptionPackage.objects.filter(is_active=True).first()
                if not default_package:
                    default_package = SubscriptionPackage.objects.create(
                        name="Default Package",
                        description="Automatically created default package",
                        price_monthly=99.99,
                        price_yearly=999.99,
                        max_assistants=3,
                        max_users=100,
                        max_specialties=5,
                        features=["Basic features"],
                        is_active=True
                    )
                    self.stdout.write(self.style.SUCCESS(f"Created default subscription package: {default_package.name}"))
                
                for doctor in doctors_without_subscriptions:
                    self.stdout.write(self.style.SUCCESS(f"\nCreating subscription for doctor: {doctor.email}"))
                    
                    # Create a subscription
                    subscription = DoctorSubscription.objects.create(
                        doctor=doctor,
                        package=default_package,
                        status='active',
                        billing_cycle='monthly',  # Default to monthly
                        start_date=timezone.now(),
                        end_date=timezone.now() + timezone.timedelta(days=180),  # 6 months
                        price=default_package.price_monthly
                    )
                    
                    self.stdout.write(self.style.SUCCESS(f"Created subscription: {subscription.id} (expires: {subscription.end_date.strftime('%Y-%m-%d')})"))
            except Exception as e:
                self.stdout.write(self.style.ERROR(f"Error creating subscriptions: {e}"))
