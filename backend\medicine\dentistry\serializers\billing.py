"""
Billing serializers for the dentistry application.
"""
from rest_framework import serializers
from dentistry.models import (
    DentistryBillingCode, DentistryInvoice, DentistryPatientInsurance
)

class DentistryBillingCodeSerializer(serializers.ModelSerializer):
    """
    Serializer for the DentistryBillingCode model.
    """

    class Meta:
        model = DentistryBillingCode
        fields = [
            'id', 'code', 'description', 'price', 'category',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

class DentistryInvoiceSerializer(serializers.ModelSerializer):
    """
    Serializer for the DentistryInvoice model.
    """

    class Meta:
        model = DentistryInvoice
        fields = [
            'id', 'patient', 'doctor', 'invoice_number', 'date_issued',
            'due_date', 'total_amount', 'insurance_amount', 'patient_amount',
            'status', 'notes', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

class DentistryPatientInsuranceSerializer(serializers.ModelSerializer):
    """
    Serializer for the DentistryPatientInsurance model.
    """

    class Meta:
        model = DentistryPatientInsurance
        fields = [
            'id', 'patient', 'insurance_provider', 'policy_number',
            'coverage_percentage', 'annual_maximum', 'effective_date',
            'expiration_date', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
