"""
Medical record models for the dentistry application.
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from dentistry.models.base_medical_record import DentistryBaseMedicalRecord
from dentistry.models.doctor import DentistryDoctor
from dentistry.models.patient import DentistryPatient

class DentistryMedicalRecord(DentistryBaseMedicalRecord):
    """
    Dentistry-specific medical record model.
    """
    patient = models.ForeignKey(
        DentistryPatient,
        on_delete=models.CASCADE,
        related_name="medical_records",
        verbose_name=_("Patient")
    )
    doctor = models.ForeignKey(
        DentistryDoctor,
        on_delete=models.SET_NULL,
        null=True,
        related_name="dentistry_records",
        verbose_name=_("Doctor")
    )

    # Dentistry-specific fields
    oral_hygiene_status = models.CharField(
        max_length=20,
        choices=[
            ('excellent', _('Excellent')),
            ('good', _('Good')),
            ('fair', _('Fair')),
            ('poor', _('Poor')),
        ],
        default='good',
        verbose_name=_("Oral Hygiene Status")
    )
    gum_health = models.CharField(
        max_length=20,
        choices=[
            ('healthy', _('Healthy')),
            ('gingivitis', _('Gingivitis')),
            ('periodontitis', _('Periodontitis')),
            ('severe_periodontitis', _('Severe Periodontitis')),
        ],
        default='healthy',
        verbose_name=_("Gum Health")
    )
    teeth_examined = models.BooleanField(
        default=True,
        verbose_name=_("Teeth Examined")
    )
    x_rays_taken = models.BooleanField(
        default=False,
        verbose_name=_("X-Rays Taken")
    )
    teeth_cleaned = models.BooleanField(
        default=False,
        verbose_name=_("Teeth Cleaned")
    )
    fluoride_treatment = models.BooleanField(
        default=False,
        verbose_name=_("Fluoride Treatment")
    )
    teeth_chart_updated = models.BooleanField(
        default=False,
        verbose_name=_("Teeth Chart Updated")
    )

    class Meta:
        verbose_name = _("Dentistry Medical Record")
        verbose_name_plural = _("Dentistry Medical Records")
        ordering = ['-date']

    def __str__(self):
        return f"Dentistry Record for {self.patient.full_name} on {self.date}"

class DentalImaging(DentistryBaseMedicalRecord):
    """
    Dental imaging record model.
    """
    patient = models.ForeignKey(
        DentistryPatient,
        on_delete=models.CASCADE,
        related_name="dental_images",
        verbose_name=_("Patient")
    )
    doctor = models.ForeignKey(
        DentistryDoctor,
        on_delete=models.SET_NULL,
        null=True,
        related_name="dental_images",
        verbose_name=_("Doctor")
    )

    # Imaging-specific fields
    IMAGING_TYPE_CHOICES = (
        ('panoramic', _('Panoramic X-Ray')),
        ('bitewing', _('Bitewing X-Ray')),
        ('periapical', _('Periapical X-Ray')),
        ('cbct', _('Cone Beam CT (CBCT)')),
        ('intraoral', _('Intraoral Photo')),
        ('other', _('Other')),
    )
    imaging_type = models.CharField(
        max_length=20,
        choices=IMAGING_TYPE_CHOICES,
        verbose_name=_("Imaging Type")
    )
    image_file = models.FileField(
        upload_to='dental_images/',
        null=True,
        blank=True,
        verbose_name=_("Image File")
    )
    affected_teeth = models.CharField(
        max_length=100,
        blank=True,
        verbose_name=_("Affected Teeth"),
        help_text=_("Comma-separated list of tooth numbers")
    )
    findings = models.TextField(
        blank=True,
        verbose_name=_("Findings")
    )

    class Meta:
        verbose_name = _("Dental Imaging")
        verbose_name_plural = _("Dental Imaging")
        ordering = ['-date']

    def __str__(self):
        return f"{self.get_imaging_type_display()} for {self.patient.full_name} on {self.date}"
