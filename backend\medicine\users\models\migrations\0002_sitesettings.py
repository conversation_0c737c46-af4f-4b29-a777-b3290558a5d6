from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='SiteSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('support_email', models.EmailField(default='<EMAIL>', help_text='Email address for support inquiries', max_length=255, verbose_name='Support Email')),
                ('contact_email', models.EmailField(default='<EMAIL>', help_text='Email address for general inquiries', max_length=255, verbose_name='Contact Email')),
                ('phone_number', models.CharField(default='+****************', help_text='Main contact phone number', max_length=50, verbose_name='Phone Number')),
                ('address', models.TextField(blank=True, help_text='Physical address of the organization', verbose_name='Address')),
                ('site_name', models.CharField(default='Medical Portal', help_text='Name of the website', max_length=100, verbose_name='Site Name')),
                ('site_url', models.URLField(default='https://example.com', help_text='URL of the website', verbose_name='Site URL')),
                ('facebook_url', models.URLField(blank=True, help_text='URL to the Facebook page', verbose_name='Facebook URL')),
                ('twitter_url', models.URLField(blank=True, help_text='URL to the Twitter profile', verbose_name='Twitter URL')),
                ('instagram_url', models.URLField(blank=True, help_text='URL to the Instagram profile', verbose_name='Instagram URL')),
                ('linkedin_url', models.URLField(blank=True, help_text='URL to the LinkedIn profile', verbose_name='LinkedIn URL')),
                ('youtube_url', models.URLField(blank=True, help_text='URL to the YouTube channel', verbose_name='YouTube URL')),
                ('meta_title', models.CharField(blank=True, help_text='Default meta title for SEO', max_length=200, verbose_name='Meta Title')),
                ('meta_description', models.TextField(blank=True, help_text='Default meta description for SEO', verbose_name='Meta Description')),
                ('meta_keywords', models.TextField(blank=True, help_text='Default meta keywords for SEO (comma-separated)', verbose_name='Meta Keywords')),
                ('og_title', models.CharField(blank=True, help_text='Default OpenGraph title', max_length=200, verbose_name='OpenGraph Title')),
                ('og_description', models.TextField(blank=True, help_text='Default OpenGraph description', verbose_name='OpenGraph Description')),
                ('og_image', models.ImageField(blank=True, help_text='Default OpenGraph image', null=True, upload_to='site_settings/', verbose_name='OpenGraph Image')),
                ('favicon', models.ImageField(blank=True, help_text='Site favicon (should be a .ico or .png file)', null=True, upload_to='site_settings/', verbose_name='Favicon')),
                ('logo', models.ImageField(blank=True, help_text='Site logo', null=True, upload_to='site_settings/', verbose_name='Logo')),
                ('footer_text', models.TextField(blank=True, help_text='Text to display in the footer', verbose_name='Footer Text')),
                ('copyright_text', models.CharField(default='© 2025 Medical Portal. All rights reserved.', help_text='Copyright text to display in the footer', max_length=200, verbose_name='Copyright Text')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Site Settings',
                'verbose_name_plural': 'Site Settings',
            },
        ),
    ]
