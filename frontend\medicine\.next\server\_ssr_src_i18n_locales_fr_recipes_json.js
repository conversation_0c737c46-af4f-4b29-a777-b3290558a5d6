"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_i18n_locales_fr_recipes_json";
exports.ids = ["_ssr_src_i18n_locales_fr_recipes_json"];
exports.modules = {

/***/ "(ssr)/./src/i18n/locales/fr/recipes.json":
/*!******************************************!*\
  !*** ./src/i18n/locales/fr/recipes.json ***!
  \******************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"metadata":{"Title":"Recipes | Medecinsvp","Description":"Medecinsvp est la première plateforme qui prend en compte vos disponibilités pour vous organiser un rendez-vous avec un professionnel de santé en moins d\'une heure","Description-Twitter":"Avec MedecinSvp, trouvez un médecin, un spécialiste ou un dentiste et prenez rendez-vous en ligne 100% gratuit, rapide et efficace.","Keywords":"Medecinsvp,rendez-vous en ligne,trouver un docteur,rendez-vous,rdv,médecin,maroc,Medecinsvp.com,Médecine générale,Maroc"}}');

/***/ })

};
;