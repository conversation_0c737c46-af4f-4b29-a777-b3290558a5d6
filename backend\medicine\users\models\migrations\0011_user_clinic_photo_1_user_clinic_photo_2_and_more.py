# Generated by Django 5.1.3 on 2025-05-08 16:12

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0010_alter_user_user_type'),
    ]

    operations = [
        migrations.AddField(
            model_name='user',
            name='clinic_photo_1',
            field=models.ImageField(blank=True, help_text='First photo of the clinic', null=True, upload_to='clinic_photos/'),
        ),
        migrations.AddField(
            model_name='user',
            name='clinic_photo_2',
            field=models.ImageField(blank=True, help_text='Second photo of the clinic', null=True, upload_to='clinic_photos/'),
        ),
        migrations.AddField(
            model_name='user',
            name='clinic_photo_3',
            field=models.ImageField(blank=True, help_text='Third photo of the clinic', null=True, upload_to='clinic_photos/'),
        ),
        migrations.AddField(
            model_name='user',
            name='facebook_url',
            field=models.URLField(blank=True, help_text='Facebook profile URL', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='linkedin_url',
            field=models.URLField(blank=True, help_text='LinkedIn profile URL', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='telegram_url',
            field=models.URLField(blank=True, help_text='Telegram profile URL', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='twitter_url',
            field=models.URLField(blank=True, help_text='Twitter profile URL', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='whatsapp_number',
            field=models.CharField(blank=True, help_text='WhatsApp number with country code', max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='youtube_url',
            field=models.URLField(blank=True, help_text='YouTube channel URL', max_length=255, null=True),
        ),
    ]
