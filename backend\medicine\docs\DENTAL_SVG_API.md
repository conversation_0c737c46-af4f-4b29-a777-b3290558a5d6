# API Données SVG Dentaires avec Gestion d'Âge

## Vue d'ensemble

Cette API permet la gestion des données SVG dentaires des patients avec des restrictions automatiques basées sur l'âge. Le système masque ou limite l'accès aux informations dentaires selon l'âge du patient pour respecter les protocoles pédiatriques.

## Restrictions d'Âge

| Âge | Restriction | Niveau de Vue | Description |
|-----|-------------|---------------|-------------|
| < 6 ans | `under_6` | `hidden` | Aucune donnée dentaire affichée |
| < 7.5 ans | `under_7_5` | `minimal` | Vue minimale (8 dents de devant) |
| < 12 ans | `under_12` | `limited` | Vue limitée (16 dents) |
| < 13.5 ans | `under_13_5` | `partial` | Vue partielle (24 dents) |
| ≥ 13.5 ans | `none` | `full` | Accès complet (32 dents) |

## Endpoints Principaux

### 1. Donn<PERSON> SVG Dentaires

#### GET `/api/dental-svg/{patient_id}/`
Récupère les données SVG dentaires d'un patient avec filtrage automatique selon l'âge.

**Réponse :**
```json
{
  "id": "uuid",
  "patient": "uuid",
  "patient_name": "John Doe",
  "patient_birth_date": "2010-05-15",
  "patient_age": 13.2,
  "dental_svg_upper": "...",
  "dental_svg_lower": "...",
  "current_age_restriction": "under_13_5",
  "dental_view_level": "partial",
  "can_view_dental_data": true,
  "allowed_teeth_count": 24,
  "filtered_svg_data": {
    "dental_svg_upper": "...",
    "dental_svg_lower": "...",
    "view_level": "partial",
    "allowed_teeth": 24
  },
  "modifications": [...]
}
```

#### POST `/api/dental-svg/`
Crée de nouvelles données SVG dentaires pour un patient.

**Payload :**
```json
{
  "patient": "uuid",
  "dental_svg_upper": "<svg>...</svg>",
  "dental_svg_lower": "<svg>...</svg>"
}
```

#### PUT `/api/dental-svg/{patient_id}/`
Met à jour les données SVG dentaires d'un patient.

### 2. Vérification des Restrictions d'Âge

#### GET `/api/dental-svg/check-age-restrictions/{patient_id}/`
Vérifie les restrictions d'âge pour un patient sans récupérer les données SVG.

**Réponse :**
```json
{
  "patient_id": "uuid",
  "age": 13.2,
  "age_restriction": "under_13_5",
  "dental_view_level": "partial",
  "can_view_dental_data": true,
  "allowed_teeth_count": 24,
  "restriction_message": "Patient de moins de 13 ans et demi - vue partielle autorisée"
}
```

### 3. Modifications Dentaires

#### GET `/api/dental-modifications/`
Liste les modifications dentaires avec filtres optionnels.

**Paramètres de requête :**
- `patient_id`: UUID du patient
- `specialty`: Spécialité dentaire
- `tooth_number`: Numéro de dent

#### POST `/api/dental-modifications/`
Crée une nouvelle modification dentaire.

**Payload :**
```json
{
  "patient_id": "uuid",
  "tooth_number": 12,
  "path_id": "coronal_section",
  "modification_type": "color",
  "value": "#FF0000",
  "specialty": "therapeutic",
  "applied_by_button": "RestoratinPermanent"
}
```

#### DELETE `/api/dental-modifications/clear-patient/{patient_id}/`
Supprime toutes les modifications d'un patient.

### 4. Mise à Jour en Lot

#### POST `/api/dental-svg/bulk-update/`
Met à jour en lot les données SVG et les modifications.

**Payload :**
```json
{
  "patient_id": "uuid",
  "dental_svg_upper": "<svg>...</svg>",
  "dental_svg_lower": "<svg>...</svg>",
  "modifications": [
    {
      "tooth_number": 12,
      "path_id": "coronal_section",
      "modification_type": "color",
      "value": "#FF0000",
      "specialty": "therapeutic",
      "applied_by_button": "RestoratinPermanent"
    }
  ]
}
```

## Codes d'Erreur

| Code | Description |
|------|-------------|
| 403 | Accès refusé pour cet âge |
| 404 | Patient non trouvé |
| 400 | Données invalides |

## Exemples d'Utilisation

### Vérifier si un patient peut voir ses données dentaires

```javascript
const response = await fetch(`/api/dental-svg/check-age-restrictions/${patientId}/`);
const data = await response.json();

if (data.can_view_dental_data) {
  // Charger l'interface dentaire
  loadDentalInterface(data.allowed_teeth_count);
} else {
  // Afficher un message d'âge
  showAgeRestrictionMessage(data.restriction_message);
}
```

### Sauvegarder des modifications dentaires

```javascript
const modification = {
  patient_id: patientId,
  tooth_number: 12,
  path_id: "coronal_section",
  modification_type: "color",
  value: "#FF0000",
  specialty: "therapeutic",
  applied_by_button: "RestoratinPermanent"
};

const response = await fetch('/api/dental-modifications/', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(modification)
});
```

## Sécurité

- Toutes les APIs nécessitent une authentification
- Les restrictions d'âge sont automatiquement appliquées
- Les données sensibles sont filtrées selon l'âge
- Audit trail des modifications dentaires

## Notes Techniques

- Les âges sont calculés avec précision (décimales)
- Les restrictions sont mises à jour automatiquement
- Les données SVG sont stockées en format texte
- Support des modifications en lot pour les performances
