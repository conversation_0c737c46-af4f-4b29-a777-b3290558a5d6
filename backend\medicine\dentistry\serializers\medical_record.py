"""
Medical record serializers for the dentistry application.
"""
from rest_framework import serializers
from dentistry.models import DentistryMedicalRecord, DentalImaging
from dentistry.serializers.patient import DentistryPatientSerializer

class DentistryMedicalRecordSerializer(serializers.ModelSerializer):
    """
    Serializer for the DentistryMedicalRecord model.
    """
    patient_details = DentistryPatientSerializer(source='patient', read_only=True)
    doctor_name = serializers.CharField(source='doctor.get_full_name', read_only=True)
    oral_hygiene_status_display = serializers.ReadOnlyField(source='get_oral_hygiene_status_display')
    gum_health_display = serializers.ReadOnlyField(source='get_gum_health_display')

    class Meta:
        model = DentistryMedicalRecord
        fields = [
            'id', 'patient', 'patient_details', 'doctor', 'doctor_name',
            'date', 'chief_complaint', 'diagnosis', 'treatment_plan', 'notes',
            'oral_hygiene_status', 'oral_hygiene_status_display',
            'gum_health', 'gum_health_display', 'teeth_examined',
            'x_rays_taken', 'teeth_cleaned', 'fluoride_treatment',
            'teeth_chart_updated', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

class DentalImagingSerializer(serializers.ModelSerializer):
    """
    Serializer for the DentalImaging model.
    """
    patient_details = DentistryPatientSerializer(source='patient', read_only=True)
    doctor_name = serializers.CharField(source='doctor.get_full_name', read_only=True)
    imaging_type_display = serializers.ReadOnlyField(source='get_imaging_type_display')

    class Meta:
        model = DentalImaging
        fields = [
            'id', 'patient', 'patient_details', 'doctor', 'doctor_name',
            'date', 'imaging_type', 'imaging_type_display', 'image_file',
            'affected_teeth', 'findings', 'notes', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']
