import React, { useState } from 'react';
import {
  Card,
  Text,
  Switch,
  Stack,
  Group,
  Table,
  Button,
  ActionIcon,
  Modal,
  TextInput,
  Select,
} from '@mantine/core';
import {
  IconRuler,
  IconEdit,
  IconTrash,
  IconPlus,
  IconX,
} from '@tabler/icons-react';

// Interfaces pour les données de biométrie
interface BiometricItem {
  id: string;
  nom: string;
  type: string;
  unite: string;
  desactive: boolean;
  patientEnCours: boolean;
}

const Biometrie: React.FC = () => {
  // État pour les options générales
  const [activerTendancesPediatriques, setActiverTendancesPediatriques] = useState(false);
  const [activerTelechargementDirect, setActiverTelechargementDirect] = useState(true);

  // État pour les données de biométrie
  const [biometricItems, setBiometricItems] = useState<BiometricItem[]>([
    {
      id: '1',
      nom: 'Poids',
      type: 'Floton',
      unite: 'Kg',
      desactive: false,
      patientEnCours: true,
    },
    {
      id: '2',
      nom: 'Taille',
      type: 'Floton',
      unite: 'cm',
      desactive: false,
      patientEnCours: true,
    },
    {
      id: '3',
      nom: 'IMC',
      type: 'Calculé',
      unite: 'Kg/m²',
      desactive: false,
      patientEnCours: false,
    },
    {
      id: '4',
      nom: 'Pouls',
      type: 'Entier',
      unite: '/min',
      desactive: false,
      patientEnCours: true,
    },
    {
      id: '5',
      nom: 'T°',
      type: 'Floton',
      unite: 'C°',
      desactive: false,
      patientEnCours: false,
    },
    {
      id: '6',
      nom: 'TA SYS',
      type: 'Floton',
      unite: 'mmHg',
      desactive: false,
      patientEnCours: false,
    },
    {
      id: '7',
      nom: 'TA DIA',
      type: 'Floton',
      unite: 'mmHg',
      desactive: false,
      patientEnCours: false,
    },
    {
      id: '8',
      nom: 'SO2',
      type: 'Entier',
      unite: '%',
      desactive: false,
      patientEnCours: false,
    },
  ]);

  // États pour les modals
  const [newItemModalOpened, setNewItemModalOpened] = useState(false);
  const [editItemModalOpened, setEditItemModalOpened] = useState(false);

  // États pour les formulaires des modals
  const [newItemForm, setNewItemForm] = useState({ nom: '', type: '', unite: '' });
  const [editItemForm, setEditItemForm] = useState({ id: '', nom: '', type: '', unite: '' });

  // Options pour les selects
  const typeOptions = [
    { value: 'Floton', label: 'Floton' },
    { value: 'Entier', label: 'Entier' },
    { value: 'Calculé', label: 'Calculé' },
  ];

  const uniteOptions = [
    { value: 'Kg', label: 'Kg' },
    { value: 'cm', label: 'cm' },
    { value: 'Kg/m²', label: 'Kg/m²' },
    { value: '/min', label: '/min' },
    { value: 'C°', label: 'C°' },
    { value: 'mmHg', label: 'mmHg' },
    { value: '%', label: '%' },
  ];

  // Fonctions pour gérer les changements
  const handleItemChange = (id: string, field: keyof BiometricItem, value: boolean) => {
    setBiometricItems(prev => prev.map(item => 
      item.id === id ? { ...item, [field]: value } : item
    ));
  };

  const handleEditItem = (item: BiometricItem) => {
    setEditItemForm({
      id: item.id,
      nom: item.nom,
      type: item.type,
      unite: item.unite,
    });
    setEditItemModalOpened(true);
  };

  const handleDeleteItem = (id: string) => {
    setBiometricItems(prev => prev.filter(item => item.id !== id));
  };

  // Fonctions pour gérer les modals
  const handleNewItemSubmit = () => {
    if (newItemForm.nom.trim() && newItemForm.type && newItemForm.unite) {
      const newItem: BiometricItem = {
        id: Date.now().toString(),
        nom: newItemForm.nom,
        type: newItemForm.type,
        unite: newItemForm.unite,
        desactive: false,
        patientEnCours: false,
      };
      setBiometricItems(prev => [...prev, newItem]);
      setNewItemForm({ nom: '', type: '', unite: '' });
      setNewItemModalOpened(false);
    }
  };

  const handleEditItemSubmit = () => {
    if (editItemForm.nom.trim() && editItemForm.type && editItemForm.unite) {
      setBiometricItems(prev => prev.map(item => 
        item.id === editItemForm.id 
          ? { ...item, nom: editItemForm.nom, type: editItemForm.type, unite: editItemForm.unite }
          : item
      ));
      setEditItemForm({ id: '', nom: '', type: '', unite: '' });
      setEditItemModalOpened(false);
    }
  };

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* En-tête */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="bg-blue-100 p-2 rounded-lg">
              <IconRuler size={24} className="text-blue-600" />
            </div>
            <Text size="xl" fw={600} className="text-gray-800">
              Biométrie
            </Text>
          </div>
          <Button
            leftSection={<IconPlus size={16} />}
            onClick={() => setNewItemModalOpened(true)}
            className="bg-blue-500 hover:bg-blue-600 text-white"
          >
            Nouveau
          </Button>
        </div>
      </div>

      {/* Contenu principal */}
      <div className="flex-1 p-6">
        <Card shadow="sm" padding="lg" radius="md" className="bg-white h-full">
          <Stack gap="lg">
            {/* Options de configuration */}
            <Group grow align="flex-start">
              <Switch
                checked={activerTendancesPediatriques}
                onChange={(event) => setActiverTendancesPediatriques(event.currentTarget.checked)}
                label="Activer les tendances pédiatriques"
                size="md"
                color="blue"
              />
              <Switch
                checked={activerTelechargementDirect}
                onChange={(event) => setActiverTelechargementDirect(event.currentTarget.checked)}
                label="Activer le téléchargement direct"
                size="md"
                color="blue"
              />
            </Group>

            {/* Tableau des éléments biométriques */}
            <Table striped highlightOnHover>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>Nom</Table.Th>
                  <Table.Th>Type</Table.Th>
                  <Table.Th>Unité</Table.Th>
                  <Table.Th className="text-center">Désactivé</Table.Th>
                  <Table.Th className="text-center">Patient en cours</Table.Th>
                  <Table.Th className="w-32"></Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {biometricItems.map((item) => (
                  <Table.Tr key={item.id}>
                    <Table.Td>{item.nom}</Table.Td>
                    <Table.Td>
                      <Text 
                        className={`${
                          item.type === 'Floton' ? 'text-blue-600' : 
                          item.type === 'Calculé' ? 'text-green-600' : 
                          'text-gray-600'
                        }`}
                      >
                        {item.type}
                      </Text>
                    </Table.Td>
                    <Table.Td>{item.unite}</Table.Td>
                    <Table.Td className="text-center">
                      <Switch
                        checked={item.desactive}
                        onChange={(event) =>
                          handleItemChange(item.id, 'desactive', event.currentTarget.checked)
                        }
                        color="gray"
                        size="sm"
                      />
                    </Table.Td>
                    <Table.Td className="text-center">
                      <Switch
                        checked={item.patientEnCours}
                        onChange={(event) =>
                          handleItemChange(item.id, 'patientEnCours', event.currentTarget.checked)
                        }
                        color="blue"
                        size="sm"
                        disabled={item.desactive}
                      />
                    </Table.Td>
                    <Table.Td>
                      <Group gap="xs">
                        <ActionIcon
                          variant="subtle"
                          color="blue"
                          onClick={() => handleEditItem(item)}
                        >
                          <IconEdit size={16} />
                        </ActionIcon>
                        <ActionIcon
                          variant="subtle"
                          color="red"
                          onClick={() => handleDeleteItem(item.id)}
                        >
                          <IconTrash size={16} />
                        </ActionIcon>
                      </Group>
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          </Stack>
        </Card>
      </div>

      {/* Modal Nouveau */}
      <Modal
        opened={newItemModalOpened}
        onClose={() => setNewItemModalOpened(false)}
        title={
          <div className="flex items-center gap-2 bg-blue-500 text-white px-4 py-2 -m-4 mb-4">
            <IconRuler size={20} />
            <span className="font-medium">Biométrie</span>
          </div>
        }
        size="md"
        withCloseButton={false}
        styles={{
          header: { backgroundColor: '#3b82f6', margin: 0, padding: 0 },
          title: { width: '100%', margin: 0 },
        }}
      >
        <div className="relative">
          <ActionIcon
            className="absolute top-2 right-2 text-white hover:bg-white/20"
            variant="transparent"
            onClick={() => setNewItemModalOpened(false)}
          >
            <IconX size={20} />
          </ActionIcon>
        </div>

        <Stack gap="md" className="mt-4">
          <Group grow>
            <TextInput
              label={<span>Nom <span className="text-red-500">*</span></span>}
              placeholder=""
              value={newItemForm.nom}
              onChange={(event) => setNewItemForm(prev => ({ ...prev, nom: event.currentTarget.value }))}
              required
              styles={{
                input: { borderBottom: '2px solid #ef4444', borderTop: 'none', borderLeft: 'none', borderRight: 'none', borderRadius: 0 }
              }}
            />
            <Select
              label={<span>Type <span className="text-red-500">*</span></span>}
              placeholder=""
              data={typeOptions}
              value={newItemForm.type}
              onChange={(value) => setNewItemForm(prev => ({ ...prev, type: value || '' }))}
              required
            />
          </Group>

          <Group grow>
            <Select
              label={<span>Unité <span className="text-red-500">*</span></span>}
              placeholder=""
              data={uniteOptions}
              value={newItemForm.unite}
              onChange={(value) => setNewItemForm(prev => ({ ...prev, unite: value || '' }))}
              required
              rightSection={<IconPlus size={16} className="text-gray-400" />}
            />
            <div></div>
          </Group>

          <Group justify="flex-end" mt="xl">
            <Button
              variant="filled"
              color="gray"
              onClick={handleNewItemSubmit}
              disabled={!newItemForm.nom.trim() || !newItemForm.type || !newItemForm.unite}
            >
              Enregistrer
            </Button>
            <Button
              variant="filled"
              color="red"
              onClick={() => setNewItemModalOpened(false)}
            >
              Annuler
            </Button>
          </Group>
        </Stack>
      </Modal>

      {/* Modal Éditer */}
      <Modal
        opened={editItemModalOpened}
        onClose={() => setEditItemModalOpened(false)}
        title={
          <div className="flex items-center gap-2 bg-blue-500 text-white px-4 py-2 -m-4 mb-4">
            <IconRuler size={20} />
            <span className="font-medium">Biométrie</span>
          </div>
        }
        size="md"
        withCloseButton={false}
        styles={{
          header: { backgroundColor: '#3b82f6', margin: 0, padding: 0 },
          title: { width: '100%', margin: 0 },
        }}
      >
        <div className="relative">
          <ActionIcon
            className="absolute top-2 right-2 text-white hover:bg-white/20"
            variant="transparent"
            onClick={() => setEditItemModalOpened(false)}
          >
            <IconX size={20} />
          </ActionIcon>
        </div>

        <Stack gap="md" className="mt-4">
          <Group grow>
            <TextInput
              label={<span>Nom <span className="text-red-500">*</span></span>}
              value={editItemForm.nom}
              onChange={(event) => setEditItemForm(prev => ({ ...prev, nom: event.currentTarget.value }))}
              required
            />
            <Select
              label={<span>Type <span className="text-red-500">*</span></span>}
              data={typeOptions}
              value={editItemForm.type}
              onChange={(value) => setEditItemForm(prev => ({ ...prev, type: value || '' }))}
              required
            />
          </Group>

          <Group grow>
            <Select
              label={<span>Unité <span className="text-red-500">*</span></span>}
              data={uniteOptions}
              value={editItemForm.unite}
              onChange={(value) => setEditItemForm(prev => ({ ...prev, unite: value || '' }))}
              required
            />
            <div></div>
          </Group>

          <Group justify="flex-end" mt="xl">
            <Button
              variant="filled"
              className="bg-blue-500 hover:bg-blue-600"
              onClick={handleEditItemSubmit}
              disabled={!editItemForm.nom.trim() || !editItemForm.type || !editItemForm.unite}
            >
              Enregistrer
            </Button>
            <Button
              variant="filled"
              color="red"
              onClick={() => setEditItemModalOpened(false)}
            >
              Annuler
            </Button>
          </Group>
        </Stack>
      </Modal>
    </div>
  );
};

export default Biometrie;
