"use client";
import { useEffect, useRef, useState } from "react";
import type SimpleBarCore from "simplebar-core";
import { useDisclosure, useMediaQuery } from "@mantine/hooks";
import { Modal, Button, TextInput, ScrollArea, Divider ,Text,Card,  Group , } from "@mantine/core";
import {ToolbarCalendarNav}  from "@/components/agenda/Appointments/ToolbarCalendarNav"
import { Select } from "@mantine/core";
import type { SlotInfo } from "react-big-calendar";
import { SlotPropGetter } from 'react-big-calendar';
import "react-big-calendar/lib/css/react-big-calendar.css";
import {  Views, Calendar, momentLocalizer, HeaderProps , DayPropGetter } from "react-big-calendar";
import SimpleBar from "simplebar-react";
import "simplebar-react/dist/simplebar.min.css";
import type { ToolbarProps } from "react-big-calendar";
import moment from "moment";
import "moment/locale/fr";
import RadioButton from "./RadioButton";
import "./WeekView.css";
import { NumberInput } from "@mantine/core";
import { showNotification } from "@mantine/notifications";
import { Popover } from '@mantine/core';
import { classMap } from './classMap';
import {  EventProps } from 'react-big-calendar';
import { CiMenuKebab } from "react-icons/ci";
import { HoverCard,  } from '@mantine/core';

import {
  CalendarClock,
  ChevronDown,
  Hourglass,
} from "lucide-react";
import { Menu, rem } from "@mantine/core";
const calculateAge = (dateDeNaissance: string): number => {
  return moment().diff(moment(dateDeNaissance, "YYYY-MM-DD"), "years");
};
const formats = {
  dayFormat: (date: Date) => moment(date).format("ddd DD/MM"), // Ex: Lundi 24/02
};
// Composant personnalisé pour l'en-tête des jours
const CustomHeader: React.FC<HeaderProps> = ({ date, label }) => {
  const dayNumber = moment(date).isoWeekday(); // Lundi = 1, Dimanche = 7
  return (
    <div style={{ display: "flex", flexDirection: "column", alignItems: "center",position: "relative",}}>
      <span style={{ fontWeight: "bold", fontSize: "14px" }}>{label}</span> {/* Exemple : lun. 17/02 */}
      <span style={{
        position: "absolute",
        bottom: "0px",
        right: "-58px",
        fontSize: "10px",
        fontWeight: "bold",
        color: "red",//#3799CE
        }}>{dayNumber}</span> {/* Numérotation en bas */}
    </div>
  );
};
interface Event {
  id: number;
  title: string;
  nom: string;
  prenom: string;
  dateDeNaissance: string;
  age: number;
  selectedOption: string;
  etatCivil: string;
  cin: string;
  adresse: string;
  telephone: string;
  email: string;
  docteur: string;
  consultation: string;
  start: Date;
  end: Date;
  type: EventType;
  clientName?: string;
  description?: string;
  color?: string;
  style?: { backgroundColor: string };
  disabled?: string;
  tooltip: string,
  // timeDifference: string;
  // ville: string;
  // codePostal: string;
  // notes: string;
}
// type EventTypes = { title: string; start: Date; end: Date ;color: string};
type EventType = "visit" | "visitor-counter" | "completed" | "diagnosis";
const localizer = momentLocalizer(moment);
moment.locale("fr");
import SelectLaSemaine from "./SelectLaSemaine";
const WeekView: React.FC = () => {
  const currentDate = new Date();
  // French month names
  const months = [
    "janvier",
    "février",
    "mars",
    "avril",
    "mai",
    "juin",
    "juillet",
    "août",
    "septembre",
    "octobre",
    "novembre",
    "décembre",
  ];
  // French day names
  const days = [
    "dimanche",
    "lundi",
    "mardi",
    "mercredi",
    "jeudi",
    "vendredi",
    "samedi",
  ];
  const messages = {
    today: "Aujourd’hui",
    previous: "Précédent",
    next: "Suivant",
    month: "Mois",
    week: "Semaine",
    day: "Jour",
    agenda: "Agenda",
    noEventsInRange: "Aucun événement prévu",
  };
  const dayOfWeek = days[currentDate.getDay()]; // Get day of the week (0-6)
  const day = currentDate.getDate(); // Get the day of the month (1-31)
  const monthIndex = currentDate.getMonth(); // Get the month (0-11)
  const year = currentDate.getFullYear(); // Get the full year (e.g., 2024)
  // Formulate the date string in French format
  const formattedDate = `${dayOfWeek} ${day} ${months[monthIndex]} ${year}`;
  const [events, setEvents] = useState<Event[]>([]);
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [eventTitle, setEventTitle] = useState("");
  const [eventDate, setEventDate] = useState("");
  const [eventTime, setEventTime] = useState("");
  const [selectEvent, setSelectEvent] = useState<Event | null>(null);
  const [eventType, setEventType] = useState<EventType>("visit");
  const [eventNom, setEventNom] = useState("");
  const [eventPrenom, setEventPrenom] = useState("");
  const [eventDateDeNaissance, setEventDateDeNaissance] = useState("");
  const [eventAge, setEventAge] = useState<number | null>(null);
  const [eventSelectedOption, setEventSelectedOption] =
    useState<string>("Homme");
  const [eventEtatCivil, setEventEtatCivil] = useState<string>("Célibataire");
  const [eventCin, setEventCin] = useState<string>("");
  const [eventAdresse, setEventAdresse] = useState<string>("");
  const [eventTelephone, setEventTelephone] = useState<string>("");
  const [eventEmail, setEventEmail] = useState<string>("");
  const [eventDocteur, setEventDocteur] = useState<string>("Docteur");
  const [eventConsultation, setEventConsultation] = useState<string>("15 min");
  const [eventIdCounter, setEventIdCounter] = useState(0);

  useEffect(() => {
    if (eventSelectedOption === "Enfant") {
      setEventCin(""); // Clear CIN when "Enfant" is selected
    }
  }, [eventSelectedOption]);

  const handleOptionChange = (value: string) => {
    setEventSelectedOption(value);
  };
  const [firstModalOpened, { open: openFirstModal, close: closeFirstModal }] =
    useDisclosure(false);
  const [thirdModalOpened, { open: openThirdModal, close: closeThirdModal }] =
    useDisclosure(false);
  const [
    fourthModalOpened,
    { open: openFourthtModal, close: closeFourthModal },
  ] = useDisclosure(false);
  const [
    secondModalOpened,
    { open: openSecondModal, close: closeSecondModal },
  ] = useDisclosure(false);
  const isMobile = useMediaQuery("(max-width: 50em)");
  const firstOpenModal = () => {
    openFirstModal();
  };
  const firstCloseModal = () => {
    closeFirstModal();
  };
  const secondOpenModal = () => {
    openSecondModal();
  };

  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const date = e.target.value;
    setEventDateDeNaissance(date);
    setEventAge(calculateAge(date));
  };
  
  const handleSelectSlot = (slotInfo: SlotInfo) => {
    firstOpenModal();
    setSelectedDate(slotInfo.start);
    setEventDate(moment(slotInfo.start).format("YYYY-MM-DD"));
    setEventTime(moment(slotInfo.start).format("HH:mm"));
    setSelectEvent(null);
    // setEventType("visit");
  };
  const handleSelectedEvent = (event: Event) => {
    // if (event.id === -1) return;
    // if (event.id ===  -1) return;
    firstOpenModal();
    setSelectEvent(event);
    setEventTitle(event.title);
    setEventNom(event.nom);
    setEventPrenom(event.prenom);
    setEventDateDeNaissance(event.dateDeNaissance);
    setEventAge(event.age);
    setEventSelectedOption(event.selectedOption);
    setEventEtatCivil(event.etatCivil);
    setEventCin(event.cin);
    setEventAdresse(event.adresse);
    setEventTelephone(event.telephone);
    setEventEmail(event.email);
    setEventDocteur(event.docteur);
    setEventConsultation(event.consultation);
    setEventDate(moment(event.start).format("YYYY-MM-DD"));
    setEventTime(moment(event.start).format("HH:mm"));
    // setEventType(event.type);
    setEventType(event.type || "visit");
  };

  const saveEvent = () => {
    // Validation check for required fields
    if (
      !eventTitle ||
      !eventNom ||
      !eventPrenom ||
      !eventDateDeNaissance ||
      eventAge === null ||
      !eventSelectedOption ||
      !eventEtatCivil ||
      (!eventCin && eventSelectedOption !== "Enfant") || // CIN is required unless "Enfant" is selected
      !eventAdresse ||
      !eventTelephone ||
      !eventEmail ||
      !eventDocteur ||
      !eventConsultation ||
      !eventDate ||
      !eventTime
    ) {
      showNotification({
        title: "Erreur",
        message: "Veuillez remplir tous les champs obligatoires.",
        color: "red",
      });
      return; // Prevent saving the event
    }
    const updatedStartDate = moment(
      `${eventDate} ${eventTime}`,
      "YYYY-MM-DD HH:mm",
    ).toDate();
    const colorMap: Record<EventType, string> = {
      visit: "#34D1BF",
      "visitor-counter": "#F17105",
      completed: "#0496FF",
      diagnosis: "#ED0423",
    };
    const color = colorMap[eventType];
    const eventDetails = {
      title: eventTitle,
      nom: eventNom,
      prenom: eventPrenom,
      dateDeNaissance: eventDateDeNaissance,
      age: eventAge,
      selectedOption: eventSelectedOption,
      etatCivil: eventEtatCivil,
      cin: eventSelectedOption === "Enfant" ? "" : eventCin, // Clear CIN if "Enfant" is selected
      adresse: eventAdresse,
      telephone: eventTelephone,
      email: eventEmail,
      docteur: eventDocteur,
      consultation: eventConsultation,
      start: updatedStartDate,
      end: moment(updatedStartDate)
        .add(parseInt(eventConsultation, 10), "minutes")
        .toDate(),
      type: eventType,
      color,
      style: { backgroundColor: color },
      tooltip: '',
    };

    if (selectEvent) {
      const updatedEvent = {
        ...selectEvent,
        ...eventDetails,
      };
      setEvents(
        events.map((event) =>
          event.id === selectEvent.id ? updatedEvent : event,
        ),
      );
    } else {
      setEvents([...events, { id: eventIdCounter, ...eventDetails }]);
      setEventIdCounter(eventIdCounter + 1);
    }

    resetForm();
  };

  const deleteEvent = () => {
    if (selectEvent) {
      setEvents(events.filter((event) => event.id !== selectEvent.id));
      resetForm();
      // setEventType("visit");
    }
  };
  const resetForm = () => {
    setEventTitle("");
    setEventNom("");
    setEventPrenom("");
    setEventDateDeNaissance("");
    setEventAge(null);
    setEventSelectedOption("Homme");
    setEventEtatCivil("Célibataire");
    setEventCin("");
    setEventAdresse("");
    setEventTelephone("");
    setEventEmail("");
    setEventDocteur("Docteur");
    setEventConsultation("15 min");
    setEventDate("");
    setEventTime("");
    setSelectEvent(null);
    setEventType("visit");
  };
const CustomToolbarLaSemaine: React.FC<ToolbarProps<Event, object>> = ({
    localizer: { messages },
    label,
    onNavigate,
  }) => {
  
    const [selectedDateW, setselectedDateW] = useState(new Date());
    useEffect(() => {
      setselectedDateW(moment(label, "DD MMMM YYYY").toDate());
    }, [label]);
    
    const handleDateChange = (newDate: Date) => {
      setselectedDateW(newDate);
      // Compare the selected day with the current label (assuming label is the current date)
      const currentDate = moment(label, "DD MMMM YYYY");
      const newSelectedDate = moment(newDate);
      const isSameMonth = currentDate.isSame(newSelectedDate, "month");
      if (!isSameMonth) {
        const direction = newSelectedDate.isAfter(currentDate)
          ? "NEXT"
          : "PREV";
        // Calculate number of months difference
        const diffMonths = Math.abs(
          newSelectedDate.diff(currentDate, "months"),
        );
        for (let i = 0; i < diffMonths; i++) {
          onNavigate(direction); // Navigate through months if necessary
        }
      }
      // Navigate to the exact day (use "DATE" to specify a direct jump if available in your Calendar)
      onNavigate("DATE", newDate); // Assuming 'DATE' is handled in your calendar's navigation
     
    };
    return (
      <div className="rounded-t-lg bg-[#3799CE] p-1 text-[#ffffff]">
        <div className="card-title h-[38px]">
          <div className="mx-auto w-[70px]">
            <Button
              variant="filled"
              size="sm"
              onClick={() => onNavigate("TODAY")}
              className="ho rounded-md bg-[var(--color-gray-tab)] text-[var(--text-tab)]"
              fz="xs"
            >
              {messages.today}
            </Button>
          </div>
          <div className="flex w-full flex-row items-center text-center capitalize">
            <div className="mx-auto flex flex-row items-center rounded-l-lg text-center capitalize">
              <button
                className="btn-sm mt-1"
                onClick={() => onNavigate("PREV")}
              >
                <i className="icon icon-chevron-left text-lg"></i>
              </button>
              <span className="text-sm font-medium capitalize">
                <h4 className="rounded-md px-3 py-1.5 text-sm font-medium">
                  {label}
                </h4>
              </span>
              <button
                className="btn-sm mt-1"
                onClick={() => onNavigate("NEXT")}
              >
                <i className="icon icon-chevron-right text-lg"></i>
              </button>
            </div>
          </div>
            <SelectLaSemaine
              date={selectedDateW}
              setter={handleDateChange}
              label={moment(selectedDateW).format("DD MMMM YYYY")} // Ensure this represents the start of the week
            />
        </div>
      </div>
    );
  };
  const [step, setStep] = useState<number>(15); // Default step value
  const handleStepChange = (newStep: number) => {
    setStep(newStep);
  };
  const [minHour, setMinHour] = useState<number>(8);
  const [minMinute, setMinMinute] = useState<number>(15);
  const [maxHour, setMaxHour] = useState<number>(22);//18
  // const [maxHours, setMaxHours] = useState<number>(22);
  const [maxMinute, setMaxMinute] = useState<number>(15); //15
  // const handlersRef = useRef<NumberInputHandlers>(null);
  const [minTime, setMinTime] = useState(
    moment().startOf("week").set({ hour: 8, minute: 15 }).toDate(),
  );
  const [maxTime, setMaxTime] = useState(
    moment().endOf("week").set({ hour: 18, minute: 15 }).toDate(),
  );
  const handleSave = () => {
    const newMinTime = moment()
      .startOf("week")
      .set({ hour: minHour, minute: minMinute })
      .local() // Apply local timezone explicitly
      .toDate();
    const newMaxTime = moment()
      .startOf("week")
      .set({ hour: maxHour, minute: maxMinute })
      .local() // Apply local timezone explicitly
      .toDate();
    setMinTime(newMinTime);
    setMaxTime(newMaxTime);
    console.log(newMaxTime);
  };
const dayPropGetter: DayPropGetter = (date) => {
  const dayNumber = moment(date).isoWeekday(); // Monday = 1, Sunday = 7
  // Apply the effect on Sunday afternoon
  if (dayNumber === 7 ) { 
    return {
      style: {
        pointerEvents: "none" as const,
        opacity: 0.9,
      },
      className: "dayPropGetter",
    };
  }
  return {};
};
const slotPropGetter: SlotPropGetter = (date) => {
  const dayNumber = moment(date).isoWeekday(); // Monday = 1, Sunday = 7
  const hour = moment(date).hour(); // Hour of the day
  // Apply the effect on Saturday for hours >= maxHours
  if (dayNumber === 6 && hour >= maxHours) {
    return {
      style: {
        pointerEvents: "none" as const,
        opacity: 0.9,
      },
      className: "dayslotPropGetter",
    };
  }

  // Return an empty object for other slots
  return {};
};
const simpleBarRef = useRef<SimpleBarCore | null>(null);
const [parentHeight, setParentHeight] = useState(600);
const [isScrolled, setIsScrolled] = useState(false);
useEffect(() => {
  if (!simpleBarRef.current) return;
  const simpleBarEl = simpleBarRef.current.getScrollElement();
  if (!simpleBarEl) return;
  const handleScroll = () => {
    setIsScrolled(simpleBarEl.scrollTop > 0); // Si scroll > 0, activer isScrolled
  };
  const observer = new ResizeObserver((entries) => {
    for (let entry of entries) {
      setParentHeight(entry.contentRect.height);
    }
  });
  simpleBarEl.addEventListener("scroll", handleScroll);
  observer.observe(simpleBarEl);
  return () => {
    simpleBarEl.removeEventListener("scroll", handleScroll);
    observer.disconnect();
  };
}, []);
const [maxHours, setMaxHours] = useState<number>(12); // Valeur par défaut
// console.log("maxHours :", maxHours);
// console.log("maxHour :", maxHour);
// console.log(" minMinute :",  minMinute);
// console.log("maxMinute :",  maxMinute);
// console.log("MinTime :",  minTime);
// console.log("MaxTime :",  maxTime);

const key = `${maxHours}_${maxHour}_${maxMinute}`;
const subKey = `${minHour}_${minMinute}`;
const slotClassNameSam = (() => {

  const key = `${maxHours}_${maxHour}_${maxMinute}`;
  const subKey = `${minHour}_${minMinute}`;

  return classMap[key]?.[subKey] || '';
})();

console.log("slotClassNameSam :",  `${slotClassNameSam}`);
console.log("Key:", key, "SubKey:", subKey);
console.log("Class found:", classMap[key]?.[subKey]);
const formatTime = (date: Date) => {
  return date.toLocaleTimeString("fr-FR", {
    hour: "2-digit",
    minute: "2-digit",
  });
};

const EventComponent = ({ event}: EventProps<Event>) => {
  const getEventClassName = () => {
    if (!event.id) return ""; // Vérifie si event.id existe
  
    switch (event.consultation) {
      case "15 min":
        return "mt-[2px] ";
      case "30 min":
        return "mt-[12px] ";
      case "45 min":
        return "mt-[22px] ";
      default:
        return "";
    }
  };
  const date = new Date(); // Exemple : date actuelle
  const className = getEventClassName();
const dayNumber = moment(date).isoWeekday();

console.log(eventConsultation);
console.log(dayNumber);

const [maxHours, setMaxHours] = useState<number>(12); 
const hour = moment(date).hour(); // Hour of the day
    return (
      <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', textAlign: 'center' }} className="uppercase ">
     <div
   className={className} //style={{ width: "80%", backgroundColor: "black", borderRadius: "2px" }}
  // className={
  //   // eventConsultation === "15 min" && event.id && validDays.includes(dayNumber)
  //   eventConsultation !== "15 min" && event.id || hour >= maxHours
  //     ? "mb-[-2px] h-[2px]"
  //     : "mb-[4px] h-[4px]"
  // }
/>

      {/* Main content */}
      <div style={{ display: 'flex', alignItems: 'center' }} >
          <Group ml={1}>
            <Text c="White" size="xs"  >
            {event.nom.slice(0, 8)}/{event.prenom.slice(0, 4)}...{formatTime(event.start)} - {formatTime(event.end)}
            </Text>{" "}
          </Group>      
        <HoverCard shadow="md" data-tip={event.tooltip} data-for="event-tooltip">
               <HoverCard.Target>
               <CiMenuKebab strokeWidth="2" fill="white"/>
               </HoverCard.Target>
               <HoverCard.Dropdown>
               <div className="my-2.5">
               <div className="border-base-200 border-2 border-l-[#3799CE] px-2.5 py-2">
                 <p className="m-0 text-sm font-normal leading-6">
                   <span className="text-[#999999]">Nom Prenom:</span>{" "}
                   {event.nom} {event.prenom}
                 </p>
                 <p className="m-0 text-sm font-normal leading-6">
                   <span className="text-[#999999]">
                     Date de naissance :
                   </span>{" "}
                   {event.dateDeNaissance}
                 </p>
                 <p className="m-0 text-sm font-normal leading-6">
                   <span className="text-[#999999]">Age:</span>{" "}
                   {event.age !== null
                     ? event.age.toString()
                     : "Please enter your date of birth"}
                 </p>
                 <p className="m-0 text-sm font-normal leading-6">
                   <span className="text-[#999999]">
                     Réservation :
                   </span>{" "}
                   {moment(event.start).format("HH:mm")}
                 </p>
                 <p className="m-0 text-sm font-normal leading-6">
                   <span className="text-[#999999]">Sexe :</span>{" "}
                   {event.selectedOption}
                 </p>
                 <p className="m-0 text-sm font-normal leading-6">
                   <span className="text-[#999999]">Etat Civil :</span>
                   {event.etatCivil}
                 </p>
                 <p className="m-0 text-sm font-normal leading-6">
                   <span className="text-[#999999]">Cin :</span>
                   {event.cin}
                 </p>
                 <p className="m-0 text-sm font-normal leading-6">
                   <span className="text-[#999999]">Adresse :</span>
                   {event.adresse}
                 </p>
                 <p className="m-0 text-sm font-normal leading-6">
                   <span className="text-[#999999]">Email :</span>
                   {event.email}
                 </p>
                 <p className="m-0 text-sm font-normal leading-6">
                   <span className="text-[#999999]">Docteur :</span>{" "}
                   {event.docteur}
                 </p>
                 <p className="m-0 text-sm font-normal leading-6">
                   <span className="text-[#999999]">
                     Consultation :
                   </span>
                   {event.consultation}
                 </p>
                 <p className="m-0 text-sm font-normal leading-6">
                   <span className="text-[#999999]">
                     Date du rendez-vous:
                   </span>{" "}
                   {moment(event.start).format("DD / MM / YYYY")}
                 </p>
               </div>
               </div>
               </HoverCard.Dropdown>
             </HoverCard>
      </div>
      </div>
    );
  };
 
  const formatWeekRange = (date: Date) => {
    const start = new Date(date);
    const dayOfWeek = start.getDay(); // 0 = dimanche, 1 = lundi, etc.
    
    // Ajuster pour obtenir le lundi (début de semaine)
    // Si c'est dimanche (0), reculer de 6 jours; sinon, reculer de (dayOfWeek - 1) jours
    const daysToSubtract = dayOfWeek === 0 ? 6 : dayOfWeek - 1;
    start.setDate(start.getDate() - daysToSubtract);
    
    const end = new Date(start);
    end.setDate(start.getDate() + 6); // Fin de la semaine (dimanche)
    
    const options: Intl.DateTimeFormatOptions = { month: 'long' };
    const monthName = start.toLocaleDateString('fr-FR', options); //'en-US'
    
    const startDay = start.getDate().toString().padStart(2, '0');
    const endDay = end.getDate().toString().padStart(2, '0');
    
    if (start.getMonth() !== end.getMonth()) {
      const endMonthName = end.toLocaleDateString('fr-FR', options); //'en-US'
      return `${monthName} ${startDay} – ${endMonthName} ${endDay}`;
    }
    
    return ` ${startDay} – ${endDay} ${monthName}`;
  };
  console.log(formatWeekRange(new Date())); // mars 03 – 09
  return (
    <>
      <div className="header-nav-base mb-2 p-1">
        <div className="flex justify-between">
          <div className="flex gap-4">
            <h3 className="flex px-[.75rem] py-[.5rem] align-middle text-xs font-bold leading-4 capitalize">
              <CalendarClock className="mr-1 h-3.5 w-3.5" />
              {formatWeekRange(new Date())}. {year} {/* Ajout d'une date valide */}
            </h3>
            <span className="-mx-6 p-1">|</span>
            {/* <h3 className="flex px-[.75rem] py-[.5rem] align-middle text-xs font-bold leading-4">
              <ChevronLeft className="mr-1 h-3.5 w-3.5" />{" "}
              <CalendarCheck className="mr-1 h-3.5 w-3.5" />{" "}
              <ChevronRight className="mr-1 h-3.5 w-3.5" />
            </h3>
            <span className="-mx-6 p-1">|</span> */}
            <h3 className="flex px-[.75rem] py-[.5rem] align-middle text-xs font-bold leading-4">
              {" "}
              Aujour&apos;hui
            </h3>
    <Popover width={300} position="bottom" withArrow shadow="md"  >
      <Popover.Target>
      <Button
      size="xs"
      className="hover:bg-[#3799CE]/90"
      rightSection={<ChevronDown size={14} />}
    >Heure de fin du samedi - {maxHours} : 00</Button>
      </Popover.Target>
      <Popover.Dropdown bg="var(--mantine-color-body)" >
      <Select
      label="Heure de fin du samedi"
      placeholder="Sélectionnez une heure"
      comboboxProps={{ withinPortal: false }}
      data={[
        { value: "10", label: "10:00" },
        { value: "12", label: "12:00" },
        { value: "13", label: "13:00" },
        { value: "14", label: "14:00" },
        { value: "15", label: "15:00" },
        { value: "16", label: "16:00" },
      ]}
      value={maxHours.toString()} // Convertit en string pour éviter l'erreur TypeScript
      onChange={(value) => setMaxHours(Number(value))} // Reconvertit en number lors du changement
    />
      </Popover.Dropdown>
    </Popover>
            <div>
           </div>
          </div>
          <div className="flex gap-4">
            <Menu
              width={200}
              shadow="md"
              transitionProps={{ transition: "rotate-right", duration: 150 }}
            >
              <Menu.Target>
                <Button
                  size="xs"
                  className="hover:bg-[#3799CE]/90"
                  rightSection={<ChevronDown size={14} />}
                >
                  {/* Vue général */}
                  {minMinute} minutes
                </Button>
              </Menu.Target>
              <Menu.Dropdown>
                <Menu.Item
                  onClick={openThirdModal}
                  leftSection={
                    <Hourglass style={{ width: rem(14), height: rem(14) }} />
                  }
                >
                  Début des heures de travail
                </Menu.Item>
                <Menu.Item
                  onClick={openFourthtModal}
                  leftSection={
                    <Hourglass style={{ width: rem(14), height: rem(14) }} />
                  }
                >
                  Fin des heures de travail
                </Menu.Item>
              </Menu.Dropdown>
            </Menu>
            {/* <span className="-ml-4 -mr-6 p-1">|</span>
            <h2 className="flex px-[.75rem] py-[.5rem] align-middle text-xs font-bold leading-4">
              {" "}
              <Plus className="mr-1 h-3.5 w-3.5" /> Prenez rendez-vous
            </h2>
            <span className="-mx-6 p-1">|</span>
            <h2 className="flex px-[.75rem] py-[.5rem] align-middle text-xs font-bold leading-4">
              <Diff className="mr-1 h-3.5 w-3.5" /> Entrée
            </h2> */}
            <ToolbarCalendarNav/>
          </div>
        </div>
      </div>
      <Modal
        opened={thirdModalOpened}
        onClose={closeThirdModal}
        title="Paramètre"
      >
        {/* <SettingsPage setMinTime={setMinTime} setMaxTime={setMaxTime} /> */}
        <div className="grid gap-2">
          <div className="flex gap-2">
            <NumberInput
              label="Min Hour:"
              placeholder="Select hour"
              clampBehavior="strict"
              step={1}
              min={8}//0
              max={22}//22
              value={minHour}
              onChange={(value) => setMinHour(value as number)}
            />
            <NumberInput
              label="Min Minute:"
              placeholder="Select minute"
              clampBehavior="strict"
              step={5}
              min={0}
              max={59}
              value={minMinute}
              onChange={(value) => setMinMinute(value as number)}
            />
          </div>
          {/* <Button className="w-full hover:bg-[#3799CE]/90" onClick={handleSave} onClose={closeFourthModal}>
            Save
          </Button> */}
          <Button
            className="w-full hover:bg-[#3799CE]/90"
            onClick={() => {
              handleSave();
              closeThirdModal();
            }}
          >
            Save
          </Button>
        </div>
      </Modal>
      <Modal
        opened={fourthModalOpened}
        onClose={closeFourthModal}
        title="Paramètre"
      >
        <div className="grid gap-2">
          <div className="flex gap-2">
            <NumberInput
              label="Max Hour:"
              placeholder="Select hour"
              clampBehavior="strict"
              step={1}
              min={8}//0
              max={22}//24
              value={maxHour}
              onChange={(value) => setMaxHour(value as number)}
              className="w-[50%]"
            />
            <Select
              label="Max Minute :"
              value={step.toString()} // Ensure value is a string
              onChange={(value) => {
                if (value) {
                  setStep(parseInt(value, 10)); // Convert string to number
                }
              }}
              placeholder="Select a step"
              data={[
                { value: "5", label: "5" },
                { value: "10", label: "10" },
                { value: "15", label: "15" },
                { value: "20", label: "20" },
                { value: "25", label: "25" },
                { value: "30", label: "30" },
                { value: "45", label: "45" },
              ]}
              className="w-[50%]"
            />
          </div>
          <Button
            className="w-full hover:bg-[#3799CE]/90"
            onClick={() => {
              handleSave();
              closeFourthModal();
            }}
          >
            Save
          </Button>
        </div>
      </Modal>
      <div className="border-base-200">
             <div className="relative flex" style={{ height: parentHeight }}>
             <SimpleBar ref={simpleBarRef} className="simplebar-scrollable-y flex-1">
             <div className="pr-4 bg-[#FFFFFF]  w-full ">
           <Calendar
              className="WeekView"
              localizer={localizer}
              events={events}
              startAccessor="start"
              endAccessor="end"
              selectable={true}
              onSelectSlot={handleSelectSlot}
              onSelectEvent={handleSelectedEvent}
              defaultView={Views.WEEK}
              views={[Views.WEEK]}
              formats={formats} // Appliquer la personnalisation
              min={minTime}
              max={maxTime}
              step={step}
              timeslots={2}
              eventPropGetter={(event) => ({
                style: {
                  backgroundColor: event.color,
                },
              })}
              messages={messages} 
              // tooltipAccessor={(event) => (event.shouldShowTooltip ? event.title : null)}
              tooltipAccessor={() => ""} // Désactive les tooltips
              dayPropGetter={dayPropGetter} // Passer dayPropGetter pour personnaliser les jours  
              slotPropGetter={slotPropGetter} // Passer dayPropGetter pour personnaliser les jours  
              components={{
                toolbar: CustomToolbarLaSemaine,
                header: CustomHeader, // Utiliser le header personnalisé   
                event: EventComponent, // Personnalisation du rendu de l'événement
              }}
             
            /> 
          
         <Card
      shadow="sm"
      padding="xl"
      component="a"
      bg={"#5BAAD6"}
      w={"194.25px"}
      radius="none"
    //${slotClassNameSam} 
      className={` ${slotClassNameSam}   absolute  right-[204px]    uppercase mr-0.5 items-center justify-center`}
    >
      <Text fw={500} c="var(--text-daisy-white)" mt={20}  ta="center"  className="block mt-20 -rotate-45">
      Congrès
      </Text>
      <Text fw={500} c="var(--text-daisy-white)" size="lg"  ta="center"  className="block mt-20 -rotate-45">
      Congrès
      </Text>
      <Text fw={500} c="var(--text-daisy-white)" size="lg"  ta="center"  className="block mt-20 -rotate-45">
      Congrès
      </Text>
    
        </Card>
         </div>   
             </SimpleBar>
               <div
        className="absolute right-3 bg-[#5BAAD6] w-[205px] uppercase flex items-center justify-center"
        style={{
          height:isScrolled ? parentHeight + 0 : parentHeight - 80, 
          top: isScrolled ? "0px" : "80px", // Enlever top lorsque scroll actif
          //transition: "top 0.3s ease-in-out", // Animation fluide
        }}
      >
       <Text c="var(--text-daisy-white)" size="lg" ta="center">
          <span className="block mt-10 -rotate-45">Congrès</span>
          <span className="block mt-20 -rotate-45">Congrès</span>
          <span className="block mt-20 -rotate-45">Congrès</span>
          <span className="block mt-20 -rotate-45">Congrès</span>
          <span className="block mt-20 -rotate-45">Congrès</span>
        </Text>
      </div> 
    </div>
        <Modal.Root
          opened={firstModalOpened}
          onClose={firstCloseModal}
          size="70%"
          scrollAreaComponent={ScrollArea.Autosize}
        >
         
          <Modal.Overlay />
          <Modal.Content>
            <Modal.Header>
             <Modal.Title>
              {" "}
              <Text fw={600} className="mb-2 flex gap-2 text-lg font-semibold leading-none tracking-tight">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="1em"
                  height="1em"
                  viewBox="0 0 24 24"
                >
                  <g
                    fill="none"
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                  >
                    <path d="M21 7.5V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h3.5M16 2v4M8 2v4m-5 4h5m9.5 7.5L16 16.3V14"></path>
                    <circle cx={16} cy={16} r={6}></circle>
                  </g>
                </svg>
                {selectEvent
                  ? "Modifier rendez-vous"
                  : "Ajouter un rendez-vous"}
              </Text>
              <p className="text-muted-foreground text-sm">
                {selectEvent
                  ? "Modifiez les détails de l'événement ci-dessous."
                  : "Remplissez les détails ci-dessous pour ajouter un nouvel événement."}
              </p>
             </Modal.Title>
              <Modal.CloseButton className="mantine-focus-always" />
            </Modal.Header>
            <Divider my="md" />
            {/* <SimpleBar className="simplebar-scrollable-y h-full"> */}
            <Modal.Body>
              {" "}
              <div className="p-2">
                <div className="grid gap-3 py-4">
                  <div className="flex gap-4">
                    {/* <TextInput
                      id="event-title"
                      type="text"
                      placeholder="Titre de l'événement"
                      value={eventTitle}
                      onChange={(e) => setEventTitle(e.currentTarget.value)}
                      required
                      className="input input-bordered mb-2 w-full"
                    /> */}
                     <Select
                                                  value={eventTitle}
                                                  onChange={(value) => {
                                                    setEventTitle(value ?? "");
                                                    switch (value) {
                                                      case "Visite de malade":
                                                        setEventType("visit");
                                                        break;
                                                      case "Visitor Counter":
                                                        setEventType("visitor-counter");
                                                        break;
                                                      case "Completed":
                                                        setEventType("completed");
                                                        break;
                                                      case "Re-diagnose":
                                                        setEventType("diagnosis");
                                                        break;
                                                      default:
                                                        setEventType("completed");
                                                    }
                                                  }}
                                                  placeholder="Titre de l'événement"
                                                  data={[
                                                    {
                                                      value: "Visite de malade",
                                                      label: "Visite de malade",
                                                    },
                                                    {
                                                      value: "Visitor Counter",
                                                      label: "Visitor Counter",
                                                    },
                                                    { value: "Completed", label: "Completed" },
                                                    { value: "Re-diagnose", label: "Re-diagnose" },
                                                  ]}
                                                  className="select w-full max-w-xs"
                                                  required
                                                />
                    <TextInput
                      id="event-nom"
                      placeholder="Nom *"
                      type="text"
                      value={eventNom}
                      onChange={(e) => setEventNom(e.currentTarget.value)}
                      required
                      className="input input-bordered mb-2 w-full"
                    />
                    <TextInput
                      id="event-prenom"
                      placeholder="Prénom *"
                      type="text"
                      value={eventPrenom}
                      onChange={(e) => setEventPrenom(e.currentTarget.value)}
                      required
                      className="input input-bordered mb-2 w-full"
                    />
                  </div>
                  <div className="flex gap-4">
                    <TextInput
                      type="date"
                      placeholder="Date de Naissance"
                      id="event-dateDeNaissance"
                      value={eventDateDeNaissance}
                      onChange={handleDateChange}
                      required
                      className="input input-bordered mb-2 w-full"
                    />

                    <TextInput
                      type="text"
                      id="event-age"
                      value={eventAge?.toString() ?? ""}
                      placeholder={
                        eventAge !== null
                          ? eventAge.toString()
                          : "Please enter your date of birth"
                      }
                      readOnly
                      className="input input-bordered mb-2 w-full"
                    />

                    <div className="mb-2 flex items-center space-x-4">
                      <RadioButton
                        label="Homme"
                        value="Homme"
                        checked={eventSelectedOption === "Homme"}
                        onChange={handleOptionChange}
                      />
                      <RadioButton
                        label="Femme"
                        value="Femme"
                        checked={eventSelectedOption === "Femme"}
                        onChange={handleOptionChange}
                      />
                      <RadioButton
                        label="Enfant"
                        value="Enfant"
                        checked={eventSelectedOption === "Enfant"}
                        onChange={handleOptionChange}
                      />
                    </div>
                  </div>
                  <div className="flex gap-4">
                    <Select
                      value={eventEtatCivil}
                      onChange={(value) => setEventEtatCivil(value ?? "")}
                      placeholder="Votre état civil"
                      data={[
                        { value: "Célibataire", label: "Célibataire" },
                        { value: "Marié", label: "Marié" },
                        { value: "Autre chose", label: "Autre chose" },
                      ]}
                      className="select w-full max-w-xs"
                      required
                    />

                    {eventSelectedOption !== "Enfant" ? (
                      <TextInput
                        id="Cin"
                        placeholder="Cin"
                        value={eventCin}
                        onChange={(e) => setEventCin(e.target.value)}
                        className="input input-bordered mb-2 w-full"
                      />
                    ) : (
                      <TextInput
                        id="Cin"
                        placeholder="Cin"
                        disabled
                        className="input input-bordered mb-2 w-full"
                      />
                    )}

                    <TextInput
                      id="Adresse"
                      placeholder="Adressé par"
                      value={eventAdresse}
                      onChange={(e) => setEventAdresse(e.currentTarget.value)}
                      required
                      className="input input-bordered mb-2 w-full"
                    />
                  </div>
                  <div className="flex gap-4">
                    <TextInput
                      id="Téléphone"
                      placeholder="Téléphone"
                      value={eventTelephone}
                      onChange={(e) => setEventTelephone(e.target.value)}
                      className="input input-bordered mb-2 w-full"
                    />

                    <TextInput
                      id="Email"
                      placeholder="Email"
                      value={eventEmail}
                      onChange={(e) => setEventEmail(e.target.value)}
                      className="input input-bordered mb-2 w-full"
                    />
                  </div>
                  <Select
                    value={eventDocteur}
                    onChange={(value) => setEventDocteur(value ?? "")}
                    placeholder="Docteur"
                    data={[
                      { value: "Docteur", label: "Docteur" },
                      { value: "dr.Kader", label: "dr.Kader" },
                      { value: "dr.Kaders", label: "dr.Kaders" },
                    ]}
                    className="w-full"
                  />

                  <div className="flex gap-4">
                    <label htmlFor="event-Consultation" className="label">
                      Consultation
                    </label>
                    <Select
                      value={eventConsultation}
                      onChange={(value) => setEventConsultation(value ?? "")}
                      placeholder=" 15 min"
                      data={["15 min", "30 min", "45 min"]}
                      className="select w-full max-w-xs"
                    />

                    <button className="mb-2 w-full rounded-md bg-[#3799CE] p-[10px] text-[var(--mantine-Button-label-MB)] hover:bg-[#3799CE]/90">
                      Aganda
                    </button>
                  </div>
                  <div className="mx-auto flex gap-4">
                    <label htmlFor="event-date" className="label">
                      Date du rendez-vous
                    </label>

                    <TextInput
                      id="event-date"
                      type="date"
                      value={eventDate}
                      onChange={(e) => setEventDate(e.target.value)}
                      className="input input-bordered mb-2 w-64 max-w-64"
                    />
                    <label htmlFor="event-time" className="label">
                      De*
                    </label>
                    <TextInput
                      id="event-time"
                      type="time"
                      value={eventTime}
                      onChange={(e) => setEventTime(e.target.value)}
                      className="input input-bordered mb-2 w-64 max-w-64"
                    />
                    <label htmlFor="event-time" className="label">
                      à*
                    </label>
                    <TextInput
                      id="event-time"
                      type="text"
                      placeholder={
                        eventTime !== null
                          ? moment(eventTime, "HH:mm")
                              .add(parseInt(eventConsultation), "minutes")
                              .format("HH:mm")
                          : "Please enter your date of birth"
                      }
                      readOnly
                      className="input input-bordered mb-2 w-40"
                    />
                  </div>
                </div>
                <div className="bg-base-100 px-[24px] py-[4px]">
                  <ul className="text-daisy flex flex-wrap gap-x-4 gap-y-2">
                    <div className="flex items-center space-x-2">
                      <input
                        id="visit"
                        type="radio"
                        name="eventType"
                        value="visit"
                        aria-labelledby="r1"
                        aria-describedby="r1"
                        className="peer hidden"
                        checked={eventType === "visit"}
                        onChange={(e) =>
                          setEventType(e.target.value as EventType)
                        }
                      />

                      <label
                        htmlFor="visit"
                        className={`${
                          eventType === "visit"
                            ? "peer-checked:text-[#34D1BF]"
                            : "text-[var(--mantine-color-dark-0)]"
                        } inline-flex h-10 w-full items-center justify-center whitespace-nowrap rounded-md px-4 py-2 text-sm font-medium uppercase`}
                      >
                        <li className="flex items-center gap-2 text-xs uppercase">
                          <span className="disk-teal relative z-10 block h-[12px] w-[12px] rounded-full border-2 border-white" />
                          Visite de malade
                        </li>
                      </label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <input
                        id="visitor-counter"
                        type="radio"
                        name="eventType"
                        value="visitor-counter"
                        className="peer hidden"
                        checked={eventType === "visitor-counter"}
                        onChange={(e) =>
                          setEventType(e.target.value as EventType)
                        }
                      />

                      <label
                        htmlFor="visitor-counter"
                        className={`${
                          eventType === "visitor-counter"
                            ? "peer-checked:text-[#F17105]"
                            : "text-[var(--mantine-color-dark-0)]"
                        } inline-flex h-10 w-full items-center justify-center whitespace-nowrap rounded-md px-4 py-2 text-sm font-medium uppercase`}
                      >
                        <li className="flex items-center gap-2 text-xs uppercase">
                          <span className="disk-orange relative z-10 block h-[12px] w-[12px] rounded-full border-2 border-white" />
                          Visitor Counter
                        </li>
                      </label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <input
                        id="completed"
                        type="radio"
                        name="eventType"
                        value="completed"
                        aria-labelledby="r3"
                        aria-describedby="r3"
                        checked={eventType === "completed"}
                        onChange={(e) =>
                          setEventType(e.target.value as EventType)
                        }
                        className="peer hidden"
                      />
                      <label
                        htmlFor="completed"
                        className={`${
                          eventType === "completed"
                            ? "peer-checked:text-[#0496FF]"
                            : "text-[var(--mantine-color-dark-0)]"
                        } inline-flex h-10 w-full items-center justify-center whitespace-nowrap px-4 py-2 text-sm font-medium uppercase`}
                      >
                        <li className="flex items-center gap-2 text-xs uppercase">
                          <span className="disk-azure relative z-10 block h-[12px] w-[12px] rounded-full border-2 border-white" />
                          Completed
                        </li>
                      </label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <input
                        id="diagnosis"
                        type="radio"
                        name="eventType"
                        value="diagnosis"
                        checked={eventType === "diagnosis"}
                        onChange={(e) =>
                          setEventType(e.target.value as EventType)
                        } // Explicitly cast the value
                        className="peer hidden"
                      />
                      <label
                        htmlFor="diagnosis"
                        className={`${
                          eventType === "diagnosis"
                            ? "peer-checked:text-[#ED0423]"
                            : "text-[var(--mantine-color-dark-0)]"
                        } inline-flex h-10 w-full items-center justify-center whitespace-nowrap rounded-md px-4 py-2 text-sm font-medium uppercase`}
                      >
                        <li className="flex items-center gap-2 text-xs uppercase">
                          <span className="disk-red relative z-10 block h-[12px] w-[12px] rounded-full border-2 border-white" />
                          Re-diagnose
                        </li>
                      </label>
                    </div>
                  </ul>
                </div>
                <div className="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2">
                  <Button
                    onClick={() => {
                      saveEvent();
                      firstCloseModal();
                    }}
                    className="btn mb-2 bg-[#03A684] text-[var(--mantine-Button-label-MB)] hover:bg-[#03A684]/90"
                  >
                    {selectEvent ? "Enregistrer" : "Ajouter"}
                  </Button>
                  {selectEvent && (
                    <Button
                      color="red"
                      onClick={deleteEvent}
                      className="btn mb-2 bg-[#F3124E] text-[var(--mantine-Button-label-MB)] hover:bg-[#F3124E]/90"
                    >
                      Supprimer
                      {/* l&apos;événement */}
                    </Button>
                  )}
                  <Button
                    onClick={firstCloseModal}
                    // onClick={resetForm}
                    className="btn mb-2 bg-[#F5A524] text-[var(--mantine-Button-label-MB)] hover:bg-[#F5A524]/90"
                  >
                    Annuler
                  </Button>
                  <Button
                    className="btn mb-2 bg-[#1D86BA] text-[var(--mantine-Button-label-MB)] hover:bg-[#1D86BA]/90"
                    onClick={secondOpenModal}
                  >
                    Montrer
                  </Button>
                </div>
              </div>
            </Modal.Body>
            {/* </SimpleBar> */}
          </Modal.Content>
        </Modal.Root>
        <div className="bg-base-100 px-[24px] py-[20px]">
          <ul className="flex flex-wrap gap-x-4 gap-y-2 text-[var(--mantine-color-dark-0)]">
            {[
              { label: "Visite de malade", color: "disk-teal" },
              { label: "Visitor Counter", color: "disk-orange" },
              { label: "Completed", color: "disk-azure" },
              { label: "Re-diagnose", color: "disk-red" },
            ].map(({ label, color }) => (
              <li
                key={label}
                className="flex items-center gap-2 text-xs uppercase"
              >
                <span
                  className={`${color} relative z-10 block h-[12px] w-[12px] rounded-full border-2 border-white`}
                />
                {label}
              </li>
            ))}
          </ul>
        </div>
        <Modal.Root
          opened={secondModalOpened}
          onClose={closeSecondModal}
          fullScreen={isMobile}
          transitionProps={{ transition: "fade", duration: 200 }}
        >
          <Modal.Overlay />
          <Modal.Content>
            <Modal.Header>
              <Modal.Title>{eventTitle}</Modal.Title>
              <Modal.CloseButton />
            </Modal.Header>
            <Modal.Body>
              <div className="my-2.5">
                <div className="border-base-200 border-2 border-l-[#3799CE] px-2.5 py-2">
                  <p className="m-0 text-sm font-normal leading-6">
                    <span className="text-[#999999]">Nom Prenom:</span>{" "}
                    {eventNom} {eventPrenom}
                  </p>
                  <p className="m-0 text-sm font-normal leading-6">
                    <span className="text-[#999999]">Date de naissance :</span>{" "}
                    {eventDateDeNaissance}
                  </p>
                  <p className="m-0 text-sm font-normal leading-6">
                    <span className="text-[#999999]">Age:</span>{" "}
                    {eventAge !== null
                      ? eventAge.toString()
                      : "Please enter your date of birth"}
                  </p>
                  <p className="m-0 text-sm font-normal leading-6">
                    <span className="text-[#999999]">Réservation :</span>{" "}
                    {eventTime}
                  </p>
                  <p className="m-0 text-sm font-normal leading-6">
                    {/* Sexe : {eventSexe ? "Homme" : "Femme"}*/}
                    <span className="text-[#999999]">Sexe :</span>{" "}
                    {eventSelectedOption}
                  </p>
                  <p className="m-0 text-sm font-normal leading-6">
                    <span className="text-[#999999]">Etat Civil :</span>
                    {eventEtatCivil}
                  </p>
                  <p className="m-0 text-sm font-normal leading-6">
                    <span className="text-[#999999]">Cin :</span>
                    {eventCin}
                  </p>
                  <p className="m-0 text-sm font-normal leading-6">
                    <span className="text-[#999999]">Adresse :</span>
                    {eventAdresse}
                  </p>
                  <p className="m-0 text-sm font-normal leading-6">
                    <span className="text-[#999999]">Email :</span>
                    {eventEmail}
                  </p>
                  <p className="m-0 text-sm font-normal leading-6">
                    <span className="text-[#999999]">Docteur :</span>{" "}
                    {eventDocteur}
                  </p>
                  <p className="m-0 text-sm font-normal leading-6">
                    <span className="text-[#999999]">Consultation :</span>
                    {eventConsultation}
                  </p>
                  <p className="m-0 text-sm font-normal leading-6">
                    <span className="text-[#999999]">Date du rendez-vous:</span>{" "}
                    {eventDate}
                  </p>
                  {/* eventTimeDifference:{eventTimeDifference} */}
                </div>
              </div>
            </Modal.Body>
          </Modal.Content>
        </Modal.Root>
      </div>
    </>
  );
};

export default WeekView;
