"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/klona";
exports.ids = ["vendor-chunks/klona"];
exports.modules = {

/***/ "(ssr)/./node_modules/klona/full/index.mjs":
/*!*******************************************!*\
  !*** ./node_modules/klona/full/index.mjs ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   klona: () => (/* binding */ klona)\n/* harmony export */ });\nfunction set(obj, key, val) {\n\tif (typeof val.value === 'object') val.value = klona(val.value);\n\tif (!val.enumerable || val.get || val.set || !val.configurable || !val.writable || key === '__proto__') {\n\t\tObject.defineProperty(obj, key, val);\n\t} else obj[key] = val.value;\n}\n\nfunction klona(x) {\n\tif (typeof x !== 'object') return x;\n\n\tvar i=0, k, list, tmp, str=Object.prototype.toString.call(x);\n\n\tif (str === '[object Object]') {\n\t\ttmp = Object.create(x.__proto__ || null);\n\t} else if (str === '[object Array]') {\n\t\ttmp = Array(x.length);\n\t} else if (str === '[object Set]') {\n\t\ttmp = new Set;\n\t\tx.forEach(function (val) {\n\t\t\ttmp.add(klona(val));\n\t\t});\n\t} else if (str === '[object Map]') {\n\t\ttmp = new Map;\n\t\tx.forEach(function (val, key) {\n\t\t\ttmp.set(klona(key), klona(val));\n\t\t});\n\t} else if (str === '[object Date]') {\n\t\ttmp = new Date(+x);\n\t} else if (str === '[object RegExp]') {\n\t\ttmp = new RegExp(x.source, x.flags);\n\t} else if (str === '[object DataView]') {\n\t\ttmp = new x.constructor( klona(x.buffer) );\n\t} else if (str === '[object ArrayBuffer]') {\n\t\ttmp = x.slice(0);\n\t} else if (str.slice(-6) === 'Array]') {\n\t\t// ArrayBuffer.isView(x)\n\t\t// ~> `new` bcuz `Buffer.slice` => ref\n\t\ttmp = new x.constructor(x);\n\t}\n\n\tif (tmp) {\n\t\tfor (list=Object.getOwnPropertySymbols(x); i < list.length; i++) {\n\t\t\tset(tmp, list[i], Object.getOwnPropertyDescriptor(x, list[i]));\n\t\t}\n\n\t\tfor (i=0, list=Object.getOwnPropertyNames(x); i < list.length; i++) {\n\t\t\tif (Object.hasOwnProperty.call(tmp, k=list[i]) && tmp[k] === x[k]) continue;\n\t\t\tset(tmp, k, Object.getOwnPropertyDescriptor(x, k));\n\t\t}\n\t}\n\n\treturn tmp || x;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/klona/full/index.mjs\n");

/***/ })

};
;