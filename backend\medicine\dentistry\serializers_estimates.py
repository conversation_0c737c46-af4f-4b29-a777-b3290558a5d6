# backend/dental_medicine/dentistry/serializers_estimates.py

from rest_framework import serializers
from .models_estimates import (
    EstimateSession,
    ToothModificationEstimate,
    EstimateSessionHistory,
    EstimateTemplate,
    EstimateStatistics
)

class ToothModificationEstimateSerializer(serializers.ModelSerializer):
    """
    Serializer pour les modifications dentaires dans les estimations
    """
    modification_code = serializers.ReadOnlyField()

    class Meta:
        model = ToothModificationEstimate
        fields = [
            'id',
            'tooth_number',
            'svg_id',
            'path_id',
            'modification_type',
            'status',
            'is_visible',
            'color',
            'stroke',
            'metadata',
            'modification_code',
            'created_at',
            'updated_at'
        ]
        read_only_fields = ['id', 'modification_code', 'created_at', 'updated_at']

    def validate_tooth_number(self, value):
        """Valider que le numéro de dent est entre 1 et 32"""
        if not 1 <= value <= 32:
            raise serializers.ValidationError("Le numéro de dent doit être entre 1 et 32")
        return value

    def validate_color(self, value):
        """Valider le format de couleur hexadécimale"""
        if value and not value.startswith('#'):
            raise serializers.ValidationError("La couleur doit être au format hexadécimal (#RRGGBB)")
        return value

    def validate_stroke(self, value):
        """Valider le format de couleur hexadécimale pour le contour"""
        if value and not value.startswith('#'):
            raise serializers.ValidationError("La couleur du contour doit être au format hexadécimal (#RRGGBB)")
        return value


class EstimateStatisticsSerializer(serializers.ModelSerializer):
    """
    Serializer pour les statistiques d'estimation
    """
    class Meta:
        model = EstimateStatistics
        fields = [
            'id',
            'total_modifications',
            'applied_modifications',
            'planned_modifications',
            'esthetic_count',
            'prosthetic_count',
            'surgery_count',
            'orthodontics_count',
            'modified_teeth_count',
            'most_modified_tooth',
            'session_duration_minutes',
            'last_activity',
            'calculated_at'
        ]
        read_only_fields = fields


class EstimateSessionSerializer(serializers.ModelSerializer):
    """
    Serializer pour les sessions d'estimation
    """
    modifications = ToothModificationEstimateSerializer(many=True, read_only=True)
    statistics = EstimateStatisticsSerializer(read_only=True)
    created_by_username = serializers.CharField(source='created_by.username', read_only=True)
    modifications_count = serializers.SerializerMethodField()

    class Meta:
        model = EstimateSession
        fields = [
            'id',
            'session_name',
            'patient',
            'created_by',
            'created_by_username',
            'created_at',
            'updated_at',
            'is_active',
            'is_completed',
            'modifications',
            'modifications_count',
            'statistics'
        ]
        read_only_fields = ['id', 'created_by', 'created_by_username', 'created_at', 'updated_at', 'modifications', 'statistics']

    def get_modifications_count(self, obj):
        """Retourner le nombre de modifications dans la session"""
        return obj.modifications.count()

    def validate_session_name(self, value):
        """Valider que le nom de session n'est pas vide"""
        if not value.strip():
            raise serializers.ValidationError("Le nom de session ne peut pas être vide")
        return value.strip()


class EstimateSessionHistorySerializer(serializers.ModelSerializer):
    """
    Serializer pour l'historique des sessions d'estimation
    """
    performed_by_username = serializers.CharField(source='performed_by.username', read_only=True)
    modification_details = ToothModificationEstimateSerializer(source='modification', read_only=True)

    class Meta:
        model = EstimateSessionHistory
        fields = [
            'id',
            'action',
            'description',
            'previous_data',
            'new_data',
            'performed_by',
            'performed_by_username',
            'performed_at',
            'modification',
            'modification_details'
        ]
        read_only_fields = fields


class EstimateTemplateSerializer(serializers.ModelSerializer):
    """
    Serializer pour les modèles d'estimation
    """
    created_by_username = serializers.CharField(source='created_by.username', read_only=True)
    modifications_count = serializers.SerializerMethodField()

    class Meta:
        model = EstimateTemplate
        fields = [
            'id',
            'name',
            'description',
            'specialty',
            'template_data',
            'created_by',
            'created_by_username',
            'created_at',
            'updated_at',
            'is_active',
            'is_public',
            'modifications_count'
        ]
        read_only_fields = ['id', 'created_by', 'created_by_username', 'created_at', 'updated_at']

    def get_modifications_count(self, obj):
        """Retourner le nombre de modifications dans le modèle"""
        return len(obj.template_data.get('modifications', []))

    def validate_name(self, value):
        """Valider que le nom du modèle n'est pas vide"""
        if not value.strip():
            raise serializers.ValidationError("Le nom du modèle ne peut pas être vide")
        return value.strip()

    def validate_template_data(self, value):
        """Valider la structure des données du modèle"""
        if not isinstance(value, dict):
            raise serializers.ValidationError("Les données du modèle doivent être un objet JSON")

        # Valider la structure des modifications si présentes
        modifications = value.get('modifications', [])
        if not isinstance(modifications, list):
            raise serializers.ValidationError("Les modifications doivent être une liste")

        # Valider chaque modification
        for i, mod in enumerate(modifications):
            if not isinstance(mod, dict):
                raise serializers.ValidationError(f"La modification {i} doit être un objet")

            required_fields = ['svg_id', 'path_id', 'tooth_number', 'modification_type']
            for field in required_fields:
                if field not in mod:
                    raise serializers.ValidationError(f"La modification {i} doit contenir le champ '{field}'")

        return value


class EstimateSessionCreateSerializer(serializers.ModelSerializer):
    """
    Serializer spécialisé pour la création de sessions avec modifications initiales
    """
    modifications = ToothModificationEstimateSerializer(many=True, required=False)

    class Meta:
        model = EstimateSession
        fields = [
            'session_name',
            'patient',
            'modifications'
        ]

    def create(self, validated_data):
        """Créer une session avec ses modifications initiales"""
        modifications_data = validated_data.pop('modifications', [])
        session = EstimateSession.objects.create(**validated_data)

        # Créer les modifications initiales
        for mod_data in modifications_data:
            ToothModificationEstimate.objects.create(
                session=session,
                **mod_data
            )

        return session


class EstimateSessionUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer spécialisé pour la mise à jour de sessions avec modifications
    """
    modifications = ToothModificationEstimateSerializer(many=True, required=False)

    class Meta:
        model = EstimateSession
        fields = [
            'session_name',
            'patient',
            'is_active',
            'is_completed',
            'modifications'
        ]

    def update(self, instance, validated_data):
        """Mettre à jour une session et ses modifications"""
        modifications_data = validated_data.pop('modifications', None)

        # Mettre à jour les champs de la session
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        # Mettre à jour les modifications si fournies
        if modifications_data is not None:
            # Supprimer les anciennes modifications
            instance.modifications.all().delete()

            # Créer les nouvelles modifications
            for mod_data in modifications_data:
                ToothModificationEstimate.objects.create(
                    session=instance,
                    **mod_data
                )

        return instance


class BulkModificationSerializer(serializers.Serializer):
    """
    Serializer pour les mises à jour en lot de modifications
    """
    session_id = serializers.UUIDField()
    modifications = ToothModificationEstimateSerializer(many=True)

    def validate_session_id(self, value):
        """Valider que la session existe"""
        try:
            EstimateSession.objects.get(id=value)
        except EstimateSession.DoesNotExist:
            raise serializers.ValidationError("Session non trouvée")
        return value

    def validate_modifications(self, value):
        """Valider les modifications"""
        if not value:
            raise serializers.ValidationError("Au moins une modification est requise")
        return value
