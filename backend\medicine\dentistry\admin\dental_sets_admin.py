"""
Administration pour les ensembles dentaires
"""
from django.contrib import admin
from django.utils.html import format_html
from django.utils.translation import gettext_lazy as _
from ..models.dental_sets import (
    DentalSet, ToothButton, SpecialtyInputField,
    ReplacementButton, DentalSetConfiguration
)


@admin.register(DentalSet)
class DentalSetAdmin(admin.ModelAdmin):
    """
    Administration pour les ensembles dentaires
    """
    list_display = [
        'name', 'set_type', 'age_restriction_display',
        'is_active', 'created_at'
    ]
    list_filter = ['set_type', 'age_restriction', 'is_active']
    search_fields = ['name', 'description']
    ordering = ['set_type', '-age_restriction']

    fieldsets = (
        (_('Informations de base'), {
            'fields': ('name', 'set_type', 'age_restriction', 'description')
        }),
        (_('État'), {
            'fields': ('is_active',)
        }),
    )

    def age_restriction_display(self, obj):
        """Affichage coloré de la restriction d'âge"""
        colors = {
            13.5: '#28a745',  # vert
            12.0: '#007bff',  # bleu
            7.5: '#ffc107',   # jaune
            6.0: '#fd7e14',   # orange
            0.0: '#dc3545',   # rouge
        }
        color = colors.get(obj.age_restriction, '#6c757d')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            obj.get_age_restriction_display()
        )
    age_restriction_display.short_description = _('Restriction d\'âge')


class SpecialtyInputFieldInline(admin.TabularInline):
    """
    Inline pour les champs de spécialisation
    """
    model = SpecialtyInputField
    extra = 1
    fields = [
        'specialty_type', 'color_value', 'is_hidden',
        'advanced_treatment_135', 'intermediate_treatment_12',
        'basic_treatment_75', 'preventive_treatment_6',
        'primary_treatment_under6', 'notes'
    ]


class ReplacementButtonInline(admin.TabularInline):
    """
    Inline pour les boutons de remplacement
    """
    model = ReplacementButton
    extra = 1
    fields = [
        'button_name', 'button_action', 'button_color',
        'age_restriction', 'display_order', 'is_active'
    ]


@admin.register(ToothButton)
class ToothButtonAdmin(admin.ModelAdmin):
    """
    Administration pour les boutons de dents
    """
    list_display = [
        'tooth_number', 'tooth_name_short', 'dental_set',
        'patient', 'jaw_display', 'is_expanded', 'is_visible'
    ]
    list_filter = [
        'dental_set__set_type', 'dental_set__age_restriction',
        'is_expanded', 'is_visible'
    ]
    search_fields = [
        'tooth_number', 'tooth_name', 'patient__first_name',
        'patient__last_name'
    ]
    ordering = ['tooth_number']

    inlines = [SpecialtyInputFieldInline]

    fieldsets = (
        (_('Informations de la dent'), {
            'fields': ('dental_set', 'patient', 'tooth_number', 'tooth_name')
        }),
        (_('État du bouton'), {
            'fields': ('is_expanded', 'is_visible')
        }),
    )

    def tooth_name_short(self, obj):
        """Nom de la dent raccourci"""
        return obj.tooth_name[:30] + '...' if len(obj.tooth_name) > 30 else obj.tooth_name
    tooth_name_short.short_description = _('Nom de la dent')

    def jaw_display(self, obj):
        """Affichage de la mâchoire"""
        if obj.is_upper_jaw:
            return format_html('<span style="color: #007bff;">⬆️ Supérieure</span>')
        else:
            return format_html('<span style="color: #28a745;">⬇️ Inférieure</span>')
    jaw_display.short_description = _('Mâchoire')


@admin.register(SpecialtyInputField)
class SpecialtyInputFieldAdmin(admin.ModelAdmin):
    """
    Administration pour les champs de spécialisation
    """
    list_display = [
        'tooth_button', 'specialty_type', 'color_display',
        'age_options_summary', 'is_hidden'
    ]
    list_filter = [
        'specialty_type', 'is_hidden',
        'advanced_treatment_135', 'intermediate_treatment_12',
        'basic_treatment_75', 'preventive_treatment_6',
        'primary_treatment_under6'
    ]
    search_fields = [
        'tooth_button__tooth_name', 'tooth_button__tooth_number',
        'notes'
    ]

    inlines = [ReplacementButtonInline]

    fieldsets = (
        (_('Informations de base'), {
            'fields': ('tooth_button', 'specialty_type', 'color_value', 'is_hidden')
        }),
        (_('Options par âge'), {
            'fields': (
                'advanced_treatment_135', 'intermediate_treatment_12',
                'basic_treatment_75', 'preventive_treatment_6',
                'primary_treatment_under6'
            ),
            'description': _(
                '13.5+ ans: Traitements avancés | '
                '12-13.5 ans: Traitements intermédiaires | '
                '7.5-12 ans: Traitements de base | '
                '6-7.5 ans: Traitements préventifs | '
                'Moins de 6 ans: Traitements primaires'
            )
        }),
        (_('Notes'), {
            'fields': ('notes',)
        }),
    )

    def color_display(self, obj):
        """Affichage de la couleur"""
        return format_html(
            '<div style="width: 20px; height: 20px; background-color: {}; '
            'border: 1px solid #ccc; display: inline-block;"></div> {}',
            obj.color_value,
            obj.color_value
        )
    color_display.short_description = _('Couleur')

    def age_options_summary(self, obj):
        """Résumé des options d'âge activées"""
        options = []
        if obj.advanced_treatment_135:
            options.append('13.5+')
        if obj.intermediate_treatment_12:
            options.append('12-13.5')
        if obj.basic_treatment_75:
            options.append('7.5-12')
        if obj.preventive_treatment_6:
            options.append('6-7.5')
        if obj.primary_treatment_under6:
            options.append('<6')

        return ', '.join(options) if options else _('Aucune')
    age_options_summary.short_description = _('Options d\'âge')


@admin.register(ReplacementButton)
class ReplacementButtonAdmin(admin.ModelAdmin):
    """
    Administration pour les boutons de remplacement
    """
    list_display = [
        'button_name', 'specialty_field', 'button_color_display',
        'display_order', 'is_active'
    ]
    list_filter = [
        'specialty_field__specialty_type', 'is_active'
    ]
    search_fields = [
        'button_name', 'button_action',
        'specialty_field__tooth_button__tooth_name'
    ]
    ordering = ['display_order', 'button_name']

    fieldsets = (
        (_('Informations du bouton'), {
            'fields': ('specialty_field', 'button_name', 'button_action')
        }),
        (_('Apparence'), {
            'fields': ('button_color', 'display_order')
        }),
        (_('État'), {
            'fields': ('is_active',)
        }),
    )

    def button_color_display(self, obj):
        """Affichage de la couleur du bouton"""
        return format_html(
            '<div style="width: 20px; height: 20px; background-color: {}; '
            'border: 1px solid #ccc; display: inline-block;"></div>',
            obj.button_color
        )
    button_color_display.short_description = _('Couleur')




@admin.register(DentalSetConfiguration)
class DentalSetConfigurationAdmin(admin.ModelAdmin):
    """
    Administration pour la configuration des ensembles dentaires
    """
    list_display = [
        'patient', 'active_set_type', 'patient_age',
        'age_category_display', 'last_modified'
    ]
    list_filter = ['active_set_type', 'last_modified']
    search_fields = ['patient__first_name', 'patient__last_name']
    ordering = ['-last_modified']

    fieldsets = (
        (_('Configuration'), {
            'fields': ('patient', 'active_set_type', 'patient_age')
        }),
        (_('Informations'), {
            'fields': ('last_modified',),
            'classes': ('collapse',)
        }),
    )

    readonly_fields = ['last_modified']

    def age_category_display(self, obj):
        """Affichage de la catégorie d'âge"""
        category = obj.get_available_age_category()
        colors = {
            13.5: '#28a745',
            12.0: '#007bff',
            7.5: '#ffc107',
            6.0: '#fd7e14',
            0.0: '#dc3545',
        }
        color = colors.get(category, '#6c757d')

        labels = {
            13.5: '13.5+ ans',
            12.0: '12-13.5 ans',
            7.5: '7.5-12 ans',
            6.0: '6-7.5 ans',
            0.0: 'Moins de 6 ans',
        }

        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            labels.get(category, 'Inconnu')
        )
    age_category_display.short_description = _('Catégorie d\'âge')
