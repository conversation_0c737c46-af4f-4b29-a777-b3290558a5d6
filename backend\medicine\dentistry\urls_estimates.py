# backend/dental_medicine/dentistry/urls_estimates.py

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views_estimates import (
    EstimateSessionViewSet,
    ToothModificationEstimateViewSet,
    EstimateTemplateViewSet
)

# C<PERSON>er le routeur pour les ViewSets
router = DefaultRouter()
router.register(r'estimate-sessions', EstimateSessionViewSet, basename='estimate-sessions')
router.register(r'tooth-modifications', ToothModificationEstimateViewSet, basename='tooth-modifications')
router.register(r'estimate-templates', EstimateTemplateViewSet, basename='estimate-templates')

# URLs pour les estimations
urlpatterns = [
    # URLs du routeur
    path('api/', include(router.urls)),
    
    # URLs personnalisées si nécessaire
    # path('api/estimates/custom-endpoint/', custom_view, name='custom-estimate-endpoint'),
]

# Ajouter les URLs du routeur directement
urlpatterns += router.urls
