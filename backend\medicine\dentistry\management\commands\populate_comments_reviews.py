"""
Management command to populate the database with sample comments and reviews for dentistry doctors.
"""
from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import datetime, timedelta
import random

from dentistry.models import (
    DentistryDoctor, DentistryComment, DentistryReview, DentistryDoctorNote
)


class Command(BaseCommand):
    help = 'Populate the database with sample comments and reviews for dentistry doctors'

    def add_arguments(self, parser):
        parser.add_argument(
            '--doctors',
            type=int,
            default=5,
            help='Number of doctors to create comments for'
        )
        parser.add_argument(
            '--comments-per-doctor',
            type=int,
            default=10,
            help='Number of comments per doctor'
        )
        parser.add_argument(
            '--reviews-per-doctor',
            type=int,
            default=5,
            help='Number of reviews per doctor'
        )

    def handle(self, *args, **options):
        self.stdout.write('Creating sample comments and reviews for dentistry doctors...')

        # Sample data
        patient_names = [
            '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>',
            '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>',
            '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>b <PERSON><PERSON>',
            '<PERSON> <PERSON><PERSON><PERSON>', '<PERSON>ila <PERSON>tta<PERSON>', '<PERSON>ellatif <PERSON>frioui'
        ]

        patient_emails = [
            '<EMAIL>', '<EMAIL>', '<EMAIL>',
            '<EMAIL>', '<EMAIL>', '<EMAIL>',
            '<EMAIL>', '<EMAIL>', '<EMAIL>',
            '<EMAIL>', '<EMAIL>', '<EMAIL>',
            '<EMAIL>', '<EMAIL>', '<EMAIL>'
        ]

        dental_treatments = [
            'Nettoyage dentaire', 'Plombage', 'Extraction dentaire', 'Blanchiment',
            'Couronne dentaire', 'Implant dentaire', 'Traitement de canal',
            'Orthodontie', 'Prothèse dentaire', 'Détartrage', 'Soins de gencives'
        ]

        comment_templates = [
            "Excellent dentiste ! Très professionnel et à l'écoute. Je recommande vivement.",
            "Très satisfait de mon traitement. Le docteur explique bien les procédures.",
            "Cabinet moderne et propre. Personnel accueillant et compétent.",
            "Traitement sans douleur grâce à la technique du docteur. Merci !",
            "Rendez-vous respecté à l'heure. Service de qualité.",
            "Très bon suivi post-traitement. Je reviendrai certainement.",
            "Prix raisonnables pour la qualité des soins prodigués.",
            "Docteur patient qui prend le temps d'expliquer. Très rassurant.",
            "Équipement moderne et techniques à la pointe. Impressionnant !",
            "Résultats excellents ! Mes dents n'ont jamais été aussi belles."
        ]

        review_templates = [
            "Une expérience exceptionnelle du début à la fin. Le Dr {doctor_name} est très compétent et rassurant. Le cabinet est moderne et l'équipe est professionnelle.",
            "Très satisfait de mon traitement de {treatment}. Le docteur a pris le temps de m'expliquer chaque étape. Résultat parfait !",
            "Cabinet très propre et moderne. Le Dr {doctor_name} est à l'écoute et très professionnel. Je recommande sans hésitation.",
            "Excellent suivi et soins de qualité. Le traitement s'est déroulé sans douleur grâce à l'expertise du docteur.",
            "Personnel accueillant et docteur compétent. Les rendez-vous sont respectés et les soins sont de haute qualité."
        ]

        # Get existing doctors or create some if none exist
        doctors = list(DentistryDoctor.objects.all()[:options['doctors']])
        
        if not doctors:
            self.stdout.write(self.style.WARNING('No doctors found. Please create some doctors first.'))
            return

        total_comments = 0
        total_reviews = 0
        total_notes = 0

        for doctor in doctors:
            self.stdout.write(f'Creating data for Dr. {doctor.full_name}...')

            # Create comments
            for i in range(options['comments_per_doctor']):
                patient_name = random.choice(patient_names)
                patient_email = random.choice(patient_emails)
                treatment = random.choice(dental_treatments)
                
                # Create main comment
                comment_content = random.choice(comment_templates)
                rating = random.choices([3, 4, 5], weights=[1, 3, 6])[0]  # Bias towards higher ratings
                
                visit_date = timezone.now().date() - timedelta(days=random.randint(1, 365))
                
                comment = DentistryComment.objects.create(
                    doctor=doctor,
                    patient_name=patient_name,
                    patient_email=patient_email,
                    content=comment_content,
                    rating=rating,
                    treatment_type=treatment,
                    visit_date=visit_date,
                    is_verified=random.choice([True, False]),
                    is_featured=random.choice([True, False]) if rating >= 4 else False,
                    likes=random.randint(0, 15)
                )
                
                # Sometimes add a reply
                if random.random() < 0.3:  # 30% chance of reply
                    reply_content = "Merci pour votre commentaire ! Nous sommes ravis que vous soyez satisfait de nos services."
                    DentistryComment.objects.create(
                        doctor=doctor,
                        patient_name=f"Dr. {doctor.first_name} {doctor.last_name}",
                        patient_email=doctor.email,
                        content=reply_content,
                        parent=comment,
                        is_verified=True
                    )
                
                total_comments += 1

            # Create reviews
            for i in range(options['reviews_per_doctor']):
                patient_name = random.choice(patient_names)
                patient_email = random.choice(patient_emails)
                treatment = random.choice(dental_treatments)
                
                overall_rating = random.choices([3, 4, 5], weights=[1, 3, 6])[0]
                
                review_text = random.choice(review_templates).format(
                    doctor_name=doctor.full_name,
                    treatment=treatment
                )
                
                visit_date = timezone.now().date() - timedelta(days=random.randint(1, 365))
                
                DentistryReview.objects.create(
                    doctor=doctor,
                    patient_name=patient_name,
                    patient_email=patient_email,
                    overall_rating=overall_rating,
                    review_text=review_text,
                    professionalism_rating=random.randint(3, 5),
                    communication_rating=random.randint(3, 5),
                    pain_management_rating=random.randint(3, 5),
                    cleanliness_rating=random.randint(4, 5),
                    wait_time_rating=random.randint(3, 5),
                    treatment_received=treatment,
                    visit_date=visit_date,
                    would_recommend=overall_rating >= 4,
                    is_verified=random.choice([True, False]),
                    is_featured=random.choice([True, False]) if overall_rating >= 4 else False,
                    helpful_count=random.randint(0, 10)
                )
                
                total_reviews += 1

            # Create some internal notes
            note_types = ['general', 'performance', 'training', 'achievement']
            priorities = ['low', 'medium', 'high']
            
            for i in range(3):  # 3 notes per doctor
                note_type = random.choice(note_types)
                priority = random.choice(priorities)
                
                note_titles = {
                    'general': f'Note générale - Dr. {doctor.last_name}',
                    'performance': f'Évaluation performance - {timezone.now().strftime("%B %Y")}',
                    'training': f'Formation continue - Nouvelles techniques',
                    'achievement': f'Reconnaissance - Excellent travail'
                }
                
                note_contents = {
                    'general': 'Note administrative concernant le suivi du docteur.',
                    'performance': 'Excellents retours patients ce mois-ci. Taux de satisfaction très élevé.',
                    'training': 'Participation à la formation sur les nouvelles techniques d\'implantologie.',
                    'achievement': 'Félicitations pour les excellents résultats et la satisfaction des patients.'
                }
                
                DentistryDoctorNote.objects.create(
                    doctor=doctor,
                    title=note_titles[note_type],
                    content=note_contents[note_type],
                    note_type=note_type,
                    priority=priority,
                    is_confidential=random.choice([True, False]),
                    author_name='Administrateur',
                    author_role='Gestionnaire'
                )
                
                total_notes += 1

        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully created:\n'
                f'- {total_comments} comments\n'
                f'- {total_reviews} reviews\n'
                f'- {total_notes} internal notes\n'
                f'for {len(doctors)} doctors'
            )
        )
