"""
URL configuration for the dentistry application.
"""
from django.urls import path, include
from rest_framework.routers import Default<PERSON>out<PERSON>
from dentistry.views import (
    DentistryPatientViewSet, DentistryMedicalRecordViewSet,
    DentalImagingViewSet, DentistryAppointmentViewSet,
    DentalTreatmentViewSet, DentalProcedureViewSet,
    DentalLaboratoryViewSet, LabWorkOrderViewSet,
    DentistryRoleViewSet, DentistryStaffProfileViewSet,
    DentistryBillingCodeViewSet, DentistryInvoiceViewSet,
    DentistryPatientInsuranceViewSet, ToothViewSet
)
from dentistry.views.dental_svg_views import DentalSvgDataViewSet, DentalModificationViewSet
from dentistry.views.dental_svg_system import (
    DentalSvgConfigurationViewSet, DentalSvgPathViewSet, DentalTreatmentTemplateViewSet
)
from dentistry.views.comment import (
    DentistryCommentViewSet, DentistryReviewViewSet, DentistryDoctorNoteViewSet
)
from dentistry.views.complete_patient import (
    create_complete_patient, get_doctors_list, get_patient_complete_info, update_complete_patient
)
from dentistry.views.dashboard import (
    DentistryAppointmentSettingsViewSet, DentistryWorkingHoursViewSet,
    DentistryHolidayViewSet, DentistryLocationSettingsViewSet,
    DentistryDashboardViewSet
)

# API root view
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework.reverse import reverse

@api_view(['GET'])
def dentistry_root(request, format=None):
    """
    Root endpoint for the dentistry API.
    """
    return Response({
        'patients': reverse('dentistry-patient-list', request=request, format=format),
        'medical-records': reverse('dentistry-medical-record-list', request=request, format=format),
        'dental-imaging': reverse('dental-imaging-list', request=request, format=format),
        'appointments': reverse('dentistry-appointment-list', request=request, format=format),
        'treatments': reverse('dental-treatment-list', request=request, format=format),
        'procedures': reverse('dental-procedure-list', request=request, format=format),
        'laboratories': reverse('dental-laboratory-list', request=request, format=format),
        'lab-work-orders': reverse('lab-work-order-list', request=request, format=format),
        'roles': reverse('dentistry-role-list', request=request, format=format),
        'staff': reverse('dentistry-staff-list', request=request, format=format),
        'billing-codes': reverse('dentistry-billing-code-list', request=request, format=format),
        'invoices': reverse('dentistry-invoice-list', request=request, format=format),
        'patient-insurances': reverse('dentistry-patient-insurance-list', request=request, format=format),
        'teeth': reverse('dentistry-tooth-list', request=request, format=format),
        'comments': reverse('dentistry-comment-list', request=request, format=format),
        'reviews': reverse('dentistry-review-list', request=request, format=format),
        'doctor-notes': reverse('dentistry-doctor-note-list', request=request, format=format),
        'appointment-settings': reverse('dentistry-appointment-settings-list', request=request, format=format),
        'working-hours': reverse('dentistry-working-hours-list', request=request, format=format),
        'holidays': reverse('dentistry-holiday-list', request=request, format=format),
        'location-settings': reverse('dentistry-location-settings-list', request=request, format=format),
        'dashboard': reverse('dentistry-dashboard-summary', request=request, format=format),
        'dental-svg': reverse('dental-svg-data-list', request=request, format=format),
        'dental-modifications': reverse('dental-modifications-list', request=request, format=format),
        'dental-svg-configurations': reverse('dental-svg-configuration-list', request=request, format=format),
        'dental-svg-paths': reverse('dental-svg-path-list', request=request, format=format),
        'dental-treatment-templates': reverse('dental-treatment-template-list', request=request, format=format),
    })

# Create a router and register our viewsets with it
router = DefaultRouter()
router.register(r'patients', DentistryPatientViewSet, basename='dentistry-patient')
router.register(r'medical-records', DentistryMedicalRecordViewSet, basename='dentistry-medical-record')
router.register(r'dental-imaging', DentalImagingViewSet, basename='dental-imaging')
router.register(r'appointments', DentistryAppointmentViewSet, basename='dentistry-appointment')
router.register(r'treatments', DentalTreatmentViewSet, basename='dental-treatment')
router.register(r'procedures', DentalProcedureViewSet, basename='dental-procedure')
router.register(r'laboratories', DentalLaboratoryViewSet, basename='dental-laboratory')
router.register(r'lab-work-orders', LabWorkOrderViewSet, basename='lab-work-order')
router.register(r'roles', DentistryRoleViewSet, basename='dentistry-role')
router.register(r'staff', DentistryStaffProfileViewSet, basename='dentistry-staff')
router.register(r'billing-codes', DentistryBillingCodeViewSet, basename='dentistry-billing-code')
router.register(r'invoices', DentistryInvoiceViewSet, basename='dentistry-invoice')
router.register(r'patient-insurances', DentistryPatientInsuranceViewSet, basename='dentistry-patient-insurance')
router.register(r'teeth', ToothViewSet, basename='dentistry-tooth')
router.register(r'comments', DentistryCommentViewSet, basename='dentistry-comment')
router.register(r'reviews', DentistryReviewViewSet, basename='dentistry-review')
router.register(r'doctor-notes', DentistryDoctorNoteViewSet, basename='dentistry-doctor-note')
router.register(r'appointment-settings', DentistryAppointmentSettingsViewSet, basename='dentistry-appointment-settings')
router.register(r'working-hours', DentistryWorkingHoursViewSet, basename='dentistry-working-hours')
router.register(r'holidays', DentistryHolidayViewSet, basename='dentistry-holiday')
router.register(r'location-settings', DentistryLocationSettingsViewSet, basename='dentistry-location-settings')
router.register(r'dashboard', DentistryDashboardViewSet, basename='dentistry-dashboard')

# Nouvelles APIs pour les données SVG dentaires avec gestion d'âge
router.register(r'dental-svg', DentalSvgDataViewSet, basename='dental-svg-data')
router.register(r'dental-modifications', DentalModificationViewSet, basename='dental-modifications')

# APIs pour le système SVG dentaire dynamique
router.register(r'dental-svg-configurations', DentalSvgConfigurationViewSet, basename='dental-svg-configuration')
router.register(r'dental-svg-paths', DentalSvgPathViewSet, basename='dental-svg-path')
router.register(r'dental-treatment-templates', DentalTreatmentTemplateViewSet, basename='dental-treatment-template')

urlpatterns = [
    path('', dentistry_root, name='dentistry-root'),
    path('', include(router.urls)),
    # Include estimate URLs
    path('', include('dentistry.urls_estimates')),

    # Complete patient management endpoints
    path('patients/complete/', create_complete_patient, name='create-complete-patient'),
    path('patients/<int:patient_id>/complete/', get_patient_complete_info, name='get-complete-patient'),
    path('patients/<int:patient_id>/complete/update/', update_complete_patient, name='update-complete-patient'),
    path('doctors/', get_doctors_list, name='get-doctors-list'),
]
