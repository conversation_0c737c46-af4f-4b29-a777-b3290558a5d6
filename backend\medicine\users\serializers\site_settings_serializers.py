from rest_framework import serializers
from users.models import SiteSettings


class SiteSettingsSerializer(serializers.ModelSerializer):
    """
    Serializer for the SiteSettings model.
    """
    class Meta:
        model = SiteSettings
        fields = [
            'support_email',
            'contact_email',
            'phone_number',
            'address',
            'site_name',
            'site_url',
            'facebook_url',
            'twitter_url',
            'instagram_url',
            'linkedin_url',
            'youtube_url',
            'meta_title',
            'meta_description',
            'meta_keywords',
            'og_title',
            'og_description',
            'og_image',
            'favicon',
            'logo',
            'footer_text',
            'copyright_text',
        ]
        read_only_fields = ['created_at', 'updated_at']
