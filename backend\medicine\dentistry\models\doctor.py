"""
Doctor models for the dentistry application.
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.utils.text import slugify
from dentistry.models.base import DentistryBaseModel, DentistrySpecialty, DentistryService

class DentistryDoctor(DentistryBaseModel):
    """
    Dentistry doctor model.
    """
    user_id = models.UUIDField(
        verbose_name=_("User ID"),
        help_text=_("Reference to the user in the auth service"),
        null=True,
        blank=True
    )
    slug = models.SlugField(
        max_length=100,
        unique=True,
        null=True,
        blank=True,
        verbose_name=_("Slug")
    )
    first_name = models.CharField(
        max_length=100,
        verbose_name=_("First name"),
        null=True,
        blank=True
    )
    last_name = models.CharField(
        max_length=100,
        verbose_name=_("Last name"),
        null=True,
        blank=True
    )
    email = models.EmailField(
        verbose_name=_("Email"),
        null=True,
        blank=True
    )
    phone = models.Char<PERSON><PERSON>(
        max_length=20,
        null=True,
        blank=True,
        verbose_name=_("Phone")
    )
    specialties = models.ManyToManyField(
        DentistrySpecialty,
        related_name="doctors",
        verbose_name=_("Specialties")
    )
    services = models.ManyToManyField(
        DentistryService,
        related_name="doctors",
        verbose_name=_("Services")
    )
    license_number = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name=_("License number")
    )
    bio = models.TextField(
        null=True,
        blank=True,
        verbose_name=_("Biography")
    )
    profile_image = models.URLField(
        null=True,
        blank=True,
        verbose_name=_("Profile image")
    )

    class Meta:
        verbose_name = _("Dentistry Doctor")
        verbose_name_plural = _("Dentistry Doctors")

    def __str__(self):
        return f"Dr. {self.first_name} {self.last_name}"

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"

    def save(self, *args, **kwargs):
        # Generate slug if not provided
        if not self.slug:
            base_slug = slugify(f"{self.first_name}-{self.last_name}")

            # Check for existing slugs to avoid duplicates
            slug = base_slug
            counter = 1

            # Keep checking until we find a unique slug
            while DentistryDoctor.objects.filter(slug=slug).exclude(pk=self.pk).exists():
                # If duplicate exists, append a number
                slug = f"{base_slug}-{counter}"
                counter += 1

            self.slug = slug

        super().save(*args, **kwargs)

class DentistryDoctorSettings(DentistryBaseModel):
    """
    Dentistry doctor settings model.
    """
    doctor = models.OneToOneField(
        DentistryDoctor,
        on_delete=models.CASCADE,
        related_name="settings",
        verbose_name=_("Doctor")
    )
    theme = models.CharField(
        max_length=20,
        default="light",
        verbose_name=_("Theme")
    )
    email_notifications = models.BooleanField(
        default=True,
        verbose_name=_("Email notifications")
    )
    sms_notifications = models.BooleanField(
        default=True,
        verbose_name=_("SMS notifications")
    )
    appointment_reminders = models.BooleanField(
        default=True,
        verbose_name=_("Appointment reminders")
    )
    marketing_emails = models.BooleanField(
        default=False,
        verbose_name=_("Marketing emails")
    )
    language = models.CharField(
        max_length=10,
        default="en",
        verbose_name=_("Language")
    )
    time_format = models.CharField(
        max_length=10,
        default="24h",
        verbose_name=_("Time format")
    )
    date_format = models.CharField(
        max_length=10,
        default="yyyy-mm-dd",
        verbose_name=_("Date format")
    )
    font_size = models.PositiveIntegerField(
        default=16,
        verbose_name=_("Font size")
    )

    class Meta:
        verbose_name = _("Dentistry Doctor Settings")
        verbose_name_plural = _("Dentistry Doctor Settings")

    def __str__(self):
        return f"Settings for {self.doctor}"
