"""
Commande pour vérifier que tous les modèles sont enregistrés dans l'administration
"""
from django.core.management.base import BaseCommand
from django.contrib import admin
from django.apps import apps


class Command(BaseCommand):
    help = 'Check which models are registered in Django admin'

    def handle(self, *args, **options):
        """
        Vérifie les modèles enregistrés dans l'administration
        """
        self.stdout.write(self.style.SUCCESS('Checking Django admin registered models...'))

        # Obtenir tous les modèles de l'application dentistry
        dentistry_models = list(apps.get_app_config('dentistry').get_models())

        self.stdout.write(f'\n📋 Total models in dentistry app: {len(dentistry_models)}')

        registered_models = []
        unregistered_models = []

        for model in dentistry_models:
            if model in admin.site._registry:
                registered_models.append(model)
                admin_class = admin.site._registry[model].__class__.__name__
                self.stdout.write(
                    self.style.SUCCESS(f'✅ {model.__name__} -> {admin_class}')
                )
            else:
                unregistered_models.append(model)
                self.stdout.write(
                    self.style.WARNING(f'❌ {model.__name__} (NOT REGISTERED)')
                )

        self.stdout.write(f'\n📊 Summary:')
        self.stdout.write(f'✅ Registered: {len(registered_models)}')
        self.stdout.write(f'❌ Unregistered: {len(unregistered_models)}')

        if unregistered_models:
            self.stdout.write(f'\n⚠️  Unregistered models:')
            for model in unregistered_models:
                self.stdout.write(f'   - {model.__name__}')

        # Vérifier spécifiquement les nouveaux modèles SVG
        svg_models = [
            'DentalSvgConfiguration',
            'DentalSvgPath',
            'DentalTreatmentTemplate'
        ]

        self.stdout.write(f'\n🦷 Checking SVG System models:')
        for model_name in svg_models:
            try:
                model = apps.get_model('dentistry', model_name)
                if model in admin.site._registry:
                    self.stdout.write(
                        self.style.SUCCESS(f'✅ {model_name} is registered')
                    )
                else:
                    self.stdout.write(
                        self.style.ERROR(f'❌ {model_name} is NOT registered')
                    )
            except LookupError:
                self.stdout.write(
                    self.style.ERROR(f'❌ {model_name} model not found')
                )

        self.stdout.write(f'\n🔗 Admin URLs available:')
        for model in registered_models:
            app_label = model._meta.app_label
            model_name = model._meta.model_name
            url = f'/admin/{app_label}/{model_name}/'
            self.stdout.write(f'   {model.__name__}: {url}')

        self.stdout.write(self.style.SUCCESS('\n✨ Admin check completed!'))
