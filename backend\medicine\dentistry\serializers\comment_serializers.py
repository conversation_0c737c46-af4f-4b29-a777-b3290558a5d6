"""
Serializers for comment and review models in the dentistry application.
"""
from rest_framework import serializers
from dentistry.models import DentistryComment, DentistryReview, DentistryDoctorNote


class DentistryCommentSerializer(serializers.ModelSerializer):
    """
    Serializer for DentistryComment model.
    """
    doctor_name = serializers.CharField(source='doctor.full_name', read_only=True)
    doctor_id = serializers.CharField(source='doctor.id', read_only=True)
    replies = serializers.SerializerMethodField()
    reply_count = serializers.ReadOnlyField()
    is_reply = serializers.ReadOnlyField()

    class Meta:
        model = DentistryComment
        fields = [
            'id', 'doctor', 'doctor_id', 'doctor_name', 'patient_name', 
            'patient_email', 'patient_phone', 'content', 'parent', 'rating', 
            'likes', 'treatment_type', 'visit_date', 'is_verified', 
            'is_featured', 'is_active', 'created_at', 'updated_at',
            'replies', 'reply_count', 'is_reply'
        ]
        read_only_fields = [
            'id', 'created_at', 'updated_at', 'doctor_id', 'doctor_name',
            'replies', 'reply_count', 'is_reply', 'likes'
        ]

    def get_replies(self, obj):
        """Get replies for this comment."""
        if obj.replies.exists():
            replies = obj.replies.filter(is_active=True).order_by('created_at')
            return DentistryCommentSerializer(replies, many=True, context=self.context).data
        return []

    def validate(self, data):
        """Custom validation."""
        # Only top-level comments can have ratings
        if data.get('parent') and data.get('rating'):
            raise serializers.ValidationError("Replies cannot have ratings")
        return data


class DentistryReviewSerializer(serializers.ModelSerializer):
    """
    Serializer for DentistryReview model.
    """
    doctor_name = serializers.CharField(source='doctor.full_name', read_only=True)
    doctor_id = serializers.CharField(source='doctor.id', read_only=True)
    average_detailed_rating = serializers.ReadOnlyField()

    class Meta:
        model = DentistryReview
        fields = [
            'id', 'doctor', 'doctor_id', 'doctor_name', 'patient_name', 
            'patient_email', 'overall_rating', 'review_text',
            'professionalism_rating', 'communication_rating', 
            'pain_management_rating', 'cleanliness_rating', 'wait_time_rating',
            'treatment_received', 'visit_date', 'would_recommend',
            'is_verified', 'verification_method', 'is_featured', 'is_active',
            'helpful_count', 'created_at', 'updated_at', 'average_detailed_rating'
        ]
        read_only_fields = [
            'id', 'created_at', 'updated_at', 'doctor_id', 'doctor_name',
            'helpful_count', 'average_detailed_rating'
        ]

    def validate_overall_rating(self, value):
        """Validate overall rating."""
        if value < 1 or value > 5:
            raise serializers.ValidationError("Rating must be between 1 and 5")
        return value


class DentistryDoctorNoteSerializer(serializers.ModelSerializer):
    """
    Serializer for DentistryDoctorNote model.
    """
    doctor_name = serializers.CharField(source='doctor.full_name', read_only=True)
    doctor_id = serializers.CharField(source='doctor.id', read_only=True)

    class Meta:
        model = DentistryDoctorNote
        fields = [
            'id', 'doctor', 'doctor_id', 'doctor_name', 'title', 'content',
            'note_type', 'priority', 'is_confidential', 'author_name',
            'author_role', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'doctor_id', 'doctor_name']


class DentistryCommentCreateSerializer(serializers.ModelSerializer):
    """
    Simplified serializer for creating comments.
    """
    class Meta:
        model = DentistryComment
        fields = [
            'doctor', 'patient_name', 'patient_email', 'patient_phone',
            'content', 'parent', 'rating', 'treatment_type', 'visit_date'
        ]

    def validate(self, data):
        """Custom validation for creation."""
        # Only top-level comments can have ratings
        if data.get('parent') and data.get('rating'):
            raise serializers.ValidationError("Replies cannot have ratings")
        
        # Rating is required for top-level comments
        if not data.get('parent') and not data.get('rating'):
            raise serializers.ValidationError("Rating is required for comments")
            
        return data


class DentistryReviewCreateSerializer(serializers.ModelSerializer):
    """
    Simplified serializer for creating reviews.
    """
    class Meta:
        model = DentistryReview
        fields = [
            'doctor', 'patient_name', 'patient_email', 'overall_rating',
            'review_text', 'professionalism_rating', 'communication_rating',
            'pain_management_rating', 'cleanliness_rating', 'wait_time_rating',
            'treatment_received', 'visit_date', 'would_recommend'
        ]

    def validate_overall_rating(self, value):
        """Validate overall rating."""
        if value < 1 or value > 5:
            raise serializers.ValidationError("Rating must be between 1 and 5")
        return value


class DentistryCommentStatsSerializer(serializers.Serializer):
    """
    Serializer for comment statistics.
    """
    total_comments = serializers.IntegerField()
    total_reviews = serializers.IntegerField()
    average_rating = serializers.FloatField()
    rating_distribution = serializers.DictField()
    recent_comments = DentistryCommentSerializer(many=True)
    featured_reviews = DentistryReviewSerializer(many=True)


class DentistryDoctorRatingSerializer(serializers.Serializer):
    """
    Serializer for doctor rating summary.
    """
    doctor_id = serializers.CharField()
    doctor_name = serializers.CharField()
    total_reviews = serializers.IntegerField()
    average_overall_rating = serializers.FloatField()
    average_professionalism = serializers.FloatField()
    average_communication = serializers.FloatField()
    average_pain_management = serializers.FloatField()
    average_cleanliness = serializers.FloatField()
    average_wait_time = serializers.FloatField()
    recommendation_percentage = serializers.FloatField()
    verified_reviews_count = serializers.IntegerField()
    recent_review_date = serializers.DateTimeField()
