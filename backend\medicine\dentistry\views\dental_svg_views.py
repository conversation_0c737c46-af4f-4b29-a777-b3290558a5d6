"""
Vues API pour la gestion des données SVG dentaires avec restrictions d'âge.
"""
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.contrib.auth import get_user_model
from django.shortcuts import get_object_or_404
from django.db import transaction
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from dentistry.models.dental_svg import DentalSvgData, DentalModification
from dentistry.serializers.dental_svg_serializers import (
    DentalSvgDataSerializer,
    DentalSvgDataCreateSerializer,
    DentalModificationSerializer,
    DentalModificationCreateSerializer,
    DentalAgeRestrictionSerializer,
    DentalSvgBulkUpdateSerializer
)

User = get_user_model()


class DentalSvgDataViewSet(viewsets.ModelViewSet):
    """
    ViewSet pour la gestion des données SVG dentaires avec restrictions d'âge.
    
    Fonctionnalités:
    - CRUD complet des données SVG dentaires
    - Gestion automatique des restrictions d'âge
    - Filtrage des données selon l'âge du patient
    - Vérification des permissions d'accès
    """
    queryset = DentalSvgData.objects.all()
    serializer_class = DentalSvgDataSerializer
    permission_classes = [IsAuthenticated]
    lookup_field = 'patient_id'

    def get_serializer_class(self):
        """Retourne le serializer approprié selon l'action."""
        if self.action == 'create':
            return DentalSvgDataCreateSerializer
        return DentalSvgDataSerializer

    def get_object(self):
        """Récupère l'objet par patient_id."""
        patient_id = self.kwargs.get('patient_id')
        if patient_id:
            return get_object_or_404(DentalSvgData, patient_id=patient_id)
        return super().get_object()

    @swagger_auto_schema(
        operation_description="Récupère les données SVG dentaires d'un patient avec filtrage d'âge",
        responses={
            200: DentalSvgDataSerializer,
            404: "Patient non trouvé",
            403: "Accès refusé pour cet âge"
        }
    )
    def retrieve(self, request, patient_id=None):
        """Récupère les données dentaires d'un patient avec vérification d'âge."""
        try:
            dental_data = DentalSvgData.objects.get(patient_id=patient_id)
        except DentalSvgData.DoesNotExist:
            # Créer automatiquement si n'existe pas
            patient = get_object_or_404(User, id=patient_id)
            dental_data = DentalSvgData.objects.create(
                patient=patient,
                dental_svg_upper='',
                dental_svg_lower=''
            )
            dental_data.update_age_restriction()

        serializer = self.get_serializer(dental_data)
        return Response(serializer.data)

    @swagger_auto_schema(
        operation_description="Vérifie les restrictions d'âge pour un patient",
        manual_parameters=[
            openapi.Parameter(
                'patient_id',
                openapi.IN_PATH,
                description="ID du patient",
                type=openapi.TYPE_STRING,
                format=openapi.FORMAT_UUID
            )
        ],
        responses={
            200: DentalAgeRestrictionSerializer,
            404: "Patient non trouvé"
        }
    )
    @action(detail=False, methods=['get'], url_path='check-age-restrictions/(?P<patient_id>[^/.]+)')
    def check_age_restrictions(self, request, patient_id=None):
        """Vérifie les restrictions d'âge pour un patient donné."""
        patient = get_object_or_404(User, id=patient_id)
        
        # Récupérer ou créer les données dentaires
        dental_data, created = DentalSvgData.objects.get_or_create(
            patient=patient,
            defaults={
                'dental_svg_upper': '',
                'dental_svg_lower': ''
            }
        )
        
        if created:
            dental_data.update_age_restriction()

        # Préparer les données de réponse
        response_data = {
            'patient_id': patient_id,
            'age': dental_data.patient_age,
            'age_restriction': dental_data.current_age_restriction,
            'dental_view_level': dental_data.dental_view_level,
            'can_view_dental_data': dental_data.can_view_dental_data(),
            'allowed_teeth_count': dental_data.get_allowed_teeth_count(),
            'restriction_message': self._get_restriction_message(dental_data)
        }

        serializer = DentalAgeRestrictionSerializer(response_data)
        return Response(serializer.data)

    def _get_restriction_message(self, dental_data):
        """Génère un message explicatif pour les restrictions."""
        age = dental_data.patient_age
        if age is None:
            return "Âge non défini - accès complet par défaut"
        elif age < 6:
            return "Patient de moins de 6 ans - données dentaires masquées"
        elif age < 7.5:
            return "Patient de moins de 7 ans et demi - vue minimale autorisée"
        elif age < 12:
            return "Patient de moins de 12 ans - vue limitée autorisée"
        elif age < 13.5:
            return "Patient de moins de 13 ans et demi - vue partielle autorisée"
        else:
            return "Patient de 13 ans et demi ou plus - accès complet autorisé"

    @swagger_auto_schema(
        operation_description="Met à jour en lot les données SVG et modifications",
        request_body=DentalSvgBulkUpdateSerializer,
        responses={
            200: "Mise à jour réussie",
            400: "Données invalides",
            403: "Accès refusé pour cet âge"
        }
    )
    @action(detail=False, methods=['post'], url_path='bulk-update')
    def bulk_update(self, request):
        """Met à jour en lot les données SVG et les modifications."""
        serializer = DentalSvgBulkUpdateSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        with transaction.atomic():
            result = serializer.save()
            
        response_data = {
            'dental_svg_data': DentalSvgDataSerializer(result['dental_svg_data']).data,
            'modifications_created': len(result['modifications']),
            'message': 'Mise à jour réussie'
        }
        
        return Response(response_data, status=status.HTTP_200_OK)


class DentalModificationViewSet(viewsets.ModelViewSet):
    """
    ViewSet pour la gestion des modifications dentaires individuelles.
    """
    queryset = DentalModification.objects.all()
    serializer_class = DentalModificationSerializer
    permission_classes = [IsAuthenticated]

    def get_serializer_class(self):
        """Retourne le serializer approprié selon l'action."""
        if self.action == 'create':
            return DentalModificationCreateSerializer
        return DentalModificationSerializer

    def get_queryset(self):
        """Filtre les modifications par patient si spécifié."""
        queryset = super().get_queryset()
        patient_id = self.request.query_params.get('patient_id')
        
        if patient_id:
            queryset = queryset.filter(dental_svg_data__patient_id=patient_id)
            
        return queryset.select_related('dental_svg_data__patient')

    @swagger_auto_schema(
        operation_description="Récupère les modifications dentaires d'un patient",
        manual_parameters=[
            openapi.Parameter(
                'patient_id',
                openapi.IN_QUERY,
                description="ID du patient pour filtrer les modifications",
                type=openapi.TYPE_STRING,
                format=openapi.FORMAT_UUID
            ),
            openapi.Parameter(
                'specialty',
                openapi.IN_QUERY,
                description="Spécialité dentaire pour filtrer",
                type=openapi.TYPE_STRING
            ),
            openapi.Parameter(
                'tooth_number',
                openapi.IN_QUERY,
                description="Numéro de dent pour filtrer",
                type=openapi.TYPE_INTEGER
            )
        ]
    )
    def list(self, request, *args, **kwargs):
        """Liste les modifications avec filtres optionnels."""
        queryset = self.get_queryset()
        
        # Filtres additionnels
        specialty = request.query_params.get('specialty')
        if specialty:
            queryset = queryset.filter(specialty=specialty)
            
        tooth_number = request.query_params.get('tooth_number')
        if tooth_number:
            queryset = queryset.filter(tooth_number=tooth_number)
        
        # Pagination
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @swagger_auto_schema(
        operation_description="Supprime toutes les modifications d'un patient",
        manual_parameters=[
            openapi.Parameter(
                'patient_id',
                openapi.IN_PATH,
                description="ID du patient",
                type=openapi.TYPE_STRING,
                format=openapi.FORMAT_UUID
            )
        ]
    )
    @action(detail=False, methods=['delete'], url_path='clear-patient/(?P<patient_id>[^/.]+)')
    def clear_patient_modifications(self, request, patient_id=None):
        """Supprime toutes les modifications d'un patient."""
        deleted_count = DentalModification.objects.filter(
            dental_svg_data__patient_id=patient_id
        ).delete()[0]
        
        return Response({
            'message': f'{deleted_count} modifications supprimées',
            'patient_id': patient_id
        }, status=status.HTTP_200_OK)
