from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='DoctorLicense',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('license_number', models.CharField(max_length=100, unique=True)),
                ('status', models.CharField(choices=[('pending', 'Pending Activation'), ('active', 'Active'), ('expired', 'Expired'), ('revoked', 'Revoked')], default='pending', max_length=20)),
                ('license_data', models.JSONField(default=dict)),
                ('issue_date', models.DateTimeField(default=django.utils.timezone.now)),
                ('expiry_date', models.DateTimeField()),
                ('activation_code', models.Char<PERSON>ield(blank=True, max_length=50, null=True)),
                ('activation_date', models.DateTimeField(blank=True, null=True)),
                ('revocation_date', models.DateTimeField(blank=True, null=True)),
                ('revocation_reason', models.TextField(blank=True, null=True)),
                ('is_renewed', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('previous_license', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='renewal', to='users.doctorlicense')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='doctor_license', to='users.user')),
            ],
            options={
                'verbose_name': 'Doctor License',
                'verbose_name_plural': 'Doctor Licenses',
            },
        ),
    ]
