/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/nextjs-toploader";
exports.ids = ["vendor-chunks/nextjs-toploader"];
exports.modules = {

/***/ "(rsc)/./node_modules/nextjs-toploader/dist/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/nextjs-toploader/dist/index.js ***!
  \*****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

const { createProxy } = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js")

module.exports = createProxy("C:\\Users\\<USER>\\Desktop\\svp\\frontend\\medicine\\node_modules\\nextjs-toploader\\dist\\index.js")


/***/ }),

/***/ "(ssr)/./node_modules/nextjs-toploader/dist/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/nextjs-toploader/dist/index.js ***!
  \*****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("/* __next_internal_client_entry_do_not_use__  cjs */ \nvar D = Object.create;\nvar y = Object.defineProperty;\nvar G = Object.getOwnPropertyDescriptor;\nvar Q = Object.getOwnPropertyNames;\nvar V = Object.getPrototypeOf, Y = Object.prototype.hasOwnProperty;\nvar i = (o, e)=>y(o, \"name\", {\n        value: e,\n        configurable: !0\n    });\nvar Z = (o, e)=>{\n    for(var p in e)y(o, p, {\n        get: e[p],\n        enumerable: !0\n    });\n}, C = (o, e, p, h)=>{\n    if (e && typeof e == \"object\" || typeof e == \"function\") for (let l of Q(e))!Y.call(o, l) && l !== p && y(o, l, {\n        get: ()=>e[l],\n        enumerable: !(h = G(e, l)) || h.enumerable\n    });\n    return o;\n};\nvar T = (o, e, p)=>(p = o != null ? D(V(o)) : {}, C(e || !o || !o.__esModule ? y(p, \"default\", {\n        value: o,\n        enumerable: !0\n    }) : p, o)), _ = (o)=>C(y({}, \"__esModule\", {\n        value: !0\n    }), o);\nvar re = {};\nZ(re, {\n    default: ()=>ee,\n    useTopLoader: ()=>O\n});\nmodule.exports = _(re);\nvar t = T(__webpack_require__(/*! prop-types */ \"(ssr)/./node_modules/prop-types/index.js\")), L = T(__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\")), a = T(__webpack_require__(/*! nprogress */ \"(ssr)/./node_modules/nprogress/nprogress.js\"));\nvar s = T(__webpack_require__(/*! nprogress */ \"(ssr)/./node_modules/nprogress/nprogress.js\"));\nvar O = i(()=>({\n        start: ()=>s.start(),\n        done: (e)=>s.done(e),\n        remove: ()=>s.remove(),\n        setProgress: (e)=>s.set(e),\n        inc: (e)=>s.inc(e),\n        trickle: ()=>s.trickle(),\n        isStarted: ()=>s.isStarted(),\n        isRendered: ()=>s.isRendered(),\n        getPositioningCSS: ()=>s.getPositioningCSS()\n    }), \"useTopLoader\");\nvar z = i(({ color: o, height: e, showSpinner: p, crawl: h, crawlSpeed: l, initialPosition: v, easing: S, speed: k, shadow: N, template: E, zIndex: A = 1600, showAtBottom: H = !1, showForHashAnchor: K = !0 })=>{\n    let W = \"#29d\", u = o != null ? o : W, j = e != null ? e : 3, B = !N && N !== void 0 ? \"\" : N ? `box-shadow:${N}` : `box-shadow:0 0 10px ${u},0 0 5px ${u}`, F = L.createElement(\"style\", null, `#nprogress{pointer-events:none}#nprogress .bar{background:${u};position:fixed;z-index:${A};${H ? \"bottom: 0;\" : \"top: 0;\"}left:0;width:100%;height:${j}px}#nprogress .peg{display:block;position:absolute;right:0;width:100px;height:100%;${B};opacity:1;-webkit-transform:rotate(3deg) translate(0px,-4px);-ms-transform:rotate(3deg) translate(0px,-4px);transform:rotate(3deg) translate(0px,-4px)}#nprogress .spinner{display:block;position:fixed;z-index:${A};${H ? \"bottom: 15px;\" : \"top: 15px;\"}right:15px}#nprogress .spinner-icon{width:18px;height:18px;box-sizing:border-box;border:2px solid transparent;border-top-color:${u};border-left-color:${u};border-radius:50%;-webkit-animation:nprogress-spinner 400ms linear infinite;animation:nprogress-spinner 400ms linear infinite}.nprogress-custom-parent{overflow:hidden;position:relative}.nprogress-custom-parent #nprogress .bar,.nprogress-custom-parent #nprogress .spinner{position:absolute}@-webkit-keyframes nprogress-spinner{0%{-webkit-transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg)}}@keyframes nprogress-spinner{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}`), f = i((m)=>new URL(m, window.location.href).href, \"toAbsoluteURL\"), q = i((m, b)=>{\n        let d = new URL(f(m)), P = new URL(f(b));\n        return d.href.split(\"#\")[0] === P.href.split(\"#\")[0];\n    }, \"isHashAnchor\"), I = i((m, b)=>{\n        let d = new URL(f(m)), P = new URL(f(b));\n        return d.hostname.replace(/^www\\./, \"\") === P.hostname.replace(/^www\\./, \"\");\n    }, \"isSameHostName\");\n    return L.useEffect({\n        \"z.useEffect\": ()=>{\n            a.configure({\n                showSpinner: p != null ? p : !0,\n                trickle: h != null ? h : !0,\n                trickleSpeed: l != null ? l : 200,\n                minimum: v != null ? v : .08,\n                easing: S != null ? S : \"ease\",\n                speed: k != null ? k : 200,\n                template: E != null ? E : '<div class=\"bar\" role=\"bar\"><div class=\"peg\"></div></div><div class=\"spinner\" role=\"spinner\"><div class=\"spinner-icon\"></div></div>'\n            });\n            function m(r, g) {\n                let n = new URL(r), c = new URL(g);\n                if (n.hostname === c.hostname && n.pathname === c.pathname && n.search === c.search) {\n                    let w = n.hash, x = c.hash;\n                    return w !== x && n.href.replace(w, \"\") === c.href.replace(x, \"\");\n                }\n                return !1;\n            }\n            i(m, \"isAnchorOfCurrentUrl\");\n            var b = document.querySelectorAll(\"html\");\n            let d = i({\n                \"z.useEffect.d\": ()=>b.forEach({\n                        \"z.useEffect.d\": (r)=>r.classList.remove(\"nprogress-busy\")\n                    }[\"z.useEffect.d\"])\n            }[\"z.useEffect.d\"], \"removeNProgressClass\");\n            function P(r) {\n                for(; r && r.tagName.toLowerCase() !== \"a\";)r = r.parentElement;\n                return r;\n            }\n            i(P, \"findClosestAnchor\");\n            function R(r) {\n                try {\n                    let g = r.target, n = P(g), c = n == null ? void 0 : n.href;\n                    if (c) {\n                        let w = window.location.href, x = n.target !== \"\", J = [\n                            \"tel:\",\n                            \"mailto:\",\n                            \"sms:\",\n                            \"blob:\",\n                            \"download:\"\n                        ].some({\n                            \"z.useEffect.R.J\": (X)=>c.startsWith(X)\n                        }[\"z.useEffect.R.J\"]);\n                        if (!I(window.location.href, n.href)) return;\n                        let M = m(w, c) || q(window.location.href, n.href);\n                        if (!K && M) return;\n                        c === w || x || J || M || r.ctrlKey || r.metaKey || r.shiftKey || r.altKey || !f(n.href).startsWith(\"http\") ? (a.start(), a.done(), d()) : a.start();\n                    }\n                } catch (g) {\n                    a.start(), a.done();\n                }\n            }\n            i(R, \"handleClick\"), ({\n                \"z.useEffect\": (r)=>{\n                    let g = r.pushState;\n                    r.pushState = ({\n                        \"z.useEffect\": (...n)=>(a.done(), d(), g.apply(r, n))\n                    })[\"z.useEffect\"];\n                }\n            })[\"z.useEffect\"](window.history), ({\n                \"z.useEffect\": (r)=>{\n                    let g = r.replaceState;\n                    r.replaceState = ({\n                        \"z.useEffect\": (...n)=>(a.done(), d(), g.apply(r, n))\n                    })[\"z.useEffect\"];\n                }\n            })[\"z.useEffect\"](window.history);\n            function U() {\n                a.done(), d();\n            }\n            i(U, \"handlePageHide\");\n            function $() {\n                a.done();\n            }\n            return i($, \"handleBackAndForth\"), window.addEventListener(\"popstate\", $), document.addEventListener(\"click\", R), window.addEventListener(\"pagehide\", U), ({\n                \"z.useEffect\": ()=>{\n                    document.removeEventListener(\"click\", R), window.removeEventListener(\"pagehide\", U), window.removeEventListener(\"popstate\", $);\n                }\n            })[\"z.useEffect\"];\n        }\n    }[\"z.useEffect\"], []), F;\n}, \"NextTopLoader\"), ee = z;\nz.propTypes = {\n    color: t.string,\n    height: t.number,\n    showSpinner: t.bool,\n    crawl: t.bool,\n    crawlSpeed: t.number,\n    initialPosition: t.number,\n    easing: t.string,\n    speed: t.number,\n    template: t.string,\n    shadow: t.oneOfType([\n        t.string,\n        t.bool\n    ]),\n    zIndex: t.number,\n    showAtBottom: t.bool\n};\n0 && (0); /**\n *\n * NextTopLoader\n * @license MIT\n * @param {NextTopLoaderProps} props The properties to configure NextTopLoader\n * @returns {React.JSX.Element}\n *\n */  //# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/nextjs-toploader/dist/index.js\n");

/***/ })

};
;