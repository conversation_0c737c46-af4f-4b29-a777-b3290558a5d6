# Generated manually for adding tooth fields

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('dentistry', '0006_dentalsvgconfiguration_dentaltreatmenttemplate_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='tooth',
            name='name',
            field=models.CharField(
                help_text='Name of the tooth (e.g., Upper Right Central Incisor)',
                max_length=100,
                verbose_name='Tooth name',
                default=''
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='tooth',
            name='tooth_type',
            field=models.CharField(
                choices=[
                    ('incisor', 'Incisor'),
                    ('canine', 'Canine'),
                    ('premolar', 'Premolar'),
                    ('molar', 'Molar'),
                    ('wisdom', 'Wisdom Tooth'),
                    ('other', 'Other'),
                ],
                max_length=20,
                verbose_name='Tooth type',
                default='other'
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='tooth',
            name='quadrant',
            field=models.CharField(
                choices=[
                    ('upper_right', 'Upper Right'),
                    ('upper_left', 'Upper Left'),
                    ('lower_left', 'Lower Left'),
                    ('lower_right', 'Lower Right'),
                ],
                max_length=20,
                verbose_name='Quadrant',
                default='upper_right'
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='tooth',
            name='position',
            field=models.PositiveSmallIntegerField(
                help_text='Position of the tooth within its quadrant (1-8)',
                verbose_name='Position in quadrant',
                default=1
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='tooth',
            name='is_permanent',
            field=models.BooleanField(
                default=True,
                help_text='True for permanent teeth, False for primary teeth',
                verbose_name='Is permanent tooth'
            ),
        ),
        migrations.AddField(
            model_name='tooth',
            name='description',
            field=models.TextField(
                blank=True,
                help_text='Additional description or notes about the tooth',
                null=True,
                verbose_name='Description'
            ),
        ),
    ]
