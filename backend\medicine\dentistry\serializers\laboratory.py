"""
Laboratory serializers for the dentistry application.
"""
from rest_framework import serializers
from dentistry.models import DentalLaboratory, LabWorkOrder
from dentistry.serializers.patient import DentistryPatientSerializer

class DentalLaboratorySerializer(serializers.ModelSerializer):
    """
    Serializer for the DentalLaboratory model.
    """
    class Meta:
        model = DentalLaboratory
        fields = [
            'id', 'name', 'address', 'phone', 'email', 'website',
            'contact_person', 'specializations', 'is_preferred',
            'contract_start_date', 'contract_end_date', 'pricing_agreement',
            'average_turnaround_days', 'quality_rating', 'notes',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

class LabWorkOrderSerializer(serializers.ModelSerializer):
    """
    Serializer for the LabWorkOrder model.
    """
    laboratory_details = serializers.CharField(source='laboratory.get_full_name', read_only=True)
    patient_details = serializers.CharField(source='patient.get_full_name', read_only=True)
    doctor_name = serializers.CharField(source='doctor.get_full_name', read_only=True)
    work_type_display = serializers.ReadOnlyField(source='get_work_type_display')
    material_display = serializers.ReadOnlyField(source='get_material_display')
    status_display = serializers.ReadOnlyField(source='get_status_display')

    class Meta:
        model = LabWorkOrder
        fields = [
            'id', 'laboratory', 'laboratory_details', 'patient', 'patient_details',
            'doctor', 'doctor_name', 'order_number', 'work_type', 'work_type_display',
            'description', 'tooth_numbers', 'material', 'material_display', 'shade',
            'date_sent', 'requested_return_date', 'actual_return_date',
            'status', 'status_display', 'cost', 'invoice_number',
            'is_invoiced', 'is_paid', 'attachments', 'special_instructions',
            'lab_notes', 'internal_notes', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']
