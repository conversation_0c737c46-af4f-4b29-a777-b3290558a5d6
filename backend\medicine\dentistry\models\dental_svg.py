"""
Modèles pour la gestion des données SVG dentaires avec restrictions d'âge.
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from datetime import date, timedelta
import json
import uuid

User = get_user_model()


class DentalSvgData(models.Model):
    """
    Modèle pour stocker les données SVG dentaires des patients avec gestion d'âge.
    """
    AGE_RESTRICTION_CHOICES = [
        ('none', _('Aucune restriction')),
        ('under_6', _('Moins de 6 ans')),
        ('under_7_5', _('Moins de 7 ans et demi')),
        ('under_12', _('Moins de 12 ans')),
        ('under_13_5', _('Moins de 13 ans et demi')),
    ]

    DENTAL_VIEW_LEVEL_CHOICES = [
        ('full', _('Vue complète')),
        ('partial', _('Vue partielle')),
        ('limited', _('Vue limitée')),
        ('minimal', _('Vue minimale')),
        ('hidden', _('Masqué')),
    ]

    id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False
    )
    
    patient = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name="dental_svg_data",
        verbose_name=_("Patient")
    )
    
    # SVG Data Storage
    dental_svg_upper = models.TextField(
        verbose_name=_("SVG Mâchoire Supérieure"),
        help_text=_("Données SVG complètes pour la mâchoire supérieure"),
        blank=True
    )
    
    dental_svg_lower = models.TextField(
        verbose_name=_("SVG Mâchoire Inférieure"),
        help_text=_("Données SVG complètes pour la mâchoire inférieure"),
        blank=True
    )
    
    # Age-based restrictions
    current_age_restriction = models.CharField(
        max_length=20,
        choices=AGE_RESTRICTION_CHOICES,
        default='none',
        verbose_name=_("Restriction d'âge actuelle")
    )
    
    dental_view_level = models.CharField(
        max_length=20,
        choices=DENTAL_VIEW_LEVEL_CHOICES,
        default='full',
        verbose_name=_("Niveau de vue dentaire")
    )
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    last_modification_date = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_("Dernière modification dentaire")
    )
    
    class Meta:
        verbose_name = _("Données SVG Dentaires")
        verbose_name_plural = _("Données SVG Dentaires")
        ordering = ['-updated_at']

    def __str__(self):
        return f"Données dentaires SVG - {self.patient.get_full_name()}"

    @property
    def patient_age(self):
        """Calcule l'âge du patient en années avec décimales."""
        if not self.patient.date_of_birth:
            return None
        
        today = date.today()
        birth_date = self.patient.date_of_birth
        
        # Calcul précis avec décimales
        age_years = today.year - birth_date.year
        age_days = (today - date(today.year, birth_date.month, birth_date.day)).days
        
        if age_days < 0:
            age_years -= 1
            age_days += 365
            
        return age_years + (age_days / 365.25)

    def update_age_restriction(self):
        """Met à jour automatiquement les restrictions d'âge."""
        age = self.patient_age
        
        if age is None:
            self.current_age_restriction = 'none'
            self.dental_view_level = 'full'
        elif age < 6:
            self.current_age_restriction = 'under_6'
            self.dental_view_level = 'hidden'
        elif age < 7.5:
            self.current_age_restriction = 'under_7_5'
            self.dental_view_level = 'minimal'
        elif age < 12:
            self.current_age_restriction = 'under_12'
            self.dental_view_level = 'limited'
        elif age < 13.5:
            self.current_age_restriction = 'under_13_5'
            self.dental_view_level = 'partial'
        else:
            self.current_age_restriction = 'none'
            self.dental_view_level = 'full'
            
        self.save(update_fields=['current_age_restriction', 'dental_view_level'])

    def can_view_dental_data(self):
        """Détermine si les données dentaires peuvent être affichées."""
        return self.dental_view_level != 'hidden'

    def get_allowed_teeth_count(self):
        """Retourne le nombre de dents autorisées selon l'âge."""
        if self.dental_view_level == 'hidden':
            return 0
        elif self.dental_view_level == 'minimal':
            return 8  # Dents de devant seulement
        elif self.dental_view_level == 'limited':
            return 16  # Dents de lait + quelques permanentes
        elif self.dental_view_level == 'partial':
            return 24  # Presque toutes les dents
        else:
            return 32  # Toutes les dents adultes

    def get_filtered_svg_data(self):
        """Retourne les données SVG filtrées selon l'âge."""
        if not self.can_view_dental_data():
            return {
                'dental_svg_upper': '',
                'dental_svg_lower': '',
                'message': 'Données dentaires masquées pour cet âge'
            }
        
        # Pour l'instant, retourne les données complètes
        # TODO: Implémenter le filtrage SVG selon le niveau
        return {
            'dental_svg_upper': self.dental_svg_upper,
            'dental_svg_lower': self.dental_svg_lower,
            'view_level': self.dental_view_level,
            'allowed_teeth': self.get_allowed_teeth_count()
        }


class DentalModification(models.Model):
    """
    Modèle pour stocker les modifications dentaires individuelles.
    """
    MODIFICATION_TYPE_CHOICES = [
        ('color', _('Coloration')),
        ('replacement', _('Remplacement')),
        ('addition', _('Ajout')),
        ('removal', _('Suppression')),
        ('restoration', _('Restauration')),
    ]

    SPECIALTY_CHOICES = [
        ('therapeutic', _('Dentisterie Thérapeutique')),
        ('esthetic', _('Dentisterie Esthétique')),
        ('prosthetic', _('Prosthodontie')),
        ('orthodontic', _('Orthodontie')),
        ('surgery', _('Chirurgie')),
    ]

    id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False
    )
    
    dental_svg_data = models.ForeignKey(
        DentalSvgData,
        on_delete=models.CASCADE,
        related_name='modifications',
        verbose_name=_("Données SVG Dentaires")
    )
    
    # Tooth identification
    tooth_number = models.IntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(32)],
        verbose_name=_("Numéro de dent")
    )
    
    path_id = models.CharField(
        max_length=50,
        verbose_name=_("ID du chemin SVG"),
        help_text=_("Identifiant de la partie de la dent modifiée")
    )
    
    # Modification details
    modification_type = models.CharField(
        max_length=20,
        choices=MODIFICATION_TYPE_CHOICES,
        verbose_name=_("Type de modification")
    )
    
    value = models.TextField(
        verbose_name=_("Valeur"),
        help_text=_("Couleur hex, SVG de remplacement, ou autre valeur")
    )
    
    specialty = models.CharField(
        max_length=20,
        choices=SPECIALTY_CHOICES,
        verbose_name=_("Spécialité")
    )
    
    applied_by_button = models.CharField(
        max_length=100,
        verbose_name=_("Appliqué par le bouton"),
        help_text=_("Nom du bouton qui a appliqué cette modification")
    )
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(
        default=True,
        verbose_name=_("Actif")
    )
    
    class Meta:
        verbose_name = _("Modification Dentaire")
        verbose_name_plural = _("Modifications Dentaires")
        ordering = ['-created_at']
        unique_together = ['dental_svg_data', 'tooth_number', 'path_id', 'modification_type']

    def __str__(self):
        return f"Modification {self.modification_type} - Dent {self.tooth_number} - {self.specialty}"

    def save(self, *args, **kwargs):
        """Met à jour la date de dernière modification du SVG parent."""
        super().save(*args, **kwargs)
        self.dental_svg_data.last_modification_date = timezone.now()
        self.dental_svg_data.save(update_fields=['last_modification_date'])
