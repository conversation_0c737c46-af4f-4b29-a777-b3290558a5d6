from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.utils import timezone
from .models import SubscriptionPackage, DoctorSubscription, Coupon, SubscriptionTransaction
from .serializers import (
    SubscriptionPackageSerializer, 
    DoctorSubscriptionSerializer, 
    CouponSerializer,
    ApplyCouponSerializer,
    SubscriptionTransactionSerializer
)

class SubscriptionPackageViewSet(viewsets.ReadOnlyModelViewSet):
    """API endpoint for viewing subscription packages"""
    queryset = SubscriptionPackage.objects.filter(is_active=True)
    serializer_class = SubscriptionPackageSerializer
    permission_classes = [permissions.AllowAny]  # Allow anyone to view packages

class DoctorSubscriptionViewSet(viewsets.ModelViewSet):
    """API endpoint for managing doctor subscriptions"""
    serializer_class = DoctorSubscriptionSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        # Doctors can only see their own subscriptions
        if self.request.user.is_doctor:
            return DoctorSubscription.objects.filter(doctor=self.request.user)
        # <PERSON><PERSON> can see all subscriptions
        elif self.request.user.is_staff:
            return DoctorSubscription.objects.all()
        # Others can't see any subscriptions
        return DoctorSubscription.objects.none()
    
    @action(detail=False, methods=['get'])
    def current(self, request):
        """Get the current active subscription for the logged-in doctor"""
        if not request.user.is_doctor:
            return Response({"detail": "Only doctors can have subscriptions"}, status=status.HTTP_403_FORBIDDEN)
        
        subscription = DoctorSubscription.objects.filter(
            doctor=request.user,
            status='active',
            end_date__gt=timezone.now()
        ).first()
        
        if not subscription:
            return Response({"detail": "No active subscription found"}, status=status.HTTP_404_NOT_FOUND)
        
        serializer = self.get_serializer(subscription)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def cancel(self, request, pk=None):
        """Cancel a subscription"""
        subscription = self.get_object()
        
        # Only the doctor who owns the subscription or an admin can cancel it
        if subscription.doctor != request.user and not request.user.is_staff:
            return Response({"detail": "You do not have permission to cancel this subscription"}, 
                           status=status.HTTP_403_FORBIDDEN)
        
        subscription.status = 'cancelled'
        subscription.save()
        
        serializer = self.get_serializer(subscription)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def renew(self, request, pk=None):
        """Renew a subscription"""
        subscription = self.get_object()
        
        # Only the doctor who owns the subscription or an admin can renew it
        if subscription.doctor != request.user and not request.user.is_staff:
            return Response({"detail": "You do not have permission to renew this subscription"}, 
                           status=status.HTTP_403_FORBIDDEN)
        
        # Calculate new dates
        start_date = timezone.now()
        if subscription.billing_cycle == 'annual':
            end_date = start_date + timezone.timedelta(days=365)
        else:  # monthly
            end_date = start_date + timezone.timedelta(days=30)
        
        # Create a new subscription
        new_subscription = DoctorSubscription.objects.create(
            doctor=subscription.doctor,
            package=subscription.package,
            status='active',
            billing_cycle=subscription.billing_cycle,
            start_date=start_date,
            end_date=end_date,
            current_assistant_count=subscription.current_assistant_count,
            current_user_count=subscription.current_user_count,
            current_specialty_count=subscription.current_specialty_count
        )
        
        serializer = self.get_serializer(new_subscription)
        return Response(serializer.data)

class CouponViewSet(viewsets.ReadOnlyModelViewSet):
    """API endpoint for validating coupons"""
    queryset = Coupon.objects.filter(is_active=True)
    serializer_class = CouponSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    @action(detail=False, methods=['post'])
    def validate(self, request):
        """Validate a coupon code"""
        serializer = ApplyCouponSerializer(data=request.data)
        if serializer.is_valid():
            code = serializer.validated_data['code']
            package_id = serializer.validated_data.get('package_id')
            
            try:
                coupon = Coupon.objects.get(code=code, is_active=True)
                
                # Check if coupon is valid
                if not coupon.is_valid():
                    return Response({"detail": "This coupon has expired or reached its usage limit"}, 
                                   status=status.HTTP_400_BAD_REQUEST)
                
                # Check if coupon is applicable to the package
                if package_id and coupon.applicable_packages.exists():
                    if not coupon.applicable_packages.filter(id=package_id).exists():
                        return Response({"detail": "This coupon is not applicable to the selected package"}, 
                                       status=status.HTTP_400_BAD_REQUEST)
                
                return Response({
                    "coupon": CouponSerializer(coupon).data,
                    "valid": True
                })
                
            except Coupon.DoesNotExist:
                return Response({"detail": "Invalid coupon code"}, status=status.HTTP_404_NOT_FOUND)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class SubscriptionTransactionViewSet(viewsets.ModelViewSet):
    """API endpoint for managing subscription transactions"""
    serializer_class = SubscriptionTransactionSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        # Doctors can only see transactions for their own subscriptions
        if self.request.user.is_doctor:
            return SubscriptionTransaction.objects.filter(subscription__doctor=self.request.user)
        # Admins can see all transactions
        elif self.request.user.is_staff:
            return SubscriptionTransaction.objects.all()
        # Others can't see any transactions
        return SubscriptionTransaction.objects.none()
