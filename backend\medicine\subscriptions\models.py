from django.db import models
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from django.conf import settings

class SubscriptionPackage(models.Model):
    """Model for defining subscription packages"""
    name = models.CharField(max_length=100, help_text=_("Package name"))
    description = models.TextField(blank=True, null=True, help_text=_("Package description"))

    # Limits
    max_assistants = models.PositiveIntegerField(default=1, help_text=_("Maximum number of assistants allowed"))
    max_users = models.PositiveIntegerField(default=2, help_text=_("Maximum number of users allowed"))
    max_specialties = models.PositiveIntegerField(default=1, help_text=_("Maximum number of specialties allowed"))

    # Pricing
    price_monthly = models.DecimalField(max_digits=10, decimal_places=2, help_text=_("Monthly price"))
    price_yearly = models.DecimalField(max_digits=10, decimal_places=2, help_text=_("Yearly price"))

    # Features
    features = models.J<PERSON><PERSON>ield(default=list, help_text=_("List of features included in this package"))

    # Status
    is_active = models.BooleanField(default=True, help_text=_("Whether this package is available for purchase"))

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Subscription Package"
        verbose_name_plural = "Subscription Packages"

    def __str__(self):
        return self.name

class DoctorSubscription(models.Model):
    """Model for tracking doctor subscriptions"""
    STATUS_CHOICES = (
        ('active', 'Active'),
        ('expired', 'Expired'),
        ('cancelled', 'Cancelled'),
        ('pending', 'Pending'),
    )

    BILLING_CYCLE_CHOICES = (
        ('monthly', 'Monthly'),
        ('annual', 'Annual'),
    )

    doctor = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='subscriptions',
                              limit_choices_to={'user_type': 'doctor'})
    package = models.ForeignKey(SubscriptionPackage, on_delete=models.PROTECT, related_name='doctor_subscriptions')

    # Subscription details
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active')
    billing_cycle = models.CharField(max_length=20, choices=BILLING_CYCLE_CHOICES, default='monthly')

    # Dates
    start_date = models.DateTimeField(default=timezone.now)
    end_date = models.DateTimeField()

    # Usage tracking
    current_assistant_count = models.PositiveIntegerField(default=0)
    current_user_count = models.PositiveIntegerField(default=0)
    current_specialty_count = models.PositiveIntegerField(default=0)

    # Payment tracking
    last_payment_date = models.DateTimeField(null=True, blank=True)
    next_payment_date = models.DateTimeField(null=True, blank=True)

    # Additional data
    metadata = models.JSONField(default=dict, blank=True, null=True, help_text=_("Additional metadata for the subscription"))

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Doctor Subscription"
        verbose_name_plural = "Doctor Subscriptions"

    def __str__(self):
        return f"{self.doctor.email} - {self.package.name} ({self.get_status_display()})"

    def is_active(self):
        return self.status == 'active' and self.end_date is not None and self.end_date > timezone.now()

    def days_remaining(self):
        if not self.is_active() or self.end_date is None:
            return 0
        delta = self.end_date - timezone.now()
        return max(0, delta.days)

class Coupon(models.Model):
    """Model for discount coupons"""
    DISCOUNT_TYPE_CHOICES = (
        ('percentage', 'Percentage'),
        ('fixed', 'Fixed Amount'),
    )

    code = models.CharField(max_length=50, unique=True, help_text=_("Coupon code"))
    description = models.TextField(blank=True, null=True, help_text=_("Coupon description"))

    # Discount details
    discount_type = models.CharField(max_length=20, choices=DISCOUNT_TYPE_CHOICES, default='percentage')
    discount_value = models.DecimalField(max_digits=10, decimal_places=2, help_text=_("Discount value"))

    # Validity
    valid_from = models.DateTimeField(default=timezone.now)
    valid_until = models.DateTimeField(null=True, blank=True)
    max_uses = models.PositiveIntegerField(null=True, blank=True, help_text=_("Maximum number of times this coupon can be used"))
    current_uses = models.PositiveIntegerField(default=0)

    # Restrictions
    applicable_packages = models.ManyToManyField(SubscriptionPackage, blank=True, related_name='applicable_coupons')
    minimum_purchase = models.DecimalField(max_digits=10, decimal_places=2, default=0, help_text=_("Minimum purchase amount"))

    # Status
    is_active = models.BooleanField(default=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Coupon"
        verbose_name_plural = "Coupons"

    def __str__(self):
        return self.code

    def is_valid(self):
        now = timezone.now()
        if not self.is_active:
            return False
        if self.valid_until and now > self.valid_until:
            return False
        if self.max_uses and self.current_uses >= self.max_uses:
            return False
        return True

class SubscriptionTransaction(models.Model):
    """Model for tracking subscription transactions"""
    STATUS_CHOICES = (
        ('pending', 'Pending'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('refunded', 'Refunded'),
    )

    subscription = models.ForeignKey(DoctorSubscription, on_delete=models.CASCADE, related_name='transactions')
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')

    # Payment details
    payment_method = models.CharField(max_length=50, blank=True, null=True)
    transaction_id = models.CharField(max_length=100, blank=True, null=True)

    # Coupon usage
    coupon = models.ForeignKey(Coupon, on_delete=models.SET_NULL, null=True, blank=True, related_name='transactions')
    discount_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Subscription Transaction"
        verbose_name_plural = "Subscription Transactions"

    def __str__(self):
        return f"{self.subscription.doctor.email} - {self.amount} ({self.get_status_display()})"
