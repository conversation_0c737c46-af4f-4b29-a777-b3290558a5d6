"""
Serializers pour les données SVG dentaires avec gestion des restrictions d'âge.
"""
from rest_framework import serializers
from django.contrib.auth import get_user_model
from dentistry.models.dental_svg import DentalSvgData, DentalModification

User = get_user_model()


class DentalModificationSerializer(serializers.ModelSerializer):
    """
    Serializer pour les modifications dentaires individuelles.
    """
    
    class Meta:
        model = DentalModification
        fields = [
            'id',
            'tooth_number',
            'path_id',
            'modification_type',
            'value',
            'specialty',
            'applied_by_button',
            'created_at',
            'updated_at',
            'is_active'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def validate_tooth_number(self, value):
        """Valide que le numéro de dent est dans la plage correcte."""
        if not 1 <= value <= 32:
            raise serializers.ValidationError(
                "Le numéro de dent doit être entre 1 et 32."
            )
        return value

    def validate_value(self, value):
        """Valide la valeur selon le type de modification."""
        modification_type = self.initial_data.get('modification_type')
        
        if modification_type == 'color':
            # Valider que c'est une couleur hex valide
            if not value.startswith('#') or len(value) != 7:
                raise serializers.ValidationError(
                    "La couleur doit être au format hex (#RRGGBB)."
                )
        
        return value


class DentalSvgDataSerializer(serializers.ModelSerializer):
    """
    Serializer pour les données SVG dentaires avec gestion d'âge.
    """
    modifications = DentalModificationSerializer(many=True, read_only=True)
    patient_age = serializers.ReadOnlyField()
    can_view_dental_data = serializers.ReadOnlyField()
    allowed_teeth_count = serializers.ReadOnlyField(source='get_allowed_teeth_count')
    filtered_svg_data = serializers.ReadOnlyField(source='get_filtered_svg_data')
    
    # Informations patient (lecture seule)
    patient_name = serializers.CharField(source='patient.get_full_name', read_only=True)
    patient_birth_date = serializers.DateField(source='patient.date_of_birth', read_only=True)
    
    class Meta:
        model = DentalSvgData
        fields = [
            'id',
            'patient',
            'patient_name',
            'patient_birth_date',
            'patient_age',
            'dental_svg_upper',
            'dental_svg_lower',
            'current_age_restriction',
            'dental_view_level',
            'can_view_dental_data',
            'allowed_teeth_count',
            'filtered_svg_data',
            'created_at',
            'updated_at',
            'last_modification_date',
            'modifications'
        ]
        read_only_fields = [
            'id', 
            'current_age_restriction', 
            'dental_view_level',
            'created_at', 
            'updated_at',
            'last_modification_date'
        ]

    def create(self, validated_data):
        """Crée une nouvelle instance avec mise à jour automatique des restrictions d'âge."""
        instance = super().create(validated_data)
        instance.update_age_restriction()
        return instance

    def update(self, instance, validated_data):
        """Met à jour l'instance avec recalcul des restrictions d'âge."""
        instance = super().update(instance, validated_data)
        instance.update_age_restriction()
        return instance


class DentalSvgDataCreateSerializer(serializers.ModelSerializer):
    """
    Serializer simplifié pour la création de données SVG dentaires.
    """
    
    class Meta:
        model = DentalSvgData
        fields = [
            'patient',
            'dental_svg_upper',
            'dental_svg_lower'
        ]

    def create(self, validated_data):
        """Crée une nouvelle instance avec initialisation automatique."""
        instance = DentalSvgData.objects.create(**validated_data)
        instance.update_age_restriction()
        return instance


class DentalModificationCreateSerializer(serializers.ModelSerializer):
    """
    Serializer pour créer de nouvelles modifications dentaires.
    """
    patient_id = serializers.UUIDField(write_only=True)
    
    class Meta:
        model = DentalModification
        fields = [
            'patient_id',
            'tooth_number',
            'path_id',
            'modification_type',
            'value',
            'specialty',
            'applied_by_button'
        ]

    def create(self, validated_data):
        """Crée une modification en associant automatiquement les données SVG du patient."""
        patient_id = validated_data.pop('patient_id')
        
        # Récupérer ou créer les données SVG du patient
        dental_svg_data, created = DentalSvgData.objects.get_or_create(
            patient_id=patient_id,
            defaults={
                'dental_svg_upper': '',
                'dental_svg_lower': ''
            }
        )
        
        if created:
            dental_svg_data.update_age_restriction()
        
        # Vérifier si le patient peut avoir des modifications dentaires
        if not dental_svg_data.can_view_dental_data():
            raise serializers.ValidationError(
                "Les modifications dentaires ne sont pas autorisées pour cet âge."
            )
        
        validated_data['dental_svg_data'] = dental_svg_data
        return super().create(validated_data)


class DentalAgeRestrictionSerializer(serializers.Serializer):
    """
    Serializer pour vérifier les restrictions d'âge sans créer d'objet.
    """
    patient_id = serializers.UUIDField()
    age = serializers.FloatField(read_only=True)
    age_restriction = serializers.CharField(read_only=True)
    dental_view_level = serializers.CharField(read_only=True)
    can_view_dental_data = serializers.BooleanField(read_only=True)
    allowed_teeth_count = serializers.IntegerField(read_only=True)
    restriction_message = serializers.CharField(read_only=True)

    def validate_patient_id(self, value):
        """Valide que le patient existe."""
        try:
            User.objects.get(id=value)
        except User.DoesNotExist:
            raise serializers.ValidationError("Patient non trouvé.")
        return value


class DentalSvgBulkUpdateSerializer(serializers.Serializer):
    """
    Serializer pour les mises à jour en lot des données SVG.
    """
    patient_id = serializers.UUIDField()
    dental_svg_upper = serializers.CharField(required=False, allow_blank=True)
    dental_svg_lower = serializers.CharField(required=False, allow_blank=True)
    modifications = DentalModificationCreateSerializer(many=True, required=False)

    def validate_patient_id(self, value):
        """Valide que le patient existe."""
        try:
            User.objects.get(id=value)
        except User.DoesNotExist:
            raise serializers.ValidationError("Patient non trouvé.")
        return value

    def create(self, validated_data):
        """Traite la mise à jour en lot."""
        patient_id = validated_data['patient_id']
        modifications_data = validated_data.pop('modifications', [])
        
        # Récupérer ou créer les données SVG
        dental_svg_data, created = DentalSvgData.objects.get_or_create(
            patient_id=patient_id,
            defaults={
                'dental_svg_upper': validated_data.get('dental_svg_upper', ''),
                'dental_svg_lower': validated_data.get('dental_svg_lower', '')
            }
        )
        
        if not created:
            # Mettre à jour les données SVG existantes
            for field in ['dental_svg_upper', 'dental_svg_lower']:
                if field in validated_data:
                    setattr(dental_svg_data, field, validated_data[field])
            dental_svg_data.save()
        
        dental_svg_data.update_age_restriction()
        
        # Vérifier les restrictions d'âge
        if not dental_svg_data.can_view_dental_data():
            raise serializers.ValidationError(
                "Les modifications dentaires ne sont pas autorisées pour cet âge."
            )
        
        # Créer les modifications
        created_modifications = []
        for mod_data in modifications_data:
            mod_data['dental_svg_data'] = dental_svg_data
            modification = DentalModification.objects.create(**mod_data)
            created_modifications.append(modification)
        
        return {
            'dental_svg_data': dental_svg_data,
            'modifications': created_modifications
        }
