from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0002_doctorlicense'),
    ]

    operations = [
        migrations.CreateModel(
            name='Specialty',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(max_length=100, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('slug', models.SlugField(max_length=100, unique=True)),
                ('icon', models.CharField(blank=True, help_text="Icon class (e.g., 'fa-heart' for FontAwesome)", max_length=50, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Specialty',
                'verbose_name_plural': 'Specialties',
                'ordering': ['name'],
            },
        ),
        migrations.AddField(
            model_name='user',
            name='bio',
            field=models.TextField(blank=True, help_text='Doctor biography/description', null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='certifications',
            field=models.TextField(blank=True, help_text='Professional certifications', null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='education',
            field=models.TextField(blank=True, help_text='Educational background', null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='specialties',
            field=models.ManyToManyField(blank=True, related_name='doctors', to='users.specialty'),
        ),
        migrations.AddField(
            model_name='user',
            name='years_of_experience',
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='user',
            name='specialization',
            field=models.CharField(blank=True, help_text='Legacy field - use specialties instead', max_length=100, null=True),
        ),
    ]
