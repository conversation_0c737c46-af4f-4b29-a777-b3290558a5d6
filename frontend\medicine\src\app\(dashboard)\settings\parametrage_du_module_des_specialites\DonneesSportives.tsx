"use client";

import React, { useState } from 'react';
import {
  Button,
  TextInput,
  Group,
  ActionIcon,
  Modal,
  Stack,
  Text,
  Card,
  Textarea,
  Select,
  Tabs,
  NumberInput,
  Switch,
  Divider
} from '@mantine/core';
import {
  IconSearch,
  IconPlus,
  IconSportBillard,
  IconX,
  IconUsers
} from '@tabler/icons-react';
import { useDisclosure } from '@mantine/hooks';
import { useForm } from '@mantine/form';
import { DataTable } from 'mantine-datatable';

// Types
interface Sport {
  id: string;
  valeur: string;
  description: string;
}

interface TypeTerrain {
  id: string;
  valeur: string;
  description: string;
}

interface OrganisationSportive {
  id: string;
  nom: string;
  abbreviation: string;
  contact: string;
  telephone: string;
  email: string;
  adresse: string;
  pays: string;
  ville: string;
  equipeNational: boolean;
}

interface Ville {
  id: string;
  nomComplet: string;
  nomCourt: string;
  pays: string;
}

// Types pour les formulaires
interface SportFormData {
  valeur: string;
  description: string;
}

interface TypeTerrainFormData {
  valeur: string;
  description: string;
}

interface OrganisationFormData {
  nom: string;
  abbreviation: string;
  contact: string;
  telephone: string;
  email: string;
  adresse: string;
  pays: string;
  ville: string;
  equipeNational: boolean;
}

interface VilleFormData {
  nomComplet: string;
  nomCourt: string;
  pays: string;
}

const DonneesSportives = () => {
  // États pour les onglets et modales
  const [activeTab, setActiveTab] = useState('general');
  const [sportModalOpened, { open: openSportModal, close: closeSportModal }] = useDisclosure(false);
  const [typeTerrainModalOpened, { open: openTypeTerrainModal, close: closeTypeTerrainModal }] = useDisclosure(false);
  const [organisationModalOpened, { open: openOrganisationModal, close: closeOrganisationModal }] = useDisclosure(false);
  const [villeModalOpened, { open: openVilleModal, close: closeVilleModal }] = useDisclosure(false);

  // États pour les données
  const [searchQuery, setSearchQuery] = useState('');
  const [ageMajoriteCivile, setAgeMajoriteCivile] = useState(18);
  const [sportSelectionne, setSportSelectionne] = useState('');
  const [typeTerrainSelectionne, setTypeTerrainSelectionne] = useState('');
  const [sports, setSports] = useState<Sport[]>([]);
  const [typesTerrains, setTypesTerrains] = useState<TypeTerrain[]>([]);
  const [organisations, setOrganisations] = useState<OrganisationSportive[]>([]);
  const [villes, setVilles] = useState<Ville[]>([]);

  // Formulaires
  const sportForm = useForm<SportFormData>({
    initialValues: {
      valeur: '',
      description: ''
    },
    validate: {
      valeur: (value) => (!value ? 'La valeur est requise' : null)
    }
  });

  const typeTerrainForm = useForm<TypeTerrainFormData>({
    initialValues: {
      valeur: '',
      description: ''
    },
    validate: {
      valeur: (value) => (!value ? 'La valeur est requise' : null)
    }
  });

  const organisationForm = useForm<OrganisationFormData>({
    initialValues: {
      nom: '',
      abbreviation: '',
      contact: '',
      telephone: '',
      email: '',
      adresse: '',
      pays: 'MAROC',
      ville: '',
      equipeNational: false
    },
    validate: {
      nom: (value) => (!value ? 'Le nom est requis' : null)
    }
  });

  const villeForm = useForm<VilleFormData>({
    initialValues: {
      nomComplet: '',
      nomCourt: '',
      pays: 'MAROC'
    },
    validate: {
      nomComplet: (value) => (!value ? 'Le nom complet est requis' : null),
      pays: (value) => (!value ? 'Le pays est requis' : null)
    }
  });

  // Gestionnaires pour les onglets
  const handleTabChange = (value: string | null) => {
    if (value) setActiveTab(value);
  };

  // Gestionnaires de soumission
  const handleSportSubmit = (values: SportFormData) => {
    const newSport: Sport = {
      id: Date.now().toString(),
      ...values
    };
    setSports(prev => [...prev, newSport]);
    closeSportModal();
    sportForm.reset();
  };

  const handleTypeTerrainSubmit = (values: TypeTerrainFormData) => {
    const newTypeTerrain: TypeTerrain = {
      id: Date.now().toString(),
      ...values
    };
    setTypesTerrains(prev => [...prev, newTypeTerrain]);
    closeTypeTerrainModal();
    typeTerrainForm.reset();
  };

  const handleOrganisationSubmit = (values: OrganisationFormData) => {
    const newOrganisation: OrganisationSportive = {
      id: Date.now().toString(),
      ...values
    };
    setOrganisations(prev => [...prev, newOrganisation]);
    closeOrganisationModal();
    organisationForm.reset();
  };

  const handleVilleSubmit = (values: VilleFormData) => {
    const newVille: Ville = {
      id: Date.now().toString(),
      ...values
    };
    setVilles(prev => [...prev, newVille]);
    closeVilleModal();
    villeForm.reset();
  };

  // Colonnes pour la table des organisations
  const organisationColumns = [
    { accessor: 'nom', title: 'Nom' },
    { accessor: 'abbreviation', title: 'Abréviation' },
    { accessor: 'contact', title: 'Contact' },
    { accessor: 'pays', title: 'Pays' }
  ];

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <IconSportBillard size={32} className="text-blue-600" />
          <h1 className="text-2xl font-bold text-gray-800">Données sportives</h1>
        </div>
      </div>

      {/* Onglets principaux */}
      <Card shadow="sm" padding="lg" radius="md" withBorder>
        <Tabs value={activeTab} onChange={handleTabChange}>
          <Tabs.List>
            <Tabs.Tab value="general">Général</Tabs.Tab>
            <Tabs.Tab value="organisations-sportives">Organisations Sportives</Tabs.Tab>
          </Tabs.List>

          {/* Onglet Général */}
          <Tabs.Panel value="general" pt="md">
            <Stack gap="xl">
              {/* Section Valeurs par défaut */}
              <div>
                <Text size="lg" fw={600} mb="md" className="text-gray-800">
                  Valeurs par défaut
                </Text>

                <div className="grid grid-cols-2 gap-4">
                  <div className="flex gap-2 items-end">
                    <Select
                      label="Sport"
                      placeholder=""
                      data={sports.map(s => ({ value: s.id, label: s.valeur }))}
                      value={sportSelectionne}
                      onChange={(value) => setSportSelectionne(value || '')}
                      className="flex-1"
                    />
                    <ActionIcon
                      size="lg"
                      className="bg-blue-500 hover:bg-blue-600"
                      onClick={openSportModal}
                    >
                      <IconPlus size={16} />
                    </ActionIcon>
                  </div>

                  <div className="flex gap-2 items-end">
                    <Select
                      label="Type du terrain"
                      placeholder=""
                      data={typesTerrains.map(t => ({ value: t.id, label: t.valeur }))}
                      value={typeTerrainSelectionne}
                      onChange={(value) => setTypeTerrainSelectionne(value || '')}
                      className="flex-1"
                    />
                    <ActionIcon
                      size="lg"
                      className="bg-blue-500 hover:bg-blue-600"
                      onClick={openTypeTerrainModal}
                    >
                      <IconPlus size={16} />
                    </ActionIcon>
                  </div>
                </div>
              </div>

              {/* Section Paramètres */}
              <div>
                <Text size="lg" fw={600} mb="md" className="text-gray-800">
                  Paramètres
                </Text>

                <NumberInput
                  label="Âge de majorité civile"
                  value={ageMajoriteCivile}
                  onChange={(value) => setAgeMajoriteCivile(Number(value))}
                  min={1}
                  max={100}
                  className="w-64"
                />
              </div>
            </Stack>
          </Tabs.Panel>

          {/* Onglet Organisations Sportives */}
          <Tabs.Panel value="organisations-sportives" pt="md">
            <div className="flex items-center justify-between mb-4">
              <TextInput
                placeholder="Rechercher"
                leftSection={<IconSearch size={16} />}
                value={searchQuery}
                onChange={(event) => setSearchQuery(event.currentTarget.value)}
                className="w-96"
              />
              <Button
                leftSection={<IconPlus size={16} />}
                onClick={openOrganisationModal}
                className="bg-blue-500 hover:bg-blue-600"
              >
                Club/Equipe National
              </Button>
            </div>

            <DataTable
              columns={organisationColumns}
              records={organisations}
              noRecordsText="Aucun élément trouvé."
              minHeight={200}
            />
          </Tabs.Panel>
        </Tabs>
      </Card>

      {/* Modal Ajouter Sport */}
      <Modal
        opened={sportModalOpened}
        onClose={closeSportModal}
        title={
          <div className="flex items-center gap-2">
            <IconSportBillard size={20} className="text-white" />
            <Text fw={600} className="text-white">Ajouter Sport</Text>
          </div>
        }
        size="md"
        styles={{
          header: { backgroundColor: '#3b82f6', color: 'white' },
          title: { color: 'white' }
        }}
      >
        <form onSubmit={sportForm.onSubmit(handleSportSubmit)}>
          <Stack gap="md">
            <TextInput
              label="Valeur"
              placeholder=""
              required
              styles={{ label: { color: 'red' } }}
              {...sportForm.getInputProps('valeur')}
            />

            <Textarea
              label="Description"
              placeholder=""
              rows={3}
              {...sportForm.getInputProps('description')}
            />

            <Group justify="flex-end" mt="md">
              <Button variant="outline" color="gray" onClick={closeSportModal}>
                Enregistrer
              </Button>
              <Button type="submit" color="red">
                Annuler
              </Button>
            </Group>
          </Stack>
        </form>
      </Modal>

      {/* Modal Ajouter Type du terrain */}
      <Modal
        opened={typeTerrainModalOpened}
        onClose={closeTypeTerrainModal}
        title={
          <div className="flex items-center gap-2">
            <IconSportBillard size={20} className="text-white" />
            <Text fw={600} className="text-white">Ajouter Type du terrain</Text>
          </div>
        }
        size="md"
        styles={{
          header: { backgroundColor: '#3b82f6', color: 'white' },
          title: { color: 'white' }
        }}
      >
        <form onSubmit={typeTerrainForm.onSubmit(handleTypeTerrainSubmit)}>
          <Stack gap="md">
            <TextInput
              label="Valeur"
              placeholder=""
              required
              styles={{ label: { color: 'red' } }}
              {...typeTerrainForm.getInputProps('valeur')}
            />

            <Textarea
              label="Description"
              placeholder=""
              rows={3}
              {...typeTerrainForm.getInputProps('description')}
            />

            <Group justify="flex-end" mt="md">
              <Button variant="outline" color="gray" onClick={closeTypeTerrainModal}>
                Enregistrer
              </Button>
              <Button type="submit" color="red">
                Annuler
              </Button>
            </Group>
          </Stack>
        </form>
      </Modal>

      {/* Modal Club/Equipe National */}
      <Modal
        opened={organisationModalOpened}
        onClose={closeOrganisationModal}
        title={
          <div className="flex items-center gap-2">
            <IconUsers size={20} className="text-white" />
            <Text fw={600} className="text-white">Club/Equipe National</Text>
          </div>
        }
        size="lg"
        styles={{
          header: { backgroundColor: '#3b82f6', color: 'white' },
          title: { color: 'white' }
        }}
      >
        <form onSubmit={organisationForm.onSubmit(handleOrganisationSubmit)}>
          <Stack gap="md">
            <TextInput
              label="Nom"
              placeholder=""
              required
              styles={{ label: { color: 'red' } }}
              {...organisationForm.getInputProps('nom')}
            />

            <div className="flex items-center justify-between">
              <TextInput
                label="Abréviation"
                placeholder=""
                className="flex-1 mr-4"
                {...organisationForm.getInputProps('abbreviation')}
              />
              <div className="flex items-center gap-2 mt-6">
                <Switch
                  label="Équipe national"
                  {...organisationForm.getInputProps('equipeNational', { type: 'checkbox' })}
                />
              </div>
            </div>

            <Divider />

            <TextInput
              label="Contact"
              placeholder=""
              {...organisationForm.getInputProps('contact')}
            />

            <TextInput
              label="Téléphone"
              placeholder=""
              {...organisationForm.getInputProps('telephone')}
            />

            <TextInput
              label="Email"
              placeholder=""
              type="email"
              {...organisationForm.getInputProps('email')}
            />

            <Textarea
              label="Adresse"
              placeholder=""
              rows={3}
              {...organisationForm.getInputProps('adresse')}
            />

            <div className="grid grid-cols-2 gap-4">
              <Select
                label="Pays"
                placeholder=""
                data={[
                  { value: 'MAROC', label: 'MAROC' },
                  { value: 'FRANCE', label: 'FRANCE' },
                  { value: 'ESPAGNE', label: 'ESPAGNE' }
                ]}
                {...organisationForm.getInputProps('pays')}
              />

              <div className="flex gap-2">
                <Select
                  label="Ville"
                  placeholder=""
                  data={villes.map(v => ({ value: v.id, label: v.nomComplet }))}
                  className="flex-1"
                  {...organisationForm.getInputProps('ville')}
                />
                <ActionIcon
                  size="lg"
                  className="mt-6 bg-blue-500 hover:bg-blue-600"
                  onClick={openVilleModal}
                >
                  <IconPlus size={16} />
                </ActionIcon>
                <ActionIcon
                  size="lg"
                  className="mt-6 bg-red-500 hover:bg-red-600"
                >
                  <IconX size={16} />
                </ActionIcon>
              </div>
            </div>

            <Group justify="flex-end" mt="md">
              <Button variant="outline" color="gray" onClick={closeOrganisationModal}>
                Enregistrer
              </Button>
              <Button type="submit" color="red">
                Annuler
              </Button>
            </Group>
          </Stack>
        </form>
      </Modal>

      {/* Modal Ajouter Ville */}
      <Modal
        opened={villeModalOpened}
        onClose={closeVilleModal}
        title={
          <div className="flex items-center gap-2">
            <Text fw={600} className="text-blue-500">Ajouter Ville</Text>
          </div>
        }
        size="md"
        styles={{
          header: { backgroundColor: 'white', color: 'black' },
          title: { color: 'black' }
        }}
      >
        <form onSubmit={villeForm.onSubmit(handleVilleSubmit)}>
          <Stack gap="md">
            <TextInput
              label="Nom complet"
              placeholder=""
              required
              styles={{ label: { color: 'red' } }}
              {...villeForm.getInputProps('nomComplet')}
            />

            <TextInput
              label="Nom court"
              placeholder=""
              {...villeForm.getInputProps('nomCourt')}
            />

            <Select
              label="Pays"
              placeholder=""
              required
              styles={{ label: { color: 'red' } }}
              data={[
                { value: 'MAROC', label: 'MAROC' },
                { value: 'FRANCE', label: 'FRANCE' },
                { value: 'ESPAGNE', label: 'ESPAGNE' }
              ]}
              {...villeForm.getInputProps('pays')}
            />

            <Group justify="flex-end" mt="md">
              <Button variant="outline" color="gray" onClick={closeVilleModal}>
                Enregistrer
              </Button>
              <Button type="submit" color="red">
                Annuler
              </Button>
            </Group>
          </Stack>
        </form>
      </Modal>
    </div>
  );
};

export default DonneesSportives;
