"""
Base medical record models for the dentistry application.
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from dentistry.models.base import DentistryBaseModel

class DentistryBaseMedicalRecord(DentistryBaseModel):
    """
    Abstract base model for dental medical records.
    """
    # These fields will be defined in the concrete models
    # patient = models.ForeignKey(DentistryPatient, on_delete=models.CASCADE)
    # doctor = models.ForeignKey(DentistryDoctor, on_delete=models.CASCADE)

    date = models.DateField(
        verbose_name=_("Record Date")
    )

    chief_complaint = models.TextField(
        verbose_name=_("Chief Complaint"),
        blank=True
    )
    diagnosis = models.TextField(
        verbose_name=_("Diagnosis"),
        blank=True
    )
    treatment_plan = models.TextField(
        verbose_name=_("Treatment Plan"),
        blank=True
    )
    notes = models.TextField(
        verbose_name=_("Notes"),
        blank=True
    )

    class Meta:
        abstract = True

    def __str__(self):
        # This will be implemented in concrete models
        return f"Dental Medical Record from {self.date}"

class DentistryBaseAppointment(DentistryBaseModel):
    """
    Abstract base model for dental appointments.
    """
    # These fields will be defined in the concrete models
    # patient = models.ForeignKey(DentistryPatient, on_delete=models.CASCADE)
    # doctor = models.ForeignKey(DentistryDoctor, on_delete=models.CASCADE)

    appointment_date = models.DateField(
        verbose_name=_("Appointment Date")
    )
    appointment_time = models.TimeField(
        verbose_name=_("Appointment Time")
    )
    duration_minutes = models.PositiveIntegerField(
        default=30,
        verbose_name=_("Duration (minutes)")
    )

    STATUS_CHOICES = (
        ('scheduled', _('Scheduled')),
        ('confirmed', _('Confirmed')),
        ('cancelled', _('Cancelled')),
        ('completed', _('Completed')),
        ('no_show', _('No Show')),
    )
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='scheduled',
        verbose_name=_("Status")
    )

    reason = models.TextField(
        verbose_name=_("Reason for Visit"),
        blank=True
    )
    notes = models.TextField(
        verbose_name=_("Notes"),
        blank=True
    )

    class Meta:
        abstract = True

    def __str__(self):
        # This will be implemented in concrete models
        return f"Dental Appointment on {self.appointment_date} at {self.appointment_time}"
