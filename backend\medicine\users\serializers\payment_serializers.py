from rest_framework import serializers
from users.models import PaymentConfiguration

class PaymentConfigurationSerializer(serializers.ModelSerializer):
    """
    Serializer for the PaymentConfiguration model.
    """
    class Meta:
        model = PaymentConfiguration
        fields = '__all__'
        read_only_fields = ['created_at', 'updated_at']

class PaymentConfigurationPublicSerializer(serializers.ModelSerializer):
    """
    Serializer for public payment configuration information.
    This excludes sensitive information that shouldn't be exposed to clients.
    """
    class Meta:
        model = PaymentConfiguration
        fields = [
            'id', 'name', 'description',
            'bank_name', 'account_number', 'account_holder',
            'basic_price_6months', 'basic_price_annual',
            'standard_price_6months', 'standard_price_annual',
            'premium_price_6months', 'premium_price_annual',
            'basic_max_users', 'basic_max_assistants',
            'standard_max_users', 'standard_max_assistants',
            'premium_max_users', 'premium_max_assistants',
            'enable_bank_transfer', 'enable_credit_card', 'enable_paypal',
            'support_email', 'sales_email',
            'updated_at'
        ]
        read_only_fields = fields
