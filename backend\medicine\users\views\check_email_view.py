from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status, permissions
from django.contrib.auth import get_user_model
import logging

# Set up logging
logger = logging.getLogger(__name__)

User = get_user_model()

class CheckEmailView(APIView):
    """
    View to check if an email already exists in the system.
    """
    permission_classes = [permissions.AllowAny]  # Allow unauthenticated access
    def post(self, request, *args, **kwargs):
        email = request.data.get('email')
        logger.info(f"POST check-email request received for email: {email}")

        if not email:
            logger.warning("Email parameter missing in POST request")
            return Response(
                {"error": "Email is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Check if the email exists
        try:
            user = User.objects.get(email=email)
            logger.info(f"Email found: {email}, user_type: {user.user_type}")

            # Check if the user has an active license
            is_activated = False
            try:
                if hasattr(user, 'doctor_license'):
                    is_activated = user.doctor_license.status == 'active'
                    logger.info(f"License status for {email}: {is_activated}")
            except Exception as e:
                logger.error(f"Error checking license status for {email}: {e}")

            return Response({
                "exists": True,
                "is_activated": is_activated,
                "user_type": user.user_type
            })
        except User.DoesNotExist:
            logger.info(f"Email not found: {email}")
            return Response({
                "exists": False,
                "is_activated": False,
                "user_type": None
            }, status=status.HTTP_200_OK)  # Return 200 even when not found
        except Exception as e:
            logger.error(f"Unexpected error in CheckEmailView POST: {e}", exc_info=True)
            return Response({
                "error": "An unexpected error occurred while checking email",
                "detail": str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    # Add GET method to support both POST and GET requests
    def get(self, request, *args, **kwargs):
        email = request.query_params.get('email')
        logger.info(f"GET check-email request received for email: {email}")

        if not email:
            logger.warning("Email parameter missing in GET request")
            return Response(
                {"error": "Email parameter is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Check if the email exists
        try:
            user = User.objects.get(email=email)
            logger.info(f"Email found: {email}, user_type: {user.user_type}")

            # Check if the user has an active license
            is_activated = False
            try:
                if hasattr(user, 'doctor_license'):
                    is_activated = user.doctor_license.status == 'active'
                    logger.info(f"License status for {email}: {is_activated}")
            except Exception as e:
                logger.error(f"Error checking license status for {email}: {e}")

            return Response({
                "exists": True,
                "is_activated": is_activated,
                "user_type": user.user_type
            })
        except User.DoesNotExist:
            logger.info(f"Email not found: {email}")
            return Response({
                "exists": False,
                "is_activated": False,
                "user_type": None
            }, status=status.HTTP_200_OK)  # Return 200 even when not found
        except Exception as e:
            logger.error(f"Unexpected error in CheckEmailView GET: {e}", exc_info=True)
            return Response({
                "error": "An unexpected error occurred while checking email",
                "detail": str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
