# Pharmacy Management System

## Vue d'ensemble

Le système de gestion de pharmacie est une application Django qui fournit des APIs REST pour gérer les opérations pharmaceutiques, incluant les fournisseurs, les produits, les demandes d'achat, l'inventaire et les mouvements de stock.

## Modèles

### Supplier (Fournisseur)
- Gestion des informations des fournisseurs
- Coordonnées et conditions commerciales
- Informations légales (ICE, RC)

### Depot (Dépôt)
- Gestion des entrepôts/dépôts
- Informations de localisation et responsables

### ProductCategory (Famille de produits)
- Classification des produits
- Organisation hiérarchique

### Product (Produit/Article)
- Catalogue des produits pharmaceutiques
- Informations de prix et stock
- Codes à barres et descriptions

### PurchaseRequest (Demande d'achat)
- Gestion des demandes d'achat
- Workflow d'approbation
- Statuts: draft, pending, approved, rejected, completed

### PurchaseRequestItem (Article de demande)
- Détails des articles dans une demande d'achat
- Quantités et prix

### Inventory (Inventaire)
- Suivi des stocks par produit et dépôt
- Quantités disponibles et réservées

### StockMovement (Mouvement de stock)
- Historique des mouvements de stock
- Types: entrée, sortie, transfert, ajustement

## APIs Disponibles

### Endpoints principaux

- `GET /api/pharmacy/suppliers/` - Liste des fournisseurs
- `POST /api/pharmacy/suppliers/` - Créer un fournisseur
- `GET /api/pharmacy/suppliers/{id}/` - Détails d'un fournisseur
- `PUT /api/pharmacy/suppliers/{id}/` - Modifier un fournisseur
- `DELETE /api/pharmacy/suppliers/{id}/` - Supprimer un fournisseur

- `GET /api/pharmacy/depots/` - Liste des dépôts
- `GET /api/pharmacy/products/` - Liste des produits
- `GET /api/pharmacy/purchase-requests/` - Liste des demandes d'achat
- `GET /api/pharmacy/inventory/` - État de l'inventaire
- `GET /api/pharmacy/stock-movements/` - Mouvements de stock

### Endpoints spéciaux

- `GET /api/pharmacy/suppliers/simple_list/` - Liste simplifiée pour dropdowns
- `GET /api/pharmacy/depots/simple_list/` - Liste simplifiée des dépôts
- `GET /api/pharmacy/products/simple_list/` - Liste simplifiée des produits
- `GET /api/pharmacy/inventory/low_stock/` - Produits en rupture de stock
- `POST /api/pharmacy/purchase-requests/{id}/approve/` - Approuver une demande
- `POST /api/pharmacy/purchase-requests/{id}/reject/` - Rejeter une demande

## Installation et Configuration

### 1. Ajouter l'application aux settings

```python
INSTALLED_APPS = [
    # ...
    'pharmacy',
]
```

### 2. Inclure les URLs

```python
# core/urls.py
urlpatterns = [
    # ...
    path('api/pharmacy/', include('pharmacy.urls')),
]
```

### 3. Exécuter les migrations

```bash
python manage.py makemigrations pharmacy
python manage.py migrate
```

### 4. Configuration automatique

```bash
python setup_pharmacy.py
```

### 5. Test de l'installation

```bash
python test_pharmacy_api.py
```

## Utilisation avec le Frontend

Le frontend utilise le service `pharmacyService.ts` pour interagir avec l'API:

```typescript
import { supplierService, depotService, productService } from '~/services/pharmacyService';

// Récupérer les fournisseurs
const suppliers = await supplierService.getAll();

// Créer une demande d'achat
const purchaseRequest = await purchaseRequestService.create({
  numero: 'DA001',
  date: '2024-01-15',
  supplier: 'supplier-id',
  items: [...]
});
```

## Fonctionnalités Frontend

### Composants principaux

1. **Demandes_dachats.tsx** - Création et gestion des demandes d'achat
2. **Listedesfournisseurs.tsx** - Gestion des fournisseurs
3. **Inventaire.tsx** - Gestion de l'inventaire
4. **Pharmacie.tsx** - Vue d'ensemble de la pharmacie

### Intégration API

- Chargement automatique des données de référence
- Validation côté client et serveur
- Gestion des erreurs et notifications
- Interface utilisateur réactive avec Mantine UI

## Permissions et Sécurité

- Authentification JWT requise pour tous les endpoints
- Permissions basées sur les rôles utilisateur
- Validation des données côté serveur
- Audit trail avec created_by et timestamps

## Développement

### Structure des fichiers

```
pharmacy/
├── models.py          # Modèles de données
├── serializers.py     # Sérialiseurs DRF
├── views.py          # ViewSets et logique API
├── urls.py           # Configuration des routes
├── admin.py          # Interface d'administration
└── README.md         # Documentation
```

### Tests

```bash
# Tests unitaires
python manage.py test pharmacy

# Tests d'intégration API
python test_pharmacy_api.py
```

### Données d'exemple

Le script `setup_pharmacy.py` crée automatiquement:
- 2 fournisseurs d'exemple
- 3 dépôts
- 3 catégories de produits
- 3 produits d'exemple

## Troubleshooting

### Problèmes courants

1. **Erreur 404 sur les APIs**
   - Vérifier que les URLs sont correctement configurées
   - S'assurer que l'application est dans INSTALLED_APPS

2. **Erreurs de migration**
   - Supprimer les fichiers de migration et recréer
   - Vérifier les dépendances entre modèles

3. **Problèmes de CORS**
   - Configurer django-cors-headers
   - Vérifier les domaines autorisés

### Logs et debugging

```python
# settings.py
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'file': {
            'level': 'DEBUG',
            'class': 'logging.FileHandler',
            'filename': 'pharmacy.log',
        },
    },
    'loggers': {
        'pharmacy': {
            'handlers': ['file'],
            'level': 'DEBUG',
            'propagate': True,
        },
    },
}
```
