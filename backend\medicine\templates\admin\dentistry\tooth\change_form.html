{% extends "admin/change_form.html" %}
{% load i18n admin_urls static admin_modify %}

{% block extrahead %}
    {{ block.super }}
    <style>
        .dental-treatment-buttons {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .dental-treatment-buttons h3 {
            color: #495057;
            margin-bottom: 15px;
            font-size: 18px;
            border-bottom: 2px solid #007cba;
            padding-bottom: 8px;
        }
        .treatment-button-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-bottom: 20px;
        }
        .treatment-button {
            background: white;
            border: 2px solid #ddd;
            border-radius: 6px;
            padding: 12px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            position: relative;
        }
        .treatment-button:hover {
            border-color: #007cba;
            box-shadow: 0 2px 8px rgba(0,123,186,0.2);
            transform: translateY(-2px);
        }
        .treatment-button.active {
            background: #007cba;
            color: white;
            border-color: #005a87;
        }
        .treatment-button .icon {
            font-size: 24px;
            display: block;
            margin-bottom: 8px;
        }
        .treatment-button .name {
            font-weight: bold;
            font-size: 14px;
            margin-bottom: 4px;
        }
        .treatment-button .id {
            font-size: 12px;
            color: #6c757d;
        }
        .treatment-button.active .id {
            color: rgba(255,255,255,0.8);
        }
        .tooth-info-panel {
            background: linear-gradient(135deg, #007cba, #005a87);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .tooth-info-panel h2 {
            margin: 0 0 10px 0;
            font-size: 24px;
        }
        .tooth-info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .tooth-info-item {
            background: rgba(255,255,255,0.1);
            padding: 10px;
            border-radius: 4px;
            text-align: center;
        }
        .tooth-info-value {
            font-size: 18px;
            font-weight: bold;
            display: block;
        }
        .tooth-info-label {
            font-size: 12px;
            opacity: 0.9;
        }
        .age-visibility-controls {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
        }
        .age-visibility-controls h4 {
            color: #856404;
            margin-bottom: 10px;
        }
        .age-button {
            display: inline-block;
            background: white;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 8px 12px;
            margin: 4px;
            cursor: pointer;
            transition: all 0.2s;
        }
        .age-button:hover {
            background: #ffeaa7;
        }
        .age-button.active {
            background: #f39c12;
            color: white;
            border-color: #e67e22;
        }
    </style>
{% endblock %}

{% block field_sets %}
    {% if original %}
        <!-- Tooth Information Panel -->
        <div class="tooth-info-panel">
            <h2>🦷 Tooth {{ original.tooth_number }} - {{ original.name }}</h2>
            <p>{{ original.get_tooth_type_display }} in {{ original.get_quadrant_display }}</p>

            <div class="tooth-info-grid">
                <div class="tooth-info-item">
                    <span class="tooth-info-value">{{ original.tooth_number }}</span>
                    <span class="tooth-info-label">FDI Number</span>
                </div>
                <div class="tooth-info-item">
                    <span class="tooth-info-value">{{ original.svg_id|default:"N/A" }}</span>
                    <span class="tooth-info-label">SVG ID</span>
                </div>
                <div class="tooth-info-item">
                    <span class="tooth-info-value">{{ original.position }}</span>
                    <span class="tooth-info-label">Position</span>
                </div>
                <div class="tooth-info-item">
                    <span class="tooth-info-value">{% if original.is_permanent %}🦷 Permanent{% else %}🍼 Primary{% endif %}</span>
                    <span class="tooth-info-label">Type</span>
                </div>
            </div>
        </div>

        <!-- Treatment Buttons Interface -->
        <div class="dental-treatment-buttons">
            <h3>🧼 Basic Treatments</h3>
            <div class="treatment-button-grid">
                <div class="treatment-button" data-field="cleaning_applied" data-id="17">
                    <span class="icon">🧼</span>
                    <div class="name">Cleaning</div>
                    <div class="id">ID: 17</div>
                </div>
                <div class="treatment-button" data-field="fluoride_applied" data-id="18">
                    <span class="icon">💧</span>
                    <div class="name">Fluoride</div>
                    <div class="id">ID: 18</div>
                </div>
                <div class="treatment-button" data-field="sealant_applied" data-id="19">
                    <span class="icon">🔒</span>
                    <div class="name">Sealant</div>
                    <div class="id">ID: 19</div>
                </div>
                <div class="treatment-button" data-field="whitening_applied" data-id="20-23">
                    <span class="icon">✨</span>
                    <div class="name">Whitening</div>
                    <div class="id">IDs: 20-23</div>
                </div>
            </div>

            <h3>🔧 Restorations</h3>
            <div class="treatment-button-grid">
                <div class="treatment-button" data-field="restoration_temporary_applied" data-id="24-25">
                    <span class="icon">🔧</span>
                    <div class="name">Temporary Restoration</div>
                    <div class="id">IDs: 24-25</div>
                </div>
                <div class="treatment-button" data-field="restoration_amalgam_applied" data-id="26">
                    <span class="icon">⚫</span>
                    <div class="name">Amalgam Restoration</div>
                    <div class="id">ID: 26</div>
                </div>
                <div class="treatment-button" data-field="restoration_glass_ionomer_applied" data-id="27">
                    <span class="icon">🔵</span>
                    <div class="name">Glass Ionomer</div>
                    <div class="id">ID: 27</div>
                </div>
            </div>

            <h3>🌿 Root Treatments</h3>
            <div class="treatment-button-grid">
                <div class="treatment-button" data-field="root_temporary_applied" data-id="28-29">
                    <span class="icon">🌱</span>
                    <div class="name">Root Temporary</div>
                    <div class="id">IDs: 28-29</div>
                </div>
                <div class="treatment-button" data-field="root_calcium_applied" data-id="30-33">
                    <span class="icon">🦴</span>
                    <div class="name">Root Calcium</div>
                    <div class="id">IDs: 30-33</div>
                </div>
                <div class="treatment-button" data-field="root_gutta_percha_applied" data-id="34-35">
                    <span class="icon">🌿</span>
                    <div class="name">Gutta Percha</div>
                    <div class="id">IDs: 34-35</div>
                </div>
                <div class="treatment-button" data-field="post_care_applied" data-id="36">
                    <span class="icon">🏥</span>
                    <div class="name">Post Care</div>
                    <div class="id">ID: 36</div>
                </div>
            </div>

            <h3>👑 Advanced Treatments</h3>
            <div class="treatment-button-grid">
                <div class="treatment-button" data-field="veneer_applied" data-id="37">
                    <span class="icon">💎</span>
                    <div class="name">Veneer</div>
                    <div class="id">ID: 37</div>
                </div>
                <div class="treatment-button" data-field="onlay_applied" data-id="38-39">
                    <span class="icon">🔶</span>
                    <div class="name">Onlay</div>
                    <div class="id">IDs: 38-39</div>
                </div>
                <div class="treatment-button" data-field="crown_applied" data-id="40-41">
                    <span class="icon">👑</span>
                    <div class="name">Crown</div>
                    <div class="id">IDs: 40-41</div>
                </div>
                <div class="treatment-button" data-field="crown_gold_applied" data-id="43-44">
                    <span class="icon">🥇</span>
                    <div class="name">Gold Crown</div>
                    <div class="id">IDs: 43-44</div>
                </div>
                <div class="treatment-button" data-field="crown_zirconia_applied" data-id="45-47">
                    <span class="icon">💍</span>
                    <div class="name">Zirconia Crown</div>
                    <div class="id">IDs: 45-47</div>
                </div>
                <div class="treatment-button" data-field="denture_applied" data-id="48-50">
                    <span class="icon">🦷</span>
                    <div class="name">Denture</div>
                    <div class="id">IDs: 48-50</div>
                </div>
                <div class="treatment-button" data-field="bridge_applied" data-id="51-52">
                    <span class="icon">🌉</span>
                    <div class="name">Bridge</div>
                    <div class="id">IDs: 51-52</div>
                </div>
                <div class="treatment-button" data-field="implant_applied" data-id="54-60">
                    <span class="icon">🔩</span>
                    <div class="name">Implant</div>
                    <div class="id">IDs: 54-60</div>
                </div>
                <div class="treatment-button" data-field="bone_applied" data-id="61-63">
                    <span class="icon">🦴</span>
                    <div class="name">Bone Work</div>
                    <div class="id">IDs: 61-63</div>
                </div>
                <div class="treatment-button" data-field="resection_applied" data-id="various">
                    <span class="icon">✂️</span>
                    <div class="name">Resection</div>
                    <div class="id">Various IDs</div>
                </div>
            </div>
        </div>

        <!-- Age-Based Visibility Controls -->
        <div class="age-visibility-controls">
            <h4>👶 Age-Based Visibility Controls</h4>
            <p>Control when this tooth is visible based on patient age:</p>
            <div class="age-button" data-field="visible_under_6_years">👶 Under 6 years</div>
            <div class="age-button" data-field="visible_under_7_5_years">🧒 Under 7.5 years</div>
            <div class="age-button" data-field="visible_under_12_years">👦 Under 12 years</div>
            <div class="age-button" data-field="visible_under_13_5_years">👧 Under 13.5 years</div>
            <div class="age-button" data-field="visible_adult">👨 Adult</div>
        </div>
    {% endif %}

    {{ block.super }}
{% endblock %}

{% block extrajs %}
    {{ block.super }}
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize treatment buttons
            const treatmentButtons = document.querySelectorAll('.treatment-button');
            const ageButtons = document.querySelectorAll('.age-button');

            // Function to update button state based on checkbox value
            function updateButtonState(button, isActive) {
                if (isActive) {
                    button.classList.add('active');
                } else {
                    button.classList.remove('active');
                }
            }

            // Function to sync button with form field
            function syncButtonWithField(button) {
                const fieldName = button.getAttribute('data-field');
                const checkbox = document.querySelector(`input[name="${fieldName}"]`);

                if (checkbox) {
                    updateButtonState(button, checkbox.checked);

                    // Add click handler to button
                    button.addEventListener('click', function() {
                        checkbox.checked = !checkbox.checked;
                        updateButtonState(button, checkbox.checked);

                        // Trigger change event for Django admin
                        checkbox.dispatchEvent(new Event('change'));

                        // Visual feedback
                        button.style.transform = 'scale(0.95)';
                        setTimeout(() => {
                            button.style.transform = '';
                        }, 150);
                    });

                    // Listen for checkbox changes
                    checkbox.addEventListener('change', function() {
                        updateButtonState(button, checkbox.checked);
                    });
                }
            }

            // Initialize all treatment buttons
            treatmentButtons.forEach(syncButtonWithField);
            ageButtons.forEach(syncButtonWithField);

            // Add hover effects and tooltips
            treatmentButtons.forEach(button => {
                const fieldName = button.getAttribute('data-field');
                const treatmentId = button.getAttribute('data-id');

                button.title = `Toggle ${fieldName.replace(/_/g, ' ')} (ID: ${treatmentId})`;

                button.addEventListener('mouseenter', function() {
                    if (!button.classList.contains('active')) {
                        button.style.borderColor = '#007cba';
                    }
                });

                button.addEventListener('mouseleave', function() {
                    if (!button.classList.contains('active')) {
                        button.style.borderColor = '#ddd';
                    }
                });
            });

            // Add tooltips for age buttons
            ageButtons.forEach(button => {
                const fieldName = button.getAttribute('data-field');
                button.title = `Toggle visibility for ${fieldName.replace(/_/g, ' ')}`;
            });

            // Add quick action buttons
            const quickActionsHtml = `
                <div style="margin: 20px 0; padding: 15px; background: #e8f4fd; border-radius: 6px;">
                    <h4 style="margin-bottom: 10px; color: #0c5460;">🚀 Quick Actions</h4>
                    <button type="button" id="reset-all-treatments" class="button" style="margin-right: 10px;">
                        🔄 Reset All Treatments
                    </button>
                    <button type="button" id="apply-basic-care" class="button" style="margin-right: 10px;">
                        🧼 Apply Basic Care
                    </button>
                    <button type="button" id="toggle-visibility" class="button">
                        👁️ Toggle All Visibility
                    </button>
                </div>
            `;

            const treatmentSection = document.querySelector('.dental-treatment-buttons');
            if (treatmentSection) {
                treatmentSection.insertAdjacentHTML('afterend', quickActionsHtml);

                // Reset all treatments
                document.getElementById('reset-all-treatments').addEventListener('click', function() {
                    treatmentButtons.forEach(button => {
                        const fieldName = button.getAttribute('data-field');
                        const checkbox = document.querySelector(`input[name="${fieldName}"]`);
                        if (checkbox) {
                            checkbox.checked = false;
                            updateButtonState(button, false);
                        }
                    });
                    alert('🔄 All treatments have been reset!');
                });

                // Apply basic care (cleaning + fluoride)
                document.getElementById('apply-basic-care').addEventListener('click', function() {
                    ['cleaning_applied', 'fluoride_applied'].forEach(fieldName => {
                        const checkbox = document.querySelector(`input[name="${fieldName}"]`);
                        const button = document.querySelector(`[data-field="${fieldName}"]`);
                        if (checkbox && button) {
                            checkbox.checked = true;
                            updateButtonState(button, true);
                        }
                    });
                    alert('🧼 Basic care (Cleaning + Fluoride) applied!');
                });

                // Toggle all visibility
                document.getElementById('toggle-visibility').addEventListener('click', function() {
                    const visibilityFields = [
                        'visible_under_6_years', 'visible_under_7_5_years',
                        'visible_under_12_years', 'visible_under_13_5_years', 'visible_adult'
                    ];

                    const firstCheckbox = document.querySelector(`input[name="${visibilityFields[0]}"]`);
                    const newState = !firstCheckbox.checked;

                    visibilityFields.forEach(fieldName => {
                        const checkbox = document.querySelector(`input[name="${fieldName}"]`);
                        const button = document.querySelector(`[data-field="${fieldName}"]`);
                        if (checkbox && button) {
                            checkbox.checked = newState;
                            updateButtonState(button, newState);
                        }
                    });

                    alert(`👁️ All visibility settings ${newState ? 'enabled' : 'disabled'}!`);
                });
            }

            // Add save confirmation
            const form = document.querySelector('#tooth_form');
            if (form) {
                form.addEventListener('submit', function() {
                    const activetreatments = Array.from(treatmentButtons)
                        .filter(btn => btn.classList.contains('active'))
                        .map(btn => btn.querySelector('.name').textContent);

                    if (activetreatments.length > 0) {
                        console.log('💾 Saving tooth with treatments:', activetreatments.join(', '));
                    }
                });
            }
        });
    </script>
{% endblock %}
