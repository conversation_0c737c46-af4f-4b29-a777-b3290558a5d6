from django.contrib import admin
from .models import (
    Supplier, Depot, ProductCategory, Product,
    PurchaseRequest, PurchaseRequestItem,
    Inventory, StockMovement
)


@admin.register(Supplier)
class SupplierAdmin(admin.ModelAdmin):
    list_display = ['raison_sociale', 'email', 'ville', 'tel_gestionnaire', 'is_active', 'created_at']
    list_filter = ['ville', 'mode_paiement', 'is_active', 'created_at']
    search_fields = ['raison_sociale', 'email', 'tel_gestionnaire']
    readonly_fields = ['created_at', 'updated_at']
    fieldsets = (
        ('Informations générales', {
            'fields': ('raison_sociale', 'email', 'ville', 'adresse')
        }),
        ('Contact', {
            'fields': ('tel_gestionnaire', 'tel_fixe', 'fax')
        }),
        ('Informations commerciales', {
            'fields': ('mode_paiement', 'condition_paiement', 'directeur_commercial', 'gestionnaire_vente')
        }),
        ('Informations légales', {
            'fields': ('ice', 'rc')
        }),
        ('Autres', {
            'fields': ('commentaire', 'is_active', 'created_at', 'updated_at')
        }),
    )


@admin.register(Depot)
class DepotAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'manager', 'phone', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'code', 'manager']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(ProductCategory)
class ProductCategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'code']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    list_display = ['code', 'designation', 'category', 'unit', 'price', 'min_stock', 'max_stock', 'is_active']
    list_filter = ['category', 'unit', 'is_active', 'created_at']
    search_fields = ['code', 'designation', 'barcode']
    readonly_fields = ['created_at', 'updated_at']
    fieldsets = (
        ('Informations de base', {
            'fields': ('code', 'designation', 'category', 'unit', 'barcode')
        }),
        ('Prix et stock', {
            'fields': ('price', 'min_stock', 'max_stock')
        }),
        ('Autres', {
            'fields': ('description', 'is_active', 'created_at', 'updated_at')
        }),
    )


class PurchaseRequestItemInline(admin.TabularInline):
    model = PurchaseRequestItem
    extra = 1
    readonly_fields = ['total_price']


@admin.register(PurchaseRequest)
class PurchaseRequestAdmin(admin.ModelAdmin):
    list_display = ['numero', 'date', 'supplier', 'status', 'urgent', 'created_at']
    list_filter = ['status', 'urgent', 'date', 'created_at']
    search_fields = ['numero', 'supplier__raison_sociale']
    readonly_fields = ['created_at', 'updated_at']
    inlines = [PurchaseRequestItemInline]
    fieldsets = (
        ('Informations générales', {
            'fields': ('numero', 'date', 'date_echeance', 'supplier')
        }),
        ('Statut', {
            'fields': ('status', 'urgent')
        }),
        ('Commentaires', {
            'fields': ('commentaire',)
        }),
        ('Métadonnées', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(PurchaseRequestItem)
class PurchaseRequestItemAdmin(admin.ModelAdmin):
    list_display = ['purchase_request', 'product', 'depot', 'quantity', 'unit_price', 'total_price']
    list_filter = ['depot', 'product__category', 'created_at']
    search_fields = ['purchase_request__numero', 'product__designation', 'product__code']
    readonly_fields = ['total_price', 'created_at', 'updated_at']


@admin.register(Inventory)
class InventoryAdmin(admin.ModelAdmin):
    list_display = ['product', 'depot', 'quantity', 'reserved_quantity', 'available_quantity', 'last_movement_date']
    list_filter = ['depot', 'product__category', 'last_movement_date']
    search_fields = ['product__code', 'product__designation', 'depot__name']
    readonly_fields = ['available_quantity', 'last_movement_date', 'created_at', 'updated_at']


@admin.register(StockMovement)
class StockMovementAdmin(admin.ModelAdmin):
    list_display = ['product', 'depot', 'movement_type', 'quantity', 'reference', 'date']
    list_filter = ['movement_type', 'depot', 'product__category', 'date']
    search_fields = ['product__code', 'product__designation', 'reference']
    readonly_fields = ['date', 'created_at', 'updated_at']
    fieldsets = (
        ('Informations du mouvement', {
            'fields': ('product', 'depot', 'movement_type', 'quantity')
        }),
        ('Détails', {
            'fields': ('reference', 'reason')
        }),
        ('Métadonnées', {
            'fields': ('date', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
