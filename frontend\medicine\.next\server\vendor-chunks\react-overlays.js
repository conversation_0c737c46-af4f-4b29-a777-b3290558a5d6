"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-overlays";
exports.ids = ["vendor-chunks/react-overlays"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-overlays/esm/Overlay.js":
/*!****************************************************!*\
  !*** ./node_modules/react-overlays/esm/Overlay.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutPropertiesLoose */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! prop-types */ \"(ssr)/./node_modules/prop-types/index.js\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _restart_hooks_useCallbackRef__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @restart/hooks/useCallbackRef */ \"(ssr)/./node_modules/@restart/hooks/esm/useCallbackRef.js\");\n/* harmony import */ var _restart_hooks_useMergedRefs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @restart/hooks/useMergedRefs */ \"(ssr)/./node_modules/@restart/hooks/esm/useMergedRefs.js\");\n/* harmony import */ var _popper__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./popper */ \"(ssr)/./node_modules/@popperjs/core/lib/enums.js\");\n/* harmony import */ var _usePopper__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./usePopper */ \"(ssr)/./node_modules/react-overlays/esm/usePopper.js\");\n/* harmony import */ var _useRootClose__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./useRootClose */ \"(ssr)/./node_modules/react-overlays/esm/useRootClose.js\");\n/* harmony import */ var _useWaitForDOMRef__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./useWaitForDOMRef */ \"(ssr)/./node_modules/react-overlays/esm/useWaitForDOMRef.js\");\n/* harmony import */ var _mergeOptionsWithPopperConfig__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./mergeOptionsWithPopperConfig */ \"(ssr)/./node_modules/react-overlays/esm/mergeOptionsWithPopperConfig.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n/**\n * Built on top of `Popper.js`, the overlay component is\n * great for custom tooltip overlays.\n */\nvar Overlay = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2___default().forwardRef(function (props, outerRef) {\n  var flip = props.flip,\n      offset = props.offset,\n      placement = props.placement,\n      _props$containerPaddi = props.containerPadding,\n      containerPadding = _props$containerPaddi === void 0 ? 5 : _props$containerPaddi,\n      _props$popperConfig = props.popperConfig,\n      popperConfig = _props$popperConfig === void 0 ? {} : _props$popperConfig,\n      Transition = props.transition;\n\n  var _useCallbackRef = (0,_restart_hooks_useCallbackRef__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(),\n      rootElement = _useCallbackRef[0],\n      attachRef = _useCallbackRef[1];\n\n  var _useCallbackRef2 = (0,_restart_hooks_useCallbackRef__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(),\n      arrowElement = _useCallbackRef2[0],\n      attachArrowRef = _useCallbackRef2[1];\n\n  var mergedRef = (0,_restart_hooks_useMergedRefs__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(attachRef, outerRef);\n  var container = (0,_useWaitForDOMRef__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(props.container);\n  var target = (0,_useWaitForDOMRef__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(props.target);\n\n  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(!props.show),\n      exited = _useState[0],\n      setExited = _useState[1];\n\n  var _usePopper = (0,_usePopper__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(target, rootElement, (0,_mergeOptionsWithPopperConfig__WEBPACK_IMPORTED_MODULE_8__[\"default\"])({\n    placement: placement,\n    enableEvents: !!props.show,\n    containerPadding: containerPadding || 5,\n    flip: flip,\n    offset: offset,\n    arrowElement: arrowElement,\n    popperConfig: popperConfig\n  })),\n      styles = _usePopper.styles,\n      attributes = _usePopper.attributes,\n      popper = (0,_babel_runtime_helpers_esm_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_usePopper, [\"styles\", \"attributes\"]);\n\n  if (props.show) {\n    if (exited) setExited(false);\n  } else if (!props.transition && !exited) {\n    setExited(true);\n  }\n\n  var handleHidden = function handleHidden() {\n    setExited(true);\n\n    if (props.onExited) {\n      props.onExited.apply(props, arguments);\n    }\n  }; // Don't un-render the overlay while it's transitioning out.\n\n\n  var mountOverlay = props.show || Transition && !exited;\n  (0,_useRootClose__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(rootElement, props.onHide, {\n    disabled: !props.rootClose || props.rootCloseDisabled,\n    clickTrigger: props.rootCloseEvent\n  });\n\n  if (!mountOverlay) {\n    // Don't bother showing anything if we don't have to.\n    return null;\n  }\n\n  var child = props.children((0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, popper, {\n    show: !!props.show,\n    props: (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, attributes.popper, {\n      style: styles.popper,\n      ref: mergedRef\n    }),\n    arrowProps: (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, attributes.arrow, {\n      style: styles.arrow,\n      ref: attachArrowRef\n    })\n  }));\n\n  if (Transition) {\n    var onExit = props.onExit,\n        onExiting = props.onExiting,\n        onEnter = props.onEnter,\n        onEntering = props.onEntering,\n        onEntered = props.onEntered;\n    child = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2___default().createElement(Transition, {\n      \"in\": props.show,\n      appear: true,\n      onExit: onExit,\n      onExiting: onExiting,\n      onExited: handleHidden,\n      onEnter: onEnter,\n      onEntering: onEntering,\n      onEntered: onEntered\n    }, child);\n  }\n\n  return container ? /*#__PURE__*/react_dom__WEBPACK_IMPORTED_MODULE_3___default().createPortal(child, container) : null;\n});\nOverlay.displayName = 'Overlay';\nOverlay.propTypes = {\n  /**\n   * Set the visibility of the Overlay\n   */\n  show: (prop_types__WEBPACK_IMPORTED_MODULE_10___default().bool),\n\n  /** Specify where the overlay element is positioned in relation to the target element */\n  placement: prop_types__WEBPACK_IMPORTED_MODULE_10___default().oneOf(_popper__WEBPACK_IMPORTED_MODULE_11__.placements),\n\n  /**\n   * A DOM Element, Ref to an element, or function that returns either. The `target` element is where\n   * the overlay is positioned relative to.\n   */\n  target: (prop_types__WEBPACK_IMPORTED_MODULE_10___default().any),\n\n  /**\n   * A DOM Element, Ref to an element, or function that returns either. The `container` will have the Portal children\n   * appended to it.\n   */\n  container: (prop_types__WEBPACK_IMPORTED_MODULE_10___default().any),\n\n  /**\n   * Enables the Popper.js `flip` modifier, allowing the Overlay to\n   * automatically adjust it's placement in case of overlap with the viewport or toggle.\n   * Refer to the [flip docs](https://popper.js.org/popper-documentation.html#modifiers..flip.enabled) for more info\n   */\n  flip: (prop_types__WEBPACK_IMPORTED_MODULE_10___default().bool),\n\n  /**\n   * A render prop that returns an element to overlay and position. See\n   * the [react-popper documentation](https://github.com/FezVrasta/react-popper#children) for more info.\n   *\n   * @type {Function ({\n   *   show: boolean,\n   *   placement: Placement,\n   *   update: () => void,\n   *   forceUpdate: () => void,\n   *   props: {\n   *     ref: (?HTMLElement) => void,\n   *     style: { [string]: string | number },\n   *     aria-labelledby: ?string\n   *     [string]: string | number,\n   *   },\n   *   arrowProps: {\n   *     ref: (?HTMLElement) => void,\n   *     style: { [string]: string | number },\n   *     [string]: string | number,\n   *   },\n   * }) => React.Element}\n   */\n  children: (prop_types__WEBPACK_IMPORTED_MODULE_10___default().func).isRequired,\n\n  /**\n   * Control how much space there is between the edge of the boundary element and overlay.\n   * A convenience shortcut to setting `popperConfig.modfiers.preventOverflow.padding`\n   */\n  containerPadding: (prop_types__WEBPACK_IMPORTED_MODULE_10___default().number),\n\n  /**\n   * A set of popper options and props passed directly to react-popper's Popper component.\n   */\n  popperConfig: (prop_types__WEBPACK_IMPORTED_MODULE_10___default().object),\n\n  /**\n   * Specify whether the overlay should trigger `onHide` when the user clicks outside the overlay\n   */\n  rootClose: (prop_types__WEBPACK_IMPORTED_MODULE_10___default().bool),\n\n  /**\n   * Specify event for toggling overlay\n   */\n  rootCloseEvent: prop_types__WEBPACK_IMPORTED_MODULE_10___default().oneOf(['click', 'mousedown']),\n\n  /**\n   * Specify disabled for disable RootCloseWrapper\n   */\n  rootCloseDisabled: (prop_types__WEBPACK_IMPORTED_MODULE_10___default().bool),\n\n  /**\n   * A Callback fired by the Overlay when it wishes to be hidden.\n   *\n   * __required__ when `rootClose` is `true`.\n   *\n   * @type func\n   */\n  onHide: function onHide(props) {\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n\n    if (props.rootClose) {\n      var _PropTypes$func;\n\n      return (_PropTypes$func = (prop_types__WEBPACK_IMPORTED_MODULE_10___default().func)).isRequired.apply(_PropTypes$func, [props].concat(args));\n    }\n\n    return prop_types__WEBPACK_IMPORTED_MODULE_10___default().func.apply((prop_types__WEBPACK_IMPORTED_MODULE_10___default()), [props].concat(args));\n  },\n\n  /**\n   * A `react-transition-group@2.0.0` `<Transition/>` component\n   * used to animate the overlay as it changes visibility.\n   */\n  // @ts-ignore\n  transition: (prop_types__WEBPACK_IMPORTED_MODULE_10___default().elementType),\n\n  /**\n   * Callback fired before the Overlay transitions in\n   */\n  onEnter: (prop_types__WEBPACK_IMPORTED_MODULE_10___default().func),\n\n  /**\n   * Callback fired as the Overlay begins to transition in\n   */\n  onEntering: (prop_types__WEBPACK_IMPORTED_MODULE_10___default().func),\n\n  /**\n   * Callback fired after the Overlay finishes transitioning in\n   */\n  onEntered: (prop_types__WEBPACK_IMPORTED_MODULE_10___default().func),\n\n  /**\n   * Callback fired right before the Overlay transitions out\n   */\n  onExit: (prop_types__WEBPACK_IMPORTED_MODULE_10___default().func),\n\n  /**\n   * Callback fired as the Overlay begins to transition out\n   */\n  onExiting: (prop_types__WEBPACK_IMPORTED_MODULE_10___default().func),\n\n  /**\n   * Callback fired after the Overlay finishes transitioning out\n   */\n  onExited: (prop_types__WEBPACK_IMPORTED_MODULE_10___default().func)\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Overlay);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-overlays/esm/Overlay.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-overlays/esm/mergeOptionsWithPopperConfig.js":
/*!*************************************************************************!*\
  !*** ./node_modules/react-overlays/esm/mergeOptionsWithPopperConfig.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ mergeOptionsWithPopperConfig),\n/* harmony export */   toModifierArray: () => (/* binding */ toModifierArray),\n/* harmony export */   toModifierMap: () => (/* binding */ toModifierMap)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n\nfunction toModifierMap(modifiers) {\n  var result = {};\n\n  if (!Array.isArray(modifiers)) {\n    return modifiers || result;\n  } // eslint-disable-next-line no-unused-expressions\n\n\n  modifiers == null ? void 0 : modifiers.forEach(function (m) {\n    result[m.name] = m;\n  });\n  return result;\n}\nfunction toModifierArray(map) {\n  if (map === void 0) {\n    map = {};\n  }\n\n  if (Array.isArray(map)) return map;\n  return Object.keys(map).map(function (k) {\n    map[k].name = k;\n    return map[k];\n  });\n}\nfunction mergeOptionsWithPopperConfig(_ref) {\n  var _modifiers$preventOve, _modifiers$preventOve2, _modifiers$offset, _modifiers$arrow;\n\n  var enabled = _ref.enabled,\n      enableEvents = _ref.enableEvents,\n      placement = _ref.placement,\n      flip = _ref.flip,\n      offset = _ref.offset,\n      fixed = _ref.fixed,\n      containerPadding = _ref.containerPadding,\n      arrowElement = _ref.arrowElement,\n      _ref$popperConfig = _ref.popperConfig,\n      popperConfig = _ref$popperConfig === void 0 ? {} : _ref$popperConfig;\n  var modifiers = toModifierMap(popperConfig.modifiers);\n  return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, popperConfig, {\n    placement: placement,\n    enabled: enabled,\n    strategy: fixed ? 'fixed' : popperConfig.strategy,\n    modifiers: toModifierArray((0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, modifiers, {\n      eventListeners: {\n        enabled: enableEvents\n      },\n      preventOverflow: (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, modifiers.preventOverflow, {\n        options: containerPadding ? (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n          padding: containerPadding\n        }, (_modifiers$preventOve = modifiers.preventOverflow) == null ? void 0 : _modifiers$preventOve.options) : (_modifiers$preventOve2 = modifiers.preventOverflow) == null ? void 0 : _modifiers$preventOve2.options\n      }),\n      offset: {\n        options: (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n          offset: offset\n        }, (_modifiers$offset = modifiers.offset) == null ? void 0 : _modifiers$offset.options)\n      },\n      arrow: (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, modifiers.arrow, {\n        enabled: !!arrowElement,\n        options: (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, (_modifiers$arrow = modifiers.arrow) == null ? void 0 : _modifiers$arrow.options, {\n          element: arrowElement\n        })\n      }),\n      flip: (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n        enabled: !!flip\n      }, modifiers.flip)\n    }))\n  });\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-overlays/esm/mergeOptionsWithPopperConfig.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-overlays/esm/ownerDocument.js":
/*!**********************************************************!*\
  !*** ./node_modules/react-overlays/esm/ownerDocument.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var dom_helpers_ownerDocument__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! dom-helpers/ownerDocument */ \"(ssr)/./node_modules/dom-helpers/esm/ownerDocument.js\");\n/* harmony import */ var _safeFindDOMNode__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./safeFindDOMNode */ \"(ssr)/./node_modules/react-overlays/esm/safeFindDOMNode.js\");\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (function (componentOrElement) {\n  return (0,dom_helpers_ownerDocument__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_safeFindDOMNode__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(componentOrElement));\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3Qtb3ZlcmxheXMvZXNtL293bmVyRG9jdW1lbnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXNEO0FBQ047QUFDaEQsaUVBQWdCO0FBQ2hCLFNBQVMscUVBQWEsQ0FBQyw0REFBZTtBQUN0QyxDQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNvcnJ5XFxEZXNrdG9wXFxzdnBcXGZyb250ZW5kXFxtZWRpY2luZVxcbm9kZV9tb2R1bGVzXFxyZWFjdC1vdmVybGF5c1xcZXNtXFxvd25lckRvY3VtZW50LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBvd25lckRvY3VtZW50IGZyb20gJ2RvbS1oZWxwZXJzL293bmVyRG9jdW1lbnQnO1xuaW1wb3J0IHNhZmVGaW5kRE9NTm9kZSBmcm9tICcuL3NhZmVGaW5kRE9NTm9kZSc7XG5leHBvcnQgZGVmYXVsdCAoZnVuY3Rpb24gKGNvbXBvbmVudE9yRWxlbWVudCkge1xuICByZXR1cm4gb3duZXJEb2N1bWVudChzYWZlRmluZERPTU5vZGUoY29tcG9uZW50T3JFbGVtZW50KSk7XG59KTsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-overlays/esm/ownerDocument.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-overlays/esm/popper.js":
/*!***************************************************!*\
  !*** ./node_modules/react-overlays/esm/popper.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createPopper: () => (/* binding */ createPopper),\n/* harmony export */   placements: () => (/* reexport safe */ _popperjs_core_lib_enums__WEBPACK_IMPORTED_MODULE_9__.placements)\n/* harmony export */ });\n/* harmony import */ var _popperjs_core_lib_modifiers_arrow__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @popperjs/core/lib/modifiers/arrow */ \"(ssr)/./node_modules/@popperjs/core/lib/modifiers/arrow.js\");\n/* harmony import */ var _popperjs_core_lib_modifiers_computeStyles__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @popperjs/core/lib/modifiers/computeStyles */ \"(ssr)/./node_modules/@popperjs/core/lib/modifiers/computeStyles.js\");\n/* harmony import */ var _popperjs_core_lib_modifiers_eventListeners__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @popperjs/core/lib/modifiers/eventListeners */ \"(ssr)/./node_modules/@popperjs/core/lib/modifiers/eventListeners.js\");\n/* harmony import */ var _popperjs_core_lib_modifiers_flip__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @popperjs/core/lib/modifiers/flip */ \"(ssr)/./node_modules/@popperjs/core/lib/modifiers/flip.js\");\n/* harmony import */ var _popperjs_core_lib_modifiers_hide__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @popperjs/core/lib/modifiers/hide */ \"(ssr)/./node_modules/@popperjs/core/lib/modifiers/hide.js\");\n/* harmony import */ var _popperjs_core_lib_modifiers_offset__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @popperjs/core/lib/modifiers/offset */ \"(ssr)/./node_modules/@popperjs/core/lib/modifiers/offset.js\");\n/* harmony import */ var _popperjs_core_lib_modifiers_popperOffsets__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @popperjs/core/lib/modifiers/popperOffsets */ \"(ssr)/./node_modules/@popperjs/core/lib/modifiers/popperOffsets.js\");\n/* harmony import */ var _popperjs_core_lib_modifiers_preventOverflow__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @popperjs/core/lib/modifiers/preventOverflow */ \"(ssr)/./node_modules/@popperjs/core/lib/modifiers/preventOverflow.js\");\n/* harmony import */ var _popperjs_core_lib_enums__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @popperjs/core/lib/enums */ \"(ssr)/./node_modules/@popperjs/core/lib/enums.js\");\n/* harmony import */ var _popperjs_core_lib_popper_base__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @popperjs/core/lib/popper-base */ \"(ssr)/./node_modules/@popperjs/core/lib/createPopper.js\");\n\n\n\n\n\n\n\n\n\n // For the common JS build we will turn this file into a bundle with no imports.\n// This is b/c the Popper lib is all esm files, and would break in a common js only environment\n\nvar createPopper = (0,_popperjs_core_lib_popper_base__WEBPACK_IMPORTED_MODULE_0__.popperGenerator)({\n  defaultModifiers: [_popperjs_core_lib_modifiers_hide__WEBPACK_IMPORTED_MODULE_1__[\"default\"], _popperjs_core_lib_modifiers_popperOffsets__WEBPACK_IMPORTED_MODULE_2__[\"default\"], _popperjs_core_lib_modifiers_computeStyles__WEBPACK_IMPORTED_MODULE_3__[\"default\"], _popperjs_core_lib_modifiers_eventListeners__WEBPACK_IMPORTED_MODULE_4__[\"default\"], _popperjs_core_lib_modifiers_offset__WEBPACK_IMPORTED_MODULE_5__[\"default\"], _popperjs_core_lib_modifiers_flip__WEBPACK_IMPORTED_MODULE_6__[\"default\"], _popperjs_core_lib_modifiers_preventOverflow__WEBPACK_IMPORTED_MODULE_7__[\"default\"], _popperjs_core_lib_modifiers_arrow__WEBPACK_IMPORTED_MODULE_8__[\"default\"]]\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3Qtb3ZlcmxheXMvZXNtL3BvcHBlci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFBdUQ7QUFDZ0I7QUFDRTtBQUNwQjtBQUNBO0FBQ0k7QUFDYztBQUNJO0FBQ3JCO0FBQ1csQ0FBQztBQUNsRTs7QUFFTyxtQkFBbUIsK0VBQWU7QUFDekMscUJBQXFCLHlFQUFJLEVBQUUsa0ZBQWEsRUFBRSxrRkFBYSxFQUFFLG1GQUFjLEVBQUUsMkVBQU0sRUFBRSx5RUFBSSxFQUFFLG9GQUFlLEVBQUUsMEVBQUs7QUFDN0csQ0FBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzb3JyeVxcRGVza3RvcFxcc3ZwXFxmcm9udGVuZFxcbWVkaWNpbmVcXG5vZGVfbW9kdWxlc1xccmVhY3Qtb3ZlcmxheXNcXGVzbVxccG9wcGVyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBhcnJvdyBmcm9tICdAcG9wcGVyanMvY29yZS9saWIvbW9kaWZpZXJzL2Fycm93JztcbmltcG9ydCBjb21wdXRlU3R5bGVzIGZyb20gJ0Bwb3BwZXJqcy9jb3JlL2xpYi9tb2RpZmllcnMvY29tcHV0ZVN0eWxlcyc7XG5pbXBvcnQgZXZlbnRMaXN0ZW5lcnMgZnJvbSAnQHBvcHBlcmpzL2NvcmUvbGliL21vZGlmaWVycy9ldmVudExpc3RlbmVycyc7XG5pbXBvcnQgZmxpcCBmcm9tICdAcG9wcGVyanMvY29yZS9saWIvbW9kaWZpZXJzL2ZsaXAnO1xuaW1wb3J0IGhpZGUgZnJvbSAnQHBvcHBlcmpzL2NvcmUvbGliL21vZGlmaWVycy9oaWRlJztcbmltcG9ydCBvZmZzZXQgZnJvbSAnQHBvcHBlcmpzL2NvcmUvbGliL21vZGlmaWVycy9vZmZzZXQnO1xuaW1wb3J0IHBvcHBlck9mZnNldHMgZnJvbSAnQHBvcHBlcmpzL2NvcmUvbGliL21vZGlmaWVycy9wb3BwZXJPZmZzZXRzJztcbmltcG9ydCBwcmV2ZW50T3ZlcmZsb3cgZnJvbSAnQHBvcHBlcmpzL2NvcmUvbGliL21vZGlmaWVycy9wcmV2ZW50T3ZlcmZsb3cnO1xuaW1wb3J0IHsgcGxhY2VtZW50cyB9IGZyb20gJ0Bwb3BwZXJqcy9jb3JlL2xpYi9lbnVtcyc7XG5pbXBvcnQgeyBwb3BwZXJHZW5lcmF0b3IgfSBmcm9tICdAcG9wcGVyanMvY29yZS9saWIvcG9wcGVyLWJhc2UnOyAvLyBGb3IgdGhlIGNvbW1vbiBKUyBidWlsZCB3ZSB3aWxsIHR1cm4gdGhpcyBmaWxlIGludG8gYSBidW5kbGUgd2l0aCBubyBpbXBvcnRzLlxuLy8gVGhpcyBpcyBiL2MgdGhlIFBvcHBlciBsaWIgaXMgYWxsIGVzbSBmaWxlcywgYW5kIHdvdWxkIGJyZWFrIGluIGEgY29tbW9uIGpzIG9ubHkgZW52aXJvbm1lbnRcblxuZXhwb3J0IHZhciBjcmVhdGVQb3BwZXIgPSBwb3BwZXJHZW5lcmF0b3Ioe1xuICBkZWZhdWx0TW9kaWZpZXJzOiBbaGlkZSwgcG9wcGVyT2Zmc2V0cywgY29tcHV0ZVN0eWxlcywgZXZlbnRMaXN0ZW5lcnMsIG9mZnNldCwgZmxpcCwgcHJldmVudE92ZXJmbG93LCBhcnJvd11cbn0pO1xuZXhwb3J0IHsgcGxhY2VtZW50cyB9OyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-overlays/esm/popper.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-overlays/esm/safeFindDOMNode.js":
/*!************************************************************!*\
  !*** ./node_modules/react-overlays/esm/safeFindDOMNode.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ safeFindDOMNode)\n/* harmony export */ });\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction safeFindDOMNode(componentOrElement) {\n  if (componentOrElement && 'setState' in componentOrElement) {\n    return react_dom__WEBPACK_IMPORTED_MODULE_0___default().findDOMNode(componentOrElement);\n  }\n\n  return componentOrElement != null ? componentOrElement : null;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3Qtb3ZlcmxheXMvZXNtL3NhZmVGaW5kRE9NTm9kZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBaUM7QUFDbEI7QUFDZjtBQUNBLFdBQVcsNERBQW9CO0FBQy9COztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc29ycnlcXERlc2t0b3BcXHN2cFxcZnJvbnRlbmRcXG1lZGljaW5lXFxub2RlX21vZHVsZXNcXHJlYWN0LW92ZXJsYXlzXFxlc21cXHNhZmVGaW5kRE9NTm9kZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3RET00gZnJvbSAncmVhY3QtZG9tJztcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHNhZmVGaW5kRE9NTm9kZShjb21wb25lbnRPckVsZW1lbnQpIHtcbiAgaWYgKGNvbXBvbmVudE9yRWxlbWVudCAmJiAnc2V0U3RhdGUnIGluIGNvbXBvbmVudE9yRWxlbWVudCkge1xuICAgIHJldHVybiBSZWFjdERPTS5maW5kRE9NTm9kZShjb21wb25lbnRPckVsZW1lbnQpO1xuICB9XG5cbiAgcmV0dXJuIGNvbXBvbmVudE9yRWxlbWVudCAhPSBudWxsID8gY29tcG9uZW50T3JFbGVtZW50IDogbnVsbDtcbn0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-overlays/esm/safeFindDOMNode.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-overlays/esm/usePopper.js":
/*!******************************************************!*\
  !*** ./node_modules/react-overlays/esm/usePopper.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutPropertiesLoose */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _restart_hooks_useSafeState__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @restart/hooks/useSafeState */ \"(ssr)/./node_modules/@restart/hooks/esm/useSafeState.js\");\n/* harmony import */ var _popper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./popper */ \"(ssr)/./node_modules/react-overlays/esm/popper.js\");\n\n\n\n\n\n\nvar initialPopperStyles = function initialPopperStyles(position) {\n  return {\n    position: position,\n    top: '0',\n    left: '0',\n    opacity: '0',\n    pointerEvents: 'none'\n  };\n};\n\nvar disabledApplyStylesModifier = {\n  name: 'applyStyles',\n  enabled: false\n}; // In order to satisfy the current usage of options, including undefined\n\nvar ariaDescribedByModifier = {\n  name: 'ariaDescribedBy',\n  enabled: true,\n  phase: 'afterWrite',\n  effect: function effect(_ref) {\n    var state = _ref.state;\n    return function () {\n      var _state$elements = state.elements,\n          reference = _state$elements.reference,\n          popper = _state$elements.popper;\n\n      if ('removeAttribute' in reference) {\n        var ids = (reference.getAttribute('aria-describedby') || '').split(',').filter(function (id) {\n          return id.trim() !== popper.id;\n        });\n        if (!ids.length) reference.removeAttribute('aria-describedby');else reference.setAttribute('aria-describedby', ids.join(','));\n      }\n    };\n  },\n  fn: function fn(_ref2) {\n    var _popper$getAttribute;\n\n    var state = _ref2.state;\n    var _state$elements2 = state.elements,\n        popper = _state$elements2.popper,\n        reference = _state$elements2.reference;\n    var role = (_popper$getAttribute = popper.getAttribute('role')) == null ? void 0 : _popper$getAttribute.toLowerCase();\n\n    if (popper.id && role === 'tooltip' && 'setAttribute' in reference) {\n      var ids = reference.getAttribute('aria-describedby');\n\n      if (ids && ids.split(',').indexOf(popper.id) !== -1) {\n        return;\n      }\n\n      reference.setAttribute('aria-describedby', ids ? ids + \",\" + popper.id : popper.id);\n    }\n  }\n};\nvar EMPTY_MODIFIERS = [];\n/**\n * Position an element relative some reference element using Popper.js\n *\n * @param referenceElement\n * @param popperElement\n * @param {object}      options\n * @param {object=}     options.modifiers Popper.js modifiers\n * @param {boolean=}    options.enabled toggle the popper functionality on/off\n * @param {string=}     options.placement The popper element placement relative to the reference element\n * @param {string=}     options.strategy the positioning strategy\n * @param {boolean=}    options.eventsEnabled have Popper listen on window resize events to reposition the element\n * @param {function=}   options.onCreate called when the popper is created\n * @param {function=}   options.onUpdate called when the popper is updated\n *\n * @returns {UsePopperState} The popper state\n */\n\nfunction usePopper(referenceElement, popperElement, _temp) {\n  var _ref3 = _temp === void 0 ? {} : _temp,\n      _ref3$enabled = _ref3.enabled,\n      enabled = _ref3$enabled === void 0 ? true : _ref3$enabled,\n      _ref3$placement = _ref3.placement,\n      placement = _ref3$placement === void 0 ? 'bottom' : _ref3$placement,\n      _ref3$strategy = _ref3.strategy,\n      strategy = _ref3$strategy === void 0 ? 'absolute' : _ref3$strategy,\n      _ref3$modifiers = _ref3.modifiers,\n      modifiers = _ref3$modifiers === void 0 ? EMPTY_MODIFIERS : _ref3$modifiers,\n      config = (0,_babel_runtime_helpers_esm_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_ref3, [\"enabled\", \"placement\", \"strategy\", \"modifiers\"]);\n\n  var popperInstanceRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)();\n  var update = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {\n    var _popperInstanceRef$cu;\n\n    (_popperInstanceRef$cu = popperInstanceRef.current) == null ? void 0 : _popperInstanceRef$cu.update();\n  }, []);\n  var forceUpdate = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {\n    var _popperInstanceRef$cu2;\n\n    (_popperInstanceRef$cu2 = popperInstanceRef.current) == null ? void 0 : _popperInstanceRef$cu2.forceUpdate();\n  }, []);\n\n  var _useSafeState = (0,_restart_hooks_useSafeState__WEBPACK_IMPORTED_MODULE_3__[\"default\"])((0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n    placement: placement,\n    update: update,\n    forceUpdate: forceUpdate,\n    attributes: {},\n    styles: {\n      popper: initialPopperStyles(strategy),\n      arrow: {}\n    }\n  })),\n      popperState = _useSafeState[0],\n      setState = _useSafeState[1];\n\n  var updateModifier = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(function () {\n    return {\n      name: 'updateStateModifier',\n      enabled: true,\n      phase: 'write',\n      requires: ['computeStyles'],\n      fn: function fn(_ref4) {\n        var state = _ref4.state;\n        var styles = {};\n        var attributes = {};\n        Object.keys(state.elements).forEach(function (element) {\n          styles[element] = state.styles[element];\n          attributes[element] = state.attributes[element];\n        });\n        setState({\n          state: state,\n          styles: styles,\n          attributes: attributes,\n          update: update,\n          forceUpdate: forceUpdate,\n          placement: state.placement\n        });\n      }\n    };\n  }, [update, forceUpdate, setState]);\n  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {\n    if (!popperInstanceRef.current || !enabled) return;\n    popperInstanceRef.current.setOptions({\n      placement: placement,\n      strategy: strategy,\n      modifiers: [].concat(modifiers, [updateModifier, disabledApplyStylesModifier])\n    }); // intentionally NOT re-running on new modifiers\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [strategy, placement, updateModifier, enabled]);\n  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {\n    if (!enabled || referenceElement == null || popperElement == null) {\n      return undefined;\n    }\n\n    popperInstanceRef.current = (0,_popper__WEBPACK_IMPORTED_MODULE_4__.createPopper)(referenceElement, popperElement, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, config, {\n      placement: placement,\n      strategy: strategy,\n      modifiers: [].concat(modifiers, [ariaDescribedByModifier, updateModifier])\n    }));\n    return function () {\n      if (popperInstanceRef.current != null) {\n        popperInstanceRef.current.destroy();\n        popperInstanceRef.current = undefined;\n        setState(function (s) {\n          return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, s, {\n            attributes: {},\n            styles: {\n              popper: initialPopperStyles(strategy)\n            }\n          });\n        });\n      }\n    }; // This is only run once to _create_ the popper\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [enabled, referenceElement, popperElement]);\n  return popperState;\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (usePopper);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3Qtb3ZlcmxheXMvZXNtL3VzZVBvcHBlci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQTBEO0FBQzBDO0FBQzFCO0FBQ25CO0FBQ2Y7O0FBRXhDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVCx1RUFBdUU7QUFDdkU7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLGFBQWE7QUFDeEIsV0FBVyxhQUFhO0FBQ3hCLFdBQVcsYUFBYTtBQUN4QixXQUFXLGFBQWE7QUFDeEIsV0FBVyxhQUFhO0FBQ3hCLFdBQVcsYUFBYTtBQUN4QixXQUFXLGFBQWE7QUFDeEIsV0FBVyxhQUFhO0FBQ3hCO0FBQ0EsYUFBYSxnQkFBZ0I7QUFDN0I7O0FBRUE7QUFDQSxvQ0FBb0M7QUFDcEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsbUdBQTZCOztBQUU1QywwQkFBMEIsNkNBQU07QUFDaEMsZUFBZSxrREFBVztBQUMxQjs7QUFFQTtBQUNBLEdBQUc7QUFDSCxvQkFBb0Isa0RBQVc7QUFDL0I7O0FBRUE7QUFDQSxHQUFHOztBQUVILHNCQUFzQix1RUFBWSxDQUFDLCtDQUFRO0FBQzNDO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQjtBQUNsQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBOztBQUVBLHVCQUF1Qiw4Q0FBTztBQUM5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLEdBQUc7QUFDSCxFQUFFLGdEQUFTO0FBQ1g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUssR0FBRztBQUNSO0FBQ0EsR0FBRztBQUNILEVBQUUsZ0RBQVM7QUFDWDtBQUNBO0FBQ0E7O0FBRUEsZ0NBQWdDLHFEQUFZLGtDQUFrQyw4RUFBUSxHQUFHO0FBQ3pGO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCLDhFQUFRLEdBQUc7QUFDNUIsMEJBQTBCO0FBQzFCO0FBQ0E7QUFDQTtBQUNBLFdBQVc7QUFDWCxTQUFTO0FBQ1Q7QUFDQSxPQUFPO0FBQ1A7QUFDQSxHQUFHO0FBQ0g7QUFDQTs7QUFFQSxpRUFBZSxTQUFTIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNvcnJ5XFxEZXNrdG9wXFxzdnBcXGZyb250ZW5kXFxtZWRpY2luZVxcbm9kZV9tb2R1bGVzXFxyZWFjdC1vdmVybGF5c1xcZXNtXFx1c2VQb3BwZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IF9leHRlbmRzIGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9leHRlbmRzXCI7XG5pbXBvcnQgX29iamVjdFdpdGhvdXRQcm9wZXJ0aWVzTG9vc2UgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL29iamVjdFdpdGhvdXRQcm9wZXJ0aWVzTG9vc2VcIjtcbmltcG9ydCB7IHVzZUNhbGxiYWNrLCB1c2VFZmZlY3QsIHVzZU1lbW8sIHVzZVJlZiwgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgdXNlU2FmZVN0YXRlIGZyb20gJ0ByZXN0YXJ0L2hvb2tzL3VzZVNhZmVTdGF0ZSc7XG5pbXBvcnQgeyBjcmVhdGVQb3BwZXIgfSBmcm9tICcuL3BvcHBlcic7XG5cbnZhciBpbml0aWFsUG9wcGVyU3R5bGVzID0gZnVuY3Rpb24gaW5pdGlhbFBvcHBlclN0eWxlcyhwb3NpdGlvbikge1xuICByZXR1cm4ge1xuICAgIHBvc2l0aW9uOiBwb3NpdGlvbixcbiAgICB0b3A6ICcwJyxcbiAgICBsZWZ0OiAnMCcsXG4gICAgb3BhY2l0eTogJzAnLFxuICAgIHBvaW50ZXJFdmVudHM6ICdub25lJ1xuICB9O1xufTtcblxudmFyIGRpc2FibGVkQXBwbHlTdHlsZXNNb2RpZmllciA9IHtcbiAgbmFtZTogJ2FwcGx5U3R5bGVzJyxcbiAgZW5hYmxlZDogZmFsc2Vcbn07IC8vIEluIG9yZGVyIHRvIHNhdGlzZnkgdGhlIGN1cnJlbnQgdXNhZ2Ugb2Ygb3B0aW9ucywgaW5jbHVkaW5nIHVuZGVmaW5lZFxuXG52YXIgYXJpYURlc2NyaWJlZEJ5TW9kaWZpZXIgPSB7XG4gIG5hbWU6ICdhcmlhRGVzY3JpYmVkQnknLFxuICBlbmFibGVkOiB0cnVlLFxuICBwaGFzZTogJ2FmdGVyV3JpdGUnLFxuICBlZmZlY3Q6IGZ1bmN0aW9uIGVmZmVjdChfcmVmKSB7XG4gICAgdmFyIHN0YXRlID0gX3JlZi5zdGF0ZTtcbiAgICByZXR1cm4gZnVuY3Rpb24gKCkge1xuICAgICAgdmFyIF9zdGF0ZSRlbGVtZW50cyA9IHN0YXRlLmVsZW1lbnRzLFxuICAgICAgICAgIHJlZmVyZW5jZSA9IF9zdGF0ZSRlbGVtZW50cy5yZWZlcmVuY2UsXG4gICAgICAgICAgcG9wcGVyID0gX3N0YXRlJGVsZW1lbnRzLnBvcHBlcjtcblxuICAgICAgaWYgKCdyZW1vdmVBdHRyaWJ1dGUnIGluIHJlZmVyZW5jZSkge1xuICAgICAgICB2YXIgaWRzID0gKHJlZmVyZW5jZS5nZXRBdHRyaWJ1dGUoJ2FyaWEtZGVzY3JpYmVkYnknKSB8fCAnJykuc3BsaXQoJywnKS5maWx0ZXIoZnVuY3Rpb24gKGlkKSB7XG4gICAgICAgICAgcmV0dXJuIGlkLnRyaW0oKSAhPT0gcG9wcGVyLmlkO1xuICAgICAgICB9KTtcbiAgICAgICAgaWYgKCFpZHMubGVuZ3RoKSByZWZlcmVuY2UucmVtb3ZlQXR0cmlidXRlKCdhcmlhLWRlc2NyaWJlZGJ5Jyk7ZWxzZSByZWZlcmVuY2Uuc2V0QXR0cmlidXRlKCdhcmlhLWRlc2NyaWJlZGJ5JywgaWRzLmpvaW4oJywnKSk7XG4gICAgICB9XG4gICAgfTtcbiAgfSxcbiAgZm46IGZ1bmN0aW9uIGZuKF9yZWYyKSB7XG4gICAgdmFyIF9wb3BwZXIkZ2V0QXR0cmlidXRlO1xuXG4gICAgdmFyIHN0YXRlID0gX3JlZjIuc3RhdGU7XG4gICAgdmFyIF9zdGF0ZSRlbGVtZW50czIgPSBzdGF0ZS5lbGVtZW50cyxcbiAgICAgICAgcG9wcGVyID0gX3N0YXRlJGVsZW1lbnRzMi5wb3BwZXIsXG4gICAgICAgIHJlZmVyZW5jZSA9IF9zdGF0ZSRlbGVtZW50czIucmVmZXJlbmNlO1xuICAgIHZhciByb2xlID0gKF9wb3BwZXIkZ2V0QXR0cmlidXRlID0gcG9wcGVyLmdldEF0dHJpYnV0ZSgncm9sZScpKSA9PSBudWxsID8gdm9pZCAwIDogX3BvcHBlciRnZXRBdHRyaWJ1dGUudG9Mb3dlckNhc2UoKTtcblxuICAgIGlmIChwb3BwZXIuaWQgJiYgcm9sZSA9PT0gJ3Rvb2x0aXAnICYmICdzZXRBdHRyaWJ1dGUnIGluIHJlZmVyZW5jZSkge1xuICAgICAgdmFyIGlkcyA9IHJlZmVyZW5jZS5nZXRBdHRyaWJ1dGUoJ2FyaWEtZGVzY3JpYmVkYnknKTtcblxuICAgICAgaWYgKGlkcyAmJiBpZHMuc3BsaXQoJywnKS5pbmRleE9mKHBvcHBlci5pZCkgIT09IC0xKSB7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgcmVmZXJlbmNlLnNldEF0dHJpYnV0ZSgnYXJpYS1kZXNjcmliZWRieScsIGlkcyA/IGlkcyArIFwiLFwiICsgcG9wcGVyLmlkIDogcG9wcGVyLmlkKTtcbiAgICB9XG4gIH1cbn07XG52YXIgRU1QVFlfTU9ESUZJRVJTID0gW107XG4vKipcbiAqIFBvc2l0aW9uIGFuIGVsZW1lbnQgcmVsYXRpdmUgc29tZSByZWZlcmVuY2UgZWxlbWVudCB1c2luZyBQb3BwZXIuanNcbiAqXG4gKiBAcGFyYW0gcmVmZXJlbmNlRWxlbWVudFxuICogQHBhcmFtIHBvcHBlckVsZW1lbnRcbiAqIEBwYXJhbSB7b2JqZWN0fSAgICAgIG9wdGlvbnNcbiAqIEBwYXJhbSB7b2JqZWN0PX0gICAgIG9wdGlvbnMubW9kaWZpZXJzIFBvcHBlci5qcyBtb2RpZmllcnNcbiAqIEBwYXJhbSB7Ym9vbGVhbj19ICAgIG9wdGlvbnMuZW5hYmxlZCB0b2dnbGUgdGhlIHBvcHBlciBmdW5jdGlvbmFsaXR5IG9uL29mZlxuICogQHBhcmFtIHtzdHJpbmc9fSAgICAgb3B0aW9ucy5wbGFjZW1lbnQgVGhlIHBvcHBlciBlbGVtZW50IHBsYWNlbWVudCByZWxhdGl2ZSB0byB0aGUgcmVmZXJlbmNlIGVsZW1lbnRcbiAqIEBwYXJhbSB7c3RyaW5nPX0gICAgIG9wdGlvbnMuc3RyYXRlZ3kgdGhlIHBvc2l0aW9uaW5nIHN0cmF0ZWd5XG4gKiBAcGFyYW0ge2Jvb2xlYW49fSAgICBvcHRpb25zLmV2ZW50c0VuYWJsZWQgaGF2ZSBQb3BwZXIgbGlzdGVuIG9uIHdpbmRvdyByZXNpemUgZXZlbnRzIHRvIHJlcG9zaXRpb24gdGhlIGVsZW1lbnRcbiAqIEBwYXJhbSB7ZnVuY3Rpb249fSAgIG9wdGlvbnMub25DcmVhdGUgY2FsbGVkIHdoZW4gdGhlIHBvcHBlciBpcyBjcmVhdGVkXG4gKiBAcGFyYW0ge2Z1bmN0aW9uPX0gICBvcHRpb25zLm9uVXBkYXRlIGNhbGxlZCB3aGVuIHRoZSBwb3BwZXIgaXMgdXBkYXRlZFxuICpcbiAqIEByZXR1cm5zIHtVc2VQb3BwZXJTdGF0ZX0gVGhlIHBvcHBlciBzdGF0ZVxuICovXG5cbmZ1bmN0aW9uIHVzZVBvcHBlcihyZWZlcmVuY2VFbGVtZW50LCBwb3BwZXJFbGVtZW50LCBfdGVtcCkge1xuICB2YXIgX3JlZjMgPSBfdGVtcCA9PT0gdm9pZCAwID8ge30gOiBfdGVtcCxcbiAgICAgIF9yZWYzJGVuYWJsZWQgPSBfcmVmMy5lbmFibGVkLFxuICAgICAgZW5hYmxlZCA9IF9yZWYzJGVuYWJsZWQgPT09IHZvaWQgMCA/IHRydWUgOiBfcmVmMyRlbmFibGVkLFxuICAgICAgX3JlZjMkcGxhY2VtZW50ID0gX3JlZjMucGxhY2VtZW50LFxuICAgICAgcGxhY2VtZW50ID0gX3JlZjMkcGxhY2VtZW50ID09PSB2b2lkIDAgPyAnYm90dG9tJyA6IF9yZWYzJHBsYWNlbWVudCxcbiAgICAgIF9yZWYzJHN0cmF0ZWd5ID0gX3JlZjMuc3RyYXRlZ3ksXG4gICAgICBzdHJhdGVneSA9IF9yZWYzJHN0cmF0ZWd5ID09PSB2b2lkIDAgPyAnYWJzb2x1dGUnIDogX3JlZjMkc3RyYXRlZ3ksXG4gICAgICBfcmVmMyRtb2RpZmllcnMgPSBfcmVmMy5tb2RpZmllcnMsXG4gICAgICBtb2RpZmllcnMgPSBfcmVmMyRtb2RpZmllcnMgPT09IHZvaWQgMCA/IEVNUFRZX01PRElGSUVSUyA6IF9yZWYzJG1vZGlmaWVycyxcbiAgICAgIGNvbmZpZyA9IF9vYmplY3RXaXRob3V0UHJvcGVydGllc0xvb3NlKF9yZWYzLCBbXCJlbmFibGVkXCIsIFwicGxhY2VtZW50XCIsIFwic3RyYXRlZ3lcIiwgXCJtb2RpZmllcnNcIl0pO1xuXG4gIHZhciBwb3BwZXJJbnN0YW5jZVJlZiA9IHVzZVJlZigpO1xuICB2YXIgdXBkYXRlID0gdXNlQ2FsbGJhY2soZnVuY3Rpb24gKCkge1xuICAgIHZhciBfcG9wcGVySW5zdGFuY2VSZWYkY3U7XG5cbiAgICAoX3BvcHBlckluc3RhbmNlUmVmJGN1ID0gcG9wcGVySW5zdGFuY2VSZWYuY3VycmVudCkgPT0gbnVsbCA/IHZvaWQgMCA6IF9wb3BwZXJJbnN0YW5jZVJlZiRjdS51cGRhdGUoKTtcbiAgfSwgW10pO1xuICB2YXIgZm9yY2VVcGRhdGUgPSB1c2VDYWxsYmFjayhmdW5jdGlvbiAoKSB7XG4gICAgdmFyIF9wb3BwZXJJbnN0YW5jZVJlZiRjdTI7XG5cbiAgICAoX3BvcHBlckluc3RhbmNlUmVmJGN1MiA9IHBvcHBlckluc3RhbmNlUmVmLmN1cnJlbnQpID09IG51bGwgPyB2b2lkIDAgOiBfcG9wcGVySW5zdGFuY2VSZWYkY3UyLmZvcmNlVXBkYXRlKCk7XG4gIH0sIFtdKTtcblxuICB2YXIgX3VzZVNhZmVTdGF0ZSA9IHVzZVNhZmVTdGF0ZSh1c2VTdGF0ZSh7XG4gICAgcGxhY2VtZW50OiBwbGFjZW1lbnQsXG4gICAgdXBkYXRlOiB1cGRhdGUsXG4gICAgZm9yY2VVcGRhdGU6IGZvcmNlVXBkYXRlLFxuICAgIGF0dHJpYnV0ZXM6IHt9LFxuICAgIHN0eWxlczoge1xuICAgICAgcG9wcGVyOiBpbml0aWFsUG9wcGVyU3R5bGVzKHN0cmF0ZWd5KSxcbiAgICAgIGFycm93OiB7fVxuICAgIH1cbiAgfSkpLFxuICAgICAgcG9wcGVyU3RhdGUgPSBfdXNlU2FmZVN0YXRlWzBdLFxuICAgICAgc2V0U3RhdGUgPSBfdXNlU2FmZVN0YXRlWzFdO1xuXG4gIHZhciB1cGRhdGVNb2RpZmllciA9IHVzZU1lbW8oZnVuY3Rpb24gKCkge1xuICAgIHJldHVybiB7XG4gICAgICBuYW1lOiAndXBkYXRlU3RhdGVNb2RpZmllcicsXG4gICAgICBlbmFibGVkOiB0cnVlLFxuICAgICAgcGhhc2U6ICd3cml0ZScsXG4gICAgICByZXF1aXJlczogWydjb21wdXRlU3R5bGVzJ10sXG4gICAgICBmbjogZnVuY3Rpb24gZm4oX3JlZjQpIHtcbiAgICAgICAgdmFyIHN0YXRlID0gX3JlZjQuc3RhdGU7XG4gICAgICAgIHZhciBzdHlsZXMgPSB7fTtcbiAgICAgICAgdmFyIGF0dHJpYnV0ZXMgPSB7fTtcbiAgICAgICAgT2JqZWN0LmtleXMoc3RhdGUuZWxlbWVudHMpLmZvckVhY2goZnVuY3Rpb24gKGVsZW1lbnQpIHtcbiAgICAgICAgICBzdHlsZXNbZWxlbWVudF0gPSBzdGF0ZS5zdHlsZXNbZWxlbWVudF07XG4gICAgICAgICAgYXR0cmlidXRlc1tlbGVtZW50XSA9IHN0YXRlLmF0dHJpYnV0ZXNbZWxlbWVudF07XG4gICAgICAgIH0pO1xuICAgICAgICBzZXRTdGF0ZSh7XG4gICAgICAgICAgc3RhdGU6IHN0YXRlLFxuICAgICAgICAgIHN0eWxlczogc3R5bGVzLFxuICAgICAgICAgIGF0dHJpYnV0ZXM6IGF0dHJpYnV0ZXMsXG4gICAgICAgICAgdXBkYXRlOiB1cGRhdGUsXG4gICAgICAgICAgZm9yY2VVcGRhdGU6IGZvcmNlVXBkYXRlLFxuICAgICAgICAgIHBsYWNlbWVudDogc3RhdGUucGxhY2VtZW50XG4gICAgICAgIH0pO1xuICAgICAgfVxuICAgIH07XG4gIH0sIFt1cGRhdGUsIGZvcmNlVXBkYXRlLCBzZXRTdGF0ZV0pO1xuICB1c2VFZmZlY3QoZnVuY3Rpb24gKCkge1xuICAgIGlmICghcG9wcGVySW5zdGFuY2VSZWYuY3VycmVudCB8fCAhZW5hYmxlZCkgcmV0dXJuO1xuICAgIHBvcHBlckluc3RhbmNlUmVmLmN1cnJlbnQuc2V0T3B0aW9ucyh7XG4gICAgICBwbGFjZW1lbnQ6IHBsYWNlbWVudCxcbiAgICAgIHN0cmF0ZWd5OiBzdHJhdGVneSxcbiAgICAgIG1vZGlmaWVyczogW10uY29uY2F0KG1vZGlmaWVycywgW3VwZGF0ZU1vZGlmaWVyLCBkaXNhYmxlZEFwcGx5U3R5bGVzTW9kaWZpZXJdKVxuICAgIH0pOyAvLyBpbnRlbnRpb25hbGx5IE5PVCByZS1ydW5uaW5nIG9uIG5ldyBtb2RpZmllcnNcbiAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgcmVhY3QtaG9va3MvZXhoYXVzdGl2ZS1kZXBzXG4gIH0sIFtzdHJhdGVneSwgcGxhY2VtZW50LCB1cGRhdGVNb2RpZmllciwgZW5hYmxlZF0pO1xuICB1c2VFZmZlY3QoZnVuY3Rpb24gKCkge1xuICAgIGlmICghZW5hYmxlZCB8fCByZWZlcmVuY2VFbGVtZW50ID09IG51bGwgfHwgcG9wcGVyRWxlbWVudCA9PSBudWxsKSB7XG4gICAgICByZXR1cm4gdW5kZWZpbmVkO1xuICAgIH1cblxuICAgIHBvcHBlckluc3RhbmNlUmVmLmN1cnJlbnQgPSBjcmVhdGVQb3BwZXIocmVmZXJlbmNlRWxlbWVudCwgcG9wcGVyRWxlbWVudCwgX2V4dGVuZHMoe30sIGNvbmZpZywge1xuICAgICAgcGxhY2VtZW50OiBwbGFjZW1lbnQsXG4gICAgICBzdHJhdGVneTogc3RyYXRlZ3ksXG4gICAgICBtb2RpZmllcnM6IFtdLmNvbmNhdChtb2RpZmllcnMsIFthcmlhRGVzY3JpYmVkQnlNb2RpZmllciwgdXBkYXRlTW9kaWZpZXJdKVxuICAgIH0pKTtcbiAgICByZXR1cm4gZnVuY3Rpb24gKCkge1xuICAgICAgaWYgKHBvcHBlckluc3RhbmNlUmVmLmN1cnJlbnQgIT0gbnVsbCkge1xuICAgICAgICBwb3BwZXJJbnN0YW5jZVJlZi5jdXJyZW50LmRlc3Ryb3koKTtcbiAgICAgICAgcG9wcGVySW5zdGFuY2VSZWYuY3VycmVudCA9IHVuZGVmaW5lZDtcbiAgICAgICAgc2V0U3RhdGUoZnVuY3Rpb24gKHMpIHtcbiAgICAgICAgICByZXR1cm4gX2V4dGVuZHMoe30sIHMsIHtcbiAgICAgICAgICAgIGF0dHJpYnV0ZXM6IHt9LFxuICAgICAgICAgICAgc3R5bGVzOiB7XG4gICAgICAgICAgICAgIHBvcHBlcjogaW5pdGlhbFBvcHBlclN0eWxlcyhzdHJhdGVneSlcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9KTtcbiAgICAgICAgfSk7XG4gICAgICB9XG4gICAgfTsgLy8gVGhpcyBpcyBvbmx5IHJ1biBvbmNlIHRvIF9jcmVhdGVfIHRoZSBwb3BwZXJcbiAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgcmVhY3QtaG9va3MvZXhoYXVzdGl2ZS1kZXBzXG4gIH0sIFtlbmFibGVkLCByZWZlcmVuY2VFbGVtZW50LCBwb3BwZXJFbGVtZW50XSk7XG4gIHJldHVybiBwb3BwZXJTdGF0ZTtcbn1cblxuZXhwb3J0IGRlZmF1bHQgdXNlUG9wcGVyOyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-overlays/esm/usePopper.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-overlays/esm/useRootClose.js":
/*!*********************************************************!*\
  !*** ./node_modules/react-overlays/esm/useRootClose.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var dom_helpers_contains__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! dom-helpers/contains */ \"(ssr)/./node_modules/dom-helpers/esm/contains.js\");\n/* harmony import */ var dom_helpers_listen__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! dom-helpers/listen */ \"(ssr)/./node_modules/dom-helpers/esm/listen.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _restart_hooks_useEventCallback__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @restart/hooks/useEventCallback */ \"(ssr)/./node_modules/@restart/hooks/esm/useEventCallback.js\");\n/* harmony import */ var warning__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! warning */ \"(ssr)/./node_modules/warning/warning.js\");\n/* harmony import */ var warning__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(warning__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _ownerDocument__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ownerDocument */ \"(ssr)/./node_modules/react-overlays/esm/ownerDocument.js\");\n\n\n\n\n\n\nvar escapeKeyCode = 27;\n\nvar noop = function noop() {};\n\nfunction isLeftClickEvent(event) {\n  return event.button === 0;\n}\n\nfunction isModifiedEvent(event) {\n  return !!(event.metaKey || event.altKey || event.ctrlKey || event.shiftKey);\n}\n\nvar getRefTarget = function getRefTarget(ref) {\n  return ref && ('current' in ref ? ref.current : ref);\n};\n\n/**\n * The `useRootClose` hook registers your callback on the document\n * when rendered. Powers the `<Overlay/>` component. This is used achieve modal\n * style behavior where your callback is triggered when the user tries to\n * interact with the rest of the document or hits the `esc` key.\n *\n * @param {Ref<HTMLElement>| HTMLElement} ref  The element boundary\n * @param {function} onRootClose\n * @param {object=}  options\n * @param {boolean=} options.disabled\n * @param {string=}  options.clickTrigger The DOM event name (click, mousedown, etc) to attach listeners on\n */\nfunction useRootClose(ref, onRootClose, _temp) {\n  var _ref = _temp === void 0 ? {} : _temp,\n      disabled = _ref.disabled,\n      _ref$clickTrigger = _ref.clickTrigger,\n      clickTrigger = _ref$clickTrigger === void 0 ? 'click' : _ref$clickTrigger;\n\n  var preventMouseRootCloseRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(false);\n  var onClose = onRootClose || noop;\n  var handleMouseCapture = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (e) {\n    var _e$composedPath$;\n\n    var currentTarget = getRefTarget(ref);\n    warning__WEBPACK_IMPORTED_MODULE_4___default()(!!currentTarget, 'RootClose captured a close event but does not have a ref to compare it to. ' + 'useRootClose(), should be passed a ref that resolves to a DOM node');\n    preventMouseRootCloseRef.current = !currentTarget || isModifiedEvent(e) || !isLeftClickEvent(e) || !!(0,dom_helpers_contains__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(currentTarget, (_e$composedPath$ = e.composedPath == null ? void 0 : e.composedPath()[0]) != null ? _e$composedPath$ : e.target);\n  }, [ref]);\n  var handleMouse = (0,_restart_hooks_useEventCallback__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(function (e) {\n    if (!preventMouseRootCloseRef.current) {\n      onClose(e);\n    }\n  });\n  var handleKeyUp = (0,_restart_hooks_useEventCallback__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(function (e) {\n    if (e.keyCode === escapeKeyCode) {\n      onClose(e);\n    }\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {\n    if (disabled || ref == null) return undefined; // Store the current event to avoid triggering handlers immediately\n    // https://github.com/facebook/react/issues/20074\n\n    var currentEvent = window.event;\n    var doc = (0,_ownerDocument__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(getRefTarget(ref)); // Use capture for this listener so it fires before React's listener, to\n    // avoid false positives in the contains() check below if the target DOM\n    // element is removed in the React mouse callback.\n\n    var removeMouseCaptureListener = (0,dom_helpers_listen__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(doc, clickTrigger, handleMouseCapture, true);\n    var removeMouseListener = (0,dom_helpers_listen__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(doc, clickTrigger, function (e) {\n      // skip if this event is the same as the one running when we added the handlers\n      if (e === currentEvent) {\n        currentEvent = undefined;\n        return;\n      }\n\n      handleMouse(e);\n    });\n    var removeKeyupListener = (0,dom_helpers_listen__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(doc, 'keyup', function (e) {\n      // skip if this event is the same as the one running when we added the handlers\n      if (e === currentEvent) {\n        currentEvent = undefined;\n        return;\n      }\n\n      handleKeyUp(e);\n    });\n    var mobileSafariHackListeners = [];\n\n    if ('ontouchstart' in doc.documentElement) {\n      mobileSafariHackListeners = [].slice.call(doc.body.children).map(function (el) {\n        return (0,dom_helpers_listen__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(el, 'mousemove', noop);\n      });\n    }\n\n    return function () {\n      removeMouseCaptureListener();\n      removeMouseListener();\n      removeKeyupListener();\n      mobileSafariHackListeners.forEach(function (remove) {\n        return remove();\n      });\n    };\n  }, [ref, disabled, clickTrigger, handleMouseCapture, handleMouse, handleKeyUp]);\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useRootClose);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-overlays/esm/useRootClose.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-overlays/esm/useWaitForDOMRef.js":
/*!*************************************************************!*\
  !*** ./node_modules/react-overlays/esm/useWaitForDOMRef.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useWaitForDOMRef),\n/* harmony export */   resolveContainerRef: () => (/* binding */ resolveContainerRef)\n/* harmony export */ });\n/* harmony import */ var dom_helpers_ownerDocument__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! dom-helpers/ownerDocument */ \"(ssr)/./node_modules/dom-helpers/esm/ownerDocument.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nvar resolveContainerRef = function resolveContainerRef(ref) {\n  var _ref;\n\n  if (typeof document === 'undefined') return null;\n  if (ref == null) return (0,dom_helpers_ownerDocument__WEBPACK_IMPORTED_MODULE_0__[\"default\"])().body;\n  if (typeof ref === 'function') ref = ref();\n  if (ref && 'current' in ref) ref = ref.current;\n  if ((_ref = ref) != null && _ref.nodeType) return ref || null;\n  return null;\n};\nfunction useWaitForDOMRef(ref, onResolved) {\n  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(function () {\n    return resolveContainerRef(ref);\n  }),\n      resolvedRef = _useState[0],\n      setRef = _useState[1];\n\n  if (!resolvedRef) {\n    var earlyRef = resolveContainerRef(ref);\n    if (earlyRef) setRef(earlyRef);\n  }\n\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n    if (onResolved && resolvedRef) {\n      onResolved(resolvedRef);\n    }\n  }, [onResolved, resolvedRef]);\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n    var nextRef = resolveContainerRef(ref);\n\n    if (nextRef !== resolvedRef) {\n      setRef(nextRef);\n    }\n  }, [ref, resolvedRef]);\n  return resolvedRef;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-overlays/esm/useWaitForDOMRef.js\n");

/***/ })

};
;