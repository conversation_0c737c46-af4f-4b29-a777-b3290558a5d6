import logging
from datetime import timed<PERSON><PERSON>
from decimal import Decimal

from django.utils import timezone
from rest_framework import status, serializers
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated

from users.models import User, DoctorLicense, SubscriptionUpgrade

logger = logging.getLogger(__name__)


class SubscriptionUpgradeSerializer(serializers.Serializer):
    user_id = serializers.UUIDField()
    new_subscription_period = serializers.CharField(required=True)
    transaction_id = serializers.CharField(required=False, allow_blank=True)
    payment_method = serializers.CharField(required=False, allow_blank=True)
    payment_status = serializers.CharField(required=False, allow_blank=True)
    
    def validate(self, data):
        # Verify that the user exists and is a doctor
        try:
            user = User.objects.get(id=data['user_id'], user_type='doctor')
        except User.DoesNotExist:
            raise serializers.ValidationError("Doctor not found")
        
        # Verify that the user has a license
        try:
            license_obj = DoctorLicense.objects.get(user=user)
        except Doctor<PERSON>ice<PERSON>.DoesNotExist:
            raise serializers.ValidationError("No license found for this doctor")
        
        # Verify that we are upgrading from 6 months to 1 year
        if license_obj.subscription_period != '6months' or data['new_subscription_period'] != '1year':
            raise serializers.ValidationError("This endpoint only supports upgrading from 6 months to 1 year")
        
        return data


class CalculateUpgradeView(APIView):
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        serializer = SubscriptionUpgradeSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
        validated_data = serializer.validated_data
        user_id = validated_data['user_id']
        
        # Get the user and license
        user = User.objects.get(id=user_id)
        license_obj = DoctorLicense.objects.get(user=user)
        
        # Calculate prices based on the package
        old_price = Decimal(str(license_obj.payment_amount)) if license_obj.payment_amount else Decimal('0')
        
        # Determine the new price based on the package
        if hasattr(user, 'package_name') and user.package_name:
            if 'premium' in user.package_name.lower():
                new_price = Decimal('499')
            elif 'standard' in user.package_name.lower():
                new_price = Decimal('399')
            else:  # basic
                new_price = Decimal('299')
        else:
            # Default to standard price if package name is not available
            new_price = Decimal('399')
        
        # Calculate the number of days already used in the 6-month subscription
        subscription_start_date = license_obj.created_at.date()
        today = timezone.now().date()
        days_used = (today - subscription_start_date).days
        
        # Ensure days_used is positive and doesn't exceed 180 days (6 months)
        days_used = max(0, min(days_used, 180))
        
        # Calculate the number of days in the new subscription (365 - days_used)
        days_in_new_subscription = 365 - days_used
        
        # Calculate the prorated price for the annual subscription
        prorated_annual_price = new_price * (Decimal(days_in_new_subscription) / Decimal('365'))
        
        # Round to the nearest dollar
        upgrade_price = round(prorated_annual_price, 0)
        
        # Calculate the new expiry date (1 year from the original start date)
        new_expiry_date = subscription_start_date + timedelta(days=365)
        
        # Prepare the response
        response_data = {
            "old_subscription": {
                "period": "6months",
                "start_date": subscription_start_date.isoformat(),
                "days_used": days_used,
                "price": float(old_price)
            },
            "new_subscription": {
                "period": "1year",
                "expiry_date": new_expiry_date.isoformat(),
                "days_in_new_subscription": days_in_new_subscription,
                "full_price": float(new_price),
                "prorated_price": float(prorated_annual_price),
                "upgrade_price": float(upgrade_price)
            }
        }
        
        return Response(response_data, status=status.HTTP_200_OK)


class UpgradeSubscriptionView(APIView):
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        serializer = SubscriptionUpgradeSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
        validated_data = serializer.validated_data
        user_id = validated_data['user_id']
        new_subscription_period = validated_data['new_subscription_period']
        
        # Get the user and license
        user = User.objects.get(id=user_id)
        license_obj = DoctorLicense.objects.get(user=user)
        
        # Calculate prices based on the package
        old_price = Decimal(str(license_obj.payment_amount)) if license_obj.payment_amount else Decimal('0')
        
        # Determine the new price based on the package
        if hasattr(user, 'package_name') and user.package_name:
            if 'premium' in user.package_name.lower():
                new_price = Decimal('499')
            elif 'standard' in user.package_name.lower():
                new_price = Decimal('399')
            else:  # basic
                new_price = Decimal('299')
        else:
            # Default to standard price if package name is not available
            new_price = Decimal('399')
        
        # Calculate the number of days already used in the 6-month subscription
        subscription_start_date = license_obj.created_at.date()
        today = timezone.now().date()
        days_used = (today - subscription_start_date).days
        
        # Ensure days_used is positive and doesn't exceed 180 days (6 months)
        days_used = max(0, min(days_used, 180))
        
        # Calculate the number of days in the new subscription (365 - days_used)
        days_in_new_subscription = 365 - days_used
        
        # Calculate the prorated price for the annual subscription
        prorated_annual_price = new_price * (Decimal(days_in_new_subscription) / Decimal('365'))
        
        # Round to the nearest dollar
        upgrade_price = round(prorated_annual_price, 0)
        
        # Calculate the new expiry date (1 year from the original start date)
        new_expiry_date = subscription_start_date + timedelta(days=365)
        
        # Create an upgrade record
        upgrade = SubscriptionUpgrade.objects.create(
            user=user,
            license=license_obj,
            old_subscription_period=license_obj.subscription_period,
            new_subscription_period=new_subscription_period,
            days_used=days_used,
            days_in_new_subscription=days_in_new_subscription,
            old_price=old_price,
            new_price=new_price,
            upgrade_price=upgrade_price,
            transaction_id=validated_data.get('transaction_id', ''),
            payment_method=validated_data.get('payment_method', ''),
            payment_status=validated_data.get('payment_status', '')
        )
        
        # Update the license
        license_obj.subscription_period = new_subscription_period
        license_obj.expiry_date = new_expiry_date
        
        # Update payment information if provided
        if 'transaction_id' in validated_data and validated_data['transaction_id']:
            license_obj.transaction_id = validated_data['transaction_id']
        if 'payment_method' in validated_data and validated_data['payment_method']:
            license_obj.payment_method = validated_data['payment_method']
        if 'payment_status' in validated_data and validated_data['payment_status']:
            license_obj.payment_status = validated_data['payment_status']
        
        # Update the payment amount with the new price
        license_obj.payment_amount = new_price
        
        # Save the changes
        license_obj.save()
        
        # Log the upgrade
        logger.info(f"Upgraded subscription for doctor {user.email} from 6 months to 1 year")
        
        # Prepare the response
        response_data = {
            "old_subscription": {
                "period": "6months",
                "start_date": subscription_start_date.isoformat(),
                "days_used": days_used,
                "price": float(old_price)
            },
            "new_subscription": {
                "period": "1year",
                "expiry_date": new_expiry_date.isoformat(),
                "days_in_new_subscription": days_in_new_subscription,
                "full_price": float(new_price),
                "prorated_price": float(prorated_annual_price),
                "upgrade_price": float(upgrade_price)
            },
            "license_updated": True,
            "upgrade_id": str(upgrade.id)
        }
        
        return Response(response_data, status=status.HTTP_200_OK)
