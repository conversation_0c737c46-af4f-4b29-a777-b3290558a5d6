from rest_framework import generics, permissions, status, serializers
from rest_framework.response import Response
from django.contrib.auth import get_user_model
from ..models import User
from .user_serializers import UserSerializer

class StaffSerializer(UserSerializer):
    """
    Serializer for staff members with additional fields.
    """
    date_joined = serializers.DateTimeField(read_only=True, format="%Y-%m-%d %H:%M:%S")
    is_pending = serializers.SerializerMethodField(read_only=True)
    profile_image_url = serializers.SerializerMethodField(read_only=True)
    password = serializers.CharField(write_only=True, required=False)
    password2 = serializers.CharField(write_only=True, required=False)

    class Meta(UserSerializer.Meta):
        fields = UserSerializer.Meta.fields + [
            'date_joined', 'is_pending', 'profile_image_url',
            'profile_image', 'profile_image_medium', 'profile_image_large',
            'password', 'password2'
        ]
        read_only_fields = UserSerializer.Meta.read_only_fields + ['date_joined', 'is_pending', 'profile_image_url']

    def get_is_pending(self, obj):
        """
        Return whether the staff member is pending.
        For now, we'll return False by default since there's no pending status in the model.
        This can be enhanced later to check for a specific condition.
        """
        # In the future, this could be based on a status field or other criteria
        return not obj.is_active

    def get_profile_image_url(self, obj):
        """
        Return the complete URL of the profile image if it exists.
        """
        from django.conf import settings

        if obj.profile_image and obj.profile_image.name:
            # Get the base URL from settings or use a default
            base_url = getattr(settings, 'BASE_URL', 'http://localhost:8000')

            # Make sure the URL doesn't have double slashes
            image_url = obj.profile_image.url
            if image_url.startswith('/'):
                image_url = image_url[1:]

            # Construct the full URL
            full_url = f"{base_url}/{image_url}"

            # Log the URL construction for debugging
            print(f"Constructed profile_image_url for {obj.email}: {full_url}")

            return full_url

        # Try medium and large images as fallbacks
        elif obj.profile_image_medium and obj.profile_image_medium.name:
            base_url = getattr(settings, 'BASE_URL', 'http://localhost:8000')
            image_url = obj.profile_image_medium.url
            if image_url.startswith('/'):
                image_url = image_url[1:]
            return f"{base_url}/{image_url}"

        elif obj.profile_image_large and obj.profile_image_large.name:
            base_url = getattr(settings, 'BASE_URL', 'http://localhost:8000')
            image_url = obj.profile_image_large.url
            if image_url.startswith('/'):
                image_url = image_url[1:]
            return f"{base_url}/{image_url}"

        return None

    def validate(self, attrs):
        # Check if password fields are provided and match
        if 'password' in attrs and 'password2' in attrs:
            if attrs['password'] != attrs['password2']:
                raise serializers.ValidationError({"password": "Password fields didn't match."})
        return attrs

    def create(self, validated_data):
        # Remove password2 field as it's not a field in the User model
        password2 = validated_data.pop('password2', None)

        # Create the user instance
        user = super().create(validated_data)

        # Set the password if provided
        if 'password' in validated_data:
            user.set_password(validated_data['password'])
            user.save()

        return user

    def update(self, instance, validated_data):
        # Handle password update
        if 'password' in validated_data:
            password = validated_data.pop('password')
            # Remove password2 field as it's not needed for user update
            validated_data.pop('password2', None)

            # Set the new password
            instance.set_password(password)

            # Log the password update
            print(f"Setting new password for staff {instance.email}")

        # Update the instance with the remaining validated data
        return super().update(instance, validated_data)

class StaffListView(generics.ListAPIView):
    """
    View to list all staff members.
    """
    serializer_class = StaffSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """
        Filter queryset to only include staff members.
        """
        # Get the assigned_doctor parameter from the request
        assigned_doctor = self.request.query_params.get('assigned_doctor')

        # Base queryset for staff members
        queryset = User.objects.filter(user_type='staff')

        # Log the request for debugging
        print(f"StaffListView: Request from user {self.request.user.email} (type: {self.request.user.user_type})")
        print(f"StaffListView: assigned_doctor param = {assigned_doctor}")

        # If assigned_doctor is provided, filter by it
        if assigned_doctor:
            print(f"StaffListView: Filtering by assigned_doctor_id={assigned_doctor}")
            queryset = queryset.filter(assigned_doctor_id=assigned_doctor)
        # If the current user is a doctor, only show their staff members
        elif self.request.user.user_type == 'doctor':
            print(f"StaffListView: Filtering by current doctor {self.request.user.id}")
            queryset = queryset.filter(assigned_doctor=self.request.user)

        # Log the result count
        print(f"StaffListView: Found {queryset.count()} staff members")

        return queryset

class StaffCreateView(generics.CreateAPIView):
    """
    View to create a new staff member.
    """
    serializer_class = StaffSerializer
    permission_classes = [permissions.IsAuthenticated]

    def perform_create(self, serializer):
        """
        Set the user_type to 'staff' and assigned_doctor to the current user if they are a doctor.
        Also check subscription limits before creating the staff member.
        """
        # Get the current user
        user = self.request.user

        # Check if the current user is a doctor
        if user.user_type == 'doctor':
            # Check subscription limits before creating the staff member
            from subscriptions.models import DoctorSubscription
            from rest_framework.exceptions import ValidationError

            try:
                subscription = DoctorSubscription.objects.get(doctor=user, status='active')

                # Count current staff members (including assistants)
                current_staff = User.objects.filter(
                    assigned_doctor=user,
                    user_type__in=['assistant', 'staff']
                ).count()

                # Check if the doctor can create more staff members
                if current_staff >= subscription.package.max_assistants:
                    raise ValidationError({
                        'detail': f'You have reached the maximum number of staff members ({subscription.package.max_assistants}) '
                                f'allowed by your subscription plan. Please upgrade your plan to add more staff.'
                    })

                # Create the staff member with the doctor as the assigned_doctor
                staff_member = serializer.save(user_type='staff', assigned_doctor=user)

                # Update the subscription assistant count (staff counts as assistants in the limit)
                subscription.current_assistant_count = current_staff + 1
                subscription.save(update_fields=['current_assistant_count'])

            except DoctorSubscription.DoesNotExist:
                raise ValidationError({
                    'detail': 'No active subscription found. Please contact support to activate your subscription.'
                })
        else:
            # If the current user is not a doctor, just set the user_type to 'staff'
            serializer.save(user_type='staff')

class StaffDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    View to retrieve, update, or delete a staff member.
    """
    serializer_class = StaffSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """
        Filter queryset to only include staff members.
        """
        # Get the current user
        user = self.request.user

        # Base queryset for staff members
        queryset = User.objects.filter(user_type='staff')

        # If the current user is a doctor, only allow access to their staff members
        if user.user_type == 'doctor':
            queryset = queryset.filter(assigned_doctor=user)

        return queryset

    def update(self, request, *args, **kwargs):
        """
        Update a staff member.
        """
        # Get the instance
        instance = self.get_object()

        # Log the request data for debugging
        print(f"StaffDetailView.update: Request data for {instance.email}:", request.data)

        # Ensure the user_type remains 'staff'
        if 'user_type' in request.data and request.data['user_type'] != 'staff':
            request.data['user_type'] = 'staff'

        # Store the original status value for later use
        original_status = None
        is_pending = False  # Default value for is_pending

        if 'status' in request.data:
            original_status = request.data.get('status')
            print(f"StaffDetailView.update: Original status value: {original_status}")

            # Handle status conversion
            status_value = request.data.pop('status')
            print(f"StaffDetailView.update: Converting status '{status_value}' to is_active/is_pending flags")

            # Update the instance directly
            if status_value == 'active':
                instance.is_active = True
                is_pending = False
                instance.save(update_fields=['is_active'])
                print(f"StaffDetailView.update: Set is_active=True directly on instance")
            elif status_value == 'inactive':
                instance.is_active = False
                is_pending = False
                instance.save(update_fields=['is_active'])
                print(f"StaffDetailView.update: Set is_active=False directly on instance")
            elif status_value == 'pending':
                instance.is_active = False
                is_pending = True
                instance.save(update_fields=['is_active'])
                print(f"StaffDetailView.update: Set is_active=False directly on instance (pending)")

            # Also update the request data for the serializer
            request.data['is_active'] = instance.is_active

        # Handle is_active and is_pending if they are provided directly
        elif 'is_active' in request.data:
            print(f"StaffDetailView.update: Using provided is_active flag")

            # Convert string values to boolean if needed
            if isinstance(request.data['is_active'], str):
                is_active_value = request.data['is_active'].lower() == 'true'
                print(f"StaffDetailView.update: Converted is_active string to boolean: {is_active_value}")
            else:
                is_active_value = bool(request.data['is_active'])

            # Update the instance directly
            instance.is_active = is_active_value
            instance.save(update_fields=['is_active'])
            print(f"StaffDetailView.update: Set is_active={is_active_value} directly on instance")

            # Update the request data for the serializer
            request.data['is_active'] = is_active_value

            # If is_pending is provided, store it for later use
            if 'is_pending' in request.data:
                if isinstance(request.data['is_pending'], str):
                    is_pending = request.data['is_pending'].lower() == 'true'
                else:
                    is_pending = bool(request.data['is_pending'])
                print(f"StaffDetailView.update: Using provided is_pending={is_pending}")

        # Handle password update
        if 'password' in request.data and request.data['password']:
            # Check if password and password2 match
            if 'password2' in request.data and request.data['password'] != request.data['password2']:
                return Response(
                    {"password": "Password fields didn't match."},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Set the password directly on the instance
            password = request.data.pop('password')
            # Remove password2 field as it's not needed for user update
            request.data.pop('password2', None)

            # Set the new password
            instance.set_password(password)
            instance.save(update_fields=['password'])

            # Log the password update
            print(f"StaffDetailView.update: Set new password for staff {instance.email}")

        # Check if there's a profile image in the request
        if 'profile_image' in request.data and request.data['profile_image']:
            print(f"StaffDetailView.update: Profile image found in request for staff {instance.email}")
        else:
            print(f"StaffDetailView.update: No profile image in request for staff {instance.email}")

        # Perform the update for other fields
        partial = kwargs.pop('partial', False)
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        # Get the updated instance
        updated_instance = self.get_object()
        print(f"StaffDetailView.update: Updated staff {updated_instance.email}")
        print(f"StaffDetailView.update: New is_active: {updated_instance.is_active}")

        # Create a new serializer with the updated instance
        response_serializer = self.get_serializer(updated_instance)
        serialized_data = response_serializer.data

        # Log the serialized data
        print(f"StaffDetailView.update: Serialized data: {serialized_data}")

        # Add is_pending to the serialized data
        serialized_data['is_pending'] = is_pending
        print(f"StaffDetailView.update: Added is_pending={is_pending} to serialized data")

        # If we had an original status value, add it to the serialized data
        if original_status:
            serialized_data['status'] = original_status
            print(f"StaffDetailView.update: Added original status to serialized data: {original_status}")
        else:
            # Calculate status based on is_active and is_pending
            if is_pending:
                status_value = 'pending'
            else:
                status_value = 'active' if updated_instance.is_active else 'inactive'
            serialized_data['status'] = status_value
            print(f"StaffDetailView.update: Calculated status from is_active and is_pending: {status_value}")

        # Return a custom response with the serialized data
        print(f"StaffDetailView.update: Update successful, returning custom response")

        return Response(serialized_data)

    def destroy(self, request, *args, **kwargs):
        """
        Delete a staff member.
        """
        # Get the instance
        instance = self.get_object()

        # Check if the current user is authorized to delete this staff member
        user = request.user
        if user.user_type == 'doctor' and instance.assigned_doctor != user:
            return Response(
                {"detail": "You do not have permission to delete this staff member."},
                status=status.HTTP_403_FORBIDDEN
            )

        # Log the deletion
        print(f"Deleting staff member: {instance.email} (ID: {instance.id})")

        # Call the parent destroy method
        return super().destroy(request, *args, **kwargs)
