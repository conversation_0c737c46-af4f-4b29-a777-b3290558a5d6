"""
Appointment serializers for the dentistry application.
"""
from rest_framework import serializers
from dentistry.models import DentistryAppointment
from dentistry.serializers.patient import DentistryPatientSerializer

class DentistryAppointmentSerializer(serializers.ModelSerializer):
    """
    Serializer for the DentistryAppointment model.
    """
    patient_details = DentistryPatientSerializer(source='patient', read_only=True)
    doctor_name = serializers.CharField(source='doctor.get_full_name', read_only=True)
    appointment_type_display = serializers.ReadOnlyField(source='get_appointment_type_display')
    status_display = serializers.ReadOnlyField(source='get_status_display')

    class Meta:
        model = DentistryAppointment
        fields = [
            'id', 'patient', 'patient_details', 'doctor', 'doctor_name',
            'appointment_date', 'appointment_time', 'duration_minutes',
            'status', 'status_display', 'reason', 'notes',
            'appointment_type', 'appointment_type_display',
            'affected_teeth', 'requires_xray', 'requires_anesthesia',
            'pre_appointment_instructions', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']
