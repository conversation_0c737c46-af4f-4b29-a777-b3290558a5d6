# backend/dental_medicine/dentistry/models_pathologies.py

from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model
import uuid

User = get_user_model()


class DentalPathology(models.Model):
    """
    Modèle pour les pathologies dentaires
    """
    SEVERITY_CHOICES = [
        ('mild', _('Légère')),
        ('moderate', _('Modérée')),
        ('severe', _('Sévère')),
        ('critical', _('Critique')),
    ]
    
    CATEGORY_CHOICES = [
        ('caries', _('Caries')),
        ('periodontal', _('Maladie Parodontale')),
        ('endodontic', _('Problème Endodontique')),
        ('trauma', _('Traumatisme')),
        ('malformation', _('Malformation')),
        ('infection', _('Infection')),
        ('wear', _('Usure Dentaire')),
        ('aesthetic', _('Problème Esthétique')),
        ('orthodontic', _('Problème Orthodontique')),
        ('other', _('Autre')),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    
    # Identification
    name = models.CharField(max_length=200, verbose_name=_('Nom de la pathologie'))
    code = models.CharField(max_length=20, unique=True, verbose_name=_('Code pathologie'))
    category = models.CharField(max_length=20, choices=CATEGORY_CHOICES, verbose_name=_('Catégorie'))
    
    # Description
    description = models.TextField(verbose_name=_('Description'))
    symptoms = models.TextField(blank=True, verbose_name=_('Symptômes'))
    causes = models.TextField(blank=True, verbose_name=_('Causes'))
    
    # Classification
    severity_default = models.CharField(max_length=20, choices=SEVERITY_CHOICES, default='mild')
    is_contagious = models.BooleanField(default=False, verbose_name=_('Contagieux'))
    requires_urgent_care = models.BooleanField(default=False, verbose_name=_('Soins urgents requis'))
    
    # Métadonnées
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)
    
    class Meta:
        ordering = ['category', 'name']
        verbose_name = _('Dental Pathology')
        verbose_name_plural = _('Dental Pathologies')
    
    def __str__(self):
        return f"{self.code} - {self.name}"


class PatientDiagnosis(models.Model):
    """
    Diagnostic d'un patient pour une dent spécifique
    """
    STATUS_CHOICES = [
        ('suspected', _('Suspecté')),
        ('confirmed', _('Confirmé')),
        ('treated', _('Traité')),
        ('resolved', _('Résolu')),
        ('chronic', _('Chronique')),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    
    # Relations
    pathology = models.ForeignKey(DentalPathology, on_delete=models.CASCADE, related_name='diagnoses')
    patient_id = models.CharField(max_length=100, verbose_name=_('ID Patient'))
    
    # Localisation
    tooth_number = models.PositiveIntegerField(verbose_name=_('Numéro de dent'))
    tooth_surface = models.CharField(max_length=50, blank=True, verbose_name=_('Surface de la dent'))
    
    # Diagnostic
    diagnosed_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    diagnosis_date = models.DateTimeField(auto_now_add=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='suspected')
    severity = models.CharField(max_length=20, choices=DentalPathology.SEVERITY_CHOICES)
    
    # Détails cliniques
    clinical_notes = models.TextField(blank=True, verbose_name=_('Notes cliniques'))
    pain_level = models.PositiveIntegerField(null=True, blank=True, help_text=_('Échelle 1-10'))
    
    # Suivi
    treatment_plan = models.TextField(blank=True, verbose_name=_('Plan de traitement'))
    follow_up_required = models.BooleanField(default=True)
    next_appointment = models.DateTimeField(null=True, blank=True)
    
    # Métadonnées
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-diagnosis_date']
        verbose_name = _('Patient Diagnosis')
        verbose_name_plural = _('Patient Diagnoses')
        unique_together = ['patient_id', 'tooth_number', 'pathology', 'diagnosis_date']
    
    def __str__(self):
        return f"Patient {self.patient_id} - Dent {self.tooth_number} - {self.pathology.name}"


class TreatmentProtocol(models.Model):
    """
    Protocoles de traitement pour les pathologies
    """
    URGENCY_CHOICES = [
        ('low', _('Faible')),
        ('medium', _('Moyenne')),
        ('high', _('Élevée')),
        ('emergency', _('Urgence')),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    
    # Relations
    pathology = models.ForeignKey(DentalPathology, on_delete=models.CASCADE, related_name='protocols')
    
    # Identification
    name = models.CharField(max_length=200, verbose_name=_('Nom du protocole'))
    description = models.TextField(verbose_name=_('Description'))
    
    # Traitement
    treatment_steps = models.JSONField(default=list, verbose_name=_('Étapes de traitement'))
    estimated_duration = models.PositiveIntegerField(help_text=_('Durée estimée en minutes'))
    urgency_level = models.CharField(max_length=20, choices=URGENCY_CHOICES, default='medium')
    
    # Modifications dentaires associées
    required_modifications = models.JSONField(
        default=list, 
        help_text=_('Liste des path_ids de modifications requises')
    )
    
    # Coût et ressources
    estimated_cost = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    required_materials = models.JSONField(default=list, verbose_name=_('Matériaux requis'))
    
    # Métadonnées
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)
    
    class Meta:
        ordering = ['pathology', 'urgency_level', 'name']
        verbose_name = _('Treatment Protocol')
        verbose_name_plural = _('Treatment Protocols')
    
    def __str__(self):
        return f"{self.pathology.name} - {self.name}"


# Données de pathologies communes
COMMON_PATHOLOGIES = [
    {
        'name': 'Carie Dentaire Superficielle',
        'code': 'CAR001',
        'category': 'caries',
        'description': 'Carie limitée à l\'émail dentaire',
        'symptoms': 'Sensibilité au sucré, tache brune sur la dent',
        'severity_default': 'mild',
        'protocols': [
            {
                'name': 'Restauration Composite',
                'treatment_steps': ['Anesthésie locale', 'Préparation cavitaire', 'Pose composite', 'Polissage'],
                'required_modifications': ['26'],  # restoration_amalgam
                'estimated_duration': 45,
                'urgency_level': 'medium'
            }
        ]
    },
    {
        'name': 'Carie Dentaire Profonde',
        'code': 'CAR002',
        'category': 'caries',
        'description': 'Carie atteignant la dentine avec risque pulpaire',
        'symptoms': 'Douleur intense, sensibilité au chaud/froid',
        'severity_default': 'severe',
        'protocols': [
            {
                'name': 'Traitement Endodontique + Couronne',
                'treatment_steps': ['Anesthésie', 'Accès pulpaire', 'Traitement canalaire', 'Obturation', 'Couronne'],
                'required_modifications': ['34', '35', '41'],  # root_gutta_percha + crown
                'estimated_duration': 120,
                'urgency_level': 'high'
            }
        ]
    },
    {
        'name': 'Gingivite',
        'code': 'GIN001',
        'category': 'periodontal',
        'description': 'Inflammation des gencives',
        'symptoms': 'Saignement des gencives, rougeur, gonflement',
        'severity_default': 'mild',
        'protocols': [
            {
                'name': 'Détartrage + Polissage',
                'treatment_steps': ['Détartrage', 'Polissage', 'Application fluorée'],
                'required_modifications': ['17', '18'],  # cleaning + fluoride
                'estimated_duration': 30,
                'urgency_level': 'low'
            }
        ]
    },
    {
        'name': 'Fracture Dentaire',
        'code': 'FRA001',
        'category': 'trauma',
        'description': 'Fracture de la couronne dentaire',
        'symptoms': 'Douleur, sensibilité, bord tranchant',
        'severity_default': 'moderate',
        'protocols': [
            {
                'name': 'Restauration ou Couronne',
                'treatment_steps': ['Évaluation', 'Anesthésie', 'Préparation', 'Restauration'],
                'required_modifications': ['41', '42'],  # crown_permanent ou crown_temporary
                'estimated_duration': 60,
                'urgency_level': 'medium'
            }
        ]
    },
    {
        'name': 'Dent de Sagesse Incluse',
        'code': 'IMP001',
        'category': 'orthodontic',
        'description': 'Dent de sagesse sans espace pour éruption',
        'symptoms': 'Douleur, gonflement, difficulté à ouvrir la bouche',
        'severity_default': 'moderate',
        'protocols': [
            {
                'name': 'Extraction Chirurgicale',
                'treatment_steps': ['Anesthésie', 'Incision', 'Extraction', 'Sutures', 'Soins post-op'],
                'required_modifications': ['53', '36'],  # extraction + post_care
                'estimated_duration': 45,
                'urgency_level': 'medium'
            }
        ]
    }
]
