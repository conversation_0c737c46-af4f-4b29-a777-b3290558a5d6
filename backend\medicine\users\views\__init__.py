from rest_framework import generics, permissions, status
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.decorators import api_view, permission_classes
from rest_framework_simplejwt.views import TokenObtainPairView
from django.contrib.auth import get_user_model
from django.contrib.auth.tokens import default_token_generator
from django.utils.http import urlsafe_base64_encode, urlsafe_base64_decode
from django.utils.encoding import force_bytes, force_str
from django.core.mail import send_mail
from django.conf import settings
from django.utils import timezone
import uuid
import datetime
# Import serializers from the local user_serializers.py file to avoid circular imports
from django.contrib.auth import get_user_model
User = get_user_model()

# Import serializers from the local user_serializers.py file
from .user_serializers import (
    UserSerializer,
    DoctorSerializer,
    PatientSerializer,
    AssistantSerializer,
    RegisterSerializer,
    TrialRequestSerializer
)

# Import specialty views
from .specialty_views import (
    SpecialtyListView,
    SpecialtyDetailView,
    DoctorsBySpecialtyView
)

# Import check email view
from .check_email_view import CheckEmailView

# Import location views
from .location_views import (
    CountryListView,
    CountryDetailView,
    RegionListView,
    RegionDetailView,
    CityListView,
    CityDetailView
)

# Import location import view
from .location_import_view import import_locations

# Import staff views
from .staff_views import StaffListView, StaffCreateView, StaffDetailView

# Import site settings views
from .site_settings_views import SiteSettingsView

User = get_user_model()

class RegisterView(generics.CreateAPIView):
    queryset = User.objects.all()
    permission_classes = [permissions.AllowAny]
    serializer_class = RegisterSerializer

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = self.perform_create(serializer)

        # Create a custom response with user data and license info
        response_data = {
            'id': str(user.id),
            'email': user.email,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'user_type': user.user_type,
            'license_number': user.license_number if hasattr(user, 'license_number') else None,
            'message': 'User registered successfully'
        }

        headers = self.get_success_headers(serializer.data)
        return Response(response_data, status=status.HTTP_201_CREATED, headers=headers)

    def perform_create(self, serializer):
        return serializer.save()

class UserDetailView(generics.RetrieveUpdateAPIView):
    queryset = User.objects.all()
    serializer_class = UserSerializer

    def get_object(self):
        return self.request.user

class DoctorListView(generics.ListAPIView):
    queryset = User.objects.filter(user_type='doctor')
    serializer_class = DoctorSerializer
    permission_classes = [permissions.AllowAny]

class DoctorDetailView(generics.RetrieveAPIView):
    queryset = User.objects.filter(user_type='doctor')
    serializer_class = DoctorSerializer
    permission_classes = [permissions.AllowAny]

class PatientListView(generics.ListAPIView):
    queryset = User.objects.filter(user_type='patient')
    serializer_class = PatientSerializer

    def get_queryset(self):
        user = self.request.user
        if user.is_doctor:
            # Doctors can see all their patients
            return User.objects.filter(user_type='patient')
        elif user.is_assistant:
            # Assistants can see patients of their assigned doctor
            if user.assigned_doctor:
                return User.objects.filter(user_type='patient')
        return User.objects.none()

# Assistant views have been moved to assistants_views.py


class ForgotPasswordView(APIView):
    permission_classes = [permissions.AllowAny]

    def post(self, request):
        email = request.data.get('email')
        if not email:
            return Response({'error': 'Email is required'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            user = User.objects.get(email=email)

            # Generate token
            token = default_token_generator.make_token(user)
            uid = urlsafe_base64_encode(force_bytes(user.pk))

            # Create reset link
            reset_link = f"http://localhost:3001/reset-password?token={token}&email={email}"

            # Send email (in production, use a proper email service)
            try:
                send_mail(
                    'Password Reset Request',
                    f'Please click the following link to reset your password: {reset_link}',
                    '<EMAIL>',
                    [email],
                    fail_silently=False,
                )
            except Exception as e:
                # For development, just print the link
                print(f"Password reset link: {reset_link}")

            return Response({'message': 'Password reset email sent'}, status=status.HTTP_200_OK)
        except User.DoesNotExist:
            # Don't reveal that the user doesn't exist
            return Response({'message': 'Password reset email sent'}, status=status.HTTP_200_OK)


class ResetPasswordView(APIView):
    permission_classes = [permissions.AllowAny]

    def post(self, request):
        token = request.data.get('token')
        email = request.data.get('email')
        password = request.data.get('password')

        if not token or not email or not password:
            return Response({'error': 'Token, email, and password are required'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            user = User.objects.get(email=email)

            # Verify token
            if default_token_generator.check_token(user, token):
                user.set_password(password)
                user.save()
                return Response({'message': 'Password reset successful'}, status=status.HTTP_200_OK)
            else:
                return Response({'error': 'Invalid or expired token'}, status=status.HTTP_400_BAD_REQUEST)
        except User.DoesNotExist:
            return Response({'error': 'Invalid email'}, status=status.HTTP_400_BAD_REQUEST)


class VerifyEmailView(APIView):
    permission_classes = [permissions.AllowAny]

    def post(self, request):
        token = request.data.get('token')
        email = request.data.get('email')

        if not token or not email:
            return Response({'error': 'Token and email are required'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            user = User.objects.get(email=email)

            # In a real implementation, you would verify the token
            # For now, we'll just mark the email as verified
            user.is_active = True
            user.save()

            return Response({'message': 'Email verified successfully'}, status=status.HTTP_200_OK)
        except User.DoesNotExist:
            return Response({'error': 'Invalid email'}, status=status.HTTP_400_BAD_REQUEST)


class TrialStatusView(APIView):
    """
    View to check the trial status of the current user.
    """
    def get(self, request):
        user = request.user

        if not user.is_authenticated:
            return Response({'error': 'Authentication required'}, status=status.HTTP_401_UNAUTHORIZED)

        # Get trial information
        trial_data = {
            'is_trial': user.is_trial,
            'is_trial_active': user.is_trial_active,
            'trial_days_remaining': user.trial_days_remaining,
            'trial_start_date': user.trial_start_date,
            'trial_end_date': user.trial_end_date,
            'trial_duration_months': user.trial_duration_months,
            'trial_requested': user.trial_requested,
            'trial_request_date': user.trial_request_date,
            'trial_request_approved': user.trial_request_approved,
            'trial_request_duration_months': user.trial_request_duration_months,
            'has_pending_trial_request': user.has_pending_trial_request,
        }

        return Response(trial_data, status=status.HTTP_200_OK)


class TrialRequestView(APIView):
    """
    View to request a trial period.
    """
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        user = request.user

        # Only doctors can request trials
        if user.user_type != 'doctor':
            return Response(
                {'error': 'Only doctors can request trial periods'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Check if user already has an active trial
        if user.is_trial and user.is_trial_active:
            return Response(
                {'error': 'You already have an active trial period'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Check if user already has a pending trial request
        if user.has_pending_trial_request:
            return Response(
                {'error': 'You already have a pending trial request'},
                status=status.HTTP_400_BAD_REQUEST
            )

        serializer = TrialRequestSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save(user)

            return Response({
                'message': 'Trial request submitted successfully',
                'trial_requested': True,
                'trial_request_date': user.trial_request_date,
                'trial_request_duration_months': user.trial_request_duration_months
            }, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([permissions.IsAdminUser])
def start_trial(request):
    """
    Start a trial period for a user.
    """
    user_id = request.data.get('user_id')
    duration_months = request.data.get('duration_months', 1)

    if not user_id:
        return Response({'error': 'User ID is required'}, status=status.HTTP_400_BAD_REQUEST)

    try:
        duration_months = int(duration_months)
        if duration_months not in [1, 2, 3]:
            return Response({'error': 'Duration must be 1, 2, or 3 months'}, status=status.HTTP_400_BAD_REQUEST)
    except (ValueError, TypeError):
        return Response({'error': 'Invalid duration'}, status=status.HTTP_400_BAD_REQUEST)

    try:
        user = User.objects.get(id=user_id)

        # Start the trial
        user.is_trial = True
        user.trial_start_date = timezone.now()
        user.trial_end_date = user.trial_start_date + datetime.timedelta(days=30 * duration_months)
        user.trial_duration_months = duration_months
        user.trial_notification_sent = False
        user.save()

        return Response({
            'message': f'Started {duration_months}-month trial for {user.email}',
            'trial_end_date': user.trial_end_date
        }, status=status.HTTP_200_OK)
    except User.DoesNotExist:
        return Response({'error': 'User not found'}, status=status.HTTP_404_NOT_FOUND)


@api_view(['POST'])
@permission_classes([permissions.IsAdminUser])
def end_trial(request):
    """
    End a trial period for a user.
    """
    user_id = request.data.get('user_id')

    if not user_id:
        return Response({'error': 'User ID is required'}, status=status.HTTP_400_BAD_REQUEST)

    try:
        user = User.objects.get(id=user_id)

        if not user.is_trial:
            return Response({'error': 'User is not in a trial period'}, status=status.HTTP_400_BAD_REQUEST)

        # End the trial
        user.is_trial = False
        user.save()

        return Response({
            'message': f'Ended trial for {user.email}'
        }, status=status.HTTP_200_OK)
    except User.DoesNotExist:
        return Response({'error': 'User not found'}, status=status.HTTP_404_NOT_FOUND)
