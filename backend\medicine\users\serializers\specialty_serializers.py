from rest_framework import serializers
from ..models import Specialty
from django.contrib.auth import get_user_model

User = get_user_model()

class SpecialtySerializer(serializers.ModelSerializer):
    """
    Serializer for the Specialty model.
    """
    doctor_count = serializers.IntegerField(read_only=True)
    
    class Meta:
        model = Specialty
        fields = [
            'id', 'name', 'description', 'slug', 'icon', 
            'is_active', 'doctor_count'
        ]
        read_only_fields = ['id', 'doctor_count']

class SpecialtyDetailSerializer(serializers.ModelSerializer):
    """
    Detailed serializer for the Specialty model including doctors.
    """
    doctor_count = serializers.IntegerField(read_only=True)
    doctors = serializers.SerializerMethodField()
    
    class Meta:
        model = Specialty
        fields = [
            'id', 'name', 'description', 'slug', 'icon', 
            'is_active', 'doctor_count', 'doctors'
        ]
        read_only_fields = ['id', 'doctor_count']
    
    def get_doctors(self, obj):
        # Get active doctors in this specialty
        doctors = obj.doctors.filter(
            is_active=True,
            user_type='doctor'
        )
        
        # Import here to avoid circular imports
        from ..views.user_serializers import DoctorSerializer
        
        return DoctorSerializer(doctors, many=True).data
