"""
Appointment views for the dentistry application.
"""
from rest_framework import viewsets, permissions, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from dentistry.models import DentistryAppointment
from dentistry.serializers import DentistryAppointmentSerializer

class DentistryAppointmentViewSet(viewsets.ModelViewSet):
    """
    API endpoint for dentistry appointments.
    """
    queryset = DentistryAppointment.objects.all()
    serializer_class = DentistryAppointmentSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['patient', 'doctor', 'appointment_date', 'status', 'appointment_type']
    search_fields = ['reason', 'notes', 'affected_teeth']
    ordering_fields = ['appointment_date', 'appointment_time', 'created_at']
    
    def get_queryset(self):
        """
        Filter appointments based on query parameters.
        """
        queryset = super().get_queryset()
        
        # Filter by patient ID
        patient_id = self.request.query_params.get('patient_id')
        if patient_id:
            queryset = queryset.filter(patient_id=patient_id)
        
        # Filter by date range
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        
        if start_date:
            queryset = queryset.filter(appointment_date__gte=start_date)
        if end_date:
            queryset = queryset.filter(appointment_date__lte=end_date)
        
        # Filter by affected teeth
        tooth_number = self.request.query_params.get('tooth_number')
        if tooth_number:
            queryset = queryset.filter(affected_teeth__contains=tooth_number)
        
        return queryset
    
    @action(detail=True, methods=['post'])
    def confirm(self, request, pk=None):
        """
        Confirm an appointment.
        """
        appointment = self.get_object()
        
        if appointment.status != 'scheduled':
            return Response(
                {"detail": "Only scheduled appointments can be confirmed."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        appointment.status = 'confirmed'
        appointment.save()
        
        serializer = self.get_serializer(appointment)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def cancel(self, request, pk=None):
        """
        Cancel an appointment.
        """
        appointment = self.get_object()
        
        if appointment.status in ['completed', 'cancelled', 'no_show']:
            return Response(
                {"detail": "This appointment cannot be cancelled."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        appointment.status = 'cancelled'
        appointment.save()
        
        serializer = self.get_serializer(appointment)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def complete(self, request, pk=None):
        """
        Mark an appointment as completed.
        """
        appointment = self.get_object()
        
        if appointment.status not in ['scheduled', 'confirmed']:
            return Response(
                {"detail": "Only scheduled or confirmed appointments can be completed."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        appointment.status = 'completed'
        appointment.save()
        
        serializer = self.get_serializer(appointment)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def no_show(self, request, pk=None):
        """
        Mark an appointment as no-show.
        """
        appointment = self.get_object()
        
        if appointment.status not in ['scheduled', 'confirmed']:
            return Response(
                {"detail": "Only scheduled or confirmed appointments can be marked as no-show."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        appointment.status = 'no_show'
        appointment.save()
        
        serializer = self.get_serializer(appointment)
        return Response(serializer.data)
