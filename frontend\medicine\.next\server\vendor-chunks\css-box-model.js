"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/css-box-model";
exports.ids = ["vendor-chunks/css-box-model"];
exports.modules = {

/***/ "(ssr)/./node_modules/css-box-model/dist/css-box-model.esm.js":
/*!**************************************************************!*\
  !*** ./node_modules/css-box-model/dist/css-box-model.esm.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateBox: () => (/* binding */ calculateBox),\n/* harmony export */   createBox: () => (/* binding */ createBox),\n/* harmony export */   expand: () => (/* binding */ expand),\n/* harmony export */   getBox: () => (/* binding */ getBox),\n/* harmony export */   getRect: () => (/* binding */ getRect),\n/* harmony export */   offset: () => (/* binding */ offset),\n/* harmony export */   shrink: () => (/* binding */ shrink),\n/* harmony export */   withScroll: () => (/* binding */ withScroll)\n/* harmony export */ });\n/* harmony import */ var tiny_invariant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tiny-invariant */ \"(ssr)/./node_modules/tiny-invariant/dist/esm/tiny-invariant.js\");\n\n\nvar getRect = function getRect(_ref) {\n  var top = _ref.top,\n      right = _ref.right,\n      bottom = _ref.bottom,\n      left = _ref.left;\n  var width = right - left;\n  var height = bottom - top;\n  var rect = {\n    top: top,\n    right: right,\n    bottom: bottom,\n    left: left,\n    width: width,\n    height: height,\n    x: left,\n    y: top,\n    center: {\n      x: (right + left) / 2,\n      y: (bottom + top) / 2\n    }\n  };\n  return rect;\n};\nvar expand = function expand(target, expandBy) {\n  return {\n    top: target.top - expandBy.top,\n    left: target.left - expandBy.left,\n    bottom: target.bottom + expandBy.bottom,\n    right: target.right + expandBy.right\n  };\n};\nvar shrink = function shrink(target, shrinkBy) {\n  return {\n    top: target.top + shrinkBy.top,\n    left: target.left + shrinkBy.left,\n    bottom: target.bottom - shrinkBy.bottom,\n    right: target.right - shrinkBy.right\n  };\n};\n\nvar shift = function shift(target, shiftBy) {\n  return {\n    top: target.top + shiftBy.y,\n    left: target.left + shiftBy.x,\n    bottom: target.bottom + shiftBy.y,\n    right: target.right + shiftBy.x\n  };\n};\n\nvar noSpacing = {\n  top: 0,\n  right: 0,\n  bottom: 0,\n  left: 0\n};\nvar createBox = function createBox(_ref2) {\n  var borderBox = _ref2.borderBox,\n      _ref2$margin = _ref2.margin,\n      margin = _ref2$margin === void 0 ? noSpacing : _ref2$margin,\n      _ref2$border = _ref2.border,\n      border = _ref2$border === void 0 ? noSpacing : _ref2$border,\n      _ref2$padding = _ref2.padding,\n      padding = _ref2$padding === void 0 ? noSpacing : _ref2$padding;\n  var marginBox = getRect(expand(borderBox, margin));\n  var paddingBox = getRect(shrink(borderBox, border));\n  var contentBox = getRect(shrink(paddingBox, padding));\n  return {\n    marginBox: marginBox,\n    borderBox: getRect(borderBox),\n    paddingBox: paddingBox,\n    contentBox: contentBox,\n    margin: margin,\n    border: border,\n    padding: padding\n  };\n};\n\nvar parse = function parse(raw) {\n  var value = raw.slice(0, -2);\n  var suffix = raw.slice(-2);\n\n  if (suffix !== 'px') {\n    return 0;\n  }\n\n  var result = Number(value);\n  !!isNaN(result) ?  true ? (0,tiny_invariant__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(false, \"Could not parse value [raw: \" + raw + \", without suffix: \" + value + \"]\") : 0 : void 0;\n  return result;\n};\n\nvar getWindowScroll = function getWindowScroll() {\n  return {\n    x: window.pageXOffset,\n    y: window.pageYOffset\n  };\n};\n\nvar offset = function offset(original, change) {\n  var borderBox = original.borderBox,\n      border = original.border,\n      margin = original.margin,\n      padding = original.padding;\n  var shifted = shift(borderBox, change);\n  return createBox({\n    borderBox: shifted,\n    border: border,\n    margin: margin,\n    padding: padding\n  });\n};\nvar withScroll = function withScroll(original, scroll) {\n  if (scroll === void 0) {\n    scroll = getWindowScroll();\n  }\n\n  return offset(original, scroll);\n};\nvar calculateBox = function calculateBox(borderBox, styles) {\n  var margin = {\n    top: parse(styles.marginTop),\n    right: parse(styles.marginRight),\n    bottom: parse(styles.marginBottom),\n    left: parse(styles.marginLeft)\n  };\n  var padding = {\n    top: parse(styles.paddingTop),\n    right: parse(styles.paddingRight),\n    bottom: parse(styles.paddingBottom),\n    left: parse(styles.paddingLeft)\n  };\n  var border = {\n    top: parse(styles.borderTopWidth),\n    right: parse(styles.borderRightWidth),\n    bottom: parse(styles.borderBottomWidth),\n    left: parse(styles.borderLeftWidth)\n  };\n  return createBox({\n    borderBox: borderBox,\n    margin: margin,\n    padding: padding,\n    border: border\n  });\n};\nvar getBox = function getBox(el) {\n  var borderBox = el.getBoundingClientRect();\n  var styles = window.getComputedStyle(el);\n  return calculateBox(borderBox, styles);\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/css-box-model/dist/css-box-model.esm.js\n");

/***/ })

};
;