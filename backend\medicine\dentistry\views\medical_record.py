"""
Medical record views for the dentistry application.
"""
from rest_framework import viewsets, permissions, filters
from django_filters.rest_framework import DjangoFilterBackend
from dentistry.models import DentistryMedicalRecord, DentalImaging
from dentistry.serializers import (
    DentistryMedicalRecordSerializer, DentalImagingSerializer
)

class DentistryMedicalRecordViewSet(viewsets.ModelViewSet):
    """
    API endpoint for dentistry medical records.
    """
    queryset = DentistryMedicalRecord.objects.all()
    serializer_class = DentistryMedicalRecordSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['patient', 'doctor', 'date', 'oral_hygiene_status', 'gum_health', 'teeth_cleaned']
    search_fields = ['diagnosis', 'treatment_plan', 'notes']
    ordering_fields = ['date', 'created_at']
    
    def get_queryset(self):
        """
        Filter records based on query parameters.
        """
        queryset = super().get_queryset()
        
        # Filter by patient ID
        patient_id = self.request.query_params.get('patient_id')
        if patient_id:
            queryset = queryset.filter(patient_id=patient_id)
        
        # Filter by date range
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        
        if start_date:
            queryset = queryset.filter(date__gte=start_date)
        if end_date:
            queryset = queryset.filter(date__lte=end_date)
        
        return queryset

class DentalImagingViewSet(viewsets.ModelViewSet):
    """
    API endpoint for dental imaging records.
    """
    queryset = DentalImaging.objects.all()
    serializer_class = DentalImagingSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['patient', 'doctor', 'date', 'imaging_type']
    search_fields = ['findings', 'notes', 'affected_teeth']
    ordering_fields = ['date', 'created_at']
    
    def get_queryset(self):
        """
        Filter records based on query parameters.
        """
        queryset = super().get_queryset()
        
        # Filter by patient ID
        patient_id = self.request.query_params.get('patient_id')
        if patient_id:
            queryset = queryset.filter(patient_id=patient_id)
        
        # Filter by date range
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        
        if start_date:
            queryset = queryset.filter(date__gte=start_date)
        if end_date:
            queryset = queryset.filter(date__lte=end_date)
        
        # Filter by affected teeth
        tooth_number = self.request.query_params.get('tooth_number')
        if tooth_number:
            # This is a simple implementation - in a real app, you might want to use a more
            # sophisticated query to match comma-separated values
            queryset = queryset.filter(affected_teeth__contains=tooth_number)
        
        return queryset
