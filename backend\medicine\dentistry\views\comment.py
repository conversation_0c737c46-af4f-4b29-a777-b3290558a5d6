"""
Views for comment and review models in the dentistry application.
"""
from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db.models import Avg, Count, Q
from django.utils import timezone
from datetime import timed<PERSON><PERSON>

from dentistry.models import Den<PERSON>ryComment, DentistryReview, DentistryDoctorNote, DentistryDoctor
from dentistry.serializers.comment_serializers import (
    DentistryCommentSerializer, DentistryCommentCreateSerializer,
    DentistryReviewSerializer, DentistryReviewCreateSerializer,
    DentistryDoctorNoteSerializer, DentistryCommentStatsSerializer,
    DentistryDoctorRatingSerializer
)


class DentistryCommentViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing dentistry comments.
    """
    queryset = DentistryComment.objects.filter(is_active=True)
    permission_classes = [permissions.AllowAny]  # Allow anonymous comments

    def get_serializer_class(self):
        if self.action == 'create':
            return DentistryCommentCreateSerializer
        return DentistryCommentSerializer

    def get_queryset(self):
        queryset = super().get_queryset()
        doctor_id = self.request.query_params.get('doctor_id')
        if doctor_id:
            queryset = queryset.filter(doctor_id=doctor_id)

        # Only return top-level comments by default
        if self.action == 'list':
            queryset = queryset.filter(parent__isnull=True)

        return queryset.order_by('-created_at')

    def perform_create(self, serializer):
        """Add metadata when creating a comment."""
        # Get client IP and user agent
        ip_address = self.get_client_ip()
        user_agent = self.request.META.get('HTTP_USER_AGENT', '')

        serializer.save(
            ip_address=ip_address,
            user_agent=user_agent
        )

    def get_client_ip(self):
        """Get client IP address."""
        x_forwarded_for = self.request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = self.request.META.get('REMOTE_ADDR')
        return ip

    @action(detail=True, methods=['post'])
    def like(self, request, pk=None):
        """Like a comment."""
        comment = self.get_object()
        comment.likes += 1
        comment.save()
        return Response({'likes': comment.likes})

    @action(detail=True, methods=['post'])
    def unlike(self, request, pk=None):
        """Unlike a comment."""
        comment = self.get_object()
        comment.likes = max(0, comment.likes - 1)
        comment.save()
        return Response({'likes': comment.likes})

    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get comment statistics for a doctor."""
        doctor_id = request.query_params.get('doctor_id')
        if not doctor_id:
            return Response({'error': 'doctor_id is required'}, status=400)

        # Get comments and reviews for the doctor
        comments = DentistryComment.objects.filter(
            doctor_id=doctor_id, is_active=True, parent__isnull=True
        )
        reviews = DentistryReview.objects.filter(
            doctor_id=doctor_id, is_active=True
        )

        # Calculate statistics
        total_comments = comments.count()
        total_reviews = reviews.count()

        # Average rating from comments
        avg_rating = comments.aggregate(avg=Avg('rating'))['avg'] or 0

        # Rating distribution
        rating_dist = {}
        for i in range(1, 6):
            rating_dist[str(i)] = comments.filter(rating=i).count()

        # Recent comments (last 5)
        recent_comments = comments.order_by('-created_at')[:5]

        # Featured reviews
        featured_reviews = reviews.filter(is_featured=True).order_by('-created_at')[:3]

        stats_data = {
            'total_comments': total_comments,
            'total_reviews': total_reviews,
            'average_rating': round(avg_rating, 2),
            'rating_distribution': rating_dist,
            'recent_comments': DentistryCommentSerializer(recent_comments, many=True).data,
            'featured_reviews': DentistryReviewSerializer(featured_reviews, many=True).data
        }

        serializer = DentistryCommentStatsSerializer(stats_data)
        return Response(serializer.data)


class DentistryReviewViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing dentistry reviews.
    """
    queryset = DentistryReview.objects.filter(is_active=True)
    permission_classes = [permissions.AllowAny]  # Allow anonymous reviews

    def get_serializer_class(self):
        if self.action == 'create':
            return DentistryReviewCreateSerializer
        return DentistryReviewSerializer

    def get_queryset(self):
        queryset = super().get_queryset()
        doctor_id = self.request.query_params.get('doctor_id')
        if doctor_id:
            queryset = queryset.filter(doctor_id=doctor_id)

        return queryset.order_by('-created_at')

    @action(detail=True, methods=['post'])
    def mark_helpful(self, request, pk=None):
        """Mark a review as helpful."""
        review = self.get_object()
        review.helpful_count += 1
        review.save()
        return Response({'helpful_count': review.helpful_count})

    @action(detail=False, methods=['get'])
    def doctor_ratings(self, request):
        """Get comprehensive rating summary for a doctor."""
        doctor_id = request.query_params.get('doctor_id')
        if not doctor_id:
            return Response({'error': 'doctor_id is required'}, status=400)

        try:
            doctor = DentistryDoctor.objects.get(id=doctor_id)
        except DentistryDoctor.DoesNotExist:
            return Response({'error': 'Doctor not found'}, status=404)

        reviews = DentistryReview.objects.filter(
            doctor_id=doctor_id, is_active=True
        )

        if not reviews.exists():
            return Response({
                'doctor_id': doctor_id,
                'doctor_name': doctor.full_name,
                'total_reviews': 0,
                'message': 'No reviews found for this doctor'
            })

        # Calculate averages
        aggregates = reviews.aggregate(
            avg_overall=Avg('overall_rating'),
            avg_professionalism=Avg('professionalism_rating'),
            avg_communication=Avg('communication_rating'),
            avg_pain_management=Avg('pain_management_rating'),
            avg_cleanliness=Avg('cleanliness_rating'),
            avg_wait_time=Avg('wait_time_rating'),
        )

        # Recommendation percentage
        total_reviews = reviews.count()
        recommended_count = reviews.filter(would_recommend=True).count()
        recommendation_percentage = (recommended_count / total_reviews * 100) if total_reviews > 0 else 0

        # Verified reviews count
        verified_count = reviews.filter(is_verified=True).count()

        # Most recent review date
        recent_review = reviews.order_by('-created_at').first()

        rating_data = {
            'doctor_id': doctor_id,
            'doctor_name': doctor.full_name,
            'total_reviews': total_reviews,
            'average_overall_rating': round(aggregates['avg_overall'] or 0, 2),
            'average_professionalism': round(aggregates['avg_professionalism'] or 0, 2),
            'average_communication': round(aggregates['avg_communication'] or 0, 2),
            'average_pain_management': round(aggregates['avg_pain_management'] or 0, 2),
            'average_cleanliness': round(aggregates['avg_cleanliness'] or 0, 2),
            'average_wait_time': round(aggregates['avg_wait_time'] or 0, 2),
            'recommendation_percentage': round(recommendation_percentage, 2),
            'verified_reviews_count': verified_count,
            'recent_review_date': recent_review.created_at if recent_review else None
        }

        serializer = DentistryDoctorRatingSerializer(rating_data)
        return Response(serializer.data)


class DentistryDoctorNoteViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing internal doctor notes.
    """
    queryset = DentistryDoctorNote.objects.all()
    serializer_class = DentistryDoctorNoteSerializer
    permission_classes = [permissions.IsAuthenticated]  # Require authentication for notes

    def get_queryset(self):
        queryset = super().get_queryset()
        doctor_id = self.request.query_params.get('doctor_id')
        if doctor_id:
            queryset = queryset.filter(doctor_id=doctor_id)

        # Filter by note type
        note_type = self.request.query_params.get('note_type')
        if note_type:
            queryset = queryset.filter(note_type=note_type)

        # Filter by priority
        priority = self.request.query_params.get('priority')
        if priority:
            queryset = queryset.filter(priority=priority)

        # Filter confidential notes based on user permissions
        if not self.request.user.is_staff:
            queryset = queryset.filter(is_confidential=False)

        return queryset.order_by('-created_at')

    @action(detail=False, methods=['get'])
    def by_priority(self, request):
        """Get notes grouped by priority."""
        doctor_id = request.query_params.get('doctor_id')
        if not doctor_id:
            return Response({'error': 'doctor_id is required'}, status=400)

        queryset = self.get_queryset().filter(doctor_id=doctor_id)

        priorities = ['urgent', 'high', 'medium', 'low']
        grouped_notes = {}

        for priority in priorities:
            notes = queryset.filter(priority=priority)
            grouped_notes[priority] = DentistryDoctorNoteSerializer(notes, many=True).data

        return Response(grouped_notes)


# Additional utility functions for comment management
def get_doctor_comment_summary(doctor_id):
    """Get a summary of comments and reviews for a doctor."""
    comments = DentistryComment.objects.filter(
        doctor_id=doctor_id, is_active=True, parent__isnull=True
    )
    reviews = DentistryReview.objects.filter(
        doctor_id=doctor_id, is_active=True
    )

    return {
        'total_comments': comments.count(),
        'total_reviews': reviews.count(),
        'average_comment_rating': comments.aggregate(avg=Avg('rating'))['avg'] or 0,
        'average_review_rating': reviews.aggregate(avg=Avg('overall_rating'))['avg'] or 0,
        'verified_reviews': reviews.filter(is_verified=True).count(),
        'featured_reviews': reviews.filter(is_featured=True).count(),
    }
