from django.core.management.base import BaseCommand
from users.models import DoctorLicense
from django.utils import timezone
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Updates the days_remaining field for all active doctor licenses'

    def handle(self, *args, **options):
        # Get all active licenses
        active_licenses = DoctorLicense.objects.filter(status='active')
        
        self.stdout.write(f"Found {active_licenses.count()} active licenses")
        
        updated_count = 0
        expired_count = 0
        
        for license in active_licenses:
            # Update days remaining
            was_active = license.update_days_remaining()
            
            if was_active:
                updated_count += 1
                self.stdout.write(f"Updated license {license.license_number}: {license.days_remaining} days remaining")
            else:
                expired_count += 1
                self.stdout.write(self.style.WARNING(f"License {license.license_number} has expired"))
        
        self.stdout.write(self.style.SUCCESS(f"Successfully updated {updated_count} licenses"))
        if expired_count > 0:
            self.stdout.write(self.style.WARNING(f"{expired_count} licenses have expired"))
