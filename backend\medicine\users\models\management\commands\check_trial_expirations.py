from django.core.management.base import BaseCommand
from django.utils import timezone
from django.core.mail import send_mail
from django.conf import settings
from django.contrib.auth import get_user_model
import datetime

User = get_user_model()

class Command(BaseCommand):
    help = 'Check for trial expirations and send notifications'

    def add_arguments(self, parser):
        parser.add_argument(
            '--days-before',
            type=int,
            default=7,
            help='Send notifications this many days before expiration'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Do not send emails or update notification status'
        )

    def handle(self, *args, **options):
        days_before = options['days_before']
        dry_run = options['dry_run']
        
        # Calculate the date range for notifications
        notification_date = timezone.now() + datetime.timedelta(days=days_before)
        
        # Find users whose trial is about to expire
        expiring_users = User.objects.filter(
            is_trial=True,
            trial_notification_sent=False,
            trial_end_date__lte=notification_date,
            trial_end_date__gt=timezone.now()
        )
        
        self.stdout.write(f"Found {expiring_users.count()} users with trials expiring in the next {days_before} days")
        
        # Find users whose trial has expired
        expired_users = User.objects.filter(
            is_trial=True,
            trial_end_date__lte=timezone.now()
        )
        
        self.stdout.write(f"Found {expired_users.count()} users with expired trials")
        
        # Send notifications to users with expiring trials
        for user in expiring_users:
            days_remaining = (user.trial_end_date - timezone.now()).days
            self.stdout.write(f"User {user.email} has {days_remaining} days remaining in trial")
            
            if not dry_run:
                try:
                    # Send email notification
                    subject = f"Your trial period will expire in {days_remaining} days"
                    message = f"""
                    Dear {user.first_name},
                    
                    Your trial period will expire in {days_remaining} days on {user.trial_end_date.strftime('%Y-%m-%d')}.
                    
                    To continue using our services without interruption, please subscribe to one of our plans.
                    
                    Thank you for using our service!
                    """
                    send_mail(
                        subject,
                        message,
                        settings.DEFAULT_FROM_EMAIL,
                        [user.email],
                        fail_silently=False,
                    )
                    
                    # Mark notification as sent
                    user.trial_notification_sent = True
                    user.save()
                    
                    self.stdout.write(self.style.SUCCESS(f"Sent expiration notification to {user.email}"))
                except Exception as e:
                    self.stdout.write(self.style.ERROR(f"Failed to send notification to {user.email}: {str(e)}"))
            else:
                self.stdout.write(f"Would send expiration notification to {user.email} (dry run)")
        
        # End expired trials
        for user in expired_users:
            self.stdout.write(f"User {user.email} trial has expired")
            
            if not dry_run:
                try:
                    # Send expiration email
                    subject = "Your trial period has expired"
                    message = f"""
                    Dear {user.first_name},
                    
                    Your trial period has expired on {user.trial_end_date.strftime('%Y-%m-%d')}.
                    
                    To continue using our services, please subscribe to one of our plans.
                    
                    Thank you for using our service!
                    """
                    send_mail(
                        subject,
                        message,
                        settings.DEFAULT_FROM_EMAIL,
                        [user.email],
                        fail_silently=False,
                    )
                    
                    # End the trial
                    user.is_trial = False
                    user.save()
                    
                    self.stdout.write(self.style.SUCCESS(f"Ended expired trial for {user.email}"))
                except Exception as e:
                    self.stdout.write(self.style.ERROR(f"Failed to process expired trial for {user.email}: {str(e)}"))
            else:
                self.stdout.write(f"Would end expired trial for {user.email} (dry run)")
        
        if dry_run:
            self.stdout.write(self.style.WARNING("Dry run completed. No changes were made."))
        else:
            self.stdout.write(self.style.SUCCESS("Trial expiration check completed successfully."))
