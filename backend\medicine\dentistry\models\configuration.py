"""
Configuration models for the dentistry application.
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from dentistry.models.base import DentistryBaseModel
from dentistry.models.doctor import DentistryDoctor

class DentistryConfiguration(DentistryBaseModel):
    """
    Dentistry-specific configuration model.
    """
    doctor = models.OneToOneField(
        DentistryDoctor,
        on_delete=models.CASCADE,
        related_name="dentistry_configuration",
        verbose_name=_("Doctor")
    )

    # Teeth chart settings
    teeth_chart_adult_enabled = models.BooleanField(
        default=True,
        verbose_name=_("Adult Teeth Chart Enabled")
    )
    teeth_chart_child_enabled = models.BooleanField(
        default=True,
        verbose_name=_("Child Teeth Chart Enabled")
    )
    teeth_chart_numbering_system = models.CharField(
        max_length=20,
        default="universal",
        choices=[
            ("universal", _("Universal Numbering System")),
            ("fdi", _("FDI/ISO System")),
            ("palmer", _("Palmer Notation")),
        ],
        verbose_name=_("Teeth Chart Numbering System")
    )

    # Teeth chart colors
    teeth_color_normal = models.CharField(
        max_length=20,
        default="#FFFFFF",
        verbose_name=_("Normal Tooth Color")
    )
    teeth_color_filling = models.CharField(
        max_length=20,
        default="#0000FF",
        verbose_name=_("Filling Color")
    )
    teeth_color_crown = models.CharField(
        max_length=20,
        default="#FFD700",
        verbose_name=_("Crown Color")
    )
    teeth_color_root_canal = models.CharField(
        max_length=20,
        default="#FF0000",
        verbose_name=_("Root Canal Color")
    )
    teeth_color_implant = models.CharField(
        max_length=20,
        default="#808080",
        verbose_name=_("Implant Color")
    )
    teeth_color_missing = models.CharField(
        max_length=20,
        default="#000000",
        verbose_name=_("Missing Tooth Color")
    )

    # X-ray display settings
    xray_default_brightness = models.IntegerField(
        default=0,
        verbose_name=_("X-ray Default Brightness")
    )
    xray_default_contrast = models.IntegerField(
        default=0,
        verbose_name=_("X-ray Default Contrast")
    )
    xray_annotations_enabled = models.BooleanField(
        default=True,
        verbose_name=_("X-ray Annotations Enabled")
    )

    # Treatment plan settings
    treatment_plan_phases_enabled = models.BooleanField(
        default=True,
        verbose_name=_("Treatment Plan Phases Enabled")
    )
    treatment_plan_cost_display = models.BooleanField(
        default=True,
        verbose_name=_("Treatment Plan Cost Display")
    )
    treatment_plan_insurance_display = models.BooleanField(
        default=True,
        verbose_name=_("Treatment Plan Insurance Display")
    )

    # Report settings
    report_logo_enabled = models.BooleanField(
        default=True,
        verbose_name=_("Report Logo Enabled")
    )
    report_logo = models.ImageField(
        upload_to='dentistry/logos/',
        null=True,
        blank=True,
        verbose_name=_("Report Logo")
    )
    report_header = models.TextField(
        blank=True,
        verbose_name=_("Report Header")
    )
    report_footer = models.TextField(
        blank=True,
        verbose_name=_("Report Footer")
    )

    # Default appointment settings
    default_appointment_duration = models.PositiveIntegerField(
        default=30,
        verbose_name=_("Default Appointment Duration (minutes)")
    )
    default_cleaning_duration = models.PositiveIntegerField(
        default=60,
        verbose_name=_("Default Cleaning Duration (minutes)")
    )

    # UI settings
    ui_theme = models.CharField(
        max_length=20,
        default="light",
        verbose_name=_("UI Theme")
    )
    font_size = models.PositiveIntegerField(
        default=14,
        verbose_name=_("Font Size")
    )

    class Meta:
        verbose_name = _("Dentistry Configuration")
        verbose_name_plural = _("Dentistry Configurations")

    def __str__(self):
        return f"Dentistry Configuration for {self.doctor}"
