"""
Appointment models for the dentistry application.
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from dentistry.models.base_medical_record import DentistryBaseAppointment
from dentistry.models.doctor import DentistryDoctor
from dentistry.models.patient import DentistryPatient

class DentistryAppointment(DentistryBaseAppointment):
    """
    Dentistry-specific appointment model.
    """
    patient = models.ForeignKey(
        DentistryPatient,
        on_delete=models.CASCADE,
        related_name="appointments",
        verbose_name=_("Patient")
    )
    doctor = models.ForeignKey(
        DentistryDoctor,
        on_delete=models.SET_NULL,
        null=True,
        related_name="appointments",
        verbose_name=_("Doctor")
    )

    # Dentistry-specific appointment types
    APPOINTMENT_TYPE_CHOICES = (
        ('check_up', _('Regular Check-up')),
        ('cleaning', _('Teeth Cleaning')),
        ('filling', _('Filling')),
        ('root_canal', _('Root Canal')),
        ('extraction', _('Tooth Extraction')),
        ('crown', _('Crown')),
        ('bridge', _('Bridge')),
        ('implant', _('Implant')),
        ('dentures', _('Dentures')),
        ('orthodontics', _('Orthodontics')),
        ('whitening', _('Teeth Whitening')),
        ('emergency', _('Emergency')),
        ('consultation', _('Consultation')),
        ('other', _('Other')),
    )
    appointment_type = models.CharField(
        max_length=20,
        choices=APPOINTMENT_TYPE_CHOICES,
        default='check_up',
        verbose_name=_("Appointment Type")
    )

    # Status field
    STATUS_CHOICES = (
        ('scheduled', _('Scheduled')),
        ('confirmed', _('Confirmed')),
        ('cancelled', _('Cancelled')),
        ('rescheduled', _('Rescheduled')),
        ('completed', _('Completed')),
        ('no_show', _('No Show')),
    )
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='scheduled',
        verbose_name=_("Status")
    )

    # Additional fields
    affected_teeth = models.CharField(
        max_length=100,
        blank=True,
        verbose_name=_("Affected Teeth"),
        help_text=_("Comma-separated list of tooth numbers")
    )
    requires_xray = models.BooleanField(
        default=False,
        verbose_name=_("Requires X-Ray")
    )
    requires_anesthesia = models.BooleanField(
        default=False,
        verbose_name=_("Requires Anesthesia")
    )
    pre_appointment_instructions = models.TextField(
        blank=True,
        verbose_name=_("Pre-appointment Instructions")
    )

    class Meta:
        verbose_name = _("Dentistry Appointment")
        verbose_name_plural = _("Dentistry Appointments")
        ordering = ['-appointment_date', '-appointment_time']

    def __str__(self):
        return f"{self.get_appointment_type_display()} for {self.patient.full_name} on {self.appointment_date} at {self.appointment_time}"

    def save(self, *args, **kwargs):
        """
        Override save to set default duration based on appointment type.
        """
        # Set default duration based on appointment type if not specified
        if not self.pk and not self.duration_minutes:
            if self.appointment_type == 'check_up':
                self.duration_minutes = 30
            elif self.appointment_type == 'cleaning':
                self.duration_minutes = 60
            elif self.appointment_type == 'filling':
                self.duration_minutes = 60
            elif self.appointment_type == 'root_canal':
                self.duration_minutes = 90
            elif self.appointment_type == 'extraction':
                self.duration_minutes = 45
            elif self.appointment_type == 'crown':
                self.duration_minutes = 90
            elif self.appointment_type == 'bridge':
                self.duration_minutes = 90
            elif self.appointment_type == 'implant':
                self.duration_minutes = 120
            elif self.appointment_type == 'dentures':
                self.duration_minutes = 60
            elif self.appointment_type == 'orthodontics':
                self.duration_minutes = 45
            elif self.appointment_type == 'whitening':
                self.duration_minutes = 60
            elif self.appointment_type == 'emergency':
                self.duration_minutes = 45
            elif self.appointment_type == 'consultation':
                self.duration_minutes = 30
            else:
                self.duration_minutes = 15  # 30

        super().save(*args, **kwargs)
