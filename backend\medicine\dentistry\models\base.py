"""
Base models for the dentistry application.
"""
import uuid
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.utils.text import slugify

class DentistryBaseModel(models.Model):
    """
    Base model for all models in the dentistry application.
    """
    id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
        verbose_name=_("ID")
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_("Created at")
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_("Updated at")
    )
    is_deleted = models.BooleanField(
        default=False,
        verbose_name=_("Is deleted")
    )
    is_private = models.BooleanField(
        default=False,
        verbose_name=_("Is private")
    )
    privatized_by = models.UUIDField(
        null=True,
        blank=True,
        verbose_name=_("Privatized by")
    )
    owner = models.UUIDField(
        null=True,
        blank=True,
        verbose_name=_("Owner")
    )

    class Meta:
        abstract = True
        ordering = ['-created_at']

class DentistrySpecialty(DentistryBaseModel):
    """
    Dental specialty model.
    """
    name = models.CharField(
        max_length=100,
        verbose_name=_("Name")
    )
    description = models.TextField(
        null=True,
        blank=True,
        verbose_name=_("Description")
    )
    code = models.CharField(
        max_length=20,
        null=True,
        blank=True,
        verbose_name=_("Code")
    )
    slug = models.SlugField(
        max_length=100,
        unique=True,
        null=True,
        blank=True,
        verbose_name=_("Slug")
    )

    class Meta:
        verbose_name = _("Dental Specialty")
        verbose_name_plural = _("Dental Specialties")

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        # Generate slug if not provided
        if not self.slug:
            base_slug = slugify(self.name)

            # Check for existing slugs to avoid duplicates
            slug = base_slug
            counter = 1

            # Keep checking until we find a unique slug
            while DentistrySpecialty.objects.filter(slug=slug).exclude(pk=self.pk).exists():
                # If duplicate exists, append a number
                slug = f"{base_slug}-{counter}"
                counter += 1

            self.slug = slug

        super().save(*args, **kwargs)

class DentistryService(DentistryBaseModel):
    """
    Dental service model.
    """
    name = models.CharField(
        max_length=100,
        verbose_name=_("Name")
    )
    description = models.TextField(
        null=True,
        blank=True,
        verbose_name=_("Description")
    )
    price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name=_("Price")
    )
    duration = models.PositiveIntegerField(
        default=30,
        help_text=_("Duration in minutes"),
        verbose_name=_("Duration")
    )
    specialty = models.ForeignKey(
        DentistrySpecialty,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="services",
        verbose_name=_("Specialty")
    )

    class Meta:
        verbose_name = _("Dental Service")
        verbose_name_plural = _("Dental Services")

    def __str__(self):
        return self.name
