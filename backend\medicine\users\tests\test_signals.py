from django.test import TestCase
from django.contrib.auth import get_user_model
from subscriptions.models import DoctorSubscription, SubscriptionPackage

User = get_user_model()

class DoctorSubscriptionSignalTest(TestCase):
    """Test that a subscription is automatically created when a doctor is created."""

    def setUp(self):
        """Set up test data."""
        # Create a subscription package
        self.package = SubscriptionPackage.objects.create(
            name="Test Package",
            description="Test package for doctors",
            max_assistants=3,
            max_users=5,
            max_specialties=2,
            price_monthly="199.00",
            price_yearly="399.00",
            features=["Feature 1", "Feature 2"],
            is_active=True
        )

    def test_doctor_subscription_creation(self):
        """Test that a subscription is created when a doctor is created."""
        # Create a doctor
        doctor = User.objects.create_user(
            email="<EMAIL>",
            password="testpassword",
            first_name="Test",
            last_name="Doctor",
            user_type="doctor",
            specialization="Cardiology"
        )

        # Check that a subscription was created
        subscriptions = DoctorSubscription.objects.filter(doctor=doctor)
        self.assertEqual(subscriptions.count(), 1, "No subscription was created for the doctor")

        # Check that the subscription has the correct properties
        subscription = subscriptions.first()
        self.assertEqual(subscription.package, self.package)
        self.assertEqual(subscription.status, "active")
        self.assertEqual(subscription.billing_cycle, "monthly")
        
        # Check that the subscription is for 6 months
        self.assertEqual(subscription.metadata.get("is_six_month"), True)
        
        # Check that the subscription duration is approximately 180 days
        duration_days = (subscription.end_date - subscription.start_date).days
        self.assertTrue(150 <= duration_days <= 200, f"Subscription duration is {duration_days} days, expected around 180 days")
