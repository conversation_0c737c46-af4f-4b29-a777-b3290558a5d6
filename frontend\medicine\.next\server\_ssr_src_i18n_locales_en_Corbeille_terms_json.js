"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_i18n_locales_en_Corbeille_terms_json";
exports.ids = ["_ssr_src_i18n_locales_en_Corbeille_terms_json"];
exports.modules = {

/***/ "(ssr)/./src/i18n/locales/en/Corbeille/terms.json":
/*!**************************************************!*\
  !*** ./src/i18n/locales/en/Corbeille/terms.json ***!
  \**************************************************/
/***/ ((module) => {

module.exports = {"terms":"terms EN"};

/***/ })

};
;