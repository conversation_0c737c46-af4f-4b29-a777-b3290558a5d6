import secrets
import hashlib
import base64
import uuid
import json
from datetime import datetime, timedelta
from django.conf import settings
from django.utils import timezone
from .json_utils import UUIDEncoder, dumps

class LicenseGenerator:
    """
    Utility class for generating and validating secure license numbers.
    """

    @staticmethod
    def generate_license(user_id=None, specialization=None, expiry_days=365):
        """
        Generate a secure license number with the following components:
        - Random UUID
        - User ID (if provided)
        - Specialization code (if provided)
        - Timestamp
        - Expiry date
        - Secret key (from settings)
        - Digital signature

        Returns a tuple of (license_number, raw_data) where raw_data contains the
        unencrypted components for storage in the database.
        """
        # Generate a random UUID
        random_uuid = str(uuid.uuid4())

        # Current timestamp
        timestamp = int(datetime.now().timestamp())

        # Calculate expiry date
        expiry_date = (timezone.now() + timedelta(days=expiry_days)).isoformat()

        # Create a specialization code if provided
        spec_code = ""
        if specialization:
            # Take first 3 letters of specialization and convert to uppercase
            spec_code = specialization[:3].upper()

        # Create a raw data dictionary to store in the database
        raw_data = {
            'uuid': random_uuid,
            'user_id': user_id,
            'specialization': specialization,
            'spec_code': spec_code,
            'timestamp': timestamp,
            'expiry_date': expiry_date,
        }

        # Create the base string to hash
        base_string = f"{random_uuid}:{user_id or ''}:{spec_code}:{timestamp}:{expiry_date}:{settings.SECRET_KEY}"

        # Create a digital signature using SHA-256
        signature = hashlib.sha256(base_string.encode()).hexdigest()

        # Format the license number
        prefix = "DOC"
        if spec_code:
            prefix = f"DOC-{spec_code}"

        # Create the license number with format: PREFIX-UUID-TIMESTAMP-SIGNATURE_PART
        signature_part = signature[:8]  # Use first 8 chars of signature for brevity
        license_number = f"{prefix}-{random_uuid[:8]}-{timestamp}-{signature_part}"

        # Add the signature to the raw data
        raw_data['signature'] = signature
        raw_data['license_number'] = license_number

        return license_number, raw_data

    @staticmethod
    def validate_license(license_number, raw_data):
        """
        Validate a license number against the stored raw data.

        Returns a tuple of (is_valid, reason) where reason explains any validation failure.
        """
        try:
            # Check if the license number matches the stored one
            if license_number != raw_data.get('license_number'):
                return False, "License number does not match records"

            # Check if license has expired
            expiry_date = datetime.fromisoformat(raw_data.get('expiry_date'))
            if timezone.now() > expiry_date:
                return False, "License has expired"

            # Reconstruct the base string
            uuid_part = raw_data.get('uuid')
            user_id = raw_data.get('user_id')
            spec_code = raw_data.get('spec_code', '')
            timestamp = raw_data.get('timestamp')
            expiry_date_str = raw_data.get('expiry_date')

            base_string = f"{uuid_part}:{user_id or ''}:{spec_code}:{timestamp}:{expiry_date_str}:{settings.SECRET_KEY}"

            # Recalculate the signature
            calculated_signature = hashlib.sha256(base_string.encode()).hexdigest()

            # Compare with the stored signature
            if calculated_signature != raw_data.get('signature'):
                return False, "License signature is invalid"

            return True, "License is valid"

        except Exception as e:
            return False, f"Validation error: {str(e)}"

    @staticmethod
    def format_license_for_display(license_number):
        """
        Format a license number for display purposes, with proper spacing.
        """
        parts = license_number.split('-')
        return ' - '.join(parts)

    @staticmethod
    def generate_activation_code():
        """
        Generate a secure activation code.
        """
        # Generate a 12-character alphanumeric code
        return secrets.token_urlsafe(9)[:12].upper()
