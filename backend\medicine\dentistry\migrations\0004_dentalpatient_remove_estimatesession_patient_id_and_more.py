# Generated by Django 4.2.7 on 2025-05-25 09:43

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('dentistry', '0003_dentalpathology_toothmodificationestimate_indication_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='DentalPatient',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('first_name', models.CharField(max_length=100, verbose_name='Prénom')),
                ('last_name', models.CharField(max_length=100, verbose_name='Nom')),
                ('date_of_birth', models.DateField(verbose_name='Date de naissance')),
                ('gender', models.Char<PERSON>ield(choices=[('M', 'Masculin'), ('F', 'Féminin'), ('O', 'Autre')], max_length=1, verbose_name='Genre')),
                ('phone', models.CharField(max_length=20, verbose_name='Téléphone')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='Email')),
                ('address', models.TextField(verbose_name='Adresse')),
                ('blood_type', models.CharField(blank=True, choices=[('A+', 'A+'), ('A-', 'A-'), ('B+', 'B+'), ('B-', 'B-'), ('AB+', 'AB+'), ('AB-', 'AB-'), ('O+', 'O+'), ('O-', 'O-')], max_length=3)),
                ('allergies', models.TextField(blank=True, verbose_name='Allergies')),
                ('medical_conditions', models.TextField(blank=True, verbose_name='Conditions médicales')),
                ('medications', models.TextField(blank=True, verbose_name='Médicaments')),
                ('dental_history', models.TextField(blank=True, verbose_name='Historique dentaire')),
                ('last_cleaning', models.DateField(blank=True, null=True, verbose_name='Dernier nettoyage')),
                ('dental_anxiety_level', models.PositiveIntegerField(default=1, help_text="Niveau d'anxiété dentaire (1-10)", validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(10)])),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Dental Patient',
                'verbose_name_plural': 'Dental Patients',
                'ordering': ['last_name', 'first_name'],
            },
        ),
        migrations.RemoveField(
            model_name='estimatesession',
            name='patient_id',
        ),
        migrations.AddField(
            model_name='estimatesession',
            name='external_patient_id',
            field=models.CharField(blank=True, help_text='ID du patient externe - DEPRECATED: utiliser patient', max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='toothmodificationestimate',
            name='specialized_treatment_id',
            field=models.UUIDField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='toothmodificationestimate',
            name='specialized_treatment_type',
            field=models.ForeignKey(blank=True, limit_choices_to={'model__in': ['estheticdentistrytreatment', 'prosthodontictreatment', 'surgicaltreatment', 'orthodontictreatment', 'therapeutictreatment']}, null=True, on_delete=django.db.models.deletion.SET_NULL, to='contenttypes.contenttype'),
        ),
        migrations.CreateModel(
            name='TherapeuticTreatment',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('tooth_number', models.PositiveIntegerField(validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(32)], verbose_name='Numéro de dent')),
                ('treatment_date', models.DateTimeField(verbose_name='Date de traitement')),
                ('status', models.CharField(choices=[('planned', 'Planifié'), ('in_progress', 'En cours'), ('completed', 'Terminé'), ('cancelled', 'Annulé'), ('on_hold', 'En attente')], default='planned', max_length=20)),
                ('urgency', models.CharField(choices=[('low', 'Faible'), ('medium', 'Moyenne'), ('high', 'Élevée'), ('emergency', 'Urgence')], default='medium', max_length=20)),
                ('clinical_notes', models.TextField(blank=True, verbose_name='Notes cliniques')),
                ('treatment_plan', models.TextField(verbose_name='Plan de traitement')),
                ('estimated_duration', models.PositiveIntegerField(help_text='Durée estimée en minutes')),
                ('actual_duration', models.PositiveIntegerField(blank=True, help_text='Durée réelle en minutes', null=True)),
                ('estimated_cost', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Coût estimé')),
                ('actual_cost', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='Coût réel')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('treatment_type', models.CharField(choices=[('root_canal', 'Traitement de canal'), ('filling', 'Obturation'), ('scaling', 'Détartrage'), ('periodontal_therapy', 'Thérapie parodontale'), ('fluoride_treatment', 'Traitement au fluor'), ('sealant', 'Scellement')], max_length=20)),
                ('number_of_sessions', models.PositiveIntegerField(default=1, verbose_name='Nombre de séances')),
                ('current_session', models.PositiveIntegerField(default=1, verbose_name='Séance actuelle')),
                ('materials_used', models.JSONField(default=list, verbose_name='Matériaux utilisés')),
                ('techniques_used', models.JSONField(default=list, verbose_name='Techniques utilisées')),
                ('treatment_outcome', models.TextField(blank=True, verbose_name='Résultat du traitement')),
                ('success_rate', models.PositiveIntegerField(blank=True, help_text='Taux de succès (%)', null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('diagnosis', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='dentistry.patientdiagnosis')),
                ('doctor', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_treatments', to=settings.AUTH_USER_MODEL)),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='%(class)s_treatments', to='dentistry.dentalpatient')),
            ],
            options={
                'verbose_name': 'Therapeutic Treatment',
                'verbose_name_plural': 'Therapeutic Treatments',
            },
        ),
        migrations.CreateModel(
            name='SurgicalTreatment',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('tooth_number', models.PositiveIntegerField(validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(32)], verbose_name='Numéro de dent')),
                ('treatment_date', models.DateTimeField(verbose_name='Date de traitement')),
                ('status', models.CharField(choices=[('planned', 'Planifié'), ('in_progress', 'En cours'), ('completed', 'Terminé'), ('cancelled', 'Annulé'), ('on_hold', 'En attente')], default='planned', max_length=20)),
                ('urgency', models.CharField(choices=[('low', 'Faible'), ('medium', 'Moyenne'), ('high', 'Élevée'), ('emergency', 'Urgence')], default='medium', max_length=20)),
                ('clinical_notes', models.TextField(blank=True, verbose_name='Notes cliniques')),
                ('treatment_plan', models.TextField(verbose_name='Plan de traitement')),
                ('estimated_duration', models.PositiveIntegerField(help_text='Durée estimée en minutes')),
                ('actual_duration', models.PositiveIntegerField(blank=True, help_text='Durée réelle en minutes', null=True)),
                ('estimated_cost', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Coût estimé')),
                ('actual_cost', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='Coût réel')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('surgery_type', models.CharField(choices=[('extraction', 'Extraction'), ('implant', 'Implant'), ('bone_graft', 'Greffe osseuse'), ('sinus_lift', 'Sinus lift'), ('apicoectomy', 'Apicectomie'), ('periodontal_surgery', 'Chirurgie parodontale'), ('wisdom_tooth', 'Dent de sagesse')], max_length=20)),
                ('anesthesia_type', models.CharField(choices=[('local', 'Anesthésie locale'), ('sedation', 'Sédation'), ('general', 'Anesthésie générale')], default='local', max_length=20)),
                ('pre_operative_instructions', models.TextField(blank=True, verbose_name='Instructions pré-opératoires')),
                ('post_operative_instructions', models.TextField(blank=True, verbose_name='Instructions post-opératoires')),
                ('complications', models.TextField(blank=True, verbose_name='Complications')),
                ('follow_up_appointments', models.JSONField(default=list, verbose_name='Rendez-vous de suivi')),
                ('healing_progress', models.TextField(blank=True, verbose_name='Progression de la guérison')),
                ('medications_prescribed', models.JSONField(default=list, verbose_name='Médicaments prescrits')),
                ('diagnosis', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='dentistry.patientdiagnosis')),
                ('doctor', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_treatments', to=settings.AUTH_USER_MODEL)),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='%(class)s_treatments', to='dentistry.dentalpatient')),
            ],
            options={
                'verbose_name': 'Surgical Treatment',
                'verbose_name_plural': 'Surgical Treatments',
            },
        ),
        migrations.CreateModel(
            name='ProsthodonticTreatment',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('tooth_number', models.PositiveIntegerField(validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(32)], verbose_name='Numéro de dent')),
                ('treatment_date', models.DateTimeField(verbose_name='Date de traitement')),
                ('status', models.CharField(choices=[('planned', 'Planifié'), ('in_progress', 'En cours'), ('completed', 'Terminé'), ('cancelled', 'Annulé'), ('on_hold', 'En attente')], default='planned', max_length=20)),
                ('urgency', models.CharField(choices=[('low', 'Faible'), ('medium', 'Moyenne'), ('high', 'Élevée'), ('emergency', 'Urgence')], default='medium', max_length=20)),
                ('clinical_notes', models.TextField(blank=True, verbose_name='Notes cliniques')),
                ('treatment_plan', models.TextField(verbose_name='Plan de traitement')),
                ('estimated_duration', models.PositiveIntegerField(help_text='Durée estimée en minutes')),
                ('actual_duration', models.PositiveIntegerField(blank=True, help_text='Durée réelle en minutes', null=True)),
                ('estimated_cost', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Coût estimé')),
                ('actual_cost', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='Coût réel')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('prosthetic_type', models.CharField(choices=[('crown', 'Couronne'), ('bridge', 'Bridge'), ('partial_denture', 'Prothèse partielle'), ('complete_denture', 'Prothèse complète'), ('implant_crown', 'Couronne sur implant'), ('inlay_onlay', 'Inlay/Onlay')], max_length=20)),
                ('material', models.CharField(choices=[('ceramic', 'Céramique'), ('metal_ceramic', 'Métal-céramique'), ('zirconia', 'Zircone'), ('gold', 'Or'), ('titanium', 'Titane'), ('composite', 'Composite'), ('acrylic', 'Acrylique')], max_length=20)),
                ('impression_date', models.DateField(blank=True, null=True, verbose_name="Date d'empreinte")),
                ('try_in_date', models.DateField(blank=True, null=True, verbose_name="Date d'essayage")),
                ('delivery_date', models.DateField(blank=True, null=True, verbose_name='Date de livraison')),
                ('lab_name', models.CharField(blank=True, max_length=200, verbose_name='Nom du laboratoire')),
                ('lab_instructions', models.TextField(blank=True, verbose_name='Instructions laboratoire')),
                ('warranty_period', models.PositiveIntegerField(default=24, help_text='Période de garantie en mois')),
                ('warranty_conditions', models.TextField(blank=True, verbose_name='Conditions de garantie')),
                ('diagnosis', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='dentistry.patientdiagnosis')),
                ('doctor', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_treatments', to=settings.AUTH_USER_MODEL)),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='%(class)s_treatments', to='dentistry.dentalpatient')),
            ],
            options={
                'verbose_name': 'Prosthodontic Treatment',
                'verbose_name_plural': 'Prosthodontic Treatments',
            },
        ),
        migrations.CreateModel(
            name='OrthodonticTreatment',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('tooth_number', models.PositiveIntegerField(validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(32)], verbose_name='Numéro de dent')),
                ('treatment_date', models.DateTimeField(verbose_name='Date de traitement')),
                ('status', models.CharField(choices=[('planned', 'Planifié'), ('in_progress', 'En cours'), ('completed', 'Terminé'), ('cancelled', 'Annulé'), ('on_hold', 'En attente')], default='planned', max_length=20)),
                ('urgency', models.CharField(choices=[('low', 'Faible'), ('medium', 'Moyenne'), ('high', 'Élevée'), ('emergency', 'Urgence')], default='medium', max_length=20)),
                ('clinical_notes', models.TextField(blank=True, verbose_name='Notes cliniques')),
                ('treatment_plan', models.TextField(verbose_name='Plan de traitement')),
                ('estimated_duration', models.PositiveIntegerField(help_text='Durée estimée en minutes')),
                ('actual_duration', models.PositiveIntegerField(blank=True, help_text='Durée réelle en minutes', null=True)),
                ('estimated_cost', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Coût estimé')),
                ('actual_cost', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='Coût réel')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('treatment_type', models.CharField(choices=[('traditional_braces', 'Bagues traditionnelles'), ('ceramic_braces', 'Bagues céramiques'), ('lingual_braces', 'Bagues linguales'), ('clear_aligners', 'Gouttières transparentes'), ('retainer', 'Appareil de contention'), ('expander', 'Expanseur')], max_length=20)),
                ('initial_analysis', models.TextField(verbose_name='Analyse initiale')),
                ('treatment_objectives', models.TextField(verbose_name='Objectifs de traitement')),
                ('adjustment_appointments', models.JSONField(default=list, verbose_name="Rendez-vous d'ajustement")),
                ('progress_photos', models.JSONField(default=list, verbose_name='Photos de progression')),
                ('estimated_treatment_duration', models.PositiveIntegerField(help_text='Durée estimée en mois')),
                ('actual_treatment_duration', models.PositiveIntegerField(blank=True, help_text='Durée réelle en mois', null=True)),
                ('diagnosis', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='dentistry.patientdiagnosis')),
                ('doctor', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_treatments', to=settings.AUTH_USER_MODEL)),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='%(class)s_treatments', to='dentistry.dentalpatient')),
            ],
            options={
                'verbose_name': 'Orthodontic Treatment',
                'verbose_name_plural': 'Orthodontic Treatments',
            },
        ),
        migrations.CreateModel(
            name='EstheticDentistryTreatment',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('tooth_number', models.PositiveIntegerField(validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(32)], verbose_name='Numéro de dent')),
                ('treatment_date', models.DateTimeField(verbose_name='Date de traitement')),
                ('status', models.CharField(choices=[('planned', 'Planifié'), ('in_progress', 'En cours'), ('completed', 'Terminé'), ('cancelled', 'Annulé'), ('on_hold', 'En attente')], default='planned', max_length=20)),
                ('urgency', models.CharField(choices=[('low', 'Faible'), ('medium', 'Moyenne'), ('high', 'Élevée'), ('emergency', 'Urgence')], default='medium', max_length=20)),
                ('clinical_notes', models.TextField(blank=True, verbose_name='Notes cliniques')),
                ('treatment_plan', models.TextField(verbose_name='Plan de traitement')),
                ('estimated_duration', models.PositiveIntegerField(help_text='Durée estimée en minutes')),
                ('actual_duration', models.PositiveIntegerField(blank=True, help_text='Durée réelle en minutes', null=True)),
                ('estimated_cost', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Coût estimé')),
                ('actual_cost', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='Coût réel')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('treatment_type', models.CharField(choices=[('whitening', 'Blanchiment'), ('veneer', 'Facette'), ('bonding', 'Collage composite'), ('crown_aesthetic', 'Couronne esthétique'), ('smile_design', 'Design du sourire'), ('gum_contouring', 'Remodelage gingival')], max_length=20)),
                ('desired_shade', models.CharField(blank=True, choices=[('A1', 'A1'), ('A2', 'A2'), ('A3', 'A3'), ('A4', 'A4'), ('B1', 'B1'), ('B2', 'B2'), ('B3', 'B3'), ('B4', 'B4'), ('C1', 'C1'), ('C2', 'C2'), ('C3', 'C3'), ('C4', 'C4'), ('D1', 'D1'), ('D2', 'D2'), ('D3', 'D3'), ('D4', 'D4')], max_length=2)),
                ('current_shade', models.CharField(blank=True, choices=[('A1', 'A1'), ('A2', 'A2'), ('A3', 'A3'), ('A4', 'A4'), ('B1', 'B1'), ('B2', 'B2'), ('B3', 'B3'), ('B4', 'B4'), ('C1', 'C1'), ('C2', 'C2'), ('C3', 'C3'), ('C4', 'C4'), ('D1', 'D1'), ('D2', 'D2'), ('D3', 'D3'), ('D4', 'D4')], max_length=2)),
                ('achieved_shade', models.CharField(blank=True, choices=[('A1', 'A1'), ('A2', 'A2'), ('A3', 'A3'), ('A4', 'A4'), ('B1', 'B1'), ('B2', 'B2'), ('B3', 'B3'), ('B4', 'B4'), ('C1', 'C1'), ('C2', 'C2'), ('C3', 'C3'), ('C4', 'C4'), ('D1', 'D1'), ('D2', 'D2'), ('D3', 'D3'), ('D4', 'D4')], max_length=2)),
                ('materials_used', models.JSONField(default=list, verbose_name='Matériaux utilisés')),
                ('before_photos', models.JSONField(default=list, verbose_name='Photos avant')),
                ('after_photos', models.JSONField(default=list, verbose_name='Photos après')),
                ('patient_satisfaction', models.PositiveIntegerField(blank=True, help_text='Satisfaction patient (1-10)', null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(10)])),
                ('diagnosis', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='dentistry.patientdiagnosis')),
                ('doctor', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_treatments', to=settings.AUTH_USER_MODEL)),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='%(class)s_treatments', to='dentistry.dentalpatient')),
            ],
            options={
                'verbose_name': 'Esthetic Dentistry Treatment',
                'verbose_name_plural': 'Esthetic Dentistry Treatments',
            },
        ),
        migrations.AddField(
            model_name='estimatesession',
            name='patient',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='estimate_sessions', to='dentistry.dentalpatient', verbose_name='Patient'),
        ),
        migrations.AddField(
            model_name='toothmodificationestimate',
            name='patient',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='tooth_modifications', to='dentistry.dentalpatient', verbose_name='Patient'),
        ),
    ]
