"""
Modèles pour le système SVG dentaire dynamique avec champs configurables
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator, MaxValueValidator
from .base import DentistryBaseModel
from .patient import DentistryPatient


class DentalSvgConfiguration(DentistryBaseModel):
    """
    Configuration SVG pour une dent spécifique d'un patient
    """
    patient = models.ForeignKey(
        DentistryPatient,
        on_delete=models.CASCADE,
        related_name="dental_svg_configurations",
        verbose_name=_("Patient")
    )

    # Identifiant de la dent (1-32: 1-16 mâchoire supérieure, 17-32 mâchoire inférieure)
    tooth_id = models.PositiveSmallIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(32)],
        verbose_name=_("Tooth ID"),
        help_text=_("1-16: Upper jaw, 17-32: Lower jaw")
    )

    # Configuration SVG de base
    width = models.CharField(
        max_length=50,
        default="59.8625px",
        verbose_name=_("SVG Width"),
        help_text=_("Width of the SVG element")
    )

    position = models.CharField(
        max_length=100,
        default="0 0 50.8 172",
        verbose_name=_("SVG Position"),
        help_text=_("ViewBox position of the SVG")
    )

    # Restriction d'âge pour cette dent
    age_restriction = models.PositiveSmallIntegerField(
        null=True,
        blank=True,
        verbose_name=_("Age Restriction"),
        help_text=_("Hide this tooth if patient age is less than this value")
    )

    # États des traitements (cases à cocher)
    is_cleaning_active = models.BooleanField(
        default=False,
        verbose_name=_("Cleaning Active")
    )

    is_fluoride_active = models.BooleanField(
        default=False,
        verbose_name=_("Fluoride Active")
    )

    is_sealant_active = models.BooleanField(
        default=False,
        verbose_name=_("Sealant Active")
    )

    is_whitening_active = models.BooleanField(
        default=False,
        verbose_name=_("Whitening Active")
    )

    is_restoration_amalgam_active = models.BooleanField(
        default=False,
        verbose_name=_("Restoration Amalgam Active")
    )

    is_restoration_glass_ionomer_active = models.BooleanField(
        default=False,
        verbose_name=_("Restoration Glass Ionomer Active")
    )

    is_restoration_temporary_active = models.BooleanField(
        default=False,
        verbose_name=_("Restoration Temporary Active")
    )

    # Traitements prosthodontiques
    is_crown_zirconia_active = models.BooleanField(
        default=False,
        verbose_name=_("Crown Zirconia Active")
    )

    is_crown_gold_active = models.BooleanField(
        default=False,
        verbose_name=_("Crown Gold Active")
    )

    is_veneer_active = models.BooleanField(
        default=False,
        verbose_name=_("Veneer Active")
    )

    is_onlay_active = models.BooleanField(
        default=False,
        verbose_name=_("Onlay Active")
    )

    is_bridge_active = models.BooleanField(
        default=False,
        verbose_name=_("Bridge Active")
    )

    # Traitements chirurgicaux
    is_implant_active = models.BooleanField(
        default=False,
        verbose_name=_("Implant Active")
    )

    is_extraction_active = models.BooleanField(
        default=False,
        verbose_name=_("Extraction Active")
    )

    is_bone_graft_active = models.BooleanField(
        default=False,
        verbose_name=_("Bone Graft Active")
    )

    class Meta:
        verbose_name = _("Dental SVG Configuration")
        verbose_name_plural = _("Dental SVG Configurations")
        unique_together = ('patient', 'tooth_id')
        ordering = ['tooth_id']

    def __str__(self):
        if self.tooth_id is None:
            return f"{self.patient} - Tooth not assigned"
        jaw = "Upper" if self.tooth_id <= 16 else "Lower"
        tooth_num = self.tooth_id if self.tooth_id <= 16 else self.tooth_id - 16
        return f"{self.patient} - {jaw} Tooth {tooth_num} (ID: {self.tooth_id})"

    @property
    def is_upper_jaw(self):
        """Retourne True si la dent est dans la mâchoire supérieure"""
        if self.tooth_id is None:
            return False
        return self.tooth_id <= 16

    @property
    def is_lower_jaw(self):
        """Retourne True si la dent est dans la mâchoire inférieure"""
        if self.tooth_id is None:
            return False
        return self.tooth_id > 16

    @property
    def display_tooth_number(self):
        """Retourne le numéro d'affichage de la dent (1-16 pour chaque mâchoire)"""
        if self.tooth_id is None:
            return 0
        return self.tooth_id if self.tooth_id <= 16 else self.tooth_id - 16


class DentalSvgPath(DentistryBaseModel):
    """
    Chemin SVG individuel pour une dent
    """
    dental_svg_config = models.ForeignKey(
        DentalSvgConfiguration,
        on_delete=models.CASCADE,
        related_name="svg_paths",
        verbose_name=_("Dental SVG Configuration")
    )

    # Identifiant du path (1-16 pour base, 17+ pour traitements)
    path_id = models.CharField(
        max_length=10,
        verbose_name=_("Path ID"),
        help_text=_("1-16: Base paths, 17+: Treatment paths")
    )

    # Code CSS/classe du path
    code = models.CharField(
        max_length=50,
        verbose_name=_("CSS Code"),
        help_text=_("CSS class for styling (e.g., st0, st26)")
    )

    # Chemin SVG
    path = models.TextField(
        verbose_name=_("SVG Path"),
        help_text=_("SVG path data (d attribute)")
    )

    # Style personnalisé
    style = models.JSONField(
        default=dict,
        verbose_name=_("Custom Style"),
        help_text=_("Custom CSS styles as JSON (e.g., {'cursor': 'pointer'})")
    )

    # Transformation de position
    transform = models.CharField(
        max_length=200,
        blank=True,
        verbose_name=_("Transform"),
        help_text=_("SVG transform attribute")
    )

    # Type de traitement associé
    treatment_type = models.CharField(
        max_length=50,
        choices=[
            ('base', _('Base Tooth')),
            ('cleaning', _('Cleaning')),
            ('fluoride', _('Fluoride')),
            ('sealant', _('Sealant')),
            ('whitening', _('Whitening')),
            ('restoration_amalgam', _('Restoration Amalgam')),
            ('restoration_glass_ionomer', _('Restoration Glass Ionomer')),
            ('restoration_temporary', _('Restoration Temporary')),
            ('crown_zirconia', _('Crown Zirconia')),
            ('crown_gold', _('Crown Gold')),
            ('veneer', _('Veneer')),
            ('onlay', _('Onlay')),
            ('bridge', _('Bridge')),
            ('implant', _('Implant')),
            ('extraction', _('Extraction')),
            ('bone_graft', _('Bone Graft')),
        ],
        default='base',
        verbose_name=_("Treatment Type")
    )

    # Ordre d'affichage
    display_order = models.PositiveSmallIntegerField(
        default=0,
        verbose_name=_("Display Order")
    )

    # Actif ou non
    is_active = models.BooleanField(
        default=True,
        verbose_name=_("Is Active"),
        help_text=_("Whether this path should be displayed")
    )

    class Meta:
        verbose_name = _("Dental SVG Path")
        verbose_name_plural = _("Dental SVG Paths")
        unique_together = ('dental_svg_config', 'path_id')
        ordering = ['display_order', 'path_id']

    def __str__(self):
        return f"{self.dental_svg_config} - Path {self.path_id} ({self.treatment_type})"


class DentalTreatmentTemplate(DentistryBaseModel):
    """
    Template de traitement avec les paths prédéfinis
    """
    name = models.CharField(
        max_length=100,
        unique=True,
        verbose_name=_("Treatment Name")
    )

    description = models.TextField(
        blank=True,
        verbose_name=_("Description")
    )

    # Paths à ajouter pour ce traitement
    template_paths = models.JSONField(
        default=list,
        verbose_name=_("Template Paths"),
        help_text=_("List of path configurations for this treatment")
    )

    # Paths à supprimer pour ce traitement
    paths_to_remove = models.JSONField(
        default=list,
        verbose_name=_("Paths to Remove"),
        help_text=_("List of path IDs to remove when applying this treatment")
    )

    # Restriction d'âge
    min_age = models.PositiveSmallIntegerField(
        null=True,
        blank=True,
        verbose_name=_("Minimum Age"),
        help_text=_("Minimum patient age for this treatment")
    )

    class Meta:
        verbose_name = _("Dental Treatment Template")
        verbose_name_plural = _("Dental Treatment Templates")
        ordering = ['name']

    def __str__(self):
        return self.name
