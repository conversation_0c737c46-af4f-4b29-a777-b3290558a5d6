"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_i18n_locales_en_common_json";
exports.ids = ["_rsc_src_i18n_locales_en_common_json"];
exports.modules = {

/***/ "(rsc)/./src/i18n/locales/en/common.json":
/*!*****************************************!*\
  !*** ./src/i18n/locales/en/common.json ***!
  \*****************************************/
/***/ ((module) => {

module.exports = {};

/***/ })

};
;