# Generated by Django 5.1.3 on 2025-05-16 16:23

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0012_doctorlicense_package_price'),
    ]

    operations = [
        migrations.AddField(
            model_name='doctorlicense',
            name='subscription_period',
            field=models.CharField(default='6months', help_text='Subscription period (6months or 1year)', max_length=20),
        ),
        migrations.CreateModel(
            name='SubscriptionUpgrade',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('old_subscription_period', models.CharField(max_length=20)),
                ('new_subscription_period', models.CharField(max_length=20)),
                ('days_used', models.IntegerField(help_text='Number of days used in the old subscription')),
                ('days_in_new_subscription', models.IntegerField(help_text='Number of days in the new subscription')),
                ('old_price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('new_price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('upgrade_price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('transaction_id', models.CharField(blank=True, max_length=100, null=True)),
                ('payment_method', models.CharField(blank=True, max_length=50, null=True)),
                ('payment_status', models.CharField(blank=True, max_length=50, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('license', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='upgrades', to='users.doctorlicense')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='subscription_upgrades', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Subscription Upgrade',
                'verbose_name_plural': 'Subscription Upgrades',
            },
        ),
    ]
