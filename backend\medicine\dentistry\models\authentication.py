"""
Authentication models for the dentistry application.
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.conf import settings
from dentistry.models.base import DentistryBaseModel

class DentistryRole(DentistryBaseModel):
    """
    Dentistry-specific role model.
    """
    name = models.CharField(
        max_length=100,
        verbose_name=_("Role Name")
    )
    description = models.TextField(
        blank=True,
        verbose_name=_("Description")
    )

    # Permissions
    can_view_patients = models.BooleanField(
        default=True,
        verbose_name=_("Can View Patients")
    )
    can_edit_patients = models.BooleanField(
        default=False,
        verbose_name=_("Can Edit Patients")
    )
    can_view_appointments = models.BooleanField(
        default=True,
        verbose_name=_("Can View Appointments")
    )
    can_schedule_appointments = models.BooleanField(
        default=False,
        verbose_name=_("Can Schedule Appointments")
    )
    can_view_medical_records = models.BooleanField(
        default=True,
        verbose_name=_("Can View Medical Records")
    )
    can_create_medical_records = models.BooleanField(
        default=False,
        verbose_name=_("Can Create Medical Records")
    )
    can_edit_medical_records = models.BooleanField(
        default=False,
        verbose_name=_("Can Edit Medical Records")
    )
    can_view_treatments = models.BooleanField(
        default=True,
        verbose_name=_("Can View Treatments")
    )
    can_create_treatments = models.BooleanField(
        default=False,
        verbose_name=_("Can Create Treatments")
    )
    can_edit_treatments = models.BooleanField(
        default=False,
        verbose_name=_("Can Edit Treatments")
    )
    can_view_lab_orders = models.BooleanField(
        default=True,
        verbose_name=_("Can View Lab Orders")
    )
    can_create_lab_orders = models.BooleanField(
        default=False,
        verbose_name=_("Can Create Lab Orders")
    )
    can_edit_lab_orders = models.BooleanField(
        default=False,
        verbose_name=_("Can Edit Lab Orders")
    )
    can_view_billing = models.BooleanField(
        default=False,
        verbose_name=_("Can View Billing")
    )
    can_create_billing = models.BooleanField(
        default=False,
        verbose_name=_("Can Create Billing")
    )
    can_edit_billing = models.BooleanField(
        default=False,
        verbose_name=_("Can Edit Billing")
    )
    can_manage_staff = models.BooleanField(
        default=False,
        verbose_name=_("Can Manage Staff")
    )
    can_manage_settings = models.BooleanField(
        default=False,
        verbose_name=_("Can Manage Settings")
    )

    class Meta:
        verbose_name = _("Dentistry Role")
        verbose_name_plural = _("Dentistry Roles")
        ordering = ['name']

    def __str__(self):
        return self.name

class DentistryStaffProfile(DentistryBaseModel):
    """
    Dentistry-specific staff profile model.
    """
    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="dentistry_staff_profile",
        verbose_name=_("User")
    )
    role = models.ForeignKey(
        DentistryRole,
        on_delete=models.PROTECT,
        related_name="staff_profiles",
        verbose_name=_("Role")
    )

    # Personal information
    job_title = models.CharField(
        max_length=100,
        verbose_name=_("Job Title")
    )
    license_number = models.CharField(
        max_length=50,
        blank=True,
        verbose_name=_("License Number")
    )
    specialization = models.CharField(
        max_length=100,
        blank=True,
        verbose_name=_("Specialization")
    )

    # Contact information
    work_phone = models.CharField(
        max_length=20,
        blank=True,
        verbose_name=_("Work Phone")
    )
    work_email = models.EmailField(
        blank=True,
        verbose_name=_("Work Email")
    )

    # Employment information
    hire_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_("Hire Date")
    )
    end_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_("End Date")
    )
    is_active = models.BooleanField(
        default=True,
        verbose_name=_("Is Active")
    )

    # Schedule information
    working_days = models.JSONField(
        default=list,
        verbose_name=_("Working Days"),
        help_text=_("List of working days (0=Monday, 6=Sunday)")
    )
    working_hours = models.JSONField(
        default=dict,
        verbose_name=_("Working Hours"),
        help_text=_("Working hours for each day")
    )

    # Additional information
    bio = models.TextField(
        blank=True,
        verbose_name=_("Biography")
    )
    profile_image = models.ImageField(
        upload_to='dentistry/staff/',
        null=True,
        blank=True,
        verbose_name=_("Profile Image")
    )
    notes = models.TextField(
        blank=True,
        verbose_name=_("Notes")
    )

    class Meta:
        verbose_name = _("Dentistry Staff Profile")
        verbose_name_plural = _("Dentistry Staff Profiles")
        ordering = ['user__last_name', 'user__first_name']

    def __str__(self):
        return f"{self.user.get_full_name()} - {self.job_title}"

    @property
    def full_name(self):
        """
        Returns the staff member's full name.
        """
        return self.user.get_full_name()

    @property
    def is_dentist(self):
        """
        Returns whether the staff member is a dentist.
        """
        return 'dentist' in self.job_title.lower() or 'doctor' in self.job_title.lower()

    @property
    def is_hygienist(self):
        """
        Returns whether the staff member is a hygienist.
        """
        return 'hygienist' in self.job_title.lower()

    @property
    def is_assistant(self):
        """
        Returns whether the staff member is an assistant.
        """
        return 'assistant' in self.job_title.lower()

    @property
    def is_receptionist(self):
        """
        Returns whether the staff member is a receptionist.
        """
        return 'receptionist' in self.job_title.lower() or 'front desk' in self.job_title.lower()

    @property
    def is_manager(self):
        """
        Returns whether the staff member is a manager.
        """
        return 'manager' in self.job_title.lower() or 'director' in self.job_title.lower()
