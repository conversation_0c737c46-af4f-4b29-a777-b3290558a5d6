# Generated by Django 5.1.3 on 2025-04-30 16:25

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='SubscriptionPackage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Package name', max_length=100)),
                ('description', models.TextField(blank=True, help_text='Package description', null=True)),
                ('max_assistants', models.PositiveIntegerField(default=1, help_text='Maximum number of assistants allowed')),
                ('max_users', models.PositiveIntegerField(default=2, help_text='Maximum number of users allowed')),
                ('max_specialties', models.PositiveIntegerField(default=1, help_text='Maximum number of specialties allowed')),
                ('price_monthly', models.DecimalField(decimal_places=2, help_text='Monthly price', max_digits=10)),
                ('price_yearly', models.DecimalField(decimal_places=2, help_text='Yearly price', max_digits=10)),
                ('features', models.JSONField(default=list, help_text='List of features included in this package')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this package is available for purchase')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Subscription Package',
                'verbose_name_plural': 'Subscription Packages',
            },
        ),
        migrations.CreateModel(
            name='DoctorSubscription',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('active', 'Active'), ('expired', 'Expired'), ('cancelled', 'Cancelled'), ('pending', 'Pending')], default='active', max_length=20)),
                ('billing_cycle', models.CharField(choices=[('monthly', 'Monthly'), ('annual', 'Annual')], default='monthly', max_length=20)),
                ('start_date', models.DateTimeField(default=django.utils.timezone.now)),
                ('end_date', models.DateTimeField()),
                ('current_assistant_count', models.PositiveIntegerField(default=0)),
                ('current_user_count', models.PositiveIntegerField(default=0)),
                ('current_specialty_count', models.PositiveIntegerField(default=0)),
                ('last_payment_date', models.DateTimeField(blank=True, null=True)),
                ('next_payment_date', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('doctor', models.ForeignKey(limit_choices_to={'user_type': 'doctor'}, on_delete=django.db.models.deletion.CASCADE, related_name='subscriptions', to=settings.AUTH_USER_MODEL)),
                ('package', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='doctor_subscriptions', to='subscriptions.subscriptionpackage')),
            ],
            options={
                'verbose_name': 'Doctor Subscription',
                'verbose_name_plural': 'Doctor Subscriptions',
            },
        ),
        migrations.CreateModel(
            name='Coupon',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(help_text='Coupon code', max_length=50, unique=True)),
                ('description', models.TextField(blank=True, help_text='Coupon description', null=True)),
                ('discount_type', models.CharField(choices=[('percentage', 'Percentage'), ('fixed', 'Fixed Amount')], default='percentage', max_length=20)),
                ('discount_value', models.DecimalField(decimal_places=2, help_text='Discount value', max_digits=10)),
                ('valid_from', models.DateTimeField(default=django.utils.timezone.now)),
                ('valid_until', models.DateTimeField(blank=True, null=True)),
                ('max_uses', models.PositiveIntegerField(blank=True, help_text='Maximum number of times this coupon can be used', null=True)),
                ('current_uses', models.PositiveIntegerField(default=0)),
                ('minimum_purchase', models.DecimalField(decimal_places=2, default=0, help_text='Minimum purchase amount', max_digits=10)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('applicable_packages', models.ManyToManyField(blank=True, related_name='applicable_coupons', to='subscriptions.subscriptionpackage')),
            ],
            options={
                'verbose_name': 'Coupon',
                'verbose_name_plural': 'Coupons',
            },
        ),
        migrations.CreateModel(
            name='SubscriptionTransaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('completed', 'Completed'), ('failed', 'Failed'), ('refunded', 'Refunded')], default='pending', max_length=20)),
                ('payment_method', models.CharField(blank=True, max_length=50, null=True)),
                ('transaction_id', models.CharField(blank=True, max_length=100, null=True)),
                ('discount_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('coupon', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='transactions', to='subscriptions.coupon')),
                ('subscription', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transactions', to='subscriptions.doctorsubscription')),
            ],
            options={
                'verbose_name': 'Subscription Transaction',
                'verbose_name_plural': 'Subscription Transactions',
            },
        ),
    ]
