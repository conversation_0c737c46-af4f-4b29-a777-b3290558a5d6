# backend/dental_medicine/dentistry/views/complete_patient.py

from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.db import transaction
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import datetime, timedelta
import logging

from dentistry.models.patient import DentistryPatient
from dentistry.serializers.patient import DentistryPatientSerializer
from users.models import User
from users.views.user_serializers import UserSerializer

logger = logging.getLogger(__name__)

User = get_user_model()

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def create_complete_patient(request):
    """
    Créer un patient complet avec toutes les informations
    et optionnellement un rendez-vous associé
    """
    try:
        data = request.data
        logger.info(f"Création d'un patient complet: {data}")

        # Validation des données requises
        user_data = data.get('user_data', {})
        dentistry_profile = data.get('dentistry_profile', {})
        visit_info = data.get('visit_info', {})
        appointment_data = data.get('appointment_data', {})

        # Validation des champs obligatoires
        required_fields = ['first_name', 'last_name', 'email', 'phone']
        missing_fields = [field for field in required_fields if not user_data.get(field)]

        if missing_fields:
            return Response({
                'error': 'Champs obligatoires manquants',
                'missing_fields': missing_fields
            }, status=status.HTTP_400_BAD_REQUEST)

        # Vérifier si l'email existe déjà
        if User.objects.filter(email=user_data['email']).exists():
            return Response({
                'error': 'Un utilisateur avec cet email existe déjà'
            }, status=status.HTTP_409_CONFLICT)

        # Transaction atomique pour créer le patient complet
        with transaction.atomic():
            # 1. Créer l'utilisateur de base
            user = User.objects.create_user(
                email=user_data['email'],
                first_name=user_data['first_name'],
                last_name=user_data['last_name'],
                phone=user_data.get('phone', ''),
                user_type='patient',
                is_active=True
            )

            # Ajouter les informations supplémentaires
            if user_data.get('date_of_birth'):
                try:
                    user.date_of_birth = datetime.strptime(
                        user_data['date_of_birth'], '%Y-%m-%d'
                    ).date()
                except ValueError:
                    logger.warning(f"Format de date invalide: {user_data['date_of_birth']}")

            user.gender = user_data.get('gender', '')
            user.address = user_data.get('address', '')
            user.city = user_data.get('city', '')
            user.state = user_data.get('state', '')
            user.postal_code = user_data.get('postal_code', '')
            user.save()

            # 2. Créer le profil dentaire spécialisé
            dentistry_patient = DentistryPatient.objects.create(
                patient=user,
                dental_history=dentistry_profile.get('dental_history', ''),
                dental_insurance_provider=dentistry_profile.get('dental_insurance_provider', ''),
                dental_insurance_policy_number=dentistry_profile.get('dental_insurance_policy_number', ''),
                has_dentures=dentistry_profile.get('has_dentures', False),
                has_implants=dentistry_profile.get('has_implants', False),
                has_braces=dentistry_profile.get('has_braces', False),
                teeth_grinding=dentistry_profile.get('teeth_grinding', False),
                sensitive_teeth=dentistry_profile.get('sensitive_teeth', False)
            )

            # Initialiser le graphique dentaire
            dentistry_patient.initialize_teeth_chart()

            # 3. Ajouter les informations étendues au profil
            extended_info = {
                'allergies': dentistry_profile.get('allergies', ''),
                'emergency_contact_name': dentistry_profile.get('emergency_contact_name', ''),
                'emergency_contact_phone': dentistry_profile.get('emergency_contact_phone', ''),
                'emergency_contact_relationship': dentistry_profile.get('emergency_contact_relationship', ''),
                'national_id_number': dentistry_profile.get('national_id_number', ''),
                'passport_number': dentistry_profile.get('passport_number', ''),
                'marital_status': dentistry_profile.get('marital_status', ''),
                'additional_notes': dentistry_profile.get('additional_notes', ''),
                'visit_info': visit_info
            }

            # Stocker les informations étendues dans le champ teeth_chart (JSON)
            current_chart = dentistry_patient.teeth_chart or {}
            current_chart['extended_info'] = extended_info
            dentistry_patient.teeth_chart = current_chart
            dentistry_patient.save()

            # 4. Créer le rendez-vous si les données sont fournies
            appointment = None
            if appointment_data and appointment_data.get('start_time'):
                try:
                    from dentistry.models.appointment import DentistryAppointment
                    from dentistry.models.doctor import DentistryDoctor

                    start_datetime = datetime.fromisoformat(
                        appointment_data['start_time'].replace('Z', '+00:00')
                    )
                    end_datetime = datetime.fromisoformat(
                        appointment_data['end_time'].replace('Z', '+00:00')
                    )

                    # Calculer la durée en minutes
                    duration_minutes = int((end_datetime - start_datetime).total_seconds() / 60)

                    # Récupérer ou créer le profil médecin
                    try:
                        doctor_profile = DentistryDoctor.objects.get(user=request.user)
                    except DentistryDoctor.DoesNotExist:
                        # Créer un profil médecin basique si nécessaire
                        doctor_profile = DentistryDoctor.objects.create(
                            user=request.user,
                            license_number=getattr(request.user, 'license_number', ''),
                            specialization=getattr(request.user, 'specialization', 'general')
                        )

                    appointment = DentistryAppointment.objects.create(
                        patient=dentistry_patient,
                        doctor=doctor_profile,
                        appointment_date=start_datetime.date(),
                        appointment_time=start_datetime.time(),
                        duration_minutes=duration_minutes,
                        appointment_type=appointment_data.get('consultation_type', 'consultation'),
                        status='scheduled',
                        notes=appointment_data.get('notes', ''),
                        reason=appointment_data.get('reason', 'Consultation générale')
                    )
                    logger.info(f"Rendez-vous créé: {appointment.id}")
                except Exception as e:
                    logger.error(f"Erreur lors de la création du rendez-vous: {e}")
                    # Ne pas faire échouer la création du patient pour un problème de rendez-vous

            # 5. Préparer la réponse
            user_serializer = UserSerializer(user)
            dentistry_serializer = DentistryPatientSerializer(dentistry_patient)

            response_data = {
                'patient': user_serializer.data,
                'dentistry_profile': dentistry_serializer.data,
                'success': True,
                'message': f'Patient {user.first_name} {user.last_name} créé avec succès'
            }

            if appointment:
                from dentistry.serializers.appointment import DentistryAppointmentSerializer
                appointment_serializer = DentistryAppointmentSerializer(appointment)
                response_data['appointment'] = appointment_serializer.data

            logger.info(f"Patient complet créé avec succès: {user.id}")
            return Response(response_data, status=status.HTTP_201_CREATED)

    except Exception as e:
        logger.error(f"Erreur lors de la création du patient complet: {e}")
        return Response({
            'error': 'Erreur interne du serveur',
            'detail': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def get_doctors_list(request):
    """
    Récupérer la liste des médecins disponibles
    """
    try:
        from dentistry.models.doctor import DentistryDoctor

        # Récupérer tous les profils de médecins dentaires
        dentistry_doctors = DentistryDoctor.objects.filter(
            user__is_active=True
        ).select_related('user').order_by('user__last_name', 'user__first_name')

        doctors_data = []
        for doctor_profile in dentistry_doctors:
            user = doctor_profile.user
            doctors_data.append({
                'id': str(doctor_profile.id),
                'user_id': str(user.id),
                'first_name': user.first_name,
                'last_name': user.last_name,
                'full_name': f"Dr. {user.first_name} {user.last_name}",
                'specialization': doctor_profile.specialization,
                'license_number': doctor_profile.license_number,
                'is_assistant': doctor_profile.specialization == 'assistant',
                'is_available': True,
                'email': user.email
            })

        # Si aucun profil dentaire n'existe, créer des données à partir des utilisateurs
        if not doctors_data:
            users = User.objects.filter(
                user_type__in=['doctor', 'assistant'],
                is_active=True
            ).order_by('last_name', 'first_name')

            for user in users:
                doctors_data.append({
                    'id': str(user.id),
                    'user_id': str(user.id),
                    'first_name': user.first_name,
                    'last_name': user.last_name,
                    'full_name': f"Dr. {user.first_name} {user.last_name}",
                    'specialization': getattr(user, 'specialization', 'general'),
                    'license_number': getattr(user, 'license_number', ''),
                    'is_assistant': user.user_type == 'assistant',
                    'is_available': True,
                    'email': user.email
                })

        return Response({
            'results': doctors_data,
            'count': len(doctors_data)
        }, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Erreur lors de la récupération des médecins: {e}")
        return Response({
            'error': 'Erreur lors de la récupération des médecins',
            'detail': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def get_patient_complete_info(request, patient_id):
    """
    Récupérer toutes les informations complètes d'un patient
    """
    try:
        # Récupérer le patient
        user = User.objects.get(id=patient_id, user_type='patient')

        # Récupérer le profil dentaire
        try:
            dentistry_patient = DentistryPatient.objects.get(patient=user)
        except DentistryPatient.DoesNotExist:
            return Response({
                'error': 'Profil dentaire non trouvé pour ce patient'
            }, status=status.HTTP_404_NOT_FOUND)

        # Sérialiser les données
        user_serializer = UserSerializer(user)
        dentistry_serializer = DentistryPatientSerializer(dentistry_patient)

        # Récupérer les rendez-vous
        appointments = dentistry_patient.appointments.all().order_by('-start_time')[:10]

        response_data = {
            'patient': user_serializer.data,
            'dentistry_profile': dentistry_serializer.data,
            'appointments_count': appointments.count(),
            'last_appointment': None
        }

        if appointments:
            from dentistry.serializers.appointment import DentistryAppointmentSerializer
            response_data['last_appointment'] = DentistryAppointmentSerializer(
                appointments[0]
            ).data

        return Response(response_data, status=status.HTTP_200_OK)

    except User.DoesNotExist:
        return Response({
            'error': 'Patient non trouvé'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        logger.error(f"Erreur lors de la récupération du patient: {e}")
        return Response({
            'error': 'Erreur interne du serveur',
            'detail': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['PATCH'])
@permission_classes([permissions.IsAuthenticated])
def update_complete_patient(request, patient_id):
    """
    Mettre à jour les informations complètes d'un patient
    """
    try:
        data = request.data

        # Récupérer le patient
        user = User.objects.get(id=patient_id, user_type='patient')
        dentistry_patient = DentistryPatient.objects.get(patient=user)

        with transaction.atomic():
            # Mettre à jour les informations utilisateur
            user_data = data.get('user_data', {})
            for field, value in user_data.items():
                if hasattr(user, field) and value is not None:
                    setattr(user, field, value)
            user.save()

            # Mettre à jour le profil dentaire
            dentistry_profile = data.get('dentistry_profile', {})
            for field, value in dentistry_profile.items():
                if hasattr(dentistry_patient, field) and value is not None:
                    setattr(dentistry_patient, field, value)

            # Mettre à jour les informations étendues
            if dentistry_patient.teeth_chart:
                current_chart = dentistry_patient.teeth_chart
                if 'extended_info' not in current_chart:
                    current_chart['extended_info'] = {}

                extended_info = current_chart['extended_info']
                visit_info = data.get('visit_info', {})

                extended_info.update({
                    'allergies': dentistry_profile.get('allergies', extended_info.get('allergies', '')),
                    'emergency_contact_name': dentistry_profile.get('emergency_contact_name', extended_info.get('emergency_contact_name', '')),
                    'emergency_contact_phone': dentistry_profile.get('emergency_contact_phone', extended_info.get('emergency_contact_phone', '')),
                    'visit_info': visit_info
                })

                dentistry_patient.teeth_chart = current_chart

            dentistry_patient.save()

        # Préparer la réponse
        user_serializer = UserSerializer(user)
        dentistry_serializer = DentistryPatientSerializer(dentistry_patient)

        return Response({
            'patient': user_serializer.data,
            'dentistry_profile': dentistry_serializer.data,
            'success': True,
            'message': 'Patient mis à jour avec succès'
        }, status=status.HTTP_200_OK)

    except User.DoesNotExist:
        return Response({
            'error': 'Patient non trouvé'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        logger.error(f"Erreur lors de la mise à jour du patient: {e}")
        return Response({
            'error': 'Erreur interne du serveur',
            'detail': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
