"""
Commande pour vérifier que seuls les 9 groupes organisés sont affichés dans l'administration
"""
from django.core.management.base import BaseCommand
from django.contrib import admin
from django.apps import apps


class Command(BaseCommand):
    help = 'Check that only organized groups are displayed in admin'

    def handle(self, *args, **options):
        """
        Vérifie que seuls les modèles des 9 groupes organisés sont enregistrés
        """
        self.stdout.write(self.style.SUCCESS('Checking organized admin groups...'))
        
        # Modèles attendus dans les 9 groupes organisés
        expected_models = {
            # GROUPE 1: GESTION DES PATIENTS ET MÉDECINS
            'DentistryPatient': 'Groupe 1 - Patients et Médecins',
            'DentistryDoctor': 'Groupe 1 - Patients et Médecins',
            'DentistryRole': 'Groupe 1 - Patients et Médecins',
            'DentistryStaffProfile': 'Groupe 1 - Patients et Médecins',
            
            # GROUPE 2: RENDEZ-VOUS ET CONSULTATIONS
            'DentistryAppointment': 'Groupe 2 - Rendez-vous et Consultations',
            'DentalConsultation': 'Groupe 2 - Rendez-vous et Consultations',
            'Tooth': 'Groupe 2 - Rendez-vous et Consultations',
            
            # GROUPE 3: TRAITEMENTS ET PROCÉDURES
            'DentalImaging': 'Groupe 3 - Traitements et Procédures',
            'DentalTreatment': 'Groupe 3 - Traitements et Procédures',
            'DentalProcedure': 'Groupe 3 - Traitements et Procédures',
            'DentistryMedicalRecord': 'Groupe 3 - Traitements et Procédures',
            'DentistryDiagnosis': 'Groupe 3 - Traitements et Procédures',
            'DentistryProcedure': 'Groupe 3 - Traitements et Procédures',
            'DentistryMaterial': 'Groupe 3 - Traitements et Procédures',
            
            # GROUPE 4: LABORATOIRE ET FACTURATION
            'DentalLaboratory': 'Groupe 4 - Laboratoire et Facturation',
            'LabWorkOrder': 'Groupe 4 - Laboratoire et Facturation',
            'DentistryBillingCode': 'Groupe 4 - Laboratoire et Facturation',
            'DentistryInvoice': 'Groupe 4 - Laboratoire et Facturation',
            'DentistryPatientInsurance': 'Groupe 4 - Laboratoire et Facturation',
            
            # GROUPE 5: COMMENTAIRES ET AVIS
            'DentistryComment': 'Groupe 5 - Commentaires et Avis',
            'DentistryReview': 'Groupe 5 - Commentaires et Avis',
            'DentistryDoctorNote': 'Groupe 5 - Commentaires et Avis',
            
            # GROUPE 6: SYSTÈME SVG DENTAIRE CLASSIQUE
            'DentalSvgData': 'Groupe 6 - Système SVG Dentaire Classique',
            'DentalModification': 'Groupe 6 - Système SVG Dentaire Classique',
            
            # GROUPE 7: CONFIGURATION ET NOTIFICATIONS
            'DentistryConfiguration': 'Groupe 7 - Configuration et Notifications',
            'DentistryTemplate': 'Groupe 7 - Configuration et Notifications',
            'DentistryCustomField': 'Groupe 7 - Configuration et Notifications',
            'DentistryCustomFieldValue': 'Groupe 7 - Configuration et Notifications',
            'DentistryNotification': 'Groupe 7 - Configuration et Notifications',
            'DentalReminderSetting': 'Groupe 7 - Configuration et Notifications',
            
            # GROUPE 8: SITE WEB ET CONTENU
            'DentistryWebsiteSettings': 'Groupe 8 - Site Web et Contenu',
            'DentistryPage': 'Groupe 8 - Site Web et Contenu',
            'DentistryBeforeAfterCase': 'Groupe 8 - Site Web et Contenu',
            
            # GROUPE 9: SYSTÈME SVG DENTAIRE DYNAMIQUE (NOUVEAU)
            'DentalSvgConfiguration': 'Groupe 9 - Système SVG Dentaire Dynamique',
            'DentalSvgPath': 'Groupe 9 - Système SVG Dentaire Dynamique',
            'DentalTreatmentTemplate': 'Groupe 9 - Système SVG Dentaire Dynamique',
        }
        
        # Obtenir tous les modèles enregistrés
        registered_models = []
        for model in admin.site._registry:
            registered_models.append(model.__name__)
        
        self.stdout.write(f'\n📊 Modèles enregistrés dans l\'administration: {len(registered_models)}')
        
        # Vérifier les modèles attendus
        found_expected = 0
        missing_expected = 0
        
        self.stdout.write(f'\n🎯 Vérification des 9 groupes organisés:')
        
        for model_name, group in expected_models.items():
            if model_name in registered_models:
                found_expected += 1
                self.stdout.write(f'✅ {model_name} - {group}')
            else:
                missing_expected += 1
                self.stdout.write(f'❌ {model_name} - {group} (MANQUANT)')
        
        # Vérifier les modèles non attendus (qui ne devraient pas être là)
        unexpected_models = []
        for model_name in registered_models:
            if model_name not in expected_models:
                unexpected_models.append(model_name)
        
        if unexpected_models:
            self.stdout.write(f'\n⚠️  Modèles non organisés trouvés ({len(unexpected_models)}):')
            for model_name in unexpected_models:
                self.stdout.write(f'   - {model_name}')
        else:
            self.stdout.write(f'\n✅ Aucun modèle non organisé trouvé!')
        
        # Résumé
        self.stdout.write(f'\n📈 Résumé:')
        self.stdout.write(f'✅ Modèles attendus trouvés: {found_expected}/{len(expected_models)}')
        self.stdout.write(f'❌ Modèles attendus manquants: {missing_expected}')
        self.stdout.write(f'⚠️  Modèles non organisés: {len(unexpected_models)}')
        self.stdout.write(f'📋 Total enregistré: {len(registered_models)}')
        
        if missing_expected == 0 and len(unexpected_models) == 0:
            self.stdout.write(
                self.style.SUCCESS('\n🎉 Parfait! Seuls les 9 groupes organisés sont affichés!')
            )
        elif len(unexpected_models) == 0:
            self.stdout.write(
                self.style.WARNING(f'\n⚠️  Quelques modèles attendus manquent, mais aucun modèle non organisé n\'est affiché.')
            )
        else:
            self.stdout.write(
                self.style.ERROR(f'\n❌ Il y a encore {len(unexpected_models)} modèles non organisés affichés.')
            )
        
        self.stdout.write(self.style.SUCCESS('\n✨ Vérification terminée!'))
