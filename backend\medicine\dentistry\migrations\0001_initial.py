# Generated by Django 4.2.7 on 2025-05-22 19:19

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='BillingCode',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=20)),
                ('description', models.CharField(max_length=255)),
            ],
        ),
        migrations.CreateModel(
            name='DentalLaboratory',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_private', models.BooleanField(default=False, verbose_name='Is private')),
                ('privatized_by', models.UUIDField(blank=True, null=True, verbose_name='Privatized by')),
                ('owner', models.UUIDField(blank=True, null=True, verbose_name='Owner')),
                ('name', models.CharField(max_length=200, verbose_name='Laboratory Name')),
                ('address', models.TextField(verbose_name='Address')),
                ('phone', models.CharField(max_length=20, verbose_name='Phone Number')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='Email')),
                ('website', models.URLField(blank=True, verbose_name='Website')),
                ('contact_person', models.CharField(blank=True, max_length=100, verbose_name='Contact Person')),
                ('specializations', models.JSONField(default=list, help_text='List of laboratory specializations', verbose_name='Specializations')),
                ('is_preferred', models.BooleanField(default=False, verbose_name='Preferred Laboratory')),
                ('contract_start_date', models.DateField(blank=True, null=True, verbose_name='Contract Start Date')),
                ('contract_end_date', models.DateField(blank=True, null=True, verbose_name='Contract End Date')),
                ('pricing_agreement', models.TextField(blank=True, verbose_name='Pricing Agreement')),
                ('average_turnaround_days', models.FloatField(blank=True, null=True, verbose_name='Average Turnaround (Days)')),
                ('quality_rating', models.FloatField(blank=True, null=True, verbose_name='Quality Rating (1-5)')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
            ],
            options={
                'verbose_name': 'Dental Laboratory',
                'verbose_name_plural': 'Dental Laboratories',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='DentistryDoctor',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_private', models.BooleanField(default=False, verbose_name='Is private')),
                ('privatized_by', models.UUIDField(blank=True, null=True, verbose_name='Privatized by')),
                ('owner', models.UUIDField(blank=True, null=True, verbose_name='Owner')),
                ('user_id', models.UUIDField(blank=True, help_text='Reference to the user in the auth service', null=True, verbose_name='User ID')),
                ('slug', models.SlugField(blank=True, max_length=100, null=True, unique=True, verbose_name='Slug')),
                ('first_name', models.CharField(blank=True, max_length=100, null=True, verbose_name='First name')),
                ('last_name', models.CharField(blank=True, max_length=100, null=True, verbose_name='Last name')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='Email')),
                ('phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='Phone')),
                ('license_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='License number')),
                ('bio', models.TextField(blank=True, null=True, verbose_name='Biography')),
                ('profile_image', models.URLField(blank=True, null=True, verbose_name='Profile image')),
            ],
            options={
                'verbose_name': 'Dentistry Doctor',
                'verbose_name_plural': 'Dentistry Doctors',
            },
        ),
        migrations.CreateModel(
            name='DentistryPatient',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('dental_history', models.TextField(blank=True, verbose_name='Dental History')),
                ('last_cleaning_date', models.DateField(blank=True, null=True, verbose_name='Last Cleaning Date')),
                ('has_dentures', models.BooleanField(default=False, verbose_name='Has Dentures')),
                ('has_implants', models.BooleanField(default=False, verbose_name='Has Implants')),
                ('has_braces', models.BooleanField(default=False, verbose_name='Has Braces/Orthodontics')),
                ('teeth_grinding', models.BooleanField(default=False, verbose_name='Teeth Grinding/Bruxism')),
                ('sensitive_teeth', models.BooleanField(default=False, verbose_name='Sensitive Teeth')),
                ('teeth_chart', models.JSONField(default=dict, help_text="JSON representation of the patient's teeth chart", verbose_name='Teeth Chart')),
                ('dental_insurance_provider', models.CharField(blank=True, max_length=100, verbose_name='Dental Insurance Provider')),
                ('dental_insurance_policy_number', models.CharField(blank=True, max_length=100, verbose_name='Dental Insurance Policy Number')),
                ('patient', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='dentistry_profile', to=settings.AUTH_USER_MODEL, verbose_name='Patient')),
            ],
            options={
                'verbose_name': 'Dentistry Patient',
                'verbose_name_plural': 'Dentistry Patients',
            },
        ),
        migrations.CreateModel(
            name='DentistryRole',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_private', models.BooleanField(default=False, verbose_name='Is private')),
                ('privatized_by', models.UUIDField(blank=True, null=True, verbose_name='Privatized by')),
                ('owner', models.UUIDField(blank=True, null=True, verbose_name='Owner')),
                ('name', models.CharField(max_length=100, verbose_name='Role Name')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('can_view_patients', models.BooleanField(default=True, verbose_name='Can View Patients')),
                ('can_edit_patients', models.BooleanField(default=False, verbose_name='Can Edit Patients')),
                ('can_view_appointments', models.BooleanField(default=True, verbose_name='Can View Appointments')),
                ('can_schedule_appointments', models.BooleanField(default=False, verbose_name='Can Schedule Appointments')),
                ('can_view_medical_records', models.BooleanField(default=True, verbose_name='Can View Medical Records')),
                ('can_create_medical_records', models.BooleanField(default=False, verbose_name='Can Create Medical Records')),
                ('can_edit_medical_records', models.BooleanField(default=False, verbose_name='Can Edit Medical Records')),
                ('can_view_treatments', models.BooleanField(default=True, verbose_name='Can View Treatments')),
                ('can_create_treatments', models.BooleanField(default=False, verbose_name='Can Create Treatments')),
                ('can_edit_treatments', models.BooleanField(default=False, verbose_name='Can Edit Treatments')),
                ('can_view_lab_orders', models.BooleanField(default=True, verbose_name='Can View Lab Orders')),
                ('can_create_lab_orders', models.BooleanField(default=False, verbose_name='Can Create Lab Orders')),
                ('can_edit_lab_orders', models.BooleanField(default=False, verbose_name='Can Edit Lab Orders')),
                ('can_view_billing', models.BooleanField(default=False, verbose_name='Can View Billing')),
                ('can_create_billing', models.BooleanField(default=False, verbose_name='Can Create Billing')),
                ('can_edit_billing', models.BooleanField(default=False, verbose_name='Can Edit Billing')),
                ('can_manage_staff', models.BooleanField(default=False, verbose_name='Can Manage Staff')),
                ('can_manage_settings', models.BooleanField(default=False, verbose_name='Can Manage Settings')),
            ],
            options={
                'verbose_name': 'Dentistry Role',
                'verbose_name_plural': 'Dentistry Roles',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='DentistrySpecialty',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_private', models.BooleanField(default=False, verbose_name='Is private')),
                ('privatized_by', models.UUIDField(blank=True, null=True, verbose_name='Privatized by')),
                ('owner', models.UUIDField(blank=True, null=True, verbose_name='Owner')),
                ('name', models.CharField(max_length=100, verbose_name='Name')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('code', models.CharField(blank=True, max_length=20, null=True, verbose_name='Code')),
                ('slug', models.SlugField(blank=True, max_length=100, null=True, unique=True, verbose_name='Slug')),
            ],
            options={
                'verbose_name': 'Dental Specialty',
                'verbose_name_plural': 'Dental Specialties',
            },
        ),
        migrations.CreateModel(
            name='Invoice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date_issued', models.DateField()),
            ],
        ),
        migrations.CreateModel(
            name='PatientInsurance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_primary', models.BooleanField(default=True)),
            ],
        ),
        migrations.CreateModel(
            name='LabWorkOrder',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_private', models.BooleanField(default=False, verbose_name='Is private')),
                ('privatized_by', models.UUIDField(blank=True, null=True, verbose_name='Privatized by')),
                ('owner', models.UUIDField(blank=True, null=True, verbose_name='Owner')),
                ('order_number', models.CharField(max_length=50, unique=True, verbose_name='Order Number')),
                ('work_type', models.CharField(choices=[('crown', 'Crown'), ('bridge', 'Bridge'), ('denture', 'Denture'), ('partial_denture', 'Partial Denture'), ('implant', 'Implant'), ('veneer', 'Veneer'), ('inlay', 'Inlay'), ('onlay', 'Onlay'), ('night_guard', 'Night Guard'), ('retainer', 'Retainer'), ('other', 'Other')], max_length=20, verbose_name='Work Type')),
                ('description', models.TextField(verbose_name='Description')),
                ('tooth_numbers', models.CharField(blank=True, help_text='Comma-separated list of tooth numbers', max_length=100, verbose_name='Tooth Numbers')),
                ('material', models.CharField(choices=[('porcelain', 'Porcelain'), ('ceramic', 'Ceramic'), ('zirconia', 'Zirconia'), ('metal', 'Metal'), ('acrylic', 'Acrylic'), ('composite', 'Composite'), ('other', 'Other')], max_length=20, verbose_name='Material')),
                ('shade', models.CharField(blank=True, max_length=50, verbose_name='Shade')),
                ('date_sent', models.DateField(verbose_name='Date Sent')),
                ('requested_return_date', models.DateField(verbose_name='Requested Return Date')),
                ('actual_return_date', models.DateField(blank=True, null=True, verbose_name='Actual Return Date')),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('sent', 'Sent to Lab'), ('received_by_lab', 'Received by Lab'), ('in_progress', 'In Progress'), ('completed_by_lab', 'Completed by Lab'), ('returned', 'Returned to Office'), ('fitted', 'Fitted to Patient'), ('rejected', 'Rejected'), ('remake', 'Remake Required')], default='draft', max_length=20, verbose_name='Status')),
                ('cost', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='Cost')),
                ('invoice_number', models.CharField(blank=True, max_length=50, verbose_name='Invoice Number')),
                ('is_invoiced', models.BooleanField(default=False, verbose_name='Is Invoiced')),
                ('is_paid', models.BooleanField(default=False, verbose_name='Is Paid')),
                ('attachments', models.JSONField(blank=True, default=list, help_text='List of file paths or URLs', verbose_name='Attachments')),
                ('special_instructions', models.TextField(blank=True, verbose_name='Special Instructions')),
                ('lab_notes', models.TextField(blank=True, verbose_name='Laboratory Notes')),
                ('internal_notes', models.TextField(blank=True, verbose_name='Internal Notes')),
                ('doctor', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='lab_work_orders', to='dentistry.dentistrydoctor', verbose_name='Doctor')),
                ('laboratory', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='work_orders', to='dentistry.dentallaboratory', verbose_name='Laboratory')),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lab_work_orders', to='dentistry.dentistrypatient', verbose_name='Patient')),
            ],
            options={
                'verbose_name': 'Lab Work Order',
                'verbose_name_plural': 'Lab Work Orders',
                'ordering': ['-date_sent'],
            },
        ),
        migrations.CreateModel(
            name='DentistryWebsiteSettings',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_private', models.BooleanField(default=False, verbose_name='Is private')),
                ('privatized_by', models.UUIDField(blank=True, null=True, verbose_name='Privatized by')),
                ('owner', models.UUIDField(blank=True, null=True, verbose_name='Owner')),
                ('subdomain', models.CharField(blank=True, max_length=100, verbose_name='Subdomain')),
                ('site_title', models.CharField(blank=True, max_length=200, verbose_name='Site Title')),
                ('site_description', models.TextField(blank=True, verbose_name='Site Description')),
                ('logo', models.ImageField(blank=True, null=True, upload_to='dentistry/website/logos/', verbose_name='Logo')),
                ('favicon', models.ImageField(blank=True, null=True, upload_to='dentistry/website/favicons/', verbose_name='Favicon')),
                ('primary_color', models.CharField(default='#00BFFF', max_length=20, verbose_name='Primary Color')),
                ('secondary_color', models.CharField(default='#FFFFFF', max_length=20, verbose_name='Secondary Color')),
                ('font_family', models.CharField(default='Arial, sans-serif', max_length=100, verbose_name='Font Family')),
                ('meta_keywords', models.TextField(blank=True, verbose_name='Meta Keywords')),
                ('meta_description', models.TextField(blank=True, verbose_name='Meta Description')),
                ('google_analytics_id', models.CharField(blank=True, max_length=50, verbose_name='Google Analytics ID')),
                ('display_phone', models.CharField(blank=True, max_length=20, verbose_name='Display Phone')),
                ('display_email', models.EmailField(blank=True, max_length=254, verbose_name='Display Email')),
                ('display_address', models.TextField(blank=True, verbose_name='Display Address')),
                ('google_maps_embed', models.TextField(blank=True, verbose_name='Google Maps Embed Code')),
                ('facebook_url', models.URLField(blank=True, verbose_name='Facebook URL')),
                ('twitter_url', models.URLField(blank=True, verbose_name='Twitter URL')),
                ('instagram_url', models.URLField(blank=True, verbose_name='Instagram URL')),
                ('linkedin_url', models.URLField(blank=True, verbose_name='LinkedIn URL')),
                ('youtube_url', models.URLField(blank=True, verbose_name='YouTube URL')),
                ('enable_appointment_booking', models.BooleanField(default=True, verbose_name='Enable Appointment Booking')),
                ('enable_patient_testimonials', models.BooleanField(default=True, verbose_name='Enable Patient Testimonials')),
                ('enable_blog', models.BooleanField(default=True, verbose_name='Enable Blog')),
                ('enable_before_after_gallery', models.BooleanField(default=True, verbose_name='Enable Before/After Gallery')),
                ('enable_smile_simulator', models.BooleanField(default=True, verbose_name='Enable Smile Simulator')),
                ('enable_patient_education', models.BooleanField(default=True, verbose_name='Enable Patient Education')),
                ('doctor', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='dentistry_website_settings', to='dentistry.dentistrydoctor', verbose_name='Doctor')),
            ],
            options={
                'verbose_name': 'Dentistry Website Settings',
                'verbose_name_plural': 'Dentistry Website Settings',
            },
        ),
        migrations.CreateModel(
            name='DentistryStaffProfile',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_private', models.BooleanField(default=False, verbose_name='Is private')),
                ('privatized_by', models.UUIDField(blank=True, null=True, verbose_name='Privatized by')),
                ('owner', models.UUIDField(blank=True, null=True, verbose_name='Owner')),
                ('job_title', models.CharField(max_length=100, verbose_name='Job Title')),
                ('license_number', models.CharField(blank=True, max_length=50, verbose_name='License Number')),
                ('specialization', models.CharField(blank=True, max_length=100, verbose_name='Specialization')),
                ('work_phone', models.CharField(blank=True, max_length=20, verbose_name='Work Phone')),
                ('work_email', models.EmailField(blank=True, max_length=254, verbose_name='Work Email')),
                ('hire_date', models.DateField(blank=True, null=True, verbose_name='Hire Date')),
                ('end_date', models.DateField(blank=True, null=True, verbose_name='End Date')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('working_days', models.JSONField(default=list, help_text='List of working days (0=Monday, 6=Sunday)', verbose_name='Working Days')),
                ('working_hours', models.JSONField(default=dict, help_text='Working hours for each day', verbose_name='Working Hours')),
                ('bio', models.TextField(blank=True, verbose_name='Biography')),
                ('profile_image', models.ImageField(blank=True, null=True, upload_to='dentistry/staff/', verbose_name='Profile Image')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('role', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='staff_profiles', to='dentistry.dentistryrole', verbose_name='Role')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='dentistry_staff_profile', to=settings.AUTH_USER_MODEL, verbose_name='User')),
            ],
            options={
                'verbose_name': 'Dentistry Staff Profile',
                'verbose_name_plural': 'Dentistry Staff Profiles',
                'ordering': ['user__last_name', 'user__first_name'],
            },
        ),
        migrations.CreateModel(
            name='DentistryService',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_private', models.BooleanField(default=False, verbose_name='Is private')),
                ('privatized_by', models.UUIDField(blank=True, null=True, verbose_name='Privatized by')),
                ('owner', models.UUIDField(blank=True, null=True, verbose_name='Owner')),
                ('name', models.CharField(max_length=100, verbose_name='Name')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='Price')),
                ('duration', models.PositiveIntegerField(default=30, help_text='Duration in minutes', verbose_name='Duration')),
                ('specialty', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='services', to='dentistry.dentistryspecialty', verbose_name='Specialty')),
            ],
            options={
                'verbose_name': 'Dental Service',
                'verbose_name_plural': 'Dental Services',
            },
        ),
        migrations.CreateModel(
            name='DentistryPatientInsurance',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_private', models.BooleanField(default=False, verbose_name='Is private')),
                ('privatized_by', models.UUIDField(blank=True, null=True, verbose_name='Privatized by')),
                ('owner', models.UUIDField(blank=True, null=True, verbose_name='Owner')),
                ('annual_maximum', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='Annual Maximum')),
                ('remaining_benefit', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='Remaining Benefit')),
                ('covers_preventive', models.BooleanField(default=True, verbose_name='Covers Preventive')),
                ('preventive_coverage_percent', models.PositiveIntegerField(default=100, verbose_name='Preventive Coverage Percent')),
                ('covers_basic', models.BooleanField(default=True, verbose_name='Covers Basic')),
                ('basic_coverage_percent', models.PositiveIntegerField(default=80, verbose_name='Basic Coverage Percent')),
                ('covers_major', models.BooleanField(default=True, verbose_name='Covers Major')),
                ('major_coverage_percent', models.PositiveIntegerField(default=50, verbose_name='Major Coverage Percent')),
                ('covers_orthodontic', models.BooleanField(default=False, verbose_name='Covers Orthodontic')),
                ('orthodontic_coverage_percent', models.PositiveIntegerField(default=0, verbose_name='Orthodontic Coverage Percent')),
                ('orthodontic_lifetime_maximum', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='Orthodontic Lifetime Maximum')),
                ('waiting_period_details', models.TextField(blank=True, verbose_name='Waiting Period Details')),
                ('base_insurance', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='dentistry_insurance', to='dentistry.patientinsurance', verbose_name='Base Patient Insurance')),
                ('dentistry_patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='dentistry_insurances', to='dentistry.dentistrypatient', verbose_name='Dentistry Patient')),
            ],
            options={
                'verbose_name': 'Dentistry Patient Insurance',
                'verbose_name_plural': 'Dentistry Patient Insurances',
                'ordering': ['-base_insurance__is_primary'],
            },
        ),
        migrations.CreateModel(
            name='DentistryNotification',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_private', models.BooleanField(default=False, verbose_name='Is private')),
                ('privatized_by', models.UUIDField(blank=True, null=True, verbose_name='Privatized by')),
                ('owner', models.UUIDField(blank=True, null=True, verbose_name='Owner')),
                ('title', models.CharField(max_length=200, verbose_name='Title')),
                ('message', models.TextField(verbose_name='Message')),
                ('notification_type', models.CharField(choices=[('appointment', 'Appointment'), ('treatment', 'Treatment'), ('procedure', 'Procedure'), ('lab_work', 'Lab Work'), ('cleaning_reminder', 'Cleaning Reminder'), ('follow_up', 'Follow-up Reminder'), ('payment', 'Payment'), ('system', 'System Notification')], default='system', max_length=20, verbose_name='Notification Type')),
                ('priority', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('urgent', 'Urgent')], default='medium', max_length=10, verbose_name='Priority')),
                ('is_read', models.BooleanField(default=False, verbose_name='Is Read')),
                ('read_at', models.DateTimeField(blank=True, null=True, verbose_name='Read At')),
                ('is_sent', models.BooleanField(default=False, verbose_name='Is Sent')),
                ('sent_at', models.DateTimeField(blank=True, null=True, verbose_name='Sent At')),
                ('delivery_method', models.CharField(choices=[('in_app', 'In-App'), ('email', 'Email'), ('sms', 'SMS'), ('push', 'Push Notification')], default='in_app', max_length=10, verbose_name='Delivery Method')),
                ('related_object_type', models.CharField(blank=True, max_length=100, verbose_name='Related Object Type')),
                ('related_object_id', models.PositiveIntegerField(blank=True, null=True, verbose_name='Related Object ID')),
                ('action_url', models.CharField(blank=True, max_length=255, verbose_name='Action URL')),
                ('doctor', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='dentistry_notifications', to='dentistry.dentistrydoctor', verbose_name='Doctor')),
                ('patient', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='dentistry_notifications', to='dentistry.dentistrypatient', verbose_name='Patient')),
            ],
            options={
                'verbose_name': 'Dentistry Notification',
                'verbose_name_plural': 'Dentistry Notifications',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='DentistryMedicalRecord',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_private', models.BooleanField(default=False, verbose_name='Is private')),
                ('privatized_by', models.UUIDField(blank=True, null=True, verbose_name='Privatized by')),
                ('owner', models.UUIDField(blank=True, null=True, verbose_name='Owner')),
                ('date', models.DateField(verbose_name='Record Date')),
                ('chief_complaint', models.TextField(blank=True, verbose_name='Chief Complaint')),
                ('diagnosis', models.TextField(blank=True, verbose_name='Diagnosis')),
                ('treatment_plan', models.TextField(blank=True, verbose_name='Treatment Plan')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('oral_hygiene_status', models.CharField(choices=[('excellent', 'Excellent'), ('good', 'Good'), ('fair', 'Fair'), ('poor', 'Poor')], default='good', max_length=20, verbose_name='Oral Hygiene Status')),
                ('gum_health', models.CharField(choices=[('healthy', 'Healthy'), ('gingivitis', 'Gingivitis'), ('periodontitis', 'Periodontitis'), ('severe_periodontitis', 'Severe Periodontitis')], default='healthy', max_length=20, verbose_name='Gum Health')),
                ('teeth_examined', models.BooleanField(default=True, verbose_name='Teeth Examined')),
                ('x_rays_taken', models.BooleanField(default=False, verbose_name='X-Rays Taken')),
                ('teeth_cleaned', models.BooleanField(default=False, verbose_name='Teeth Cleaned')),
                ('fluoride_treatment', models.BooleanField(default=False, verbose_name='Fluoride Treatment')),
                ('teeth_chart_updated', models.BooleanField(default=False, verbose_name='Teeth Chart Updated')),
                ('doctor', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='dentistry_records', to='dentistry.dentistrydoctor', verbose_name='Doctor')),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='medical_records', to='dentistry.dentistrypatient', verbose_name='Patient')),
            ],
            options={
                'verbose_name': 'Dentistry Medical Record',
                'verbose_name_plural': 'Dentistry Medical Records',
                'ordering': ['-date'],
            },
        ),
        migrations.CreateModel(
            name='DentistryInvoice',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_private', models.BooleanField(default=False, verbose_name='Is private')),
                ('privatized_by', models.UUIDField(blank=True, null=True, verbose_name='Privatized by')),
                ('owner', models.UUIDField(blank=True, null=True, verbose_name='Owner')),
                ('teeth_treated', models.CharField(blank=True, help_text='Comma-separated list of tooth numbers', max_length=200, verbose_name='Teeth Treated')),
                ('x_rays_taken', models.BooleanField(default=False, verbose_name='X-Rays Taken')),
                ('cleaning_performed', models.BooleanField(default=False, verbose_name='Cleaning Performed')),
                ('lab_work_required', models.BooleanField(default=False, verbose_name='Lab Work Required')),
                ('lab_work_description', models.TextField(blank=True, verbose_name='Lab Work Description')),
                ('base_invoice', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='dentistry_invoice', to='dentistry.invoice', verbose_name='Base Invoice')),
                ('dentistry_patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='dentistry_invoices', to='dentistry.dentistrypatient', verbose_name='Dentistry Patient')),
            ],
            options={
                'verbose_name': 'Dentistry Invoice',
                'verbose_name_plural': 'Dentistry Invoices',
                'ordering': ['-base_invoice__date_issued'],
            },
        ),
        migrations.CreateModel(
            name='DentistryDoctorSettings',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_private', models.BooleanField(default=False, verbose_name='Is private')),
                ('privatized_by', models.UUIDField(blank=True, null=True, verbose_name='Privatized by')),
                ('owner', models.UUIDField(blank=True, null=True, verbose_name='Owner')),
                ('theme', models.CharField(default='light', max_length=20, verbose_name='Theme')),
                ('email_notifications', models.BooleanField(default=True, verbose_name='Email notifications')),
                ('sms_notifications', models.BooleanField(default=True, verbose_name='SMS notifications')),
                ('appointment_reminders', models.BooleanField(default=True, verbose_name='Appointment reminders')),
                ('marketing_emails', models.BooleanField(default=False, verbose_name='Marketing emails')),
                ('language', models.CharField(default='en', max_length=10, verbose_name='Language')),
                ('time_format', models.CharField(default='24h', max_length=10, verbose_name='Time format')),
                ('date_format', models.CharField(default='yyyy-mm-dd', max_length=10, verbose_name='Date format')),
                ('font_size', models.PositiveIntegerField(default=16, verbose_name='Font size')),
                ('doctor', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='settings', to='dentistry.dentistrydoctor', verbose_name='Doctor')),
            ],
            options={
                'verbose_name': 'Dentistry Doctor Settings',
                'verbose_name_plural': 'Dentistry Doctor Settings',
            },
        ),
        migrations.AddField(
            model_name='dentistrydoctor',
            name='services',
            field=models.ManyToManyField(related_name='doctors', to='dentistry.dentistryservice', verbose_name='Services'),
        ),
        migrations.AddField(
            model_name='dentistrydoctor',
            name='specialties',
            field=models.ManyToManyField(related_name='doctors', to='dentistry.dentistryspecialty', verbose_name='Specialties'),
        ),
        migrations.CreateModel(
            name='DentistryCustomField',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_private', models.BooleanField(default=False, verbose_name='Is private')),
                ('privatized_by', models.UUIDField(blank=True, null=True, verbose_name='Privatized by')),
                ('owner', models.UUIDField(blank=True, null=True, verbose_name='Owner')),
                ('name', models.CharField(max_length=100, verbose_name='Field Name')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('field_type', models.CharField(choices=[('text', 'Text'), ('number', 'Number'), ('date', 'Date'), ('boolean', 'Boolean'), ('select', 'Select'), ('multi_select', 'Multi-Select')], max_length=20, verbose_name='Field Type')),
                ('entity_type', models.CharField(choices=[('patient', 'Patient'), ('medical_record', 'Medical Record'), ('treatment', 'Treatment'), ('procedure', 'Procedure'), ('appointment', 'Appointment'), ('lab_work', 'Lab Work')], max_length=20, verbose_name='Entity Type')),
                ('options', models.JSONField(blank=True, default=list, help_text='Options for select and multi-select fields', verbose_name='Field Options')),
                ('is_required', models.BooleanField(default=False, verbose_name='Is Required')),
                ('default_value', models.CharField(blank=True, max_length=255, verbose_name='Default Value')),
                ('display_order', models.PositiveIntegerField(default=0, verbose_name='Display Order')),
                ('doctor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='dentistry_custom_fields', to='dentistry.dentistrydoctor', verbose_name='Doctor')),
            ],
            options={
                'verbose_name': 'Dentistry Custom Field',
                'verbose_name_plural': 'Dentistry Custom Fields',
                'ordering': ['entity_type', 'display_order', 'name'],
                'unique_together': {('doctor', 'entity_type', 'name')},
            },
        ),
        migrations.CreateModel(
            name='DentistryConfiguration',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_private', models.BooleanField(default=False, verbose_name='Is private')),
                ('privatized_by', models.UUIDField(blank=True, null=True, verbose_name='Privatized by')),
                ('owner', models.UUIDField(blank=True, null=True, verbose_name='Owner')),
                ('teeth_chart_adult_enabled', models.BooleanField(default=True, verbose_name='Adult Teeth Chart Enabled')),
                ('teeth_chart_child_enabled', models.BooleanField(default=True, verbose_name='Child Teeth Chart Enabled')),
                ('teeth_chart_numbering_system', models.CharField(choices=[('universal', 'Universal Numbering System'), ('fdi', 'FDI/ISO System'), ('palmer', 'Palmer Notation')], default='universal', max_length=20, verbose_name='Teeth Chart Numbering System')),
                ('teeth_color_normal', models.CharField(default='#FFFFFF', max_length=20, verbose_name='Normal Tooth Color')),
                ('teeth_color_filling', models.CharField(default='#0000FF', max_length=20, verbose_name='Filling Color')),
                ('teeth_color_crown', models.CharField(default='#FFD700', max_length=20, verbose_name='Crown Color')),
                ('teeth_color_root_canal', models.CharField(default='#FF0000', max_length=20, verbose_name='Root Canal Color')),
                ('teeth_color_implant', models.CharField(default='#808080', max_length=20, verbose_name='Implant Color')),
                ('teeth_color_missing', models.CharField(default='#000000', max_length=20, verbose_name='Missing Tooth Color')),
                ('xray_default_brightness', models.IntegerField(default=0, verbose_name='X-ray Default Brightness')),
                ('xray_default_contrast', models.IntegerField(default=0, verbose_name='X-ray Default Contrast')),
                ('xray_annotations_enabled', models.BooleanField(default=True, verbose_name='X-ray Annotations Enabled')),
                ('treatment_plan_phases_enabled', models.BooleanField(default=True, verbose_name='Treatment Plan Phases Enabled')),
                ('treatment_plan_cost_display', models.BooleanField(default=True, verbose_name='Treatment Plan Cost Display')),
                ('treatment_plan_insurance_display', models.BooleanField(default=True, verbose_name='Treatment Plan Insurance Display')),
                ('report_logo_enabled', models.BooleanField(default=True, verbose_name='Report Logo Enabled')),
                ('report_logo', models.ImageField(blank=True, null=True, upload_to='dentistry/logos/', verbose_name='Report Logo')),
                ('report_header', models.TextField(blank=True, verbose_name='Report Header')),
                ('report_footer', models.TextField(blank=True, verbose_name='Report Footer')),
                ('default_appointment_duration', models.PositiveIntegerField(default=30, verbose_name='Default Appointment Duration (minutes)')),
                ('default_cleaning_duration', models.PositiveIntegerField(default=60, verbose_name='Default Cleaning Duration (minutes)')),
                ('ui_theme', models.CharField(default='light', max_length=20, verbose_name='UI Theme')),
                ('font_size', models.PositiveIntegerField(default=14, verbose_name='Font Size')),
                ('doctor', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='dentistry_configuration', to='dentistry.dentistrydoctor', verbose_name='Doctor')),
            ],
            options={
                'verbose_name': 'Dentistry Configuration',
                'verbose_name_plural': 'Dentistry Configurations',
            },
        ),
        migrations.CreateModel(
            name='DentistryBillingCode',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_private', models.BooleanField(default=False, verbose_name='Is private')),
                ('privatized_by', models.UUIDField(blank=True, null=True, verbose_name='Privatized by')),
                ('owner', models.UUIDField(blank=True, null=True, verbose_name='Owner')),
                ('ada_code', models.CharField(blank=True, max_length=20, verbose_name='ADA Code')),
                ('dentistry_specific_details', models.TextField(blank=True, verbose_name='Dentistry-Specific Details')),
                ('is_preventive', models.BooleanField(default=False, verbose_name='Is Preventive')),
                ('is_restorative', models.BooleanField(default=False, verbose_name='Is Restorative')),
                ('is_endodontic', models.BooleanField(default=False, verbose_name='Is Endodontic')),
                ('is_periodontic', models.BooleanField(default=False, verbose_name='Is Periodontic')),
                ('is_prosthodontic', models.BooleanField(default=False, verbose_name='Is Prosthodontic')),
                ('is_orthodontic', models.BooleanField(default=False, verbose_name='Is Orthodontic')),
                ('is_surgical', models.BooleanField(default=False, verbose_name='Is Surgical')),
                ('base_code', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='dentistry_codes', to='dentistry.billingcode', verbose_name='Base Billing Code')),
            ],
            options={
                'verbose_name': 'Dentistry Billing Code',
                'verbose_name_plural': 'Dentistry Billing Codes',
                'ordering': ['ada_code', 'base_code__code'],
            },
        ),
        migrations.CreateModel(
            name='DentistryBeforeAfterCase',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_private', models.BooleanField(default=False, verbose_name='Is private')),
                ('privatized_by', models.UUIDField(blank=True, null=True, verbose_name='Privatized by')),
                ('owner', models.UUIDField(blank=True, null=True, verbose_name='Owner')),
                ('title', models.CharField(max_length=200, verbose_name='Case Title')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('category', models.CharField(choices=[('cosmetic', 'Cosmetic'), ('restorative', 'Restorative'), ('orthodontic', 'Orthodontic'), ('implant', 'Implant'), ('other', 'Other')], default='cosmetic', max_length=20, verbose_name='Category')),
                ('before_image', models.ImageField(upload_to='dentistry/before_after/before/', verbose_name='Before Image')),
                ('after_image', models.ImageField(upload_to='dentistry/before_after/after/', verbose_name='After Image')),
                ('treatment_duration', models.CharField(blank=True, max_length=100, verbose_name='Treatment Duration')),
                ('procedures_performed', models.TextField(blank=True, verbose_name='Procedures Performed')),
                ('is_published', models.BooleanField(default=True, verbose_name='Is Published')),
                ('display_order', models.PositiveIntegerField(default=0, verbose_name='Display Order')),
                ('doctor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='dentistry_before_after_cases', to='dentistry.dentistrydoctor', verbose_name='Doctor')),
            ],
            options={
                'verbose_name': 'Dentistry Before/After Case',
                'verbose_name_plural': 'Dentistry Before/After Cases',
                'ordering': ['display_order', 'title'],
            },
        ),
        migrations.CreateModel(
            name='DentistryAppointment',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_private', models.BooleanField(default=False, verbose_name='Is private')),
                ('privatized_by', models.UUIDField(blank=True, null=True, verbose_name='Privatized by')),
                ('owner', models.UUIDField(blank=True, null=True, verbose_name='Owner')),
                ('appointment_date', models.DateField(verbose_name='Appointment Date')),
                ('appointment_time', models.TimeField(verbose_name='Appointment Time')),
                ('duration_minutes', models.PositiveIntegerField(default=30, verbose_name='Duration (minutes)')),
                ('reason', models.TextField(blank=True, verbose_name='Reason for Visit')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('appointment_type', models.CharField(choices=[('check_up', 'Regular Check-up'), ('cleaning', 'Teeth Cleaning'), ('filling', 'Filling'), ('root_canal', 'Root Canal'), ('extraction', 'Tooth Extraction'), ('crown', 'Crown'), ('bridge', 'Bridge'), ('implant', 'Implant'), ('dentures', 'Dentures'), ('orthodontics', 'Orthodontics'), ('whitening', 'Teeth Whitening'), ('emergency', 'Emergency'), ('consultation', 'Consultation'), ('other', 'Other')], default='check_up', max_length=20, verbose_name='Appointment Type')),
                ('status', models.CharField(choices=[('scheduled', 'Scheduled'), ('confirmed', 'Confirmed'), ('cancelled', 'Cancelled'), ('rescheduled', 'Rescheduled'), ('completed', 'Completed'), ('no_show', 'No Show')], default='scheduled', max_length=20, verbose_name='Status')),
                ('affected_teeth', models.CharField(blank=True, help_text='Comma-separated list of tooth numbers', max_length=100, verbose_name='Affected Teeth')),
                ('requires_xray', models.BooleanField(default=False, verbose_name='Requires X-Ray')),
                ('requires_anesthesia', models.BooleanField(default=False, verbose_name='Requires Anesthesia')),
                ('pre_appointment_instructions', models.TextField(blank=True, verbose_name='Pre-appointment Instructions')),
                ('doctor', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='appointments', to='dentistry.dentistrydoctor', verbose_name='Doctor')),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='appointments', to='dentistry.dentistrypatient', verbose_name='Patient')),
            ],
            options={
                'verbose_name': 'Dentistry Appointment',
                'verbose_name_plural': 'Dentistry Appointments',
                'ordering': ['-appointment_date', '-appointment_time'],
            },
        ),
        migrations.CreateModel(
            name='DentalTreatment',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_private', models.BooleanField(default=False, verbose_name='Is private')),
                ('privatized_by', models.UUIDField(blank=True, null=True, verbose_name='Privatized by')),
                ('owner', models.UUIDField(blank=True, null=True, verbose_name='Owner')),
                ('title', models.CharField(max_length=200, verbose_name='Treatment Plan Title')),
                ('description', models.TextField(verbose_name='Description')),
                ('start_date', models.DateField(verbose_name='Start Date')),
                ('estimated_end_date', models.DateField(blank=True, null=True, verbose_name='Estimated End Date')),
                ('actual_end_date', models.DateField(blank=True, null=True, verbose_name='Actual End Date')),
                ('status', models.CharField(choices=[('planned', 'Planned'), ('in_progress', 'In Progress'), ('completed', 'Completed'), ('cancelled', 'Cancelled')], default='planned', max_length=20, verbose_name='Status')),
                ('estimated_cost', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='Estimated Cost')),
                ('insurance_coverage', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='Insurance Coverage')),
                ('patient_responsibility', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='Patient Responsibility')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('doctor', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='dental_treatment_plans', to='dentistry.dentistrydoctor', verbose_name='Doctor')),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='treatment_plans', to='dentistry.dentistrypatient', verbose_name='Patient')),
            ],
            options={
                'verbose_name': 'Dental Treatment Plan',
                'verbose_name_plural': 'Dental Treatment Plans',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='DentalReminderSetting',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_private', models.BooleanField(default=False, verbose_name='Is private')),
                ('privatized_by', models.UUIDField(blank=True, null=True, verbose_name='Privatized by')),
                ('owner', models.UUIDField(blank=True, null=True, verbose_name='Owner')),
                ('cleaning_reminder_enabled', models.BooleanField(default=True, verbose_name='Cleaning Reminder Enabled')),
                ('cleaning_interval_months', models.PositiveIntegerField(default=6, verbose_name='Cleaning Interval (months)')),
                ('days_before_cleaning_reminder', models.PositiveIntegerField(default=14, verbose_name='Days Before Cleaning Reminder')),
                ('checkup_reminder_enabled', models.BooleanField(default=True, verbose_name='Checkup Reminder Enabled')),
                ('checkup_interval_months', models.PositiveIntegerField(default=12, verbose_name='Checkup Interval (months)')),
                ('days_before_checkup_reminder', models.PositiveIntegerField(default=21, verbose_name='Days Before Checkup Reminder')),
                ('treatment_followup_enabled', models.BooleanField(default=True, verbose_name='Treatment Follow-up Enabled')),
                ('days_after_treatment_followup', models.PositiveIntegerField(default=7, verbose_name='Days After Treatment Follow-up')),
                ('notification_method', models.CharField(choices=[('in_app', 'In-App'), ('email', 'Email'), ('sms', 'SMS'), ('push', 'Push Notification'), ('all', 'All Methods')], default='all', max_length=10, verbose_name='Notification Method')),
                ('patient', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='dental_reminder_settings', to='dentistry.dentistrypatient', verbose_name='Patient')),
            ],
            options={
                'verbose_name': 'Dental Reminder Setting',
                'verbose_name_plural': 'Dental Reminder Settings',
            },
        ),
        migrations.CreateModel(
            name='DentalProcedure',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_private', models.BooleanField(default=False, verbose_name='Is private')),
                ('privatized_by', models.UUIDField(blank=True, null=True, verbose_name='Privatized by')),
                ('owner', models.UUIDField(blank=True, null=True, verbose_name='Owner')),
                ('procedure_type', models.CharField(choices=[('diagnostic', 'Diagnostic'), ('preventive', 'Preventive'), ('restorative', 'Restorative'), ('endodontic', 'Endodontic'), ('periodontic', 'Periodontic'), ('prosthodontic', 'Prosthodontic'), ('oral_surgery', 'Oral Surgery'), ('orthodontic', 'Orthodontic'), ('other', 'Other')], max_length=20, verbose_name='Procedure Type')),
                ('name', models.CharField(max_length=200, verbose_name='Procedure Name')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('tooth_numbers', models.CharField(blank=True, help_text='Comma-separated list of tooth numbers', max_length=100, verbose_name='Tooth Numbers')),
                ('scheduled_date', models.DateField(blank=True, null=True, verbose_name='Scheduled Date')),
                ('completed_date', models.DateField(blank=True, null=True, verbose_name='Completed Date')),
                ('status', models.CharField(choices=[('planned', 'Planned'), ('scheduled', 'Scheduled'), ('in_progress', 'In Progress'), ('completed', 'Completed'), ('cancelled', 'Cancelled')], default='planned', max_length=20, verbose_name='Status')),
                ('cost', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='Cost')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('treatment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='procedures', to='dentistry.dentaltreatment', verbose_name='Treatment Plan')),
            ],
            options={
                'verbose_name': 'Dental Procedure',
                'verbose_name_plural': 'Dental Procedures',
                'ordering': ['scheduled_date', 'created_at'],
            },
        ),
        migrations.CreateModel(
            name='DentalImaging',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_private', models.BooleanField(default=False, verbose_name='Is private')),
                ('privatized_by', models.UUIDField(blank=True, null=True, verbose_name='Privatized by')),
                ('owner', models.UUIDField(blank=True, null=True, verbose_name='Owner')),
                ('date', models.DateField(verbose_name='Record Date')),
                ('chief_complaint', models.TextField(blank=True, verbose_name='Chief Complaint')),
                ('diagnosis', models.TextField(blank=True, verbose_name='Diagnosis')),
                ('treatment_plan', models.TextField(blank=True, verbose_name='Treatment Plan')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('imaging_type', models.CharField(choices=[('panoramic', 'Panoramic X-Ray'), ('bitewing', 'Bitewing X-Ray'), ('periapical', 'Periapical X-Ray'), ('cbct', 'Cone Beam CT (CBCT)'), ('intraoral', 'Intraoral Photo'), ('other', 'Other')], max_length=20, verbose_name='Imaging Type')),
                ('image_file', models.FileField(blank=True, null=True, upload_to='dental_images/', verbose_name='Image File')),
                ('affected_teeth', models.CharField(blank=True, help_text='Comma-separated list of tooth numbers', max_length=100, verbose_name='Affected Teeth')),
                ('findings', models.TextField(blank=True, verbose_name='Findings')),
                ('doctor', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='dental_images', to='dentistry.dentistrydoctor', verbose_name='Doctor')),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='dental_images', to='dentistry.dentistrypatient', verbose_name='Patient')),
            ],
            options={
                'verbose_name': 'Dental Imaging',
                'verbose_name_plural': 'Dental Imaging',
                'ordering': ['-date'],
            },
        ),
        migrations.CreateModel(
            name='DentalConsultation',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_private', models.BooleanField(default=False, verbose_name='Is private')),
                ('privatized_by', models.UUIDField(blank=True, null=True, verbose_name='Privatized by')),
                ('owner', models.UUIDField(blank=True, null=True, verbose_name='Owner')),
                ('chief_complaint', models.TextField(blank=True, null=True, verbose_name='Chief complaint')),
                ('dental_history', models.TextField(blank=True, null=True, verbose_name='Dental history')),
                ('medical_history', models.TextField(blank=True, null=True, verbose_name='Medical history')),
                ('medications', models.TextField(blank=True, null=True, verbose_name='Medications')),
                ('allergies', models.TextField(blank=True, null=True, verbose_name='Allergies')),
                ('examination_notes', models.TextField(blank=True, null=True, verbose_name='Examination notes')),
                ('assessment', models.TextField(blank=True, null=True, verbose_name='Assessment')),
                ('treatment_plan', models.TextField(blank=True, null=True, verbose_name='Treatment plan')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='Notes')),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('completed', 'Completed'), ('signed', 'Signed')], default='draft', max_length=20, verbose_name='Status')),
                ('completed_at', models.DateTimeField(blank=True, null=True, verbose_name='Completed at')),
                ('signed_at', models.DateTimeField(blank=True, null=True, verbose_name='Signed at')),
                ('appointment', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='dental_consultation', to='dentistry.dentistryappointment', verbose_name='Appointment')),
                ('doctor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='dental_consultations', to='dentistry.dentistrydoctor', verbose_name='Doctor')),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='dental_consultations', to='dentistry.dentistrypatient', verbose_name='Patient')),
            ],
            options={
                'verbose_name': 'Dental Consultation',
                'verbose_name_plural': 'Dental Consultations',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Tooth',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_private', models.BooleanField(default=False, verbose_name='Is private')),
                ('privatized_by', models.UUIDField(blank=True, null=True, verbose_name='Privatized by')),
                ('owner', models.UUIDField(blank=True, null=True, verbose_name='Owner')),
                ('tooth_number', models.PositiveSmallIntegerField(choices=[(11, 'Upper Right Central Incisor'), (12, 'Upper Right Lateral Incisor'), (13, 'Upper Right Canine'), (14, 'Upper Right First Premolar'), (15, 'Upper Right Second Premolar'), (16, 'Upper Right First Molar'), (17, 'Upper Right Second Molar'), (18, 'Upper Right Third Molar'), (21, 'Upper Left Central Incisor'), (22, 'Upper Left Lateral Incisor'), (23, 'Upper Left Canine'), (24, 'Upper Left First Premolar'), (25, 'Upper Left Second Premolar'), (26, 'Upper Left First Molar'), (27, 'Upper Left Second Molar'), (28, 'Upper Left Third Molar'), (31, 'Lower Left Central Incisor'), (32, 'Lower Left Lateral Incisor'), (33, 'Lower Left Canine'), (34, 'Lower Left First Premolar'), (35, 'Lower Left Second Premolar'), (36, 'Lower Left First Molar'), (37, 'Lower Left Second Molar'), (38, 'Lower Left Third Molar'), (41, 'Lower Right Central Incisor'), (42, 'Lower Right Lateral Incisor'), (43, 'Lower Right Canine'), (44, 'Lower Right First Premolar'), (45, 'Lower Right Second Premolar'), (46, 'Lower Right First Molar'), (47, 'Lower Right Second Molar'), (48, 'Lower Right Third Molar'), (51, 'Upper Right Primary Central Incisor'), (52, 'Upper Right Primary Lateral Incisor'), (53, 'Upper Right Primary Canine'), (54, 'Upper Right Primary First Molar'), (55, 'Upper Right Primary Second Molar'), (61, 'Upper Left Primary Central Incisor'), (62, 'Upper Left Primary Lateral Incisor'), (63, 'Upper Left Primary Canine'), (64, 'Upper Left Primary First Molar'), (65, 'Upper Left Primary Second Molar'), (71, 'Lower Left Primary Central Incisor'), (72, 'Lower Left Primary Lateral Incisor'), (73, 'Lower Left Primary Canine'), (74, 'Lower Left Primary First Molar'), (75, 'Lower Left Primary Second Molar'), (81, 'Lower Right Primary Central Incisor'), (82, 'Lower Right Primary Lateral Incisor'), (83, 'Lower Right Primary Canine'), (84, 'Lower Right Primary First Molar'), (85, 'Lower Right Primary Second Molar')], verbose_name='Tooth number')),
                ('status', models.CharField(choices=[('healthy', 'Healthy'), ('decayed', 'Decayed'), ('filled', 'Filled'), ('missing', 'Missing'), ('crown', 'Crown'), ('implant', 'Implant'), ('bridge', 'Bridge'), ('root_canal', 'Root Canal'), ('other', 'Other')], default='healthy', max_length=50, verbose_name='Status')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='Notes')),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='teeth', to='dentistry.dentistrypatient', verbose_name='Patient')),
            ],
            options={
                'verbose_name': 'Tooth',
                'verbose_name_plural': 'Teeth',
                'unique_together': {('patient', 'tooth_number')},
            },
        ),
        migrations.CreateModel(
            name='DentistryTemplate',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_private', models.BooleanField(default=False, verbose_name='Is private')),
                ('privatized_by', models.UUIDField(blank=True, null=True, verbose_name='Privatized by')),
                ('owner', models.UUIDField(blank=True, null=True, verbose_name='Owner')),
                ('name', models.CharField(max_length=100, verbose_name='Template Name')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('template_type', models.CharField(choices=[('medical_record', 'Medical Record'), ('treatment_plan', 'Treatment Plan'), ('prescription', 'Prescription'), ('referral', 'Referral'), ('consent_form', 'Consent Form'), ('post_op_instructions', 'Post-Op Instructions'), ('lab_order', 'Lab Order'), ('patient_education', 'Patient Education')], max_length=20, verbose_name='Template Type')),
                ('content', models.TextField(verbose_name='Template Content')),
                ('is_default', models.BooleanField(default=False, verbose_name='Is Default Template')),
                ('doctor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='dentistry_templates', to='dentistry.dentistrydoctor', verbose_name='Doctor')),
            ],
            options={
                'verbose_name': 'Dentistry Template',
                'verbose_name_plural': 'Dentistry Templates',
                'ordering': ['template_type', 'name'],
                'unique_together': {('doctor', 'template_type', 'name')},
            },
        ),
        migrations.CreateModel(
            name='DentistryProcedure',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_private', models.BooleanField(default=False, verbose_name='Is private')),
                ('privatized_by', models.UUIDField(blank=True, null=True, verbose_name='Privatized by')),
                ('owner', models.UUIDField(blank=True, null=True, verbose_name='Owner')),
                ('code', models.CharField(max_length=20, verbose_name='Procedure Code')),
                ('name', models.CharField(max_length=200, verbose_name='Procedure Name')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('ada_code', models.CharField(blank=True, max_length=20, verbose_name='ADA Code')),
                ('category', models.CharField(choices=[('diagnostic', 'Diagnostic'), ('preventive', 'Preventive'), ('restorative', 'Restorative'), ('endodontic', 'Endodontic'), ('periodontic', 'Periodontic'), ('prosthodontic', 'Prosthodontic'), ('oral_surgery', 'Oral Surgery'), ('orthodontic', 'Orthodontic'), ('other', 'Other')], default='other', max_length=20, verbose_name='Category')),
                ('default_duration', models.PositiveIntegerField(default=30, verbose_name='Default Duration (minutes)')),
                ('default_fee', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='Default Fee')),
                ('preparation_instructions', models.TextField(blank=True, verbose_name='Preparation Instructions')),
                ('aftercare_instructions', models.TextField(blank=True, verbose_name='Aftercare Instructions')),
                ('is_common', models.BooleanField(default=False, verbose_name='Is Common Procedure')),
                ('requires_lab_work', models.BooleanField(default=False, verbose_name='Requires Lab Work')),
                ('requires_anesthesia', models.BooleanField(default=False, verbose_name='Requires Anesthesia')),
                ('doctor', models.ForeignKey(blank=True, help_text='If null, this is a system-wide procedure', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='dentistry_procedures', to='dentistry.dentistrydoctor', verbose_name='Doctor')),
            ],
            options={
                'verbose_name': 'Dentistry Procedure',
                'verbose_name_plural': 'Dentistry Procedures',
                'ordering': ['code'],
                'unique_together': {('code', 'doctor')},
            },
        ),
        migrations.CreateModel(
            name='DentistryPage',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_private', models.BooleanField(default=False, verbose_name='Is private')),
                ('privatized_by', models.UUIDField(blank=True, null=True, verbose_name='Privatized by')),
                ('owner', models.UUIDField(blank=True, null=True, verbose_name='Owner')),
                ('title', models.CharField(max_length=200, verbose_name='Page Title')),
                ('slug', models.SlugField(max_length=200, verbose_name='Page Slug')),
                ('content', models.TextField(verbose_name='Page Content')),
                ('meta_description', models.TextField(blank=True, verbose_name='Meta Description')),
                ('is_published', models.BooleanField(default=True, verbose_name='Is Published')),
                ('display_in_menu', models.BooleanField(default=True, verbose_name='Display in Menu')),
                ('menu_order', models.PositiveIntegerField(default=0, verbose_name='Menu Order')),
                ('doctor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='dentistry_pages', to='dentistry.dentistrydoctor', verbose_name='Doctor')),
            ],
            options={
                'verbose_name': 'Dentistry Page',
                'verbose_name_plural': 'Dentistry Pages',
                'ordering': ['menu_order', 'title'],
                'unique_together': {('doctor', 'slug')},
            },
        ),
        migrations.CreateModel(
            name='DentistryMaterial',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_private', models.BooleanField(default=False, verbose_name='Is private')),
                ('privatized_by', models.UUIDField(blank=True, null=True, verbose_name='Privatized by')),
                ('owner', models.UUIDField(blank=True, null=True, verbose_name='Owner')),
                ('name', models.CharField(max_length=200, verbose_name='Material Name')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('category', models.CharField(choices=[('restorative', 'Restorative'), ('impression', 'Impression'), ('cement', 'Cement'), ('temporary', 'Temporary'), ('endodontic', 'Endodontic'), ('surgical', 'Surgical'), ('other', 'Other')], default='other', max_length=20, verbose_name='Category')),
                ('manufacturer', models.CharField(blank=True, max_length=100, verbose_name='Manufacturer')),
                ('instructions', models.TextField(blank=True, verbose_name='Instructions')),
                ('contraindications', models.TextField(blank=True, verbose_name='Contraindications')),
                ('is_common', models.BooleanField(default=False, verbose_name='Is Common Material')),
                ('doctor', models.ForeignKey(blank=True, help_text='If null, this is a system-wide material', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='dentistry_materials', to='dentistry.dentistrydoctor', verbose_name='Doctor')),
            ],
            options={
                'verbose_name': 'Dentistry Material',
                'verbose_name_plural': 'Dentistry Materials',
                'ordering': ['name'],
                'unique_together': {('name', 'doctor')},
            },
        ),
        migrations.CreateModel(
            name='DentistryDiagnosis',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_private', models.BooleanField(default=False, verbose_name='Is private')),
                ('privatized_by', models.UUIDField(blank=True, null=True, verbose_name='Privatized by')),
                ('owner', models.UUIDField(blank=True, null=True, verbose_name='Owner')),
                ('code', models.CharField(max_length=20, verbose_name='Diagnosis Code')),
                ('name', models.CharField(max_length=200, verbose_name='Diagnosis Name')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('icd10_code', models.CharField(blank=True, max_length=20, verbose_name='ICD-10 Code')),
                ('category', models.CharField(choices=[('caries', 'Caries'), ('periodontal', 'Periodontal Disease'), ('endodontic', 'Endodontic'), ('orthodontic', 'Orthodontic'), ('oral_pathology', 'Oral Pathology'), ('tmj', 'TMJ Disorders'), ('other', 'Other')], default='other', max_length=20, verbose_name='Category')),
                ('is_common', models.BooleanField(default=False, verbose_name='Is Common Diagnosis')),
                ('doctor', models.ForeignKey(blank=True, help_text='If null, this is a system-wide diagnosis', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='dentistry_diagnoses', to='dentistry.dentistrydoctor', verbose_name='Doctor')),
            ],
            options={
                'verbose_name': 'Dentistry Diagnosis',
                'verbose_name_plural': 'Dentistry Diagnoses',
                'ordering': ['code'],
                'unique_together': {('code', 'doctor')},
            },
        ),
        migrations.CreateModel(
            name='DentistryCustomFieldValue',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_private', models.BooleanField(default=False, verbose_name='Is private')),
                ('privatized_by', models.UUIDField(blank=True, null=True, verbose_name='Privatized by')),
                ('owner', models.UUIDField(blank=True, null=True, verbose_name='Owner')),
                ('entity_id', models.PositiveIntegerField(verbose_name='Entity ID')),
                ('value_text', models.TextField(blank=True, verbose_name='Text Value')),
                ('value_number', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='Number Value')),
                ('value_date', models.DateField(blank=True, null=True, verbose_name='Date Value')),
                ('value_boolean', models.BooleanField(blank=True, null=True, verbose_name='Boolean Value')),
                ('value_json', models.JSONField(blank=True, default=dict, verbose_name='JSON Value')),
                ('custom_field', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='values', to='dentistry.dentistrycustomfield', verbose_name='Custom Field')),
            ],
            options={
                'verbose_name': 'Dentistry Custom Field Value',
                'verbose_name_plural': 'Dentistry Custom Field Values',
                'unique_together': {('custom_field', 'entity_id')},
            },
        ),
    ]
