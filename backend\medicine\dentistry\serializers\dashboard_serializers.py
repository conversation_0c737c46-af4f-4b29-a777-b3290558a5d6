"""
Serializers for dashboard settings models in the dentistry application.
"""
from rest_framework import serializers
from dentistry.models import (
    DentistryAppointmentSettings, DentistryWorkingHours,
    DentistryHoliday, DentistryLocationSettings
)


class DentistryAppointmentSettingsSerializer(serializers.ModelSerializer):
    """
    Serializer for DentistryAppointmentSettings model.
    """
    doctor_name = serializers.CharField(source='doctor.full_name', read_only=True)

    class Meta:
        model = DentistryAppointmentSettings
        fields = [
            'id', 'doctor', 'doctor_name', 'allow_online_booking',
            'advance_booking_days', 'min_booking_notice_hours',
            'default_appointment_duration', 'consultation_duration',
            'cleaning_duration', 'treatment_duration', 'emergency_duration',
            'buffer_time_before', 'buffer_time_after',
            'allow_patient_cancellation', 'cancellation_notice_hours',
            'send_appointment_reminders', 'reminder_hours_before',
            'reminder_methods', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'doctor_name']

    def validate_reminder_methods(self, value):
        """Validate reminder methods."""
        valid_methods = ['email', 'sms', 'push']
        if not isinstance(value, list):
            raise serializers.ValidationError("Reminder methods must be a list")
        
        for method in value:
            if method not in valid_methods:
                raise serializers.ValidationError(f"Invalid reminder method: {method}")
        
        return value


class DentistryWorkingHoursSerializer(serializers.ModelSerializer):
    """
    Serializer for DentistryWorkingHours model.
    """
    doctor_name = serializers.CharField(source='doctor.full_name', read_only=True)
    weekday_display = serializers.CharField(source='get_weekday_display', read_only=True)

    class Meta:
        model = DentistryWorkingHours
        fields = [
            'id', 'doctor', 'doctor_name', 'weekday', 'weekday_display',
            'is_working_day', 'morning_start', 'morning_end',
            'afternoon_start', 'afternoon_end', 'lunch_break_start',
            'lunch_break_end', 'notes', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'doctor_name', 'weekday_display']

    def validate(self, data):
        """Custom validation for working hours."""
        if data.get('is_working_day'):
            # If it's a working day, at least one session should be defined
            has_morning = data.get('morning_start') and data.get('morning_end')
            has_afternoon = data.get('afternoon_start') and data.get('afternoon_end')
            
            if not has_morning and not has_afternoon:
                raise serializers.ValidationError(
                    "Working days must have at least one session (morning or afternoon)"
                )
            
            # Validate morning session
            if has_morning:
                if data['morning_start'] >= data['morning_end']:
                    raise serializers.ValidationError(
                        "Morning start time must be before morning end time"
                    )
            
            # Validate afternoon session
            if has_afternoon:
                if data['afternoon_start'] >= data['afternoon_end']:
                    raise serializers.ValidationError(
                        "Afternoon start time must be before afternoon end time"
                    )
            
            # Validate lunch break
            lunch_start = data.get('lunch_break_start')
            lunch_end = data.get('lunch_break_end')
            if lunch_start and lunch_end:
                if lunch_start >= lunch_end:
                    raise serializers.ValidationError(
                        "Lunch break start time must be before lunch break end time"
                    )
        
        return data


class DentistryHolidaySerializer(serializers.ModelSerializer):
    """
    Serializer for DentistryHoliday model.
    """
    doctor_name = serializers.CharField(source='doctor.full_name', read_only=True)
    holiday_type_display = serializers.CharField(source='get_holiday_type_display', read_only=True)
    duration_days = serializers.SerializerMethodField()

    class Meta:
        model = DentistryHoliday
        fields = [
            'id', 'doctor', 'doctor_name', 'title', 'description',
            'holiday_type', 'holiday_type_display', 'start_date', 'end_date',
            'is_all_day', 'start_time', 'end_time', 'is_recurring',
            'recurrence_pattern', 'affects_appointments', 'duration_days',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'doctor_name', 'holiday_type_display', 'duration_days']

    def get_duration_days(self, obj):
        """Calculate the duration of the holiday in days."""
        return (obj.end_date - obj.start_date).days + 1

    def validate(self, data):
        """Custom validation for holidays."""
        if data['start_date'] > data['end_date']:
            raise serializers.ValidationError("Start date must be before or equal to end date")
        
        if not data.get('is_all_day'):
            if not data.get('start_time') or not data.get('end_time'):
                raise serializers.ValidationError(
                    "Start time and end time are required when not all day"
                )
            if data['start_time'] >= data['end_time']:
                raise serializers.ValidationError("Start time must be before end time")
        
        return data


class DentistryLocationSettingsSerializer(serializers.ModelSerializer):
    """
    Serializer for DentistryLocationSettings model.
    """
    doctor_name = serializers.CharField(source='doctor.full_name', read_only=True)
    full_address = serializers.ReadOnlyField()

    class Meta:
        model = DentistryLocationSettings
        fields = [
            'id', 'doctor', 'doctor_name', 'clinic_name',
            'address_line_1', 'address_line_2', 'city', 'state_province',
            'postal_code', 'country', 'phone_number', 'fax_number',
            'email', 'website', 'latitude', 'longitude', 'show_on_map',
            'map_zoom_level', 'parking_available', 'parking_instructions',
            'public_transport_info', 'accessibility_info', 'full_address',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'doctor_name', 'full_address']

    def validate_email(self, value):
        """Validate email format."""
        if value and '@' not in value:
            raise serializers.ValidationError("Enter a valid email address")
        return value

    def validate_website(self, value):
        """Validate website URL."""
        if value and not value.startswith(('http://', 'https://')):
            return f'https://{value}'
        return value


class DentistryDashboardSummarySerializer(serializers.Serializer):
    """
    Serializer for dashboard summary data.
    """
    doctor_id = serializers.CharField()
    doctor_name = serializers.CharField()
    
    # Appointment settings summary
    appointment_settings = DentistryAppointmentSettingsSerializer(read_only=True)
    
    # Working hours summary
    working_hours = DentistryWorkingHoursSerializer(many=True, read_only=True)
    
    # Current holidays
    current_holidays = DentistryHolidaySerializer(many=True, read_only=True)
    
    # Location settings
    location_settings = DentistryLocationSettingsSerializer(read_only=True)
    
    # Statistics
    total_working_days = serializers.IntegerField()
    upcoming_holidays = serializers.IntegerField()
    online_booking_enabled = serializers.BooleanField()


class DentistryWorkingHoursCreateSerializer(serializers.ModelSerializer):
    """
    Simplified serializer for creating working hours.
    """
    class Meta:
        model = DentistryWorkingHours
        fields = [
            'doctor', 'weekday', 'is_working_day',
            'morning_start', 'morning_end', 'afternoon_start', 'afternoon_end',
            'lunch_break_start', 'lunch_break_end', 'notes'
        ]

    def validate(self, data):
        """Custom validation for creation."""
        if data.get('is_working_day'):
            has_morning = data.get('morning_start') and data.get('morning_end')
            has_afternoon = data.get('afternoon_start') and data.get('afternoon_end')
            
            if not has_morning and not has_afternoon:
                raise serializers.ValidationError(
                    "Working days must have at least one session"
                )
        
        return data


class DentistryHolidayCreateSerializer(serializers.ModelSerializer):
    """
    Simplified serializer for creating holidays.
    """
    class Meta:
        model = DentistryHoliday
        fields = [
            'doctor', 'title', 'description', 'holiday_type',
            'start_date', 'end_date', 'is_all_day', 'start_time', 'end_time',
            'is_recurring', 'recurrence_pattern', 'affects_appointments'
        ]

    def validate(self, data):
        """Custom validation for creation."""
        if data['start_date'] > data['end_date']:
            raise serializers.ValidationError("Start date must be before end date")
        
        if not data.get('is_all_day'):
            if not data.get('start_time') or not data.get('end_time'):
                raise serializers.ValidationError(
                    "Time fields are required when not all day"
                )
        
        return data
