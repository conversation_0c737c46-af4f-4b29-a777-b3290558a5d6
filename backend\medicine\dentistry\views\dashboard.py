"""
Views for dashboard settings in the dentistry application.
"""
from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.utils import timezone
from datetime import datetime, timedelta
from django.db.models import Q

from dentistry.models import (
    DentistryDoctor, DentistryAppointmentSettings, DentistryWorkingHours,
    DentistryHoliday, DentistryLocationSettings
)
from dentistry.serializers.dashboard_serializers import (
    DentistryAppointmentSettingsSerializer, DentistryWorkingHoursSerializer,
    DentistryWorkingHoursCreateSerializer, DentistryHolidaySerializer,
    DentistryHolidayCreateSerializer, DentistryLocationSettingsSerializer,
    DentistryDashboardSummarySerializer
)


class DentistryAppointmentSettingsViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing dentistry appointment settings.
    """
    queryset = DentistryAppointmentSettings.objects.all()
    serializer_class = DentistryAppointmentSettingsSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()
        doctor_id = self.request.query_params.get('doctor_id')
        if doctor_id:
            queryset = queryset.filter(doctor_id=doctor_id)
        return queryset

    @action(detail=False, methods=['get'])
    def my_settings(self, request):
        """Get appointment settings for the current doctor."""
        # This would be used when we have user authentication
        # For now, we'll use a doctor_id parameter
        doctor_id = request.query_params.get('doctor_id')
        if not doctor_id:
            return Response({'error': 'doctor_id is required'}, status=400)

        try:
            settings, created = DentistryAppointmentSettings.objects.get_or_create(
                doctor_id=doctor_id,
                defaults={
                    'allow_online_booking': True,
                    'advance_booking_days': 30,
                    'min_booking_notice_hours': 24,
                    'default_appointment_duration': 30,
                    'consultation_duration': 30,
                    'cleaning_duration': 60,
                    'treatment_duration': 90,
                    'emergency_duration': 45,
                    'buffer_time_before': 10,
                    'buffer_time_after': 10,
                    'allow_patient_cancellation': True,
                    'cancellation_notice_hours': 24,
                    'send_appointment_reminders': True,
                    'reminder_hours_before': 24,
                    'reminder_methods': ['email', 'sms']
                }
            )
            
            serializer = self.get_serializer(settings)
            return Response(serializer.data)
        except Exception as e:
            return Response({'error': str(e)}, status=500)


class DentistryWorkingHoursViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing dentistry working hours.
    """
    queryset = DentistryWorkingHours.objects.all()
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.action == 'create':
            return DentistryWorkingHoursCreateSerializer
        return DentistryWorkingHoursSerializer

    def get_queryset(self):
        queryset = super().get_queryset()
        doctor_id = self.request.query_params.get('doctor_id')
        if doctor_id:
            queryset = queryset.filter(doctor_id=doctor_id)
        return queryset.order_by('weekday')

    @action(detail=False, methods=['get'])
    def weekly_schedule(self, request):
        """Get the complete weekly schedule for a doctor."""
        doctor_id = request.query_params.get('doctor_id')
        if not doctor_id:
            return Response({'error': 'doctor_id is required'}, status=400)

        # Get or create working hours for all weekdays
        working_hours = []
        for weekday in range(7):  # 0=Monday, 6=Sunday
            hours, created = DentistryWorkingHours.objects.get_or_create(
                doctor_id=doctor_id,
                weekday=weekday,
                defaults={
                    'is_working_day': weekday < 5,  # Monday-Friday by default
                    'morning_start': '09:00' if weekday < 5 else None,
                    'morning_end': '12:00' if weekday < 5 else None,
                    'afternoon_start': '14:00' if weekday < 5 else None,
                    'afternoon_end': '18:00' if weekday < 5 else None,
                    'lunch_break_start': '12:00' if weekday < 5 else None,
                    'lunch_break_end': '14:00' if weekday < 5 else None,
                }
            )
            working_hours.append(hours)

        serializer = DentistryWorkingHoursSerializer(working_hours, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['post'])
    def update_weekly_schedule(self, request):
        """Update the complete weekly schedule for a doctor."""
        doctor_id = request.data.get('doctor_id')
        schedule_data = request.data.get('schedule', [])

        if not doctor_id:
            return Response({'error': 'doctor_id is required'}, status=400)

        updated_hours = []
        for day_data in schedule_data:
            weekday = day_data.get('weekday')
            if weekday is None:
                continue

            hours, created = DentistryWorkingHours.objects.get_or_create(
                doctor_id=doctor_id,
                weekday=weekday
            )

            # Update the working hours
            for field in ['is_working_day', 'morning_start', 'morning_end', 
                         'afternoon_start', 'afternoon_end', 'lunch_break_start', 
                         'lunch_break_end', 'notes']:
                if field in day_data:
                    setattr(hours, field, day_data[field])

            hours.save()
            updated_hours.append(hours)

        serializer = DentistryWorkingHoursSerializer(updated_hours, many=True)
        return Response(serializer.data)


class DentistryHolidayViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing dentistry holidays.
    """
    queryset = DentistryHoliday.objects.all()
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.action == 'create':
            return DentistryHolidayCreateSerializer
        return DentistryHolidaySerializer

    def get_queryset(self):
        queryset = super().get_queryset()
        doctor_id = self.request.query_params.get('doctor_id')
        if doctor_id:
            queryset = queryset.filter(doctor_id=doctor_id)

        # Filter by date range
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        if start_date and end_date:
            queryset = queryset.filter(
                Q(start_date__lte=end_date) & Q(end_date__gte=start_date)
            )

        return queryset.order_by('start_date')

    @action(detail=False, methods=['get'])
    def upcoming(self, request):
        """Get upcoming holidays for a doctor."""
        doctor_id = request.query_params.get('doctor_id')
        if not doctor_id:
            return Response({'error': 'doctor_id is required'}, status=400)

        today = timezone.now().date()
        upcoming_holidays = self.get_queryset().filter(
            doctor_id=doctor_id,
            end_date__gte=today
        ).order_by('start_date')[:10]

        serializer = self.get_serializer(upcoming_holidays, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def current(self, request):
        """Get current holidays for a doctor."""
        doctor_id = request.query_params.get('doctor_id')
        if not doctor_id:
            return Response({'error': 'doctor_id is required'}, status=400)

        today = timezone.now().date()
        current_holidays = self.get_queryset().filter(
            doctor_id=doctor_id,
            start_date__lte=today,
            end_date__gte=today
        )

        serializer = self.get_serializer(current_holidays, many=True)
        return Response(serializer.data)


class DentistryLocationSettingsViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing dentistry location settings.
    """
    queryset = DentistryLocationSettings.objects.all()
    serializer_class = DentistryLocationSettingsSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()
        doctor_id = self.request.query_params.get('doctor_id')
        if doctor_id:
            queryset = queryset.filter(doctor_id=doctor_id)
        return queryset

    @action(detail=False, methods=['get'])
    def my_location(self, request):
        """Get location settings for the current doctor."""
        doctor_id = request.query_params.get('doctor_id')
        if not doctor_id:
            return Response({'error': 'doctor_id is required'}, status=400)

        try:
            location_settings, created = DentistryLocationSettings.objects.get_or_create(
                doctor_id=doctor_id,
                defaults={
                    'clinic_name': 'My Dental Clinic',
                    'address_line_1': '',
                    'city': '',
                    'state_province': '',
                    'postal_code': '',
                    'country': '',
                    'phone_number': '',
                    'email': '',
                    'show_on_map': True,
                    'map_zoom_level': 15,
                    'parking_available': True
                }
            )
            
            serializer = self.get_serializer(location_settings)
            return Response(serializer.data)
        except Exception as e:
            return Response({'error': str(e)}, status=500)


class DentistryDashboardViewSet(viewsets.ViewSet):
    """
    ViewSet for dashboard summary and overview.
    """
    permission_classes = [permissions.IsAuthenticated]

    @action(detail=False, methods=['get'])
    def summary(self, request):
        """Get dashboard summary for a doctor."""
        doctor_id = request.query_params.get('doctor_id')
        if not doctor_id:
            return Response({'error': 'doctor_id is required'}, status=400)

        try:
            doctor = DentistryDoctor.objects.get(id=doctor_id)
        except DentistryDoctor.DoesNotExist:
            return Response({'error': 'Doctor not found'}, status=404)

        # Get or create settings
        appointment_settings, _ = DentistryAppointmentSettings.objects.get_or_create(
            doctor=doctor
        )
        location_settings, _ = DentistryLocationSettings.objects.get_or_create(
            doctor=doctor
        )

        # Get working hours
        working_hours = DentistryWorkingHours.objects.filter(doctor=doctor).order_by('weekday')

        # Get current and upcoming holidays
        today = timezone.now().date()
        current_holidays = DentistryHoliday.objects.filter(
            doctor=doctor,
            start_date__lte=today,
            end_date__gte=today
        )
        upcoming_holidays_count = DentistryHoliday.objects.filter(
            doctor=doctor,
            start_date__gt=today
        ).count()

        # Calculate statistics
        total_working_days = working_hours.filter(is_working_day=True).count()

        summary_data = {
            'doctor_id': doctor_id,
            'doctor_name': doctor.full_name,
            'appointment_settings': DentistryAppointmentSettingsSerializer(appointment_settings).data,
            'working_hours': DentistryWorkingHoursSerializer(working_hours, many=True).data,
            'current_holidays': DentistryHolidaySerializer(current_holidays, many=True).data,
            'location_settings': DentistryLocationSettingsSerializer(location_settings).data,
            'total_working_days': total_working_days,
            'upcoming_holidays': upcoming_holidays_count,
            'online_booking_enabled': appointment_settings.allow_online_booking
        }

        serializer = DentistryDashboardSummarySerializer(summary_data)
        return Response(serializer.data)
