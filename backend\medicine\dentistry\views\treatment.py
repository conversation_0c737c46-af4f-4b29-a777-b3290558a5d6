"""
Treatment views for the dentistry application.
"""
from rest_framework import viewsets, permissions, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from dentistry.models import DentalTreatment, DentalProcedure
from dentistry.serializers import (
    DentalTreatmentSerializer, DentalProcedureSerializer
)

class DentalTreatmentViewSet(viewsets.ModelViewSet):
    """
    API endpoint for dental treatment plans.
    """
    queryset = DentalTreatment.objects.all()
    serializer_class = DentalTreatmentSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['patient', 'doctor', 'status']
    search_fields = ['title', 'description', 'notes']
    ordering_fields = ['start_date', 'estimated_end_date', 'created_at']
    
    def get_queryset(self):
        """
        Filter treatment plans based on query parameters.
        """
        queryset = super().get_queryset()
        
        # Filter by patient ID
        patient_id = self.request.query_params.get('patient_id')
        if patient_id:
            queryset = queryset.filter(patient_id=patient_id)
        
        # Filter by date range
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        
        if start_date:
            queryset = queryset.filter(start_date__gte=start_date)
        if end_date:
            queryset = queryset.filter(start_date__lte=end_date)
        
        return queryset
    
    @action(detail=True, methods=['get'])
    def procedures(self, request, pk=None):
        """
        Get all procedures for a treatment plan.
        """
        treatment = self.get_object()
        procedures = treatment.procedures.all()
        
        page = self.paginate_queryset(procedures)
        if page is not None:
            serializer = DentalProcedureSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = DentalProcedureSerializer(procedures, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def start(self, request, pk=None):
        """
        Start a treatment plan.
        """
        treatment = self.get_object()
        
        if treatment.status != 'planned':
            return Response(
                {"detail": "Only planned treatments can be started."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        treatment.status = 'in_progress'
        treatment.save()
        
        serializer = self.get_serializer(treatment)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def complete(self, request, pk=None):
        """
        Complete a treatment plan.
        """
        treatment = self.get_object()
        
        if treatment.status != 'in_progress':
            return Response(
                {"detail": "Only in-progress treatments can be completed."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        from datetime import date
        treatment.status = 'completed'
        treatment.actual_end_date = date.today()
        treatment.save()
        
        serializer = self.get_serializer(treatment)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def cancel(self, request, pk=None):
        """
        Cancel a treatment plan.
        """
        treatment = self.get_object()
        
        if treatment.status in ['completed', 'cancelled']:
            return Response(
                {"detail": "This treatment cannot be cancelled."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        treatment.status = 'cancelled'
        treatment.save()
        
        serializer = self.get_serializer(treatment)
        return Response(serializer.data)

class DentalProcedureViewSet(viewsets.ModelViewSet):
    """
    API endpoint for dental procedures.
    """
    queryset = DentalProcedure.objects.all()
    serializer_class = DentalProcedureSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['treatment', 'procedure_type', 'status']
    search_fields = ['name', 'description', 'notes', 'tooth_numbers']
    ordering_fields = ['scheduled_date', 'completed_date', 'created_at']
    
    def get_queryset(self):
        """
        Filter procedures based on query parameters.
        """
        queryset = super().get_queryset()
        
        # Filter by treatment ID
        treatment_id = self.request.query_params.get('treatment_id')
        if treatment_id:
            queryset = queryset.filter(treatment_id=treatment_id)
        
        # Filter by date range
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        
        if start_date:
            queryset = queryset.filter(scheduled_date__gte=start_date)
        if end_date:
            queryset = queryset.filter(scheduled_date__lte=end_date)
        
        # Filter by tooth number
        tooth_number = self.request.query_params.get('tooth_number')
        if tooth_number:
            queryset = queryset.filter(tooth_numbers__contains=tooth_number)
        
        return queryset
    
    @action(detail=True, methods=['post'])
    def schedule(self, request, pk=None):
        """
        Schedule a procedure.
        """
        procedure = self.get_object()
        
        if procedure.status != 'planned':
            return Response(
                {"detail": "Only planned procedures can be scheduled."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        scheduled_date = request.data.get('scheduled_date')
        if not scheduled_date:
            return Response(
                {"detail": "Scheduled date is required."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        procedure.scheduled_date = scheduled_date
        procedure.status = 'scheduled'
        procedure.save()
        
        serializer = self.get_serializer(procedure)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def start(self, request, pk=None):
        """
        Start a procedure.
        """
        procedure = self.get_object()
        
        if procedure.status not in ['planned', 'scheduled']:
            return Response(
                {"detail": "Only planned or scheduled procedures can be started."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        procedure.status = 'in_progress'
        procedure.save()
        
        serializer = self.get_serializer(procedure)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def complete(self, request, pk=None):
        """
        Complete a procedure.
        """
        procedure = self.get_object()
        
        if procedure.status not in ['planned', 'scheduled', 'in_progress']:
            return Response(
                {"detail": "This procedure cannot be completed."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        from datetime import date
        procedure.status = 'completed'
        procedure.completed_date = date.today()
        procedure.save()
        
        serializer = self.get_serializer(procedure)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def cancel(self, request, pk=None):
        """
        Cancel a procedure.
        """
        procedure = self.get_object()
        
        if procedure.status in ['completed', 'cancelled']:
            return Response(
                {"detail": "This procedure cannot be cancelled."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        procedure.status = 'cancelled'
        procedure.save()
        
        serializer = self.get_serializer(procedure)
        return Response(serializer.data)
