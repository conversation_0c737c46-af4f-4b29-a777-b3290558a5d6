# Generated by Django 4.2.7 on 2025-05-29 18:07

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('dentistry', '0010_dentalset_toothbutton_specialtyfield_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='toothbutton',
            name='tooth_number',
            field=models.PositiveSmallIntegerField(help_text='Numéro FDI: 11-18, 21-28 (sup<PERSON><PERSON>), 31-38, 41-48 (inférieur), 51-55, 61-65, 71-75, 81-85 (primaires)', validators=[django.core.validators.MinValueValidator(11), django.core.validators.MaxValueValidator(85)], verbose_name='Numéro de la dent'),
        ),
    ]
