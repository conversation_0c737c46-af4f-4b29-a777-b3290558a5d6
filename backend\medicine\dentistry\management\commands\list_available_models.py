"""
Commande pour lister tous les modèles disponibles dans l'application dentistry
"""
from django.core.management.base import BaseCommand
from django.apps import apps


class Command(BaseCommand):
    help = 'List all available models in dentistry app'

    def handle(self, *args, **options):
        """
        Liste tous les modèles disponibles dans l'application dentistry
        """
        self.stdout.write(self.style.SUCCESS('Listing all available models in dentistry app...'))

        try:
            # Obtenir tous les modèles de l'application dentistry
            dentistry_models = list(apps.get_app_config('dentistry').get_models())

            self.stdout.write(f'\n📋 Total models found: {len(dentistry_models)}')

            # Grouper les modèles par catégorie basée sur leur nom
            model_groups = {
                'Patient & Doctor': [],
                'Appointment & Consultation': [],
                'Treatment & Procedure': [],
                'Laboratory & Billing': [],
                'Comment & Review': [],
                'SVG System': [],
                'Configuration': [],
                'Website': [],
                'Other': []
            }

            for model in dentistry_models:
                model_name = model.__name__

                # Catégoriser les modèles
                if any(keyword in model_name.lower() for keyword in ['patient', 'doctor', 'role', 'staff']):
                    model_groups['Patient & Doctor'].append(model_name)
                elif any(keyword in model_name.lower() for keyword in ['appointment', 'consultation', 'tooth']):
                    model_groups['Appointment & Consultation'].append(model_name)
                elif any(keyword in model_name.lower() for keyword in ['treatment', 'procedure', 'imaging', 'diagnosis', 'material', 'medical']):
                    model_groups['Treatment & Procedure'].append(model_name)
                elif any(keyword in model_name.lower() for keyword in ['laboratory', 'billing', 'invoice', 'insurance']):
                    model_groups['Laboratory & Billing'].append(model_name)
                elif any(keyword in model_name.lower() for keyword in ['comment', 'review', 'note']):
                    model_groups['Comment & Review'].append(model_name)
                elif any(keyword in model_name.lower() for keyword in ['svg', 'dental']):
                    model_groups['SVG System'].append(model_name)
                elif any(keyword in model_name.lower() for keyword in ['configuration', 'template', 'field', 'notification', 'reminder']):
                    model_groups['Configuration'].append(model_name)
                elif any(keyword in model_name.lower() for keyword in ['website', 'page', 'before', 'after']):
                    model_groups['Website'].append(model_name)
                else:
                    model_groups['Other'].append(model_name)

            # Afficher les modèles par groupe
            for group_name, models in model_groups.items():
                if models:
                    self.stdout.write(f'\n🔹 {group_name} ({len(models)} models):')
                    for model_name in sorted(models):
                        self.stdout.write(f'   ✅ {model_name}')

            # Générer le code d'enregistrement pour l'admin
            self.stdout.write(f'\n📝 Admin registration code:')
            self.stdout.write('# Copy this code to your admin.py file:')
            self.stdout.write('')

            for model in sorted(dentistry_models, key=lambda x: x.__name__):
                model_name = model.__name__
                self.stdout.write(f'admin.site.register({model_name})')

            self.stdout.write(self.style.SUCCESS('\n✨ Model listing completed!'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error: {e}'))
