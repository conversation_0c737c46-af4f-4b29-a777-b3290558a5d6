"""
Patient serializers for the dentistry application.
"""
from rest_framework import serializers
from dentistry.models import DentistryPatient

class DentistryPatientSerializer(serializers.ModelSerializer):
    """
    Serializer for the DentistryPatient model.
    """
    full_name = serializers.CharField(source='get_full_name', read_only=True)

    class Meta:
        model = DentistryPatient
        fields = [
            'id', 'first_name', 'last_name', 'full_name', 'email', 'phone',
            'date_of_birth', 'gender', 'address', 'city', 'postal_code',
            'emergency_contact_name', 'emergency_contact_phone',
            'medical_history', 'allergies', 'current_medications',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'full_name', 'created_at', 'updated_at']

    def create(self, validated_data):
        """
        Create a new dentistry patient profile.
        """
        return DentistryPatient.objects.create(**validated_data)

    def update(self, instance, validated_data):
        """
        Update an existing dentistry patient profile.
        """
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        return instance
