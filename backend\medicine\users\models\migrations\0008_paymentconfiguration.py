# Generated by Django 5.1.3 on 2025-05-01 11:39

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0007_doctorlicense_days_remaining_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='PaymentConfiguration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('bank_name', models.CharField(default='Medical Bank', max_length=100)),
                ('account_number', models.Char<PERSON>ield(default='1234-5678-9012-3456', max_length=50)),
                ('account_holder', models.CharField(default='Medical Portal Inc.', max_length=100)),
                ('basic_price_6months', models.DecimalField(decimal_places=2, default=199.0, max_digits=10)),
                ('basic_price_annual', models.DecimalField(decimal_places=2, default=299.0, max_digits=10)),
                ('standard_price_6months', models.DecimalField(decimal_places=2, default=299.0, max_digits=10)),
                ('standard_price_annual', models.DecimalField(decimal_places=2, default=399.0, max_digits=10)),
                ('premium_price_6months', models.DecimalField(decimal_places=2, default=399.0, max_digits=10)),
                ('premium_price_annual', models.DecimalField(decimal_places=2, default=499.0, max_digits=10)),
                ('basic_max_users', models.IntegerField(default=3)),
                ('basic_max_assistants', models.IntegerField(default=2)),
                ('standard_max_users', models.IntegerField(default=5)),
                ('standard_max_assistants', models.IntegerField(default=3)),
                ('premium_max_users', models.IntegerField(default=10)),
                ('premium_max_assistants', models.IntegerField(default=5)),
                ('enable_bank_transfer', models.BooleanField(default=True)),
                ('enable_credit_card', models.BooleanField(default=True)),
                ('enable_paypal', models.BooleanField(default=False)),
                ('support_email', models.EmailField(default='<EMAIL>', max_length=254)),
                ('sales_email', models.EmailField(default='<EMAIL>', max_length=254)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'verbose_name': 'Payment Configuration',
                'verbose_name_plural': 'Payment Configurations',
            },
        ),
    ]
