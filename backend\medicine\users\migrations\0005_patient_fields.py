from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0004_location_and_profile_images'),
    ]

    operations = [
        migrations.RenameField(
            model_name='user',
            old_name='assigned_doctor',
            new_name='old_assigned_doctor',
        ),
        migrations.AddField(
            model_name='user',
            name='age',
            field=models.PositiveIntegerField(blank=True, help_text='Patient age (calculated from birth date)', null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='appointment_date',
            field=models.DateField(blank=True, help_text='Next appointment date', null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='appointment_end_time',
            field=models.TimeField(blank=True, help_text='Next appointment end time', null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='appointment_time',
            field=models.TimeField(blank=True, help_text='Next appointment start time', null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='assigned_doctor',
            field=models.ForeignKey(blank=True, help_text='The doctor this user is assigned to (for patients and assistants)', limit_choices_to={'user_type': 'doctor'}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_users', to='users.user'),
        ),
        migrations.AddField(
            model_name='user',
            name='cin',
            field=models.CharField(blank=True, help_text='National ID number', max_length=30, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='commentaire_liste_attente',
            field=models.TextField(blank=True, help_text='Waiting list comment', null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='consultation_duration',
            field=models.PositiveIntegerField(blank=True, help_text='Consultation duration in minutes', null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='etat_agenda',
            field=models.CharField(blank=True, help_text='Agenda status', max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='etat_civil',
            field=models.CharField(blank=True, help_text='Civil status', max_length=30, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='event_title',
            field=models.CharField(blank=True, help_text='Event title', max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='event_type',
            field=models.CharField(blank=True, help_text='Event type', max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='gender',
            field=models.CharField(blank=True, help_text='Patient gender', max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='notes',
            field=models.TextField(blank=True, help_text='Patient notes', null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='resource_id',
            field=models.PositiveIntegerField(blank=True, help_text='Resource ID', null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='social_security',
            field=models.CharField(blank=True, help_text='Social security number', max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='title',
            field=models.CharField(blank=True, help_text='Patient title (Mr., Mrs., etc.)', max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='type_consultation',
            field=models.CharField(blank=True, help_text='Consultation type', max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='visitor_count',
            field=models.PositiveIntegerField(default=0, help_text='Visitor count'),
        ),
        migrations.RemoveField(
            model_name='user',
            name='old_assigned_doctor',
        ),
    ]
