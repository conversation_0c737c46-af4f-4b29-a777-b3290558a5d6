"use client";

import React, { useState } from 'react';
import {
  Card,
  Text,
  Tabs,
  Stack,
  Group,
  Checkbox,
  Divider,
  ScrollArea
} from '@mantine/core';
import {
  IconBadge,
  IconList,
  IconSettings,
  IconTextSize,
  IconListDetails
} from '@tabler/icons-react';
import { DataTable } from 'mantine-datatable';

// Types
interface ListItem {
  id: string;
  nom: string;
  description: string;
  cache: boolean;
}

interface TypeItem {
  id: string;
  nom: string;
  slug: string;
}

const FicheTechniqueSupplementaire = () => {
  // États pour les onglets et données
  const [activeTab, setActiveTab] = useState('listes-de-choix');
  const [selectedType, setSelectedType] = useState<string | null>(null);
  const [types, setTypes] = useState<TypeItem[]>([]);
  const [listItems, setListItems] = useState<ListItem[]>([]);

  // Gestionnaires pour les onglets
  const handleTabChange = (value: string | null) => {
    if (value && value !== 'general') { // L'onglet général est désactivé
      setActiveTab(value);
    }
  };

  // Colonnes pour la table des éléments de liste
  const listColumns = [
    { accessor: 'nom', title: 'Nom' },
    { accessor: 'description', title: 'Description' },
    {
      accessor: 'cache',
      title: 'Caché',
      render: (record: ListItem) => (
        <Checkbox
          checked={record.cache}
          readOnly
          size="sm"
        />
      )
    },
    {
      accessor: 'actions',
      title: '',
      width: 120,
      render: () => (
        <Group gap="xs" justify="flex-end">
          {/* Actions seront ajoutées plus tard */}
        </Group>
      )
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header principal */}
      <div className="bg-teal-500 text-white p-4 shadow-md">
        <div className="flex items-center gap-3">
          <IconBadge size={24} />
          <h1 className="text-xl font-semibold">Fiche technique Supplémentaire</h1>
        </div>
      </div>

      {/* Contenu principal avec onglets */}
      <div className="p-0">
        <Card shadow="none" padding={0} radius={0} withBorder={false}>
          <Tabs value={activeTab} onChange={handleTabChange} variant="default">
            <Tabs.List className="border-b border-gray-200 bg-white px-4">
              <Tabs.Tab
                value="general"
                disabled
                leftSection={<IconSettings size={16} />}
                className="opacity-50 cursor-not-allowed"
              >
                Général
              </Tabs.Tab>
              <Tabs.Tab
                value="champs"
                leftSection={<IconTextSize size={16} />}
              >
                Champs
              </Tabs.Tab>
              <Tabs.Tab
                value="listes-de-choix"
                leftSection={<IconListDetails size={16} />}
              >
                Listes de choix
              </Tabs.Tab>
            </Tabs.List>

            {/* Onglet Général (désactivé) */}
            <Tabs.Panel value="general" pt="md">
              <div className="p-6">
                <Text c="dimmed">Cet onglet est désactivé.</Text>
              </div>
            </Tabs.Panel>

            {/* Onglet Champs */}
            <Tabs.Panel value="champs" pt={0}>
              <div className="p-6">
                <Text size="lg" fw={600} mb="md">
                  Configuration des champs
                </Text>
                <Text c="dimmed">
                  Contenu de l'onglet Champs à implémenter.
                </Text>
              </div>
            </Tabs.Panel>

            {/* Onglet Listes de choix */}
            <Tabs.Panel value="listes-de-choix" pt={0}>
              <div className="flex h-[calc(100vh-140px)]">
                {/* Sidebar Types */}
                <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
                  {/* Header de la sidebar */}
                  <div className="bg-blue-600 text-white p-4">
                    <div className="flex items-center gap-3">
                      <IconList size={20} />
                      <h2 className="text-lg font-semibold">Types</h2>
                    </div>
                  </div>

                  {/* Contenu de la sidebar */}
                  <ScrollArea className="flex-1 p-4">
                    {types.length === 0 ? (
                      <Text c="dimmed" size="sm" ta="center" mt="xl">
                        Aucun type disponible
                      </Text>
                    ) : (
                      <Stack gap="xs">
                        {types.map((type) => (
                          <div
                            key={type.id}
                            className={`p-3 rounded cursor-pointer transition-colors ${
                              selectedType === type.id
                                ? 'bg-blue-50 border border-blue-200'
                                : 'hover:bg-gray-50'
                            }`}
                            onClick={() => setSelectedType(type.id)}
                          >
                            <Text size="sm" fw={500}>
                              {type.nom}
                            </Text>
                          </div>
                        ))}
                      </Stack>
                    )}
                  </ScrollArea>
                </div>

                {/* Contenu principal - Table */}
                <div className="flex-1 bg-white flex flex-col">
                  <div className="flex-1 p-4">
                    <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
                      <DataTable
                        columns={listColumns}
                        records={listItems}
                        noRecordsText="Aucun élément trouvé."
                        minHeight={400}
                        striped
                        highlightOnHover
                        className="border-0"
                        styles={{
                          header: {
                            backgroundColor: '#f8f9fa',
                            borderBottom: '1px solid #dee2e6'
                          },
                          table: {
                            borderCollapse: 'separate',
                            borderSpacing: 0
                          }
                        }}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </Tabs.Panel>
          </Tabs>
        </Card>
      </div>
    </div>
  );
};

export default FicheTechniqueSupplementaire;
