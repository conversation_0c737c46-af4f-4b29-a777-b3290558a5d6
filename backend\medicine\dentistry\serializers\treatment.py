"""
Treatment serializers for the dentistry application.
"""
from rest_framework import serializers
from dentistry.models import DentalTreatment, DentalProcedure
from dentistry.serializers.patient import DentistryPatientSerializer

class DentalProcedureSerializer(serializers.ModelSerializer):
    """
    Serializer for the DentalProcedure model.
    """
    procedure_type_display = serializers.ReadOnlyField(source='get_procedure_type_display')
    status_display = serializers.ReadOnlyField(source='get_status_display')

    class Meta:
        model = DentalProcedure
        fields = [
            'id', 'treatment', 'procedure_type', 'procedure_type_display',
            'name', 'description', 'tooth_numbers', 'scheduled_date',
            'completed_date', 'status', 'status_display', 'cost',
            'notes', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

class DentalTreatmentSerializer(serializers.ModelSerializer):
    """
    Serializer for the DentalTreatment model.
    """
    patient_details = serializers.CharField(source='patient.get_full_name', read_only=True)
    doctor_name = serializers.CharField(source='doctor.get_full_name', read_only=True)
    status_display = serializers.ReadOnlyField(source='get_status_display')
    progress_percentage = serializers.ReadOnlyField()
    procedures_count = serializers.SerializerMethodField()

    class Meta:
        model = DentalTreatment
        fields = [
            'id', 'patient', 'patient_details', 'doctor', 'doctor_name',
            'title', 'description', 'start_date', 'estimated_end_date',
            'actual_end_date', 'status', 'status_display', 'estimated_cost',
            'notes', 'procedures_count', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at', 'progress_percentage']

    def get_procedures_count(self, obj):
        """
        Get the count of procedures in this treatment plan.
        """
        return obj.procedures.count()
