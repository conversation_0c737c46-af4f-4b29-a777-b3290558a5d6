.WeekView .rbc-time-view .rbc-time-header {
  display: block;
}
.rbc-time-view .rbc-row {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  min-height: 0px;
}
.WeekView .rbc-time-header {
  margin-left: 82px;
}
.WeekView .rbc-button-link {
  color: var(--text-daisy) !important;
  /* padding: 25px; */
  padding: 5px;
}
.WeekView .rbc-header {
  border-bottom: 1px solid var(---mantine-color-border) !important;
  height: 30px;
  padding: 0px !important;
}

.WeekView .rbc-header + .rbc-header {
  border-left: 1px solid var(---mantine-color-border) !important;
}
.WeekView .rbc-rtl .rbc-header + .rbc-header {
  border-left-width: 0;
  border-right: 1px solid var(---mantine-color-border) !important;
}
.WeekView .rbc-time-view .rbc-time-header-content {
  border-left: 1px solid var(---mantine-color-border) !important;
}
.WeekView .rbc-time-header.rbc-overflowing {
  border-right: 1px solid var(---mantine-color-border) !important;
}
.WeekView .rbc-today {
  /* background-color: #e6e9ec !important; */
}
.WeekView .rbc-today .rbc-button-link {
  color: #ffffff !important;
}
.WeekView .rbc-time-content > * + * > * {
  background-image: url("/add.svg");
  background-size: 12px 12px;
  background-repeat: no-repeat;
  background-position: center;
} 


/* .saturday{
  pointer-events: none;
  opacity: 0.5;
}  */

.saturday-afternoon-slot>.rbc-day-slot .rbc-time-slot {
  border-top: 1px solid #5baad6 !important;
}
/* label {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 20px;
}

select,
input {
  padding: 5px;
  font-size: 14px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

input {
  width: 100px;
} */
.dayslotPropGetter
 {
  color: #74a4c3;
  z-index: 0;
}

 /* DayView.css */
 .WeekView > .rbc-time-view >  .rbc-time-content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 1;
  -ms-flex: 1 0 0%;
  flex: 1 0 0%;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  width: 100%;
  border-top: 2px solid var(--rbc-border) !important;
  position: relative;
   padding-top: 0px !important; 
  /* overflow-y: auto; */
}
.rbc-event, .rbc-day-slot .rbc-background-event {
  padding: 0px  !important;
}
.rbc-day-slot .rbc-event, .rbc-day-slot .rbc-background-event {
   border: none !important;
   
}
.rbc-day-slot .rbc-event, .rbc-day-slot .rbc-background-event:focus {

  outline:none !important;
}
.rbc-time-content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 1;
  -ms-flex: 1 0 0%;
  flex: 1 0 0%;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  width: 100%;
  border-top: 2px solid var(--rbc-border) !important;
  position: relative;
   padding-top: 10px !important; 
  /* overflow-y: auto; */
}

/* WeekView */
.WeekView > .rbc-time-view >  .rbc-time-content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 1;
  -ms-flex: 1 0 0%;
  flex: 1 0 0%;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  width: 100%;
  border-top: 2px solid var(--rbc-border) !important;
  position: relative;
  padding-top: 20px !important;
  /* overflow-y: auto; */
}