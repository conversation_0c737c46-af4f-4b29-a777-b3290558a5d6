{% extends "admin/base_site.html" %}
{% load i18n admin_urls static %}

{% block extrastyle %}
  {{ block.super }}
  <style>
    .accordion {
      background-color: #f1f1f1;
      color: #444;
      cursor: pointer;
      padding: 18px;
      width: 100%;
      text-align: left;
      border: none;
      outline: none;
      transition: 0.4s;
      margin-bottom: 2px;
    }

    .active, .accordion:hover {
      background-color: #ddd;
    }

    .panel {
      padding: 0 18px;
      background-color: white;
      max-height: 0;
      overflow: hidden;
      transition: max-height 0.2s ease-out;
      margin-bottom: 10px;
    }

    .doctor-card {
      border: 1px solid #ddd;
      border-radius: 4px;
      margin-bottom: 15px;
      padding: 15px;
      background-color: #f9f9f9;
    }

    .doctor-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .assistant-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 10px;
    }

    .assistant-table th, .assistant-table td {
      border: 1px solid #ddd;
      padding: 8px;
      text-align: left;
    }

    .assistant-table th {
      background-color: #f2f2f2;
    }

    .assistant-table tr:nth-child(even) {
      background-color: #f9f9f9;
    }

    .assistant-table tr:hover {
      background-color: #f1f1f1;
    }

    .no-assistants {
      color: #888;
      font-style: italic;
      margin: 10px 0;
    }

    .back-link {
      margin-bottom: 20px;
      display: inline-block;
    }
  </style>
{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
<a href="{% url 'admin:index' %}">{% trans 'Home' %}</a>
&rsaquo; <a href="{% url 'admin:app_list' app_label='users' %}">Users</a>
&rsaquo; <a href="{% url 'admin:users_specialty_changelist' %}">Specialties</a>
&rsaquo; {{ specialty.name }} - Doctors
</div>
{% endblock %}

{% block content %}
<div id="content-main">
  <h1>Doctors in {{ specialty.name }} Specialty</h1>

  <a href="{% url 'admin:users_specialty_changelist' %}" class="back-link">← Back to Specialties</a>

  {% if doctors %}
    {% for doctor in doctors %}
      <div class="doctor-card">
        <button class="accordion">
          <div class="doctor-header">
            <div>
              <strong>Dr. {{ doctor.first_name }} {{ doctor.last_name }}</strong>
              {% if doctor.is_primary_specialty %}
                <span style="color: green; margin-left: 10px;">(Primary Specialty)</span>
              {% endif %}
            </div>
            <div>
              <span style="margin-right: 15px;">{{ doctor.email }}</span>
              {% if doctor.phone_number %}
                <span>{{ doctor.phone_number }}</span>
              {% endif %}
            </div>
          </div>
        </button>
        <div class="panel">
          <h3>Doctor Details</h3>
          <p><strong>Email:</strong> {{ doctor.email }}</p>
          {% if doctor.phone_number %}
            <p><strong>Phone:</strong> {{ doctor.phone_number }}</p>
          {% endif %}
          {% if doctor.address %}
            <p><strong>Address:</strong> {{ doctor.address }}</p>
          {% endif %}
          {% if doctor.license_number %}
            <p><strong>License:</strong> {{ doctor.license_number }}</p>
          {% endif %}

          <h3>Assistants</h3>
          {% if doctor.assistants %}
            <table class="assistant-table">
              <thead>
                <tr>
                  <th>Image</th>
                  <th>Name</th>
                  <th>Email</th>
                  <th>Phone</th>
                  <th>Status</th>
                  <th>Type</th>
                  <th>Created</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {% for assistant in doctor.assistants %}
                  <tr>
                    <td>
                      {% if assistant.profile_image %}
                        <img src="{{ assistant.profile_image.url }}" alt="Profile" style="width: 50px; height: 50px; border-radius: 50%; object-fit: cover;">
                      {% else %}
                        <div style="width: 50px; height: 50px; border-radius: 50%; background-color: #ccc; display: flex; align-items: center; justify-content: center;">
                          <span>{{ assistant.first_name|first }}{{ assistant.last_name|first }}</span>
                        </div>
                      {% endif %}
                    </td>
                    <td>{{ assistant.first_name }} {{ assistant.last_name }}</td>
                    <td>{{ assistant.email }}</td>
                    <td>{{ assistant.phone_number|default:"N/A" }}</td>
                    <td>{{ assistant.status|default:"Active"|title }}</td>
                    <td>Assistant</td>
                    <td>{{ assistant.date_joined|date:"Y-m-d H:i" }}</td>
                    <td>
                      <a href="{% url 'admin:users_user_change' assistant.id %}" class="button" style="padding: 5px 10px; font-size: 12px;">Edit</a>
                    </td>
                  </tr>
                {% endfor %}
              </tbody>
            </table>
          {% else %}
            <p class="no-assistants">No assistants assigned to this doctor.</p>
          {% endif %}

          <h3>Staff Members</h3>
          {% if doctor.staff %}
            <table class="assistant-table">
              <thead>
                <tr>
                  <th>Image</th>
                  <th>Name</th>
                  <th>Email</th>
                  <th>Phone</th>
                  <th>Status</th>
                  <th>Type</th>
                  <th>Created</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {% for staff_member in doctor.staff %}
                  <tr>
                    <td>
                      {% if staff_member.profile_image %}
                        <img src="{{ staff_member.profile_image.url }}" alt="Profile" style="width: 50px; height: 50px; border-radius: 50%; object-fit: cover;">
                      {% else %}
                        <div style="width: 50px; height: 50px; border-radius: 50%; background-color: #ccc; display: flex; align-items: center; justify-content: center;">
                          <span>{{ staff_member.first_name|first }}{{ staff_member.last_name|first }}</span>
                        </div>
                      {% endif %}
                    </td>
                    <td>{{ staff_member.first_name }} {{ staff_member.last_name }}</td>
                    <td>{{ staff_member.email }}</td>
                    <td>{{ staff_member.phone_number|default:"N/A" }}</td>
                    <td>{{ staff_member.status|default:"Active"|title }}</td>
                    <td>Staff</td>
                    <td>{{ staff_member.date_joined|date:"Y-m-d H:i" }}</td>
                    <td>
                      <a href="{% url 'admin:users_user_change' staff_member.id %}" class="button" style="padding: 5px 10px; font-size: 12px;">Edit</a>
                    </td>
                  </tr>
                {% endfor %}
              </tbody>
            </table>
          {% else %}
            <p class="no-assistants">No staff members assigned to this doctor.</p>
          {% endif %}
        </div>
      </div>
    {% endfor %}
  {% else %}
    <p>No doctors found in this specialty.</p>
  {% endif %}
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    var acc = document.getElementsByClassName("accordion");
    var i;

    for (i = 0; i < acc.length; i++) {
      acc[i].addEventListener("click", function() {
        this.classList.toggle("active");
        var panel = this.nextElementSibling;
        if (panel.style.maxHeight) {
          panel.style.maxHeight = null;
        } else {
          panel.style.maxHeight = panel.scrollHeight + "px";
        }
      });
    }
  });
</script>
{% endblock %}
