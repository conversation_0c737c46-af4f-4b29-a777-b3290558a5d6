/*!***********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/style/layout.module.css ***!
  \***********************************************************************************************************************************************************************************************************************************************************************/


/* navbar styles */
.layout_navbar__FpJTN {
    width: 62px;
    display: flex;
    flex-direction: column;
    border-right: 1px solid  var(--border-color);
    background-color: var(--mantine-color-body); 
    /* color: light-dark(var(--mantine-color-gray-7), var(--mantine-color-dark-0)); */
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 100;
  }
    .layout_navbarMain__rcIWt {
      flex: 1;
      /* margin-top: 50px; */
      
    }
    .layout_link__Ml3mY {
      width: 40px;
      height: 40px;
      margin-left: 2.5px;
      margin-bottom: 10px;
      border-radius: var(--mantine-radius-md);
      display: flex;
      align-items: center;
      justify-content: center;
      color: light-dark(var(--mantine-color-gray-7), var(--mantine-color-dark-0));
    
      &:hover {
        background-color:var(--bg-nav-hover);
        height: 40px;
        width: 40px;
        margin-left: 2.5px;
        color: #3799ce;
      }
      &[data-active] {
        &,
        &:hover {
          background-color: var(--blue-color);
          color: var(--text-daisy-white); 
          height: 40px;
           width: 40px;
          margin-left: 2.5px;
          /* background-color: var(--mantine-color-blue-light);
          color: var(--mantine-color-blue-light-color); */
        }
      }
    }
    :-moz-ui-invalid.layout_header__MQO8Z {
    /* height: 56px; */
    margin-bottom: 120px;
    background-color: var(--mantine-color-body);
    border-bottom: 1px solid light-dark(var(--mantine-color-gray-3), var(--mantine-color-dark-4));
    padding-left: var(--mantine-spacing-md);
    padding-right: var(--mantine-spacing-md);
  }
  .layout_inner__EguYD {
    /* height: 56px; */
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .layout_link__Ml3mY {
    display: block;
    line-height: 1;
    padding: 8px 10px;
    border-radius: var(--mantine-radius-sm);
    text-decoration: none;
    color: light-dark(var(--mantine-color-gray-7), var(--mantine-color-dark-0));
    font-size: var(--mantine-font-size-sm);
    font-weight: 500;
    @mixin hover {
      background-color: light-dark(var(--mantine-color-gray-0), var(--mantine-color-dark-6));
    }
  }
  .layout_navbarDesktop__yxxnm {
    display: none;
  }
  
  @media (min-width: 768px) {
    .layout_navbarDesktop__yxxnm {
      display: flex;
    }
  }
  .layout_navbarHidden__1QPLw {
    display: none;
  }
    /* header styles */
  .layout_header__MQO8Z {
   height: 56px; 
    background-color: var(--mantine-color-body);
    /* border-bottom: 1px solid light-dark(var(--mantine-color-gray-3), var(--mantine-color-dark-4)); */
    padding-left: var(--mantine-spacing-md);
    padding-right: var(--mantine-spacing-md);
    width: 100%;
   z-index: 999;
    
  }
  
  @media (min-width: 768px) {
    .layout_header__MQO8Z {
      width: 600px;
      margin-left: 250px;
      margin-right: 20px; 
      /* height: 50px; */
      padding: 0px;
      display: none;
      
    }
    .layout_inner__EguYD {
      height: 40px;
       display: flex;
     /* justify-content: space-between; */
     align-items: center;
     /* background-color: blueviolet; */
   }
  }
  
  .layout_inner__EguYD {
      height: 56px;  
      display: flex;
    justify-content: space-between;
    align-items: center;
    
  }
    .layout_link_H__FmgUh {
      display: block;
      line-height: 1;
      padding: 8px 12px;
      border-radius: var(--mantine-radius-sm);
      text-decoration: none;
      color: light-dark(var(--mantine-color-gray-7), var(--mantine-color-dark-0));
      font-size: var(--mantine-font-size-sm);
      font-weight: 500;
      @mixin hover {
        background-color: light-dark(var(--mantine-color-gray-0), var(--mantine-color-dark-6));
      }
    }
    /* Footer styles */
  .layout_footer__fZvpa {
    height: 60px;
    background-color: var(--mantine-color-body);
    border-top: 1px solid var(--border-color);
    padding: var(--mantine-spacing-md);
    margin-left: 56px;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 56px;
    padding-left: var(--mantine-spacing-md);
    padding-right: var(--mantine-spacing-md);
    z-index: 101;
  }
  
  .layout_footerInner__OS3dl {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
  }
  
  .layout_footerLinks__kt4WL {
    display: flex;
    gap: var(--mantine-spacing-md);
  }
  
  .layout_footerLink__O_Mm9 {
    color: light-dark(var(--mantine-color-gray-6), var(--mantine-color-dark-1));
    font-size: var(--mantine-font-size-sm);
    text-decoration: none;
    &:hover {
      text-decoration: underline;
      color: var(--mantine-color-blue-6);
    }
  }
  
  .layout_footerCopyright__4YO2Q {
    font-size: var(--mantine-font-size-xs);
    color: light-dark(var(--mantine-color-gray-5), var(--mantine-color-dark-2));
  }
  .layout_headerWithSidebar__CsBX0 {
    margin-left: 240px;
  }
  
  .layout_headerWithoutSidebar__thB_W {
    margin-left: 56px;
    @media (max-width: 768px) {
      margin-left: 0px;
    }
  }
  
  .layout_mobileMenu__Bnol3 {
    display: none;
    padding: var(--mantine-spacing-md);
    background-color: var(--mantine-color-body);
    border-bottom: 1px solid light-dark(var(--mantine-color-gray-3), var(--mantine-color-dark-4));
    position: absolute;
    width: 100%;
    z-index: 9999;
  }
  
  @media (max-width: 48em) {
    .layout_mobileMenu__Bnol3 {
      display: block;
    }
  }
  
  .layout_mobileLinks__1ZXuJ {
    display: flex;
    flex-direction: column;
    gap: var(--mantine-spacing-xs);
  }
  /* SideNavbar styles */
  .layout_navbarSide__8cQvQ {
    background-color: light-dark(var(--mantine-color-white), var(--mantine-color-dark-6));
    width: 240px;
    padding: var(--mantine-spacing-md);
    padding-bottom: 0;
    display: flex;
    flex-direction: column;
    border-right: 1px solid light-dark(var(--mantine-color-gray-3), var(--mantine-color-dark-4));
    height: 100vh; /* Couvre toute la hauteur de l'écran */
    position: fixed;
    top: 0;
    z-index: 99;
  }
  
  .layout_headerSide__9qPyi {
    padding: var(--mantine-spacing-md);
    padding-top: 0;
    margin-left: calc(var(--mantine-spacing-md) * -1);
    margin-right: calc(var(--mantine-spacing-md) * -1);
    color: light-dark(var(--mantine-color-black), var(--mantine-color-white));
    border-bottom: 1px solid light-dark(var(--mantine-color-gray-3), var(--mantine-color-dark-4));
  }
  
  .layout_linksSide__4KG45 {
    flex: 1;
    margin-left: calc(var(--mantine-spacing-md) * -1);
    margin-right: calc(var(--mantine-spacing-md) * -1);
  }
  
  .layout_linksInnerSide__VwTzL {
    padding-top: var(--mantine-spacing-xl);
    padding-bottom: var(--mantine-spacing-xl);
  }
  .layout_footerSide__5pKax {
    margin-left: calc(var(--mantine-spacing-md) * -1);
    margin-right: calc(var(--mantine-spacing-md) * -1);
    border-top: 1px solid light-dark(var(--mantine-color-gray-3), var(--mantine-color-dark-4));
  }
  .layout_pageContainer___9vUG {
    display: flex;
    min-height: calc(100vh - 116px); /* Hauteur totale moins header et footer */
    position: relative;
  }
  .layout_sidebarContainer__Xmdkl {
    position: fixed;
    top: 0;
    left: 56px; /* Largeur de la navbar */
    height: 100vh; /* Couvre toute la hauteur de l'écran */
    width: 240px;
    background-color: white;
    border-right: 1px solid light-dark(var(--mantine-color-gray-3), var(--mantine-color-dark-4));
    z-index: 95;
    /* overflow-y: auto; */
    transition: transform 0.3s ease;
    padding-bottom: 60px; /* Ajoute un espace en bas pour éviter que le contenu ne soit caché par le pied de page */
  }
  .layout_mainContent__8_ftp {
    flex: 1;
    margin-left: 56px; /* Largeur de la navbar */
    padding: 0px;
    transition: margin-left 0.3s ease;
    z-index: 10;
  }
  /* Pour les appareils mobiles */
  @media (min-width: 768px) {
    .layout_mainContent__8_ftp {
     /*  margin-top: -50px; Pas de marge sur mobile */
      padding-left: 2px;
      z-index: 99;
    }
  }
  @media (max-width: 768px) {
    .layout_mainContent__8_ftp {
      margin-left: 2px; /* Pas de marge sur mobile */
      margin-right: 2px;
    }
    
    .layout_mainContentWithSidebar___yv_8 {
      margin-left: 0; /* Ajustement pour mobile quand la sidebar est visible */
    }
  }
  .layout_mainContentWithSidebar___yv_8 {
    margin-left: 240px; /* Ajustez cette valeur à la largeur de votre barre latérale */
    
  }
  /* Ajuster la marge du contenu principal quand la sidebar est visible */
  .layout_sidebarVisible__Kqg8W .layout_mainContent__8_ftp {
    margin-left: 246px; /* Largeur de la navbar + largeur de la sidebar */
  }
  .layout_sidebarVisible__Kqg8W {
    transform: translateX(0);
    opacity: 1;
    pointer-events: all;
    left: 0;
  }
  
  .layout_sidebarHidden__s0UUG {
    transform: translateX(-100%);
    opacity: 0;
    pointer-events: none;
  }
  .layout_tabsList__FDtI3 {
   
    width: 100%;
  }
  @media (max-width: 768px) {
    .layout_tabsButton__P3fHN {
      width: 100% !important;
      
    }
  }
  .layout_tabsButton__P3fHN{
    width: 75%;
  }

  @media (max-width: 768px) {
    .layout_navBarButton__WcIM2 {
     display: none;
     
    }
  }
  .layout_navBarButton__WcIM2{
    width: 25%;
  }
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/layout/sideNavbar/NavbarLinksGroup/NavbarLinksGroup.module.css ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************/
.NavbarLinksGroup_control__shVpo {
  font-weight: 500;
  display: block;
  width: 100%;
  padding: var(--mantine-spacing-xs) var(--mantine-spacing-md);
  color: var(--mantine-color-text);
  font-size: var(--mantine-font-size-sm);

  @mixin hover {
    background-color: light-dark(var(--mantine-color-gray-0), var(--mantine-color-dark-7));
    color: light-dark(var(--mantine-color-black), var(--mantine-color-dark-0));
  }
}

.NavbarLinksGroup_link__Cvk00 {
  font-weight: 500;
  display: block;
  text-decoration: none;
  padding: var(--mantine-spacing-xs) var(--mantine-spacing-md);
  padding-left: var(--mantine-spacing-md);
  margin-left: var(--mantine-spacing-xl);
  font-size: var(--mantine-font-size-sm);
  color: light-dark(var(--mantine-color-gray-7), var(--mantine-color-dark-0));
  border-left: 1px solid light-dark(var(--mantine-color-gray-3), var(--mantine-color-dark-4));

  @mixin hover {
    background-color: light-dark(var(--mantine-color-gray-0), var(--mantine-color-dark-7));
    color: light-dark(var(--mantine-color-black), var(--mantine-color-dark-0));
  }
}

.NavbarLinksGroup_chevron__9dRJ_ {
  transition: transform 200ms ease;
}

/*!*************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/layout/sideNavbar/LeftMenu.module.css ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************/
.LeftMenu_navbar__b3w7K {
  background-color: light-dark(
    var(--mantine-color-white),
    var(--mantine-color-dark-6)
  );
  height: rem(670px);
  width: rem(240px);
  padding: var(--mantine-spacing-md);
  padding-bottom: 0;
  display: flex;
  flex-direction: column;
  padding-left: 0;
}

.LeftMenu_header__q2x8q {
  padding: var(--mantine-spacing-md);
  padding-top: 0;
  margin-left: calc(var(--mantine-spacing-md) * -1);
  margin-right: calc(var(--mantine-spacing-md) * -1);
  color: light-dark(var(--mantine-color-black), var(--mantine-color-white));

}

.LeftMenu_links__9_DtN {
  flex: 1;
  margin-left: calc(var(--mantine-spacing-md) * -1);
  margin-right: calc(var(--mantine-spacing-md) * -1);
}



.LeftMenu_footer__Id0F6 {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    margin-bottom: 44px;



}
.LeftMenu_hide__vXK37 {
  display: none;
}

