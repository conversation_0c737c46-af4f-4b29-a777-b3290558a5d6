"""
Commande pour déboguer l'enregistrement des modèles dans l'administration
"""
from django.core.management.base import BaseCommand
from django.contrib import admin
from django.apps import apps
from dentistry.models import *
import dentistry.models as models_module


class Command(BaseCommand):
    help = 'Debug admin model registration'

    def handle(self, *args, **options):
        """
        Débogue l'enregistrement des modèles dans l'administration
        """
        self.stdout.write(self.style.SUCCESS('Debugging admin model registration...'))

        # Test d'import des modèles
        self.stdout.write('\n🔍 Testing model imports...')

        models_to_test = [
            'DentistryPatient', 'DentistryDoctor', 'DentistryRole', 'DentistryStaffProfile',
            'DentistryAppointment', 'DentalConsultation', 'Tooth',
            'DentalTreatment', 'DentistryMedicalRecord', 'DentalImaging',
            'DentistryInvoice', 'DentistryBillingCode',
            'DentistryComment', 'DentistryReview',
            'DentalSvgData', 'DentalModification',
            'DentistryConfiguration', 'DentistryNotification',
            'DentistryWebsiteSettings', 'DentistryPage'
        ]

        successful_imports = []
        failed_imports = []

        # Test des modèles importés
        for model_name in models_to_test:
            try:
                model = getattr(models_module, model_name, None)
                if model:
                    successful_imports.append(model_name)
                    self.stdout.write(f'✅ {model_name} - Import successful')
                else:
                    failed_imports.append(model_name)
                    self.stdout.write(f'❌ {model_name} - Not found in models module')
            except Exception as e:
                failed_imports.append(model_name)
                self.stdout.write(f'❌ {model_name} - Import error: {e}')

        self.stdout.write(f'\n📊 Import Results:')
        self.stdout.write(f'✅ Successful: {len(successful_imports)}')
        self.stdout.write(f'❌ Failed: {len(failed_imports)}')

        # Test d'enregistrement manuel
        self.stdout.write('\n🔧 Testing manual registration...')

        try:
            # Essayer d'enregistrer quelques modèles manuellement
            test_models = []

            # Obtenir tous les modèles disponibles
            dentistry_models = list(apps.get_app_config('dentistry').get_models())

            for model in dentistry_models[:5]:  # Tester les 5 premiers
                model_name = model.__name__
                try:
                    if not admin.site.is_registered(model):
                        admin.site.register(model)
                        test_models.append(model_name)
                        self.stdout.write(f'✅ Registered: {model_name}')
                    else:
                        self.stdout.write(f'⚠️  Already registered: {model_name}')
                except Exception as e:
                    self.stdout.write(f'❌ Failed to register {model_name}: {e}')

            self.stdout.write(f'\n🎯 Successfully registered {len(test_models)} models manually')

        except Exception as e:
            self.stdout.write(f'❌ Manual registration failed: {e}')

        # Vérifier l'état actuel de l'admin
        self.stdout.write('\n📋 Current admin registry:')
        registered_models = []
        for model in admin.site._registry:
            registered_models.append(model.__name__)

        self.stdout.write(f'Total registered models: {len(registered_models)}')
        for model_name in sorted(registered_models):
            self.stdout.write(f'   - {model_name}')

        # Suggestions de correction
        self.stdout.write('\n💡 Suggestions:')
        if failed_imports:
            self.stdout.write('1. Check model imports in admin.py')
            self.stdout.write('2. Verify model names match exactly')
            self.stdout.write('3. Check for circular import issues')

        if len(registered_models) < 20:
            self.stdout.write('4. Consider restarting Django server')
            self.stdout.write('5. Check for admin.py syntax errors')

        self.stdout.write(self.style.SUCCESS('\n✨ Debug completed!'))
