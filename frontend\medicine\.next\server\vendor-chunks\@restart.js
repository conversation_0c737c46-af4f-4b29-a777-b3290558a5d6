"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@restart";
exports.ids = ["vendor-chunks/@restart"];
exports.modules = {

/***/ "(ssr)/./node_modules/@restart/hooks/esm/useCallbackRef.js":
/*!***********************************************************!*\
  !*** ./node_modules/@restart/hooks/esm/useCallbackRef.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useCallbackRef)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n\n/**\n * A convenience hook around `useState` designed to be paired with\n * the component [callback ref](https://reactjs.org/docs/refs-and-the-dom.html#callback-refs) api.\n * Callback refs are useful over `useRef()` when you need to respond to the ref being set\n * instead of lazily accessing it in an effect.\n *\n * ```ts\n * const [element, attachRef] = useCallbackRef<HTMLDivElement>()\n *\n * useEffect(() => {\n *   if (!element) return\n *\n *   const calendar = new FullCalendar.Calendar(element)\n *\n *   return () => {\n *     calendar.destroy()\n *   }\n * }, [element])\n *\n * return <div ref={attachRef} />\n * ```\n *\n * @category refs\n */\nfunction useCallbackRef() {\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJlc3RhcnQvaG9va3MvZXNtL3VzZUNhbGxiYWNrUmVmLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFpQzs7QUFFakM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBLG9CQUFvQixXQUFXO0FBQy9CO0FBQ0E7QUFDQTtBQUNBO0FBQ2U7QUFDZixTQUFTLCtDQUFRO0FBQ2pCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNvcnJ5XFxEZXNrdG9wXFxzdnBcXGZyb250ZW5kXFxtZWRpY2luZVxcbm9kZV9tb2R1bGVzXFxAcmVzdGFydFxcaG9va3NcXGVzbVxcdXNlQ2FsbGJhY2tSZWYuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XG5cbi8qKlxuICogQSBjb252ZW5pZW5jZSBob29rIGFyb3VuZCBgdXNlU3RhdGVgIGRlc2lnbmVkIHRvIGJlIHBhaXJlZCB3aXRoXG4gKiB0aGUgY29tcG9uZW50IFtjYWxsYmFjayByZWZdKGh0dHBzOi8vcmVhY3Rqcy5vcmcvZG9jcy9yZWZzLWFuZC10aGUtZG9tLmh0bWwjY2FsbGJhY2stcmVmcykgYXBpLlxuICogQ2FsbGJhY2sgcmVmcyBhcmUgdXNlZnVsIG92ZXIgYHVzZVJlZigpYCB3aGVuIHlvdSBuZWVkIHRvIHJlc3BvbmQgdG8gdGhlIHJlZiBiZWluZyBzZXRcbiAqIGluc3RlYWQgb2YgbGF6aWx5IGFjY2Vzc2luZyBpdCBpbiBhbiBlZmZlY3QuXG4gKlxuICogYGBgdHNcbiAqIGNvbnN0IFtlbGVtZW50LCBhdHRhY2hSZWZdID0gdXNlQ2FsbGJhY2tSZWY8SFRNTERpdkVsZW1lbnQ+KClcbiAqXG4gKiB1c2VFZmZlY3QoKCkgPT4ge1xuICogICBpZiAoIWVsZW1lbnQpIHJldHVyblxuICpcbiAqICAgY29uc3QgY2FsZW5kYXIgPSBuZXcgRnVsbENhbGVuZGFyLkNhbGVuZGFyKGVsZW1lbnQpXG4gKlxuICogICByZXR1cm4gKCkgPT4ge1xuICogICAgIGNhbGVuZGFyLmRlc3Ryb3koKVxuICogICB9XG4gKiB9LCBbZWxlbWVudF0pXG4gKlxuICogcmV0dXJuIDxkaXYgcmVmPXthdHRhY2hSZWZ9IC8+XG4gKiBgYGBcbiAqXG4gKiBAY2F0ZWdvcnkgcmVmc1xuICovXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiB1c2VDYWxsYmFja1JlZigpIHtcbiAgcmV0dXJuIHVzZVN0YXRlKG51bGwpO1xufSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@restart/hooks/esm/useCallbackRef.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@restart/hooks/esm/useCommittedRef.js":
/*!************************************************************!*\
  !*** ./node_modules/@restart/hooks/esm/useCommittedRef.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n\n/**\n * Creates a `Ref` whose value is updated in an effect, ensuring the most recent\n * value is the one rendered with. Generally only required for Concurrent mode usage\n * where previous work in `render()` may be discarded before being used.\n *\n * This is safe to access in an event handler.\n *\n * @param value The `Ref` value\n */\nfunction useCommittedRef(value) {\n  const ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(value);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    ref.current = value;\n  }, [value]);\n  return ref;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useCommittedRef);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJlc3RhcnQvaG9va3MvZXNtL3VzZUNvbW1pdHRlZFJlZi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBMEM7O0FBRTFDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYyw2Q0FBTTtBQUNwQixFQUFFLGdEQUFTO0FBQ1g7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBLGlFQUFlLGVBQWUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc29ycnlcXERlc2t0b3BcXHN2cFxcZnJvbnRlbmRcXG1lZGljaW5lXFxub2RlX21vZHVsZXNcXEByZXN0YXJ0XFxob29rc1xcZXNtXFx1c2VDb21taXR0ZWRSZWYuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlRWZmZWN0LCB1c2VSZWYgfSBmcm9tICdyZWFjdCc7XG5cbi8qKlxuICogQ3JlYXRlcyBhIGBSZWZgIHdob3NlIHZhbHVlIGlzIHVwZGF0ZWQgaW4gYW4gZWZmZWN0LCBlbnN1cmluZyB0aGUgbW9zdCByZWNlbnRcbiAqIHZhbHVlIGlzIHRoZSBvbmUgcmVuZGVyZWQgd2l0aC4gR2VuZXJhbGx5IG9ubHkgcmVxdWlyZWQgZm9yIENvbmN1cnJlbnQgbW9kZSB1c2FnZVxuICogd2hlcmUgcHJldmlvdXMgd29yayBpbiBgcmVuZGVyKClgIG1heSBiZSBkaXNjYXJkZWQgYmVmb3JlIGJlaW5nIHVzZWQuXG4gKlxuICogVGhpcyBpcyBzYWZlIHRvIGFjY2VzcyBpbiBhbiBldmVudCBoYW5kbGVyLlxuICpcbiAqIEBwYXJhbSB2YWx1ZSBUaGUgYFJlZmAgdmFsdWVcbiAqL1xuZnVuY3Rpb24gdXNlQ29tbWl0dGVkUmVmKHZhbHVlKSB7XG4gIGNvbnN0IHJlZiA9IHVzZVJlZih2YWx1ZSk7XG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgcmVmLmN1cnJlbnQgPSB2YWx1ZTtcbiAgfSwgW3ZhbHVlXSk7XG4gIHJldHVybiByZWY7XG59XG5leHBvcnQgZGVmYXVsdCB1c2VDb21taXR0ZWRSZWY7Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@restart/hooks/esm/useCommittedRef.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@restart/hooks/esm/useEventCallback.js":
/*!*************************************************************!*\
  !*** ./node_modules/@restart/hooks/esm/useEventCallback.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useEventCallback)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _useCommittedRef__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useCommittedRef */ \"(ssr)/./node_modules/@restart/hooks/esm/useCommittedRef.js\");\n\n\nfunction useEventCallback(fn) {\n  const ref = (0,_useCommittedRef__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(fn);\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (...args) {\n    return ref.current && ref.current(...args);\n  }, [ref]);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJlc3RhcnQvaG9va3MvZXNtL3VzZUV2ZW50Q2FsbGJhY2suanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFvQztBQUNZO0FBQ2pDO0FBQ2YsY0FBYyw0REFBZTtBQUM3QixTQUFTLGtEQUFXO0FBQ3BCO0FBQ0EsR0FBRztBQUNIIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNvcnJ5XFxEZXNrdG9wXFxzdnBcXGZyb250ZW5kXFxtZWRpY2luZVxcbm9kZV9tb2R1bGVzXFxAcmVzdGFydFxcaG9va3NcXGVzbVxcdXNlRXZlbnRDYWxsYmFjay5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VDYWxsYmFjayB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB1c2VDb21taXR0ZWRSZWYgZnJvbSAnLi91c2VDb21taXR0ZWRSZWYnO1xuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gdXNlRXZlbnRDYWxsYmFjayhmbikge1xuICBjb25zdCByZWYgPSB1c2VDb21taXR0ZWRSZWYoZm4pO1xuICByZXR1cm4gdXNlQ2FsbGJhY2soZnVuY3Rpb24gKC4uLmFyZ3MpIHtcbiAgICByZXR1cm4gcmVmLmN1cnJlbnQgJiYgcmVmLmN1cnJlbnQoLi4uYXJncyk7XG4gIH0sIFtyZWZdKTtcbn0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@restart/hooks/esm/useEventCallback.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@restart/hooks/esm/useMergedRefs.js":
/*!**********************************************************!*\
  !*** ./node_modules/@restart/hooks/esm/useMergedRefs.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   mergeRefs: () => (/* binding */ mergeRefs)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nconst toFnRef = ref => !ref || typeof ref === 'function' ? ref : value => {\n  ref.current = value;\n};\nfunction mergeRefs(refA, refB) {\n  const a = toFnRef(refA);\n  const b = toFnRef(refB);\n  return value => {\n    if (a) a(value);\n    if (b) b(value);\n  };\n}\n\n/**\n * Create and returns a single callback ref composed from two other Refs.\n *\n * ```tsx\n * const Button = React.forwardRef((props, ref) => {\n *   const [element, attachRef] = useCallbackRef<HTMLButtonElement>();\n *   const mergedRef = useMergedRefs(ref, attachRef);\n *\n *   return <button ref={mergedRef} {...props}/>\n * })\n * ```\n *\n * @param refA A Callback or mutable Ref\n * @param refB A Callback or mutable Ref\n * @category refs\n */\nfunction useMergedRefs(refA, refB) {\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => mergeRefs(refA, refB), [refA, refB]);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useMergedRefs);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJlc3RhcnQvaG9va3MvZXNtL3VzZU1lcmdlZFJlZnMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFnQztBQUNoQztBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUIsWUFBWSxTQUFTO0FBQzlDLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVMsOENBQU87QUFDaEI7QUFDQSxpRUFBZSxhQUFhIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNvcnJ5XFxEZXNrdG9wXFxzdnBcXGZyb250ZW5kXFxtZWRpY2luZVxcbm9kZV9tb2R1bGVzXFxAcmVzdGFydFxcaG9va3NcXGVzbVxcdXNlTWVyZ2VkUmVmcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VNZW1vIH0gZnJvbSAncmVhY3QnO1xuY29uc3QgdG9GblJlZiA9IHJlZiA9PiAhcmVmIHx8IHR5cGVvZiByZWYgPT09ICdmdW5jdGlvbicgPyByZWYgOiB2YWx1ZSA9PiB7XG4gIHJlZi5jdXJyZW50ID0gdmFsdWU7XG59O1xuZXhwb3J0IGZ1bmN0aW9uIG1lcmdlUmVmcyhyZWZBLCByZWZCKSB7XG4gIGNvbnN0IGEgPSB0b0ZuUmVmKHJlZkEpO1xuICBjb25zdCBiID0gdG9GblJlZihyZWZCKTtcbiAgcmV0dXJuIHZhbHVlID0+IHtcbiAgICBpZiAoYSkgYSh2YWx1ZSk7XG4gICAgaWYgKGIpIGIodmFsdWUpO1xuICB9O1xufVxuXG4vKipcbiAqIENyZWF0ZSBhbmQgcmV0dXJucyBhIHNpbmdsZSBjYWxsYmFjayByZWYgY29tcG9zZWQgZnJvbSB0d28gb3RoZXIgUmVmcy5cbiAqXG4gKiBgYGB0c3hcbiAqIGNvbnN0IEJ1dHRvbiA9IFJlYWN0LmZvcndhcmRSZWYoKHByb3BzLCByZWYpID0+IHtcbiAqICAgY29uc3QgW2VsZW1lbnQsIGF0dGFjaFJlZl0gPSB1c2VDYWxsYmFja1JlZjxIVE1MQnV0dG9uRWxlbWVudD4oKTtcbiAqICAgY29uc3QgbWVyZ2VkUmVmID0gdXNlTWVyZ2VkUmVmcyhyZWYsIGF0dGFjaFJlZik7XG4gKlxuICogICByZXR1cm4gPGJ1dHRvbiByZWY9e21lcmdlZFJlZn0gey4uLnByb3BzfS8+XG4gKiB9KVxuICogYGBgXG4gKlxuICogQHBhcmFtIHJlZkEgQSBDYWxsYmFjayBvciBtdXRhYmxlIFJlZlxuICogQHBhcmFtIHJlZkIgQSBDYWxsYmFjayBvciBtdXRhYmxlIFJlZlxuICogQGNhdGVnb3J5IHJlZnNcbiAqL1xuZnVuY3Rpb24gdXNlTWVyZ2VkUmVmcyhyZWZBLCByZWZCKSB7XG4gIHJldHVybiB1c2VNZW1vKCgpID0+IG1lcmdlUmVmcyhyZWZBLCByZWZCKSwgW3JlZkEsIHJlZkJdKTtcbn1cbmV4cG9ydCBkZWZhdWx0IHVzZU1lcmdlZFJlZnM7Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@restart/hooks/esm/useMergedRefs.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@restart/hooks/esm/useMounted.js":
/*!*******************************************************!*\
  !*** ./node_modules/@restart/hooks/esm/useMounted.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useMounted)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n\n/**\n * Track whether a component is current mounted. Generally less preferable than\n * properlly canceling effects so they don't run after a component is unmounted,\n * but helpful in cases where that isn't feasible, such as a `Promise` resolution.\n *\n * @returns a function that returns the current isMounted state of the component\n *\n * ```ts\n * const [data, setData] = useState(null)\n * const isMounted = useMounted()\n *\n * useEffect(() => {\n *   fetchdata().then((newData) => {\n *      if (isMounted()) {\n *        setData(newData);\n *      }\n *   })\n * })\n * ```\n */\nfunction useMounted() {\n  const mounted = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(true);\n  const isMounted = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(() => mounted.current);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    mounted.current = true;\n    return () => {\n      mounted.current = false;\n    };\n  }, []);\n  return isMounted.current;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJlc3RhcnQvaG9va3MvZXNtL3VzZU1vdW50ZWQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTBDOztBQUUxQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTixJQUFJO0FBQ0o7QUFDQTtBQUNlO0FBQ2Ysa0JBQWtCLDZDQUFNO0FBQ3hCLG9CQUFvQiw2Q0FBTTtBQUMxQixFQUFFLGdEQUFTO0FBQ1g7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzb3JyeVxcRGVza3RvcFxcc3ZwXFxmcm9udGVuZFxcbWVkaWNpbmVcXG5vZGVfbW9kdWxlc1xcQHJlc3RhcnRcXGhvb2tzXFxlc21cXHVzZU1vdW50ZWQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlUmVmLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5cbi8qKlxuICogVHJhY2sgd2hldGhlciBhIGNvbXBvbmVudCBpcyBjdXJyZW50IG1vdW50ZWQuIEdlbmVyYWxseSBsZXNzIHByZWZlcmFibGUgdGhhblxuICogcHJvcGVybGx5IGNhbmNlbGluZyBlZmZlY3RzIHNvIHRoZXkgZG9uJ3QgcnVuIGFmdGVyIGEgY29tcG9uZW50IGlzIHVubW91bnRlZCxcbiAqIGJ1dCBoZWxwZnVsIGluIGNhc2VzIHdoZXJlIHRoYXQgaXNuJ3QgZmVhc2libGUsIHN1Y2ggYXMgYSBgUHJvbWlzZWAgcmVzb2x1dGlvbi5cbiAqXG4gKiBAcmV0dXJucyBhIGZ1bmN0aW9uIHRoYXQgcmV0dXJucyB0aGUgY3VycmVudCBpc01vdW50ZWQgc3RhdGUgb2YgdGhlIGNvbXBvbmVudFxuICpcbiAqIGBgYHRzXG4gKiBjb25zdCBbZGF0YSwgc2V0RGF0YV0gPSB1c2VTdGF0ZShudWxsKVxuICogY29uc3QgaXNNb3VudGVkID0gdXNlTW91bnRlZCgpXG4gKlxuICogdXNlRWZmZWN0KCgpID0+IHtcbiAqICAgZmV0Y2hkYXRhKCkudGhlbigobmV3RGF0YSkgPT4ge1xuICogICAgICBpZiAoaXNNb3VudGVkKCkpIHtcbiAqICAgICAgICBzZXREYXRhKG5ld0RhdGEpO1xuICogICAgICB9XG4gKiAgIH0pXG4gKiB9KVxuICogYGBgXG4gKi9cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHVzZU1vdW50ZWQoKSB7XG4gIGNvbnN0IG1vdW50ZWQgPSB1c2VSZWYodHJ1ZSk7XG4gIGNvbnN0IGlzTW91bnRlZCA9IHVzZVJlZigoKSA9PiBtb3VudGVkLmN1cnJlbnQpO1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIG1vdW50ZWQuY3VycmVudCA9IHRydWU7XG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIG1vdW50ZWQuY3VycmVudCA9IGZhbHNlO1xuICAgIH07XG4gIH0sIFtdKTtcbiAgcmV0dXJuIGlzTW91bnRlZC5jdXJyZW50O1xufSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@restart/hooks/esm/useMounted.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@restart/hooks/esm/useSafeState.js":
/*!*********************************************************!*\
  !*** ./node_modules/@restart/hooks/esm/useSafeState.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _useMounted__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useMounted */ \"(ssr)/./node_modules/@restart/hooks/esm/useMounted.js\");\n\n\n\n/**\n * `useSafeState` takes the return value of a `useState` hook and wraps the\n * setter to prevent updates onces the component has unmounted. Can used\n * with `useMergeState` and `useStateAsync` as well\n *\n * @param state The return value of a useStateHook\n *\n * ```ts\n * const [show, setShow] = useSafeState(useState(true));\n * ```\n */\n\nfunction useSafeState(state) {\n  const isMounted = (0,_useMounted__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n  return [state[0], (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(nextState => {\n    if (!isMounted()) return;\n    return state[1](nextState);\n  }, [isMounted, state[1]])];\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useSafeState);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJlc3RhcnQvaG9va3MvZXNtL3VzZVNhZmVTdGF0ZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQW9DO0FBQ0U7O0FBRXRDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxvQkFBb0IsdURBQVU7QUFDOUIsb0JBQW9CLGtEQUFXO0FBQy9CO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQSxpRUFBZSxZQUFZIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNvcnJ5XFxEZXNrdG9wXFxzdnBcXGZyb250ZW5kXFxtZWRpY2luZVxcbm9kZV9tb2R1bGVzXFxAcmVzdGFydFxcaG9va3NcXGVzbVxcdXNlU2FmZVN0YXRlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZUNhbGxiYWNrIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHVzZU1vdW50ZWQgZnJvbSAnLi91c2VNb3VudGVkJztcblxuLyoqXG4gKiBgdXNlU2FmZVN0YXRlYCB0YWtlcyB0aGUgcmV0dXJuIHZhbHVlIG9mIGEgYHVzZVN0YXRlYCBob29rIGFuZCB3cmFwcyB0aGVcbiAqIHNldHRlciB0byBwcmV2ZW50IHVwZGF0ZXMgb25jZXMgdGhlIGNvbXBvbmVudCBoYXMgdW5tb3VudGVkLiBDYW4gdXNlZFxuICogd2l0aCBgdXNlTWVyZ2VTdGF0ZWAgYW5kIGB1c2VTdGF0ZUFzeW5jYCBhcyB3ZWxsXG4gKlxuICogQHBhcmFtIHN0YXRlIFRoZSByZXR1cm4gdmFsdWUgb2YgYSB1c2VTdGF0ZUhvb2tcbiAqXG4gKiBgYGB0c1xuICogY29uc3QgW3Nob3csIHNldFNob3ddID0gdXNlU2FmZVN0YXRlKHVzZVN0YXRlKHRydWUpKTtcbiAqIGBgYFxuICovXG5cbmZ1bmN0aW9uIHVzZVNhZmVTdGF0ZShzdGF0ZSkge1xuICBjb25zdCBpc01vdW50ZWQgPSB1c2VNb3VudGVkKCk7XG4gIHJldHVybiBbc3RhdGVbMF0sIHVzZUNhbGxiYWNrKG5leHRTdGF0ZSA9PiB7XG4gICAgaWYgKCFpc01vdW50ZWQoKSkgcmV0dXJuO1xuICAgIHJldHVybiBzdGF0ZVsxXShuZXh0U3RhdGUpO1xuICB9LCBbaXNNb3VudGVkLCBzdGF0ZVsxXV0pXTtcbn1cbmV4cG9ydCBkZWZhdWx0IHVzZVNhZmVTdGF0ZTsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@restart/hooks/esm/useSafeState.js\n");

/***/ })

};
;