# backend/dental_medicine/dentistry/models_specialized.py

from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator, MaxValueValidator
import uuid

User = get_user_model()


class DentalPatient(models.Model):
    """
    Modèle Patient pour le système dentaire
    """
    GENDER_CHOICES = [
        ('M', _('Mas<PERSON>lin')),
        ('F', _('Féminin')),
        ('O', _('Autre')),
    ]
    
    BLOOD_TYPE_CHOICES = [
        ('A+', 'A+'), ('A-', 'A-'),
        ('B+', 'B+'), ('B-', 'B-'),
        ('AB+', 'AB+'), ('AB-', 'AB-'),
        ('O+', 'O+'), ('O-', 'O-'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    
    # Informations personnelles
    first_name = models.CharField(max_length=100, verbose_name=_('Prénom'))
    last_name = models.CharField(max_length=100, verbose_name=_('Nom'))
    date_of_birth = models.DateField(verbose_name=_('Date de naissance'))
    gender = models.CharField(max_length=1, choices=GENDER_CHOICES, verbose_name=_('Genre'))
    
    # Contact
    phone = models.CharField(max_length=20, verbose_name=_('Téléphone'))
    email = models.EmailField(blank=True, verbose_name=_('Email'))
    address = models.TextField(verbose_name=_('Adresse'))
    
    # Informations médicales
    blood_type = models.CharField(max_length=3, choices=BLOOD_TYPE_CHOICES, blank=True)
    allergies = models.TextField(blank=True, verbose_name=_('Allergies'))
    medical_conditions = models.TextField(blank=True, verbose_name=_('Conditions médicales'))
    medications = models.TextField(blank=True, verbose_name=_('Médicaments'))
    
    # Informations dentaires
    dental_history = models.TextField(blank=True, verbose_name=_('Historique dentaire'))
    last_cleaning = models.DateField(null=True, blank=True, verbose_name=_('Dernier nettoyage'))
    dental_anxiety_level = models.PositiveIntegerField(
        default=1,
        validators=[MinValueValidator(1), MaxValueValidator(10)],
        help_text=_('Niveau d\'anxiété dentaire (1-10)')
    )
    
    # Métadonnées
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)
    
    class Meta:
        ordering = ['last_name', 'first_name']
        verbose_name = _('Dental Patient')
        verbose_name_plural = _('Dental Patients')
    
    def __str__(self):
        return f"{self.first_name} {self.last_name}"
    
    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"
    
    @property
    def age(self):
        from datetime import date
        today = date.today()
        return today.year - self.date_of_birth.year - ((today.month, today.day) < (self.date_of_birth.month, self.date_of_birth.day))


# ============================================================================
# MODÈLES SPÉCIALISÉS PAR SPÉCIALITÉ DENTAIRE
# ============================================================================

class BaseSpecializedTreatment(models.Model):
    """
    Modèle de base pour tous les traitements spécialisés
    """
    STATUS_CHOICES = [
        ('planned', _('Planifié')),
        ('in_progress', _('En cours')),
        ('completed', _('Terminé')),
        ('cancelled', _('Annulé')),
        ('on_hold', _('En attente')),
    ]
    
    URGENCY_CHOICES = [
        ('low', _('Faible')),
        ('medium', _('Moyenne')),
        ('high', _('Élevée')),
        ('emergency', _('Urgence')),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    
    # Relations de base
    patient = models.ForeignKey(DentalPatient, on_delete=models.CASCADE, related_name='%(class)s_treatments')
    doctor = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='%(class)s_treatments')
    diagnosis = models.ForeignKey('PatientDiagnosis', on_delete=models.SET_NULL, null=True, blank=True)
    
    # Informations de base
    tooth_number = models.PositiveIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(32)],
        verbose_name=_('Numéro de dent')
    )
    treatment_date = models.DateTimeField(verbose_name=_('Date de traitement'))
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='planned')
    urgency = models.CharField(max_length=20, choices=URGENCY_CHOICES, default='medium')
    
    # Détails cliniques
    clinical_notes = models.TextField(blank=True, verbose_name=_('Notes cliniques'))
    treatment_plan = models.TextField(verbose_name=_('Plan de traitement'))
    estimated_duration = models.PositiveIntegerField(help_text=_('Durée estimée en minutes'))
    actual_duration = models.PositiveIntegerField(null=True, blank=True, help_text=_('Durée réelle en minutes'))
    
    # Coût et facturation
    estimated_cost = models.DecimalField(max_digits=10, decimal_places=2, verbose_name=_('Coût estimé'))
    actual_cost = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name=_('Coût réel'))
    
    # Métadonnées
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        abstract = True
        ordering = ['-treatment_date']


class EstheticDentistryTreatment(BaseSpecializedTreatment):
    """
    Traitements de dentisterie esthétique
    """
    ESTHETIC_TYPE_CHOICES = [
        ('whitening', _('Blanchiment')),
        ('veneer', _('Facette')),
        ('bonding', _('Collage composite')),
        ('crown_aesthetic', _('Couronne esthétique')),
        ('smile_design', _('Design du sourire')),
        ('gum_contouring', _('Remodelage gingival')),
    ]
    
    SHADE_CHOICES = [
        ('A1', 'A1'), ('A2', 'A2'), ('A3', 'A3'), ('A4', 'A4'),
        ('B1', 'B1'), ('B2', 'B2'), ('B3', 'B3'), ('B4', 'B4'),
        ('C1', 'C1'), ('C2', 'C2'), ('C3', 'C3'), ('C4', 'C4'),
        ('D1', 'D1'), ('D2', 'D2'), ('D3', 'D3'), ('D4', 'D4'),
    ]
    
    # Spécifique à l'esthétique
    treatment_type = models.CharField(max_length=20, choices=ESTHETIC_TYPE_CHOICES)
    desired_shade = models.CharField(max_length=2, choices=SHADE_CHOICES, blank=True)
    current_shade = models.CharField(max_length=2, choices=SHADE_CHOICES, blank=True)
    achieved_shade = models.CharField(max_length=2, choices=SHADE_CHOICES, blank=True)
    
    # Matériaux utilisés
    materials_used = models.JSONField(default=list, verbose_name=_('Matériaux utilisés'))
    
    # Photos avant/après
    before_photos = models.JSONField(default=list, verbose_name=_('Photos avant'))
    after_photos = models.JSONField(default=list, verbose_name=_('Photos après'))
    
    # Satisfaction patient
    patient_satisfaction = models.PositiveIntegerField(
        null=True, blank=True,
        validators=[MinValueValidator(1), MaxValueValidator(10)],
        help_text=_('Satisfaction patient (1-10)')
    )
    
    class Meta:
        verbose_name = _('Esthetic Dentistry Treatment')
        verbose_name_plural = _('Esthetic Dentistry Treatments')
    
    def __str__(self):
        return f"{self.patient.full_name} - {self.get_treatment_type_display()} - Dent {self.tooth_number}"


class ProsthodonticTreatment(BaseSpecializedTreatment):
    """
    Traitements de prosthodontie (prothèses)
    """
    PROSTHETIC_TYPE_CHOICES = [
        ('crown', _('Couronne')),
        ('bridge', _('Bridge')),
        ('partial_denture', _('Prothèse partielle')),
        ('complete_denture', _('Prothèse complète')),
        ('implant_crown', _('Couronne sur implant')),
        ('inlay_onlay', _('Inlay/Onlay')),
    ]
    
    MATERIAL_CHOICES = [
        ('ceramic', _('Céramique')),
        ('metal_ceramic', _('Métal-céramique')),
        ('zirconia', _('Zircone')),
        ('gold', _('Or')),
        ('titanium', _('Titane')),
        ('composite', _('Composite')),
        ('acrylic', _('Acrylique')),
    ]
    
    # Spécifique à la prosthodontie
    prosthetic_type = models.CharField(max_length=20, choices=PROSTHETIC_TYPE_CHOICES)
    material = models.CharField(max_length=20, choices=MATERIAL_CHOICES)
    
    # Détails techniques
    impression_date = models.DateField(null=True, blank=True, verbose_name=_('Date d\'empreinte'))
    try_in_date = models.DateField(null=True, blank=True, verbose_name=_('Date d\'essayage'))
    delivery_date = models.DateField(null=True, blank=True, verbose_name=_('Date de livraison'))
    
    # Laboratoire
    lab_name = models.CharField(max_length=200, blank=True, verbose_name=_('Nom du laboratoire'))
    lab_instructions = models.TextField(blank=True, verbose_name=_('Instructions laboratoire'))
    
    # Garantie
    warranty_period = models.PositiveIntegerField(default=24, help_text=_('Période de garantie en mois'))
    warranty_conditions = models.TextField(blank=True, verbose_name=_('Conditions de garantie'))
    
    class Meta:
        verbose_name = _('Prosthodontic Treatment')
        verbose_name_plural = _('Prosthodontic Treatments')
    
    def __str__(self):
        return f"{self.patient.full_name} - {self.get_prosthetic_type_display()} - Dent {self.tooth_number}"


class SurgicalTreatment(BaseSpecializedTreatment):
    """
    Traitements de chirurgie dentaire
    """
    SURGERY_TYPE_CHOICES = [
        ('extraction', _('Extraction')),
        ('implant', _('Implant')),
        ('bone_graft', _('Greffe osseuse')),
        ('sinus_lift', _('Sinus lift')),
        ('apicoectomy', _('Apicectomie')),
        ('periodontal_surgery', _('Chirurgie parodontale')),
        ('wisdom_tooth', _('Dent de sagesse')),
    ]
    
    ANESTHESIA_CHOICES = [
        ('local', _('Anesthésie locale')),
        ('sedation', _('Sédation')),
        ('general', _('Anesthésie générale')),
    ]
    
    # Spécifique à la chirurgie
    surgery_type = models.CharField(max_length=20, choices=SURGERY_TYPE_CHOICES)
    anesthesia_type = models.CharField(max_length=20, choices=ANESTHESIA_CHOICES, default='local')
    
    # Détails chirurgicaux
    pre_operative_instructions = models.TextField(blank=True, verbose_name=_('Instructions pré-opératoires'))
    post_operative_instructions = models.TextField(blank=True, verbose_name=_('Instructions post-opératoires'))
    complications = models.TextField(blank=True, verbose_name=_('Complications'))
    
    # Suivi
    follow_up_appointments = models.JSONField(default=list, verbose_name=_('Rendez-vous de suivi'))
    healing_progress = models.TextField(blank=True, verbose_name=_('Progression de la guérison'))
    
    # Prescriptions
    medications_prescribed = models.JSONField(default=list, verbose_name=_('Médicaments prescrits'))
    
    class Meta:
        verbose_name = _('Surgical Treatment')
        verbose_name_plural = _('Surgical Treatments')
    
    def __str__(self):
        return f"{self.patient.full_name} - {self.get_surgery_type_display()} - Dent {self.tooth_number}"


class OrthodonticTreatment(BaseSpecializedTreatment):
    """
    Traitements d'orthodontie
    """
    ORTHODONTIC_TYPE_CHOICES = [
        ('traditional_braces', _('Bagues traditionnelles')),
        ('ceramic_braces', _('Bagues céramiques')),
        ('lingual_braces', _('Bagues linguales')),
        ('clear_aligners', _('Gouttières transparentes')),
        ('retainer', _('Appareil de contention')),
        ('expander', _('Expanseur')),
    ]
    
    # Spécifique à l'orthodontie
    treatment_type = models.CharField(max_length=20, choices=ORTHODONTIC_TYPE_CHOICES)
    
    # Analyse orthodontique
    initial_analysis = models.TextField(verbose_name=_('Analyse initiale'))
    treatment_objectives = models.TextField(verbose_name=_('Objectifs de traitement'))
    
    # Progression
    adjustment_appointments = models.JSONField(default=list, verbose_name=_('Rendez-vous d\'ajustement'))
    progress_photos = models.JSONField(default=list, verbose_name=_('Photos de progression'))
    
    # Durée estimée
    estimated_treatment_duration = models.PositiveIntegerField(help_text=_('Durée estimée en mois'))
    actual_treatment_duration = models.PositiveIntegerField(null=True, blank=True, help_text=_('Durée réelle en mois'))
    
    class Meta:
        verbose_name = _('Orthodontic Treatment')
        verbose_name_plural = _('Orthodontic Treatments')
    
    def __str__(self):
        return f"{self.patient.full_name} - {self.get_treatment_type_display()}"


class TherapeuticTreatment(BaseSpecializedTreatment):
    """
    Traitements thérapeutiques (endodontie, parodontie, etc.)
    """
    THERAPEUTIC_TYPE_CHOICES = [
        ('root_canal', _('Traitement de canal')),
        ('filling', _('Obturation')),
        ('scaling', _('Détartrage')),
        ('periodontal_therapy', _('Thérapie parodontale')),
        ('fluoride_treatment', _('Traitement au fluor')),
        ('sealant', _('Scellement')),
    ]
    
    # Spécifique au thérapeutique
    treatment_type = models.CharField(max_length=20, choices=THERAPEUTIC_TYPE_CHOICES)
    
    # Détails du traitement
    number_of_sessions = models.PositiveIntegerField(default=1, verbose_name=_('Nombre de séances'))
    current_session = models.PositiveIntegerField(default=1, verbose_name=_('Séance actuelle'))
    
    # Matériaux et techniques
    materials_used = models.JSONField(default=list, verbose_name=_('Matériaux utilisés'))
    techniques_used = models.JSONField(default=list, verbose_name=_('Techniques utilisées'))
    
    # Résultats
    treatment_outcome = models.TextField(blank=True, verbose_name=_('Résultat du traitement'))
    success_rate = models.PositiveIntegerField(
        null=True, blank=True,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        help_text=_('Taux de succès (%)')
    )
    
    class Meta:
        verbose_name = _('Therapeutic Treatment')
        verbose_name_plural = _('Therapeutic Treatments')
    
    def __str__(self):
        return f"{self.patient.full_name} - {self.get_treatment_type_display()} - Dent {self.tooth_number}"
