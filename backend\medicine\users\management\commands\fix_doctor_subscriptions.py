from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from subscriptions.models import DoctorSubscription, SubscriptionPackage
import logging

User = get_user_model()

class Command(BaseCommand):
    help = 'Fix subscriptions for doctors who do not have one'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be done without making changes',
        )
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Show detailed information about each doctor',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force creation of subscriptions even for doctors who already have one',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        verbose = options['verbose']
        force = options['force']
        
        # Set up logging
        logger = logging.getLogger(__name__)
        
        # Get all doctors
        doctors = User.objects.filter(user_type='doctor')
        self.stdout.write(f"Found {doctors.count()} doctors")
        
        # Check if there are any active subscription packages
        packages = SubscriptionPackage.objects.filter(is_active=True)
        if not packages.exists():
            self.stdout.write(self.style.ERROR("No active subscription packages found. Creating a default package..."))
            
            if not dry_run:
                # Create a default package
                default_package = SubscriptionPackage.objects.create(
                    name="Basic",
                    description="Basic package for small practices",
                    max_assistants=2,
                    max_users=3,
                    max_specialties=1,
                    price_monthly=199.00,
                    price_yearly=299.00,
                    features=["Patient management", "Appointment scheduling", "Basic reporting"],
                    is_active=True
                )
                self.stdout.write(self.style.SUCCESS(f"Created default package: {default_package.name}"))
            else:
                self.stdout.write(self.style.WARNING("Dry run: would create a default package"))
                return
        
        # Get the default package
        default_package = SubscriptionPackage.objects.filter(is_active=True).first()
        
        # Count variables
        doctors_with_subscription = 0
        doctors_without_subscription = 0
        subscriptions_created = 0
        errors = 0
        
        # Process each doctor
        for doctor in doctors:
            # Check if the doctor already has a subscription
            has_subscription = DoctorSubscription.objects.filter(doctor=doctor).exists()
            
            if has_subscription and not force:
                doctors_with_subscription += 1
                if verbose:
                    self.stdout.write(f"Doctor {doctor.email} already has a subscription")
                continue
            
            doctors_without_subscription += 1
            
            if verbose:
                self.stdout.write(f"Doctor {doctor.email} does not have a subscription")
            
            if not dry_run:
                try:
                    # Create a subscription for the doctor
                    if hasattr(doctor, 'create_doctor_subscription'):
                        # Use the new method if available
                        doctor.create_doctor_subscription()
                        subscriptions_created += 1
                        self.stdout.write(self.style.SUCCESS(f"Created subscription for doctor {doctor.email}"))
                    else:
                        # Fallback to manual creation
                        import datetime
                        
                        # Create a 6-month subscription by default
                        start_date = timezone.now()
                        end_date = start_date + datetime.timedelta(days=180)
                        
                        # Create the subscription
                        subscription = DoctorSubscription.objects.create(
                            doctor=doctor,
                            package=default_package,
                            status='active',
                            billing_cycle='monthly',  # For a 6-month package
                            start_date=start_date,
                            end_date=end_date,
                            metadata={'is_six_month': True},
                            current_assistant_count=0,
                            current_user_count=0,
                            current_specialty_count=0
                        )
                        
                        subscriptions_created += 1
                        self.stdout.write(self.style.SUCCESS(f"Created subscription for doctor {doctor.email}"))
                except Exception as e:
                    errors += 1
                    logger.error(f"Error creating subscription for doctor {doctor.email}: {str(e)}")
                    self.stdout.write(self.style.ERROR(f"Error creating subscription for doctor {doctor.email}: {str(e)}"))
            else:
                self.stdout.write(self.style.WARNING(f"Dry run: would create subscription for doctor {doctor.email}"))
        
        # Print summary
        self.stdout.write("\nSummary:")
        self.stdout.write(f"Total doctors: {doctors.count()}")
        self.stdout.write(f"Doctors with subscription: {doctors_with_subscription}")
        self.stdout.write(f"Doctors without subscription: {doctors_without_subscription}")
        
        if not dry_run:
            self.stdout.write(f"Subscriptions created: {subscriptions_created}")
            self.stdout.write(f"Errors: {errors}")
        else:
            self.stdout.write(f"Subscriptions that would be created: {doctors_without_subscription}")
            
        if errors > 0:
            self.stdout.write(self.style.WARNING("Some errors occurred. Check the logs for details."))
        elif not dry_run and subscriptions_created > 0:
            self.stdout.write(self.style.SUCCESS("Successfully fixed doctor subscriptions"))
        elif not dry_run and subscriptions_created == 0:
            self.stdout.write(self.style.SUCCESS("No subscriptions needed to be created"))
