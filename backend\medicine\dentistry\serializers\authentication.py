"""
Authentication serializers for the dentistry application.
"""
from rest_framework import serializers
from django.contrib.auth import get_user_model
from django.conf import settings
from dentistry.models import DentistryRole, DentistryStaffProfile

User = get_user_model()

class UserSerializer(serializers.ModelSerializer):
    """
    Serializer for the User model.
    """
    full_name = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'first_name', 'last_name', 'full_name', 'is_active']
        read_only_fields = ['id', 'is_active']

    def get_full_name(self, obj):
        """
        Get the user's full name.
        """
        return obj.get_full_name()

class DentistryRoleSerializer(serializers.ModelSerializer):
    """
    Serializer for the DentistryRole model.
    """
    class Meta:
        model = DentistryRole
        fields = [
            'id', 'name', 'description',
            'can_view_patients', 'can_edit_patients',
            'can_view_appointments', 'can_schedule_appointments',
            'can_view_medical_records', 'can_create_medical_records', 'can_edit_medical_records',
            'can_view_treatments', 'can_create_treatments', 'can_edit_treatments',
            'can_view_lab_orders', 'can_create_lab_orders', 'can_edit_lab_orders',
            'can_view_billing', 'can_create_billing', 'can_edit_billing',
            'can_manage_staff', 'can_manage_settings',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

class DentistryStaffProfileSerializer(serializers.ModelSerializer):
    """
    Serializer for the DentistryStaffProfile model.
    """
    user_details = serializers.CharField(source='user.get_full_name', read_only=True)
    role_details = serializers.CharField(source='role.get_full_name', read_only=True)
    full_name = serializers.ReadOnlyField()
    is_dentist = serializers.ReadOnlyField()
    is_hygienist = serializers.ReadOnlyField()
    is_assistant = serializers.ReadOnlyField()
    is_receptionist = serializers.ReadOnlyField()
    is_manager = serializers.ReadOnlyField()

    class Meta:
        model = DentistryStaffProfile
        fields = [
            'id', 'user', 'user_details', 'role', 'role_details',
            'job_title', 'license_number', 'specialization',
            'work_phone', 'work_email', 'hire_date', 'end_date', 'is_active',
            'working_days', 'working_hours', 'bio', 'profile_image', 'notes',
            'full_name', 'is_dentist', 'is_hygienist', 'is_assistant', 'is_receptionist', 'is_manager',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

class DentistryStaffProfileCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating a DentistryStaffProfile with a new User.
    """
    username = serializers.CharField(write_only=True)
    password = serializers.CharField(write_only=True, style={'input_type': 'password'})
    email = serializers.EmailField(write_only=True)
    first_name = serializers.CharField(write_only=True)
    last_name = serializers.CharField(write_only=True)

    class Meta:
        model = DentistryStaffProfile
        fields = [
            'username', 'password', 'email', 'first_name', 'last_name',
            'role', 'job_title', 'license_number', 'specialization',
            'work_phone', 'work_email', 'hire_date', 'working_days', 'working_hours',
            'bio', 'profile_image', 'notes'
        ]

    def create(self, validated_data):
        """
        Create a new User and DentistryStaffProfile.
        """
        # Extract User fields
        username = validated_data.pop('username')
        password = validated_data.pop('password')
        email = validated_data.pop('email')
        first_name = validated_data.pop('first_name')
        last_name = validated_data.pop('last_name')

        # Create the User
        user = User.objects.create_user(
            username=username,
            password=password,
            email=email,
            first_name=first_name,
            last_name=last_name
        )

        # Create the DentistryStaffProfile
        staff_profile = DentistryStaffProfile.objects.create(
            user=user,
            **validated_data
        )

        return staff_profile
