# backend/dental_medicine/dentistry/models_treatments.py

from django.db import models
from django.utils.translation import gettext_lazy as _
import uuid

class TreatmentCategory(models.Model):
    """
    Catégories principales de traitements dentaires
    """
    CATEGORY_CHOICES = [
        ('esthetic', 'Dentisterie Esthétique'),
        ('prosthetic', 'Prothèses Thérapeutiques'),
        ('surgery', 'Chirurgie'),
        ('orthodontics', 'Orthopédie'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100, choices=CATEGORY_CHOICES, unique=True)
    display_name = models.CharField(max_length=200)
    description = models.TextField(blank=True, null=True)
    icon = models.CharField(max_length=50, blank=True, null=True)  # Icon name for UI
    color = models.CharField(max_length=7, default='#2196F3')  # Hex color
    order = models.PositiveIntegerField(default=0)
    is_active = models.BooleanField(default=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['order', 'display_name']
        verbose_name = _('Treatment Category')
        verbose_name_plural = _('Treatment Categories')

    def __str__(self):
        return self.display_name


class TreatmentType(models.Model):
    """
    Types de traitements spécifiques dans chaque catégorie
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    category = models.ForeignKey(TreatmentCategory, on_delete=models.CASCADE, related_name='treatment_types')

    # Identifiant unique pour chaque type de modification
    code = models.CharField(max_length=20, unique=True, help_text="Code unique (ex: EST001, PRO001)")
    name = models.CharField(max_length=200)
    description = models.TextField(blank=True, null=True)

    # Propriétés visuelles
    icon = models.CharField(max_length=50, blank=True, null=True)
    color = models.CharField(max_length=7, blank=True, null=True)

    # Propriétés du traitement
    duration_minutes = models.PositiveIntegerField(null=True, blank=True, help_text="Durée estimée en minutes")
    base_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    complexity_level = models.CharField(max_length=20, choices=[
        ('simple', 'Simple'),
        ('moderate', 'Modéré'),
        ('complex', 'Complexe'),
        ('expert', 'Expert'),
    ], default='simple')

    # Métadonnées
    order = models.PositiveIntegerField(default=0)
    is_active = models.BooleanField(default=True)
    requires_anesthesia = models.BooleanField(default=False)
    requires_followup = models.BooleanField(default=False)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['category', 'order', 'name']
        verbose_name = _('Treatment Type')
        verbose_name_plural = _('Treatment Types')

    def __str__(self):
        return f"{self.code} - {self.name}"


class TreatmentModificationStatus(models.Model):
    """
    Statuts de modifications possibles pour chaque type de traitement
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    treatment_type = models.ForeignKey(TreatmentType, on_delete=models.CASCADE, related_name='modification_statuses')

    # Identifiant unique du statut basé sur le traitement
    status_code = models.CharField(max_length=30, help_text="Code unique (ex: EST001_PREP, EST001_APPLIED)")
    status_name = models.CharField(max_length=200)
    description = models.TextField(blank=True, null=True)

    # Propriétés du statut
    is_initial_status = models.BooleanField(default=False, help_text="Statut initial par défaut")
    is_final_status = models.BooleanField(default=False, help_text="Statut final (traitement terminé)")
    requires_validation = models.BooleanField(default=False, help_text="Nécessite une validation")

    # Ordre d'affichage et couleur
    order = models.PositiveIntegerField(default=0)
    color = models.CharField(max_length=7, default='#2196F3')
    icon = models.CharField(max_length=50, blank=True, null=True)

    # Métadonnées
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['treatment_type', 'order']
        unique_together = ['treatment_type', 'status_code']
        verbose_name = _('Treatment Modification Status')
        verbose_name_plural = _('Treatment Modification Statuses')

    def __str__(self):
        return f"{self.treatment_type.code} - {self.status_name}"


class ToothTreatment(models.Model):
    """
    Traitements appliqués à une dent spécifique
    """
    STATUS_CHOICES = [
        ('planned', 'Planifié'),
        ('in_progress', 'En cours'),
        ('completed', 'Terminé'),
        ('cancelled', 'Annulé'),
        ('on_hold', 'En attente'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    tooth = models.ForeignKey('Tooth', on_delete=models.CASCADE, related_name='treatments')
    treatment_type = models.ForeignKey(TreatmentType, on_delete=models.CASCADE)

    # Statut et planification
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='planned')
    planned_date = models.DateField(null=True, blank=True)
    completed_date = models.DateField(null=True, blank=True)

    # Statut de modification actuel
    current_modification_status = models.ForeignKey(
        TreatmentModificationStatus,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="Statut de modification actuel du traitement"
    )

    # Détails du traitement
    notes = models.TextField(blank=True, null=True)
    custom_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    actual_duration_minutes = models.PositiveIntegerField(null=True, blank=True)

    # Surfaces affectées (pour les traitements partiels)
    surfaces_affected = models.JSONField(default=list, blank=True, help_text="Liste des surfaces: ['mesial', 'distal', 'occlusal', 'buccal', 'lingual']")

    # Métadonnées
    created_by = models.ForeignKey('users.User', on_delete=models.SET_NULL, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name = _('Tooth Treatment')
        verbose_name_plural = _('Tooth Treatments')

    def __str__(self):
        return f"{self.tooth} - {self.treatment_type.name} ({self.status})"

    @property
    def final_price(self):
        """Prix final du traitement (custom ou base)"""
        return self.custom_price or self.treatment_type.base_price

    @property
    def category_name(self):
        """Nom de la catégorie du traitement"""
        return self.treatment_type.category.display_name


class TreatmentTemplate(models.Model):
    """
    Templates de traitements prédéfinis pour des cas communs
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=200)
    description = models.TextField(blank=True, null=True)
    category = models.ForeignKey(TreatmentCategory, on_delete=models.CASCADE)

    # Traitements inclus dans ce template
    treatment_types = models.ManyToManyField(TreatmentType, through='TemplateItem')

    # Propriétés
    estimated_total_duration = models.PositiveIntegerField(null=True, blank=True)
    estimated_total_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)

    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['category', 'name']
        verbose_name = _('Treatment Template')
        verbose_name_plural = _('Treatment Templates')

    def __str__(self):
        return f"{self.name} ({self.category.display_name})"


class TemplateItem(models.Model):
    """
    Items individuels dans un template de traitement
    """
    template = models.ForeignKey(TreatmentTemplate, on_delete=models.CASCADE)
    treatment_type = models.ForeignKey(TreatmentType, on_delete=models.CASCADE)
    order = models.PositiveIntegerField(default=0)
    is_optional = models.BooleanField(default=False)
    custom_notes = models.TextField(blank=True, null=True)

    class Meta:
        ordering = ['order']
        unique_together = ['template', 'treatment_type']
