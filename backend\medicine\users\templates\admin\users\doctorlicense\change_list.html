{% extends "admin/change_list.html" %}
{% load i18n admin_urls static %}

{% block extrastyle %}
  {{ block.super }}
  <style>
    .license-info-box {
      margin-bottom: 20px;
      padding: 15px;
      border-radius: 5px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .transaction-id-box {
      background-color: #f8f9fa;
      border-left: 4px solid #007bff;
    }

    .days-remaining-box {
      background-color: #f8f9fa;
      border-left: 4px solid #28a745;
    }

    .days-critical {
      background-color: #fff8f8;
      border-left: 4px solid #dc3545;
    }

    .box-title {
      margin-top: 0;
      font-size: 16px;
      font-weight: bold;
      color: #495057;
    }

    .box-content {
      font-size: 24px;
      font-weight: bold;
      margin: 10px 0;
    }

    .transaction-id {
      color: #007bff;
    }

    .days-remaining {
      color: #28a745;
    }

    .days-critical .days-remaining {
      color: #dc3545;
    }

    .box-footer {
      font-size: 12px;
      color: #6c757d;
      margin-bottom: 0;
    }

    .license-info-container {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
    }

    .license-info-box {
      flex: 1;
      min-width: 250px;
    }

    .reminder-text {
      margin-top: 10px;
      font-weight: bold;
    }

    .reminder-critical {
      color: #dc3545;
    }

    .reminder-normal {
      color: #28a745;
    }

    .email-button {
      display: inline-block;
      padding: 8px 16px;
      background-color: #007bff;
      color: white;
      text-decoration: none;
      border-radius: 4px;
      margin-top: 10px;
      font-size: 14px;
    }

    .email-button:hover {
      background-color: #0069d9;
      color: white;
    }
  </style>
{% endblock %}

{% block content %}
  <div class="license-info-container">
    <div class="license-info-box transaction-id-box">
      <h3 class="box-title">Doctors by Package Type</h3>
      <div class="box-content">
        <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
          <span style="font-size: 18px;">6-Month Package:</span>
          <span class="transaction-id">{{ six_month_count|default:"0" }}</span>
        </div>
        <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
          <span style="font-size: 18px;">1-Year Package:</span>
          <span class="transaction-id">{{ one_year_count|default:"0" }}</span>
        </div>
        <div style="display: flex; justify-content: space-between;">
          <span style="font-size: 18px;">Experimental Phase:</span>
          <span class="transaction-id" style="color: #6f42c1;">{{ experimental_count|default:"0" }}</span>
        </div>
      </div>
      <p class="box-footer">Total active subscriptions by package duration</p>
    </div>

    <div class="license-info-box days-critical">
      <h3 class="box-title">Expiring Licenses (< 30 days)</h3>
      <div class="box-content">
        <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
          <span style="font-size: 18px;">6-Month Package:</span>
          <span class="days-remaining">{{ six_month_expiring|default:"0" }}</span>
        </div>
        <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
          <span style="font-size: 18px;">1-Year Package:</span>
          <span class="days-remaining">{{ one_year_expiring|default:"0" }}</span>
        </div>
        <div style="display: flex; justify-content: space-between;">
          <span style="font-size: 18px;">Experimental Phase:</span>
          <span class="days-remaining" style="color: #6f42c1;">{{ experimental_expiring|default:"0" }}</span>
        </div>
      </div>
      <p class="box-footer">
        <span class="reminder-text reminder-critical">Doctors with licenses expiring in less than 30 days</span>
      </p>
    </div>

    <div class="license-info-box" style="background-color: #f8f9fa; border-left: 4px solid #6f42c1;">
      <h3 class="box-title">Experimental Phase</h3>
      <div class="box-content">
        <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
          <span style="font-size: 18px;">Total Doctors:</span>
          <span style="color: #6f42c1; font-weight: bold;">{{ experimental_count|default:"0" }}</span>
        </div>
        <div style="display: flex; justify-content: space-between;">
          <span style="font-size: 18px;">Active Projects:</span>
          <span style="color: #6f42c1; font-weight: bold;">{{ experimental_projects|default:"0" }}</span>
        </div>
      </div>
      <p class="box-footer">Doctors participating in experimental research and development</p>
    </div>
  </div>

  {{ block.super }}
{% endblock %}
