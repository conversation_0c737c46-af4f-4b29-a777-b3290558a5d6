"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_i18n_locales_en_Corbeille_blog_json";
exports.ids = ["_ssr_src_i18n_locales_en_Corbeille_blog_json"];
exports.modules = {

/***/ "(ssr)/./src/i18n/locales/en/Corbeille/blog.json":
/*!*************************************************!*\
  !*** ./src/i18n/locales/en/Corbeille/blog.json ***!
  \*************************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"blog":"blog AN","page-title":"Blog","app-description":"The MedicinSvp health platform is an electronic media interface for disseminating accurate health information and an electronic portal for all the services the platform provides.","keywords":"Ministry of Health website, health news, Ministry of Health announcements, health events, Ministry of Health statistics, Ministry of Health services, Ministry of Health, health center, number of injuries, examinations, health"}');

/***/ })

};
;