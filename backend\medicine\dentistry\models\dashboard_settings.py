"""
Dashboard settings models for the dentistry application.
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator, MaxValueValidator
from .base import DentistryBaseModel
from .doctor import DentistryDoctor


class DentistryAppointmentSettings(DentistryBaseModel):
    """
    Model for managing appointment settings for dentistry doctors.
    """
    doctor = models.OneToOneField(
        DentistryDoctor,
        on_delete=models.CASCADE,
        related_name='appointment_settings',
        verbose_name=_("Doctor")
    )

    # Appointment booking settings
    allow_online_booking = models.BooleanField(
        default=True,
        verbose_name=_("Allow Online Booking"),
        help_text=_("Allow patients to book appointments online")
    )

    advance_booking_days = models.PositiveIntegerField(
        default=30,
        validators=[MinValueValidator(1), MaxValueValidator(365)],
        verbose_name=_("Advance Booking Days"),
        help_text=_("How many days in advance patients can book appointments")
    )

    min_booking_notice_hours = models.PositiveIntegerField(
        default=24,
        validators=[MinValueValidator(1), MaxValueValidator(168)],
        verbose_name=_("Minimum Booking Notice (Hours)"),
        help_text=_("Minimum hours notice required for booking")
    )

    # Appointment duration settings
    default_appointment_duration = models.PositiveIntegerField(
        default=30,
        validators=[MinValueValidator(15), MaxValueValidator(240)],
        verbose_name=_("Default Appointment Duration (Minutes)")
    )

    consultation_duration = models.PositiveIntegerField(
        default=30,
        validators=[MinValueValidator(15), MaxValueValidator(120)],
        verbose_name=_("Consultation Duration (Minutes)")
    )

    cleaning_duration = models.PositiveIntegerField(
        default=60,
        validators=[MinValueValidator(30), MaxValueValidator(120)],
        verbose_name=_("Cleaning Duration (Minutes)")
    )

    treatment_duration = models.PositiveIntegerField(
        default=90,
        validators=[MinValueValidator(30), MaxValueValidator(240)],
        verbose_name=_("Treatment Duration (Minutes)")
    )

    emergency_duration = models.PositiveIntegerField(
        default=45,
        validators=[MinValueValidator(15), MaxValueValidator(120)],
        verbose_name=_("Emergency Duration (Minutes)")
    )

    # Buffer time settings
    buffer_time_before = models.PositiveIntegerField(
        default=10,
        validators=[MinValueValidator(0), MaxValueValidator(60)],
        verbose_name=_("Buffer Time Before (Minutes)"),
        help_text=_("Buffer time before each appointment")
    )

    buffer_time_after = models.PositiveIntegerField(
        default=10,
        validators=[MinValueValidator(0), MaxValueValidator(60)],
        verbose_name=_("Buffer Time After (Minutes)"),
        help_text=_("Buffer time after each appointment")
    )

    # Cancellation settings
    allow_patient_cancellation = models.BooleanField(
        default=True,
        verbose_name=_("Allow Patient Cancellation")
    )

    cancellation_notice_hours = models.PositiveIntegerField(
        default=24,
        validators=[MinValueValidator(1), MaxValueValidator(168)],
        verbose_name=_("Cancellation Notice Hours"),
        help_text=_("Minimum hours notice required for cancellation")
    )

    # Reminder settings
    send_appointment_reminders = models.BooleanField(
        default=True,
        verbose_name=_("Send Appointment Reminders")
    )

    reminder_hours_before = models.PositiveIntegerField(
        default=24,
        validators=[MinValueValidator(1), MaxValueValidator(168)],
        verbose_name=_("Reminder Hours Before"),
        help_text=_("Hours before appointment to send reminder")
    )

    reminder_methods = models.JSONField(
        default=list,
        verbose_name=_("Reminder Methods"),
        help_text=_("List of reminder methods: email, sms, push")
    )

    class Meta:
        verbose_name = _("Dentistry Appointment Settings")
        verbose_name_plural = _("Dentistry Appointment Settings")

    def __str__(self):
        return f"Appointment Settings for {self.doctor.full_name}"


class DentistryWorkingHours(DentistryBaseModel):
    """
    Model for managing working hours for dentistry doctors.
    """
    WEEKDAY_CHOICES = [
        (0, _('Monday')),
        (1, _('Tuesday')),
        (2, _('Wednesday')),
        (3, _('Thursday')),
        (4, _('Friday')),
        (5, _('Saturday')),
        (6, _('Sunday')),
    ]

    doctor = models.ForeignKey(
        DentistryDoctor,
        on_delete=models.CASCADE,
        related_name='working_hours',
        verbose_name=_("Doctor")
    )

    weekday = models.IntegerField(
        choices=WEEKDAY_CHOICES,
        verbose_name=_("Weekday")
    )

    is_working_day = models.BooleanField(
        default=True,
        verbose_name=_("Is Working Day")
    )

    # Morning session
    morning_start = models.TimeField(
        null=True,
        blank=True,
        verbose_name=_("Morning Start Time")
    )

    morning_end = models.TimeField(
        null=True,
        blank=True,
        verbose_name=_("Morning End Time")
    )

    # Afternoon session
    afternoon_start = models.TimeField(
        null=True,
        blank=True,
        verbose_name=_("Afternoon Start Time")
    )

    afternoon_end = models.TimeField(
        null=True,
        blank=True,
        verbose_name=_("Afternoon End Time")
    )

    # Break settings
    lunch_break_start = models.TimeField(
        null=True,
        blank=True,
        verbose_name=_("Lunch Break Start")
    )

    lunch_break_end = models.TimeField(
        null=True,
        blank=True,
        verbose_name=_("Lunch Break End")
    )

    # Special notes
    notes = models.TextField(
        blank=True,
        verbose_name=_("Notes"),
        help_text=_("Special notes for this day")
    )

    class Meta:
        verbose_name = _("Dentistry Working Hours")
        verbose_name_plural = _("Dentistry Working Hours")
        unique_together = ['doctor', 'weekday']
        ordering = ['weekday']

    def __str__(self):
        return f"{self.doctor.full_name} - {self.get_weekday_display()}"


class DentistryHoliday(DentistryBaseModel):
    """
    Model for managing holidays and time off for dentistry doctors.
    """
    HOLIDAY_TYPE_CHOICES = [
        ('vacation', _('Vacation')),
        ('sick_leave', _('Sick Leave')),
        ('conference', _('Conference')),
        ('training', _('Training')),
        ('personal', _('Personal')),
        ('public_holiday', _('Public Holiday')),
        ('emergency', _('Emergency')),
    ]

    doctor = models.ForeignKey(
        DentistryDoctor,
        on_delete=models.CASCADE,
        related_name='holidays',
        verbose_name=_("Doctor")
    )

    title = models.CharField(
        max_length=200,
        verbose_name=_("Holiday Title")
    )

    description = models.TextField(
        blank=True,
        verbose_name=_("Description")
    )

    holiday_type = models.CharField(
        max_length=20,
        choices=HOLIDAY_TYPE_CHOICES,
        default='vacation',
        verbose_name=_("Holiday Type")
    )

    start_date = models.DateField(
        verbose_name=_("Start Date")
    )

    end_date = models.DateField(
        verbose_name=_("End Date")
    )

    is_all_day = models.BooleanField(
        default=True,
        verbose_name=_("All Day")
    )

    start_time = models.TimeField(
        null=True,
        blank=True,
        verbose_name=_("Start Time")
    )

    end_time = models.TimeField(
        null=True,
        blank=True,
        verbose_name=_("End Time")
    )

    is_recurring = models.BooleanField(
        default=False,
        verbose_name=_("Is Recurring")
    )

    recurrence_pattern = models.CharField(
        max_length=50,
        blank=True,
        choices=[
            ('daily', _('Daily')),
            ('weekly', _('Weekly')),
            ('monthly', _('Monthly')),
            ('yearly', _('Yearly')),
        ],
        verbose_name=_("Recurrence Pattern")
    )

    affects_appointments = models.BooleanField(
        default=True,
        verbose_name=_("Affects Appointments"),
        help_text=_("Whether this holiday blocks appointment booking")
    )

    class Meta:
        verbose_name = _("Dentistry Holiday")
        verbose_name_plural = _("Dentistry Holidays")
        ordering = ['start_date']

    def __str__(self):
        return f"{self.title} - {self.doctor.full_name}"


class DentistryLocationSettings(DentistryBaseModel):
    """
    Model for managing location and map settings for dentistry doctors.
    """
    doctor = models.OneToOneField(
        DentistryDoctor,
        on_delete=models.CASCADE,
        related_name='location_settings',
        verbose_name=_("Doctor")
    )

    # Clinic information
    clinic_name = models.CharField(
        max_length=200,
        verbose_name=_("Clinic Name")
    )

    address_line_1 = models.CharField(
        max_length=200,
        verbose_name=_("Address Line 1")
    )

    address_line_2 = models.CharField(
        max_length=200,
        blank=True,
        verbose_name=_("Address Line 2")
    )

    city = models.CharField(
        max_length=100,
        verbose_name=_("City")
    )

    state_province = models.CharField(
        max_length=100,
        verbose_name=_("State/Province")
    )

    postal_code = models.CharField(
        max_length=20,
        verbose_name=_("Postal Code")
    )

    country = models.CharField(
        max_length=100,
        verbose_name=_("Country")
    )

    # Contact information
    phone_number = models.CharField(
        max_length=20,
        verbose_name=_("Phone Number")
    )

    fax_number = models.CharField(
        max_length=20,
        blank=True,
        verbose_name=_("Fax Number")
    )

    email = models.EmailField(
        verbose_name=_("Email Address")
    )

    website = models.URLField(
        blank=True,
        verbose_name=_("Website")
    )

    # Map settings
    latitude = models.DecimalField(
        max_digits=10,
        decimal_places=8,
        null=True,
        blank=True,
        verbose_name=_("Latitude")
    )

    longitude = models.DecimalField(
        max_digits=11,
        decimal_places=8,
        null=True,
        blank=True,
        verbose_name=_("Longitude")
    )

    show_on_map = models.BooleanField(
        default=True,
        verbose_name=_("Show on Map")
    )

    map_zoom_level = models.PositiveIntegerField(
        default=15,
        validators=[MinValueValidator(1), MaxValueValidator(20)],
        verbose_name=_("Map Zoom Level")
    )

    # Directions and parking
    parking_available = models.BooleanField(
        default=True,
        verbose_name=_("Parking Available")
    )

    parking_instructions = models.TextField(
        blank=True,
        verbose_name=_("Parking Instructions")
    )

    public_transport_info = models.TextField(
        blank=True,
        verbose_name=_("Public Transport Information")
    )

    accessibility_info = models.TextField(
        blank=True,
        verbose_name=_("Accessibility Information")
    )

    class Meta:
        verbose_name = _("Dentistry Location Settings")
        verbose_name_plural = _("Dentistry Location Settings")

    def __str__(self):
        return f"Location Settings for {self.doctor.full_name}"

    @property
    def full_address(self):
        """Return the full formatted address."""
        address_parts = [self.address_line_1]
        if self.address_line_2:
            address_parts.append(self.address_line_2)
        address_parts.extend([self.city, self.state_province, self.postal_code, self.country])
        return ', '.join(address_parts)


class DentistryNotificationSettings(DentistryBaseModel):
    """
    Model for managing notification preferences for dentistry doctors.
    """
    doctor = models.OneToOneField(
        DentistryDoctor,
        on_delete=models.CASCADE,
        related_name='notification_settings',
        verbose_name=_("Doctor")
    )

    # Email notifications
    email_notifications = models.BooleanField(
        default=True,
        verbose_name=_("Email Notifications"),
        help_text=_("Receive notifications via email")
    )

    appointment_reminders = models.BooleanField(
        default=True,
        verbose_name=_("Appointment Reminders"),
        help_text=_("Receive reminders about upcoming appointments")
    )

    new_appointment_notifications = models.BooleanField(
        default=True,
        verbose_name=_("New Appointment Notifications"),
        help_text=_("Get notified when patients book new appointments")
    )

    cancellation_notifications = models.BooleanField(
        default=True,
        verbose_name=_("Cancellation Notifications"),
        help_text=_("Get notified when appointments are cancelled")
    )

    # SMS notifications
    sms_notifications = models.BooleanField(
        default=True,
        verbose_name=_("SMS Notifications"),
        help_text=_("Receive notifications via SMS")
    )

    emergency_sms = models.BooleanField(
        default=True,
        verbose_name=_("Emergency SMS"),
        help_text=_("Receive emergency notifications via SMS")
    )

    # Marketing and promotional
    marketing_emails = models.BooleanField(
        default=False,
        verbose_name=_("Marketing Emails"),
        help_text=_("Receive marketing and promotional emails")
    )

    newsletter_subscription = models.BooleanField(
        default=False,
        verbose_name=_("Newsletter Subscription"),
        help_text=_("Subscribe to dental industry newsletters")
    )

    # Reminder timing
    reminder_hours_before = models.PositiveIntegerField(
        default=24,
        validators=[MinValueValidator(1), MaxValueValidator(168)],
        verbose_name=_("Reminder Hours Before"),
        help_text=_("Hours before appointment to send reminder")
    )

    # Notification methods
    preferred_notification_method = models.CharField(
        max_length=20,
        choices=[
            ('email', _('Email')),
            ('sms', _('SMS')),
            ('both', _('Both Email and SMS')),
            ('push', _('Push Notifications')),
        ],
        default='email',
        verbose_name=_("Preferred Notification Method")
    )

    class Meta:
        verbose_name = _("Dentistry Notification Settings")
        verbose_name_plural = _("Dentistry Notification Settings")

    def __str__(self):
        return f"Notification Settings for {self.doctor.full_name}"


class DentistryPrivacySettings(DentistryBaseModel):
    """
    Model for managing privacy settings for dentistry doctors.
    """
    doctor = models.OneToOneField(
        DentistryDoctor,
        on_delete=models.CASCADE,
        related_name='privacy_settings',
        verbose_name=_("Doctor")
    )

    # Profile visibility
    show_profile_to_other_doctors = models.BooleanField(
        default=True,
        verbose_name=_("Show Profile to Other Doctors"),
        help_text=_("Allow other doctors to view your profile")
    )

    show_profile_to_patients = models.BooleanField(
        default=True,
        verbose_name=_("Show Profile to Patients"),
        help_text=_("Allow patients to view your public profile")
    )

    # Data sharing
    share_anonymized_data_for_research = models.BooleanField(
        default=False,
        verbose_name=_("Share Anonymized Data for Research"),
        help_text=_("Allow anonymized data to be used for research purposes")
    )

    allow_patient_feedback = models.BooleanField(
        default=True,
        verbose_name=_("Allow Patient Feedback"),
        help_text=_("Allow patients to leave feedback and ratings")
    )

    # Contact information visibility
    show_phone_number = models.BooleanField(
        default=True,
        verbose_name=_("Show Phone Number"),
        help_text=_("Display phone number on public profile")
    )

    show_email_address = models.BooleanField(
        default=False,
        verbose_name=_("Show Email Address"),
        help_text=_("Display email address on public profile")
    )

    # Online booking privacy
    require_patient_verification = models.BooleanField(
        default=False,
        verbose_name=_("Require Patient Verification"),
        help_text=_("Require patients to verify their identity before booking")
    )

    # Data retention
    patient_data_retention_months = models.PositiveIntegerField(
        default=60,
        validators=[MinValueValidator(12), MaxValueValidator(120)],
        verbose_name=_("Patient Data Retention (Months)"),
        help_text=_("How long to retain patient data after last visit")
    )

    class Meta:
        verbose_name = _("Dentistry Privacy Settings")
        verbose_name_plural = _("Dentistry Privacy Settings")

    def __str__(self):
        return f"Privacy Settings for {self.doctor.full_name}"


class DentistryAccessibilitySettings(DentistryBaseModel):
    """
    Model for managing accessibility settings for dentistry doctors.
    """
    doctor = models.OneToOneField(
        DentistryDoctor,
        on_delete=models.CASCADE,
        related_name='accessibility_settings',
        verbose_name=_("Doctor")
    )

    # Visual accessibility
    high_contrast = models.BooleanField(
        default=False,
        verbose_name=_("High Contrast"),
        help_text=_("Increase contrast for better visibility")
    )

    large_text = models.BooleanField(
        default=False,
        verbose_name=_("Large Text"),
        help_text=_("Increase text size throughout the application")
    )

    font_size = models.PositiveIntegerField(
        default=16,
        validators=[MinValueValidator(10), MaxValueValidator(22)],
        verbose_name=_("Font Size (px)"),
        help_text=_("Base font size for the application")
    )

    # Screen reader support
    screen_reader = models.BooleanField(
        default=False,
        verbose_name=_("Screen Reader Support"),
        help_text=_("Optimize for screen readers")
    )

    # Color preferences
    color_blind_friendly = models.BooleanField(
        default=False,
        verbose_name=_("Color Blind Friendly"),
        help_text=_("Use color blind friendly color schemes")
    )

    # Interface preferences
    reduce_animations = models.BooleanField(
        default=False,
        verbose_name=_("Reduce Animations"),
        help_text=_("Reduce or disable animations and transitions")
    )

    keyboard_navigation = models.BooleanField(
        default=False,
        verbose_name=_("Enhanced Keyboard Navigation"),
        help_text=_("Enable enhanced keyboard navigation features")
    )

    class Meta:
        verbose_name = _("Dentistry Accessibility Settings")
        verbose_name_plural = _("Dentistry Accessibility Settings")

    def __str__(self):
        return f"Accessibility Settings for {self.doctor.full_name}"


class DentistryVacationPeriod(DentistryBaseModel):
    """
    Model for managing specific vacation periods for dentistry doctors.
    """
    doctor = models.ForeignKey(
        DentistryDoctor,
        on_delete=models.CASCADE,
        related_name='vacation_periods',
        verbose_name=_("Doctor")
    )

    title = models.CharField(
        max_length=200,
        verbose_name=_("Vacation Title")
    )

    start_date = models.DateField(
        verbose_name=_("Start Date")
    )

    end_date = models.DateField(
        verbose_name=_("End Date")
    )

    vacation_type = models.CharField(
        max_length=20,
        choices=[
            ('summer', _('Summer Vacation')),
            ('winter', _('Winter Vacation')),
            ('personal', _('Personal Time')),
            ('conference', _('Conference')),
            ('training', _('Training')),
            ('other', _('Other')),
        ],
        default='personal',
        verbose_name=_("Vacation Type")
    )

    description = models.TextField(
        blank=True,
        verbose_name=_("Description")
    )

    is_approved = models.BooleanField(
        default=True,
        verbose_name=_("Is Approved"),
        help_text=_("Whether this vacation period is approved")
    )

    affects_appointments = models.BooleanField(
        default=True,
        verbose_name=_("Affects Appointments"),
        help_text=_("Whether this vacation blocks appointment booking")
    )

    class Meta:
        verbose_name = _("Dentistry Vacation Period")
        verbose_name_plural = _("Dentistry Vacation Periods")
        ordering = ['start_date']

    def __str__(self):
        return f"{self.title} - {self.doctor.full_name} ({self.start_date} to {self.end_date})"

    @property
    def duration_days(self):
        """Calculate the duration of the vacation in days."""
        return (self.end_date - self.start_date).days + 1


class DentistryAppearanceSettings(DentistryBaseModel):
    """
    Model for managing appearance and theme settings for dentistry doctors.
    """
    doctor = models.OneToOneField(
        DentistryDoctor,
        on_delete=models.CASCADE,
        related_name='appearance_settings',
        verbose_name=_("Doctor")
    )

    # Theme settings
    theme = models.CharField(
        max_length=20,
        choices=[
            ('light', _('Light')),
            ('dark', _('Dark')),
            ('auto', _('Auto (System)')),
        ],
        default='light',
        verbose_name=_("Theme")
    )

    # Language settings
    language = models.CharField(
        max_length=10,
        choices=[
            ('en', _('English')),
            ('fr', _('French')),
            ('es', _('Spanish')),
            ('de', _('German')),
            ('it', _('Italian')),
            ('ar', _('Arabic')),
        ],
        default='en',
        verbose_name=_("Language")
    )

    # Date and time formats
    time_format = models.CharField(
        max_length=10,
        choices=[
            ('12h', _('12-hour (AM/PM)')),
            ('24h', _('24-hour')),
        ],
        default='12h',
        verbose_name=_("Time Format")
    )

    date_format = models.CharField(
        max_length=15,
        choices=[
            ('MM/DD/YYYY', _('MM/DD/YYYY')),
            ('DD/MM/YYYY', _('DD/MM/YYYY')),
            ('YYYY-MM-DD', _('YYYY-MM-DD')),
        ],
        default='MM/DD/YYYY',
        verbose_name=_("Date Format")
    )

    # Calendar settings
    calendar_view = models.CharField(
        max_length=10,
        choices=[
            ('day', _('Day')),
            ('week', _('Week')),
            ('month', _('Month')),
            ('agenda', _('Agenda')),
        ],
        default='month',
        verbose_name=_("Default Calendar View")
    )

    calendar_start_hour = models.PositiveIntegerField(
        default=8,
        validators=[MinValueValidator(0), MaxValueValidator(23)],
        verbose_name=_("Calendar Start Hour")
    )

    calendar_end_hour = models.PositiveIntegerField(
        default=18,
        validators=[MinValueValidator(1), MaxValueValidator(24)],
        verbose_name=_("Calendar End Hour")
    )

    class Meta:
        verbose_name = _("Dentistry Appearance Settings")
        verbose_name_plural = _("Dentistry Appearance Settings")

    def __str__(self):
        return f"Appearance Settings for {self.doctor.full_name}"
