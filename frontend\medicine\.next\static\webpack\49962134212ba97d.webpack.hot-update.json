{"c": ["app/layout", "app/(dashboard)/web/page", "webpack"], "r": ["app/_not-found/page"], "m": ["(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/color-functions/lighten/lighten.mjs", "(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/create-theme/create-theme.mjs", "(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/mantine-html-props.mjs", "(app-pages-browser)/./node_modules/@mantine/core/esm/index.mjs", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Csorry%5CDesktop%5Csvp%5Cfrontend%5Cmedicine%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-error.js&page=%2F_not-found%2Fpage!", "(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-fallback.js", "(app-pages-browser)/./node_modules/next/dist/client/components/not-found-error.js"]}