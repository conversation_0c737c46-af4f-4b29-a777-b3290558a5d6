"""
Commande Django pour créer les groupes dentaires et peupler les boutons de dents
"""
from django.core.management.base import BaseCommand
from django.db import transaction
from dentistry.models import DentalSet, ToothButton, DentistryPatient
from dentistry.data.tooth_mapping import TOOTH_NAMES, DENTAL_GROUPS, get_tooth_type, get_quadrant


class Command(BaseCommand):
    help = 'Crée les groupes dentaires (Général, Supérieur, Inférieur) avec tous les boutons de dents'

    def add_arguments(self, parser):
        parser.add_argument(
            '--patient-id',
            type=int,
            help='ID du patient pour créer les boutons de dents (optionnel)',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force la recréation des groupes existants',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🦷 Création des groupes dentaires...'))
        
        with transaction.atomic():
            # Créer les ensembles dentaires pour chaque tranche d'âge
            age_restrictions = [13.5, 12.0, 7.5, 6.0, 0.0]
            set_types = ['general', 'upper', 'lower']
            
            created_sets = 0
            
            for set_type in set_types:
                for age_restriction in age_restrictions:
                    dental_set, created = DentalSet.objects.get_or_create(
                        set_type=set_type,
                        age_restriction=age_restriction,
                        defaults={
                            'name': f'Ensemble {set_type.title()} - {age_restriction} ans',
                            'description': f'Ensemble dentaire {set_type} pour patients de {age_restriction} ans et plus',
                            'is_active': True
                        }
                    )
                    
                    if created:
                        created_sets += 1
                        self.stdout.write(
                            f'✅ Créé: {dental_set.name}'
                        )
                    elif options['force']:
                        dental_set.name = f'Ensemble {set_type.title()} - {age_restriction} ans'
                        dental_set.description = f'Ensemble dentaire {set_type} pour patients de {age_restriction} ans et plus'
                        dental_set.save()
                        self.stdout.write(
                            f'🔄 Mis à jour: {dental_set.name}'
                        )
            
            self.stdout.write(
                self.style.SUCCESS(f'📊 {created_sets} nouveaux ensembles dentaires créés')
            )
            
            # Si un patient est spécifié, créer les boutons de dents
            patient_id = options.get('patient_id')
            if patient_id:
                try:
                    patient = DentistryPatient.objects.get(id=patient_id)
                    self.create_tooth_buttons_for_patient(patient, options['force'])
                except DentistryPatient.DoesNotExist:
                    self.stdout.write(
                        self.style.ERROR(f'❌ Patient avec ID {patient_id} non trouvé')
                    )
            else:
                self.stdout.write(
                    self.style.WARNING(
                        '⚠️  Aucun patient spécifié. Utilisez --patient-id pour créer les boutons de dents.'
                    )
                )

    def create_tooth_buttons_for_patient(self, patient, force=False):
        """Crée tous les boutons de dents pour un patient donné"""
        self.stdout.write(f'🦷 Création des boutons de dents pour {patient}...')
        
        created_buttons = 0
        
        for set_type in ['general', 'upper', 'lower']:
            # Obtenir les dents pour ce groupe
            if set_type == 'general':
                tooth_numbers = list(TOOTH_NAMES.keys())
            else:
                tooth_numbers = DENTAL_GROUPS[set_type]
            
            # Obtenir l'ensemble dentaire par défaut (0.0 ans)
            try:
                dental_set = DentalSet.objects.get(
                    set_type=set_type,
                    age_restriction=0.0
                )
            except DentalSet.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'❌ Ensemble dentaire {set_type} non trouvé')
                )
                continue
            
            for tooth_number in tooth_numbers:
                tooth_name = TOOTH_NAMES[tooth_number]
                quadrant = get_quadrant(tooth_number)
                
                # Déterminer le quadrant string
                quadrant_map = {
                    1: 'upper_right', 2: 'upper_left',
                    3: 'lower_left', 4: 'lower_right',
                    5: 'upper_right', 6: 'upper_left',
                    7: 'lower_left', 8: 'lower_right'
                }
                quadrant_str = quadrant_map.get(quadrant, 'upper_right')
                
                # Position dans le quadrant
                position = tooth_number % 10 if tooth_number % 10 != 0 else 8
                
                tooth_button, created = ToothButton.objects.get_or_create(
                    dental_set=dental_set,
                    patient=patient,
                    tooth_number=tooth_number,
                    defaults={
                        'tooth_name': tooth_name,
                        'quadrant_position': position,
                        'quadrant': quadrant_str,
                        'is_expanded': False,
                        'is_visible': True
                    }
                )
                
                if created:
                    created_buttons += 1
                elif force:
                    tooth_button.tooth_name = tooth_name
                    tooth_button.quadrant = quadrant_str
                    tooth_button.quadrant_position = position
                    tooth_button.save()
        
        self.stdout.write(
            self.style.SUCCESS(f'🦷 {created_buttons} nouveaux boutons de dents créés pour {patient}')
        )
        
        # Afficher un résumé par groupe
        for set_type in ['general', 'upper', 'lower']:
            try:
                dental_set = DentalSet.objects.get(set_type=set_type, age_restriction=0.0)
                count = ToothButton.objects.filter(dental_set=dental_set, patient=patient).count()
                self.stdout.write(f'  📋 {set_type.title()}: {count} dents')
            except DentalSet.DoesNotExist:
                pass
