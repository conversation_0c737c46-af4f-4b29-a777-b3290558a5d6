"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/web/page",{

/***/ "(app-pages-browser)/./src/components/Appointments/overview/AjouterUnRendezVous.tsx":
/*!**********************************************************************!*\
  !*** ./src/components/Appointments/overview/AjouterUnRendezVous.tsx ***!
  \**********************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Text/Text.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Select/Select.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/NumberInput/NumberInput.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Button/Button.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Modal/Modal.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Group/Group.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Switch/Switch.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Menu/Menu.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Avatar/Avatar.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/TextInput/TextInput.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ActionIcon/ActionIcon.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Radio/Radio.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/InputBase/InputBase.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ColorPicker/ColorPicker.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Textarea/Textarea.mjs\");\n/* harmony import */ var simplebar_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! simplebar-react */ \"(app-pages-browser)/./node_modules/simplebar-react/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCheck_IconColorPicker_IconHexagonPlusFilled_tabler_icons_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=IconCheck,IconColorPicker,IconHexagonPlusFilled!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconHexagonPlusFilled.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCheck_IconColorPicker_IconHexagonPlusFilled_tabler_icons_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=IconCheck,IconColorPicker,IconHexagonPlusFilled!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconColorPicker.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCheck_IconColorPicker_IconHexagonPlusFilled_tabler_icons_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=IconCheck,IconColorPicker,IconHexagonPlusFilled!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconCheck.mjs\");\n/* harmony import */ var _barrel_optimize_names_FaCalendarPlus_FaMicrophone_FaMicrophoneLines_FaUserDoctor_react_icons_fa6__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=FaCalendarPlus,FaMicrophone,FaMicrophoneLines,FaUserDoctor!=!react-icons/fa6 */ \"(app-pages-browser)/./node_modules/react-icons/fa6/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_RiUserFollowLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=RiUserFollowLine!=!react-icons/ri */ \"(app-pages-browser)/./node_modules/react-icons/ri/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_LiaAddressCardSolid_LiaBirthdayCakeSolid_react_icons_lia__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=LiaAddressCardSolid,LiaBirthdayCakeSolid!=!react-icons/lia */ \"(app-pages-browser)/./node_modules/react-icons/lia/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_TbNumber_react_icons_tb__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=TbNumber!=!react-icons/tb */ \"(app-pages-browser)/./node_modules/react-icons/tb/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FiPhone_react_icons_fi__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=FiPhone!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_CiAt_react_icons_ci__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=CiAt!=!react-icons/ci */ \"(app-pages-browser)/./node_modules/react-icons/ci/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_MdOutlineBedroomChild_MdOutlineContentPasteSearch_MdOutlineSocialDistance_react_icons_md__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=MdOutlineBedroomChild,MdOutlineContentPasteSearch,MdOutlineSocialDistance!=!react-icons/md */ \"(app-pages-browser)/./node_modules/react-icons/md/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ListPlus_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=ListPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/list-plus.js\");\n/* harmony import */ var react_imask__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-imask */ \"(app-pages-browser)/./node_modules/react-imask/esm/index.js\");\n/* harmony import */ var _mantine_notifications__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @mantine/notifications */ \"(app-pages-browser)/./node_modules/@mantine/notifications/esm/notifications.store.mjs\");\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! moment */ \"(app-pages-browser)/./node_modules/moment/moment.js\");\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(moment__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst RendezVousSelector = (param)=>{\n    let { onClose } = param;\n    _s();\n    const [selectedPeriod, setSelectedPeriod] = react__WEBPACK_IMPORTED_MODULE_1___default().useState('15days');\n    const [, setStartDate] = react__WEBPACK_IMPORTED_MODULE_1___default().useState('12/06/2025');\n    const [duration, setDuration] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(30);\n    const [numberOfDays, setNumberOfDays] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(3);\n    const [selectedSlots, setSelectedSlots] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(new Set());\n    // Générer les créneaux horaires\n    const generateTimeSlots = ()=>{\n        const slots = [];\n        const startHour = 8;\n        const endHour = 14;\n        for(let hour = startHour; hour < endHour; hour++){\n            for(let minute = 0; minute < 60; minute += 30){\n                const startTime = \"\".concat(hour.toString().padStart(2, '0'), \":\").concat(minute.toString().padStart(2, '0'));\n                const endMinute = minute + 30;\n                const endHour = endMinute >= 60 ? hour + 1 : hour;\n                const adjustedEndMinute = endMinute >= 60 ? endMinute - 60 : endMinute;\n                const endTime = \"\".concat(endHour.toString().padStart(2, '0'), \":\").concat(adjustedEndMinute.toString().padStart(2, '0'));\n                slots.push({\n                    id: \"\".concat(hour, \"-\").concat(minute),\n                    startTime,\n                    endTime\n                });\n            }\n        }\n        return slots;\n    };\n    // Calculer la date selon la période sélectionnée\n    const getDateForPeriod = ()=>{\n        switch(selectedPeriod){\n            case '15days':\n                return '12 juin 2025';\n            case '1month':\n                return '26 juin 2025';\n            case '3months':\n                return '10 juillet 2025';\n            default:\n                return '12 juin 2025';\n        }\n    };\n    // Calculer la date de début selon la période\n    const getStartDateForPeriod = ()=>{\n        switch(selectedPeriod){\n            case '15days':\n                return '12/06/2025';\n            case '1month':\n                return '25/06/2025';\n            case '3months':\n                return '10/07/2025';\n            default:\n                return '12/06/2025';\n        }\n    };\n    // Calculer la date formatée selon la période\n    const getFormattedDateForPeriod = ()=>{\n        switch(selectedPeriod){\n            case '15days':\n                return '12/06/2025';\n            case '1month':\n                return '26/06/2025';\n            case '3months':\n                return '10/07/2025';\n            default:\n                return '12/06/2025';\n        }\n    };\n    const timeSlots = generateTimeSlots();\n    const handleSlotToggle = (slotId)=>{\n        const newSelectedSlots = new Set(selectedSlots);\n        if (newSelectedSlots.has(slotId)) {\n            newSelectedSlots.delete(slotId);\n        } else {\n            newSelectedSlots.add(slotId);\n        }\n        setSelectedSlots(newSelectedSlots);\n    };\n    const isValidateEnabled = selectedSlots.size > 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-12 gap-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"col-span-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 bg-gray-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                        size: \"sm\",\n                                        fw: 500,\n                                        mb: \"xs\",\n                                        children: \"\\xc0 partir de\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                        value: getStartDateForPeriod(),\n                                        onChange: (value)=>setStartDate(value || ''),\n                                        data: [\n                                            {\n                                                value: '12/06/2025',\n                                                label: '12/06/2025'\n                                            },\n                                            {\n                                                value: '25/06/2025',\n                                                label: '25/06/2025'\n                                            },\n                                            {\n                                                value: '10/07/2025',\n                                                label: '10/07/2025'\n                                            },\n                                            {\n                                                value: '10/09/2025',\n                                                label: '10/09/2025'\n                                            }\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                        size: \"sm\",\n                                        fw: 500,\n                                        mb: \"xs\",\n                                        children: \"Dur\\xe9e (min)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.NumberInput, {\n                                        value: duration,\n                                        onChange: (value)=>setDuration(Number(value)),\n                                        min: 15,\n                                        max: 120,\n                                        step: 15\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                        size: \"sm\",\n                                        fw: 500,\n                                        mb: \"xs\",\n                                        children: \"Nbre des jours\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.NumberInput, {\n                                        value: numberOfDays,\n                                        onChange: (value)=>setNumberOfDays(Number(value)),\n                                        min: 1,\n                                        max: 30\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                lineNumber: 228,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"col-span-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                    size: \"lg\",\n                                    fw: 600,\n                                    children: getDateForPeriod()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                    size: \"sm\",\n                                    color: \"dimmed\",\n                                    children: \"24\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2 max-h-96 overflow-y-auto\",\n                            children: timeSlots.map((slot)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between p-2 hover:bg-gray-50 rounded\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                    size: \"sm\",\n                                                    children: \"le\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                    size: \"sm\",\n                                                    color: \"blue\",\n                                                    fw: 500,\n                                                    children: getFormattedDateForPeriod()\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                    size: \"sm\",\n                                                    children: \"de\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                    size: \"sm\",\n                                                    color: \"red\",\n                                                    fw: 500,\n                                                    children: slot.startTime\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                    lineNumber: 285,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                    size: \"sm\",\n                                                    children: \"\\xe0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                    lineNumber: 288,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                    size: \"sm\",\n                                                    color: \"green\",\n                                                    fw: 500,\n                                                    children: slot.endTime\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: selectedSlots.has(slot.id),\n                                            onChange: ()=>handleSlotToggle(slot.id),\n                                            className: \"form-checkbox h-4 w-4 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, slot.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                            lineNumber: 276,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center mt-6 pt-4 border-t\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                            variant: selectedPeriod === '15days' ? 'filled' : 'outline',\n                                            onClick: ()=>setSelectedPeriod('15days'),\n                                            size: \"sm\",\n                                            children: \"15 jours\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                            variant: selectedPeriod === '1month' ? 'filled' : 'outline',\n                                            onClick: ()=>setSelectedPeriod('1month'),\n                                            size: \"sm\",\n                                            children: \"1 Mois\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                            variant: selectedPeriod === '3months' ? 'filled' : 'outline',\n                                            onClick: ()=>setSelectedPeriod('3months'),\n                                            size: \"sm\",\n                                            children: \"3 Mois\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                            color: isValidateEnabled ? 'blue' : 'gray',\n                                            disabled: !isValidateEnabled,\n                                            onClick: ()=>{\n                                                // Logique de validation ici\n                                                onClose();\n                                            },\n                                            children: \"Valider\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                            variant: \"outline\",\n                                            color: \"red\",\n                                            onClick: onClose,\n                                            children: \"Annuler\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                    lineNumber: 328,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                            lineNumber: 303,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                    lineNumber: 270,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                lineNumber: 269,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n        lineNumber: 227,\n        columnNumber: 5\n    }, undefined);\n};\n_s(RendezVousSelector, \"JA7kajyxwsY0NPzKL2AkP/6ymxQ=\");\n_c = RendezVousSelector;\nconst AjouterUnRendezVous = (props)=>{\n    const { opened, onClose, appointmentForm, handleSubmit, eventTitle, setEventTitle, titleOptions, setTitleOptions, newTitle, setNewTitle, patientName, setPatientName, patientlastName, setPatientlastName, openListDesPatient, eventDateDeNaissance, handleDateChange, eventAge, genderOption, handleOptionChange, eventEtatCivil, setEventEtatCivil, eventCin, setEventCin, address, setAddress, eventTelephone, setEventTelephone, email, setEmail, patientdoctor, setPatientDocteur, patientsocialSecurity, setSocialSecurity, consultationTypes, setConsultationTypes, patienttypeConsultation, setPatientTypeConsultation, setEventType, searchValue, setSearchValue, dureeDeLexamen, getEventTypeColor, newConsultationType, setNewConsultationType, newConsultationColor, setNewConsultationColor, ColorPickeropened, openedColorPicker, closeColorPicker, changeEndValue, setChangeEndValue, setDureeDeLexamen, eventAganda, setEventAganda, agendaTypes, setAgendaTypes, newAgendaType, setNewAgendaType, isWaitingList, eventDate, setEventDate, eventTime, setEventTime, eventConsultation, openListRendezVous, ListRendezVousOpened, closeListRendezVous, patientcomment, setPatientcomment, patientnotes, setPatientNotes, patientcommentairelistedattente, setPatientCommentairelistedattente, eventResourceId, setEventResourceId, eventType, checkedAppelvideo, handleAppelvideoChange, checkedRappelSms, handleRappelSmsChange, checkedRappelEmail, handleRappelEmailChange, currentPatient, waitingList, setWaitingList, setPatientModalOpen, // New props for Edit Modal\n    showEditModal, setShowEditModal, selectedEvent, setSelectedEvent, resetForm, handleEditSubmit, closeRendezVous, initialConsultationTypes } = props;\n    var _eventAge_toString, _eventAge_toString1;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Modal.Root, {\n                opened: opened,\n                onClose: onClose,\n                size: \"70%\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Modal.Overlay, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                        lineNumber: 461,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Modal.Content, {\n                        className: \"overflow-y-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Modal.Header, {\n                                style: {\n                                    height: '60px',\n                                    background: \"#3799CE\",\n                                    padding: \"11px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Modal.Title, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                fw: 600,\n                                                c: \"var(--mantine-color-white)\",\n                                                className: \"mb-2 flex gap-2 text-lg font-semibold leading-none tracking-tight\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        width: \"1em\",\n                                                        height: \"1em\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M21 7.5V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h3.5M16 2v4M8 2v4m-5 4h5m9.5 7.5L16 16.3V14\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 479,\n                                                                    columnNumber: 19\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                    cx: 16,\n                                                                    cy: 16,\n                                                                    r: 6\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 480,\n                                                                    columnNumber: 19\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 472,\n                                                            columnNumber: 17\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                        lineNumber: 466,\n                                                        columnNumber: 15\n                                                    }, undefined),\n                                                    \"Ajouter un rendez-vous\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                lineNumber: 465,\n                                                columnNumber: 13\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-muted-foreground text-sm text-[var(--mantine-color-white)]\",\n                                                children: \"Remplissez les d\\xe9tails ci-dessous pour ajouter un nouvel \\xe9v\\xe9nement.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 13\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                        lineNumber: 464,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Group, {\n                                        justify: \"flex-end\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Switch, {\n                                                defaultChecked: true,\n                                                color: \"teal\",\n                                                size: \"xs\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                lineNumber: 490,\n                                                columnNumber: 13\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-muted-foreground text-sm text-[var(--mantine-color-white)]\",\n                                                children: \"Pause\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                lineNumber: 495,\n                                                columnNumber: 13\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Modal.CloseButton, {\n                                                className: \"mantine-focus-always\",\n                                                style: {\n                                                    color: \"white\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                lineNumber: 498,\n                                                columnNumber: 13\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                        lineNumber: 489,\n                                        columnNumber: 11\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                lineNumber: 463,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Modal.Body, {\n                                style: {\n                                    padding: '0px'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"py-2 pl-4 h-[600px]\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(simplebar_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"simplebar-scrollable-y h-[calc(100%)]\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"pr-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                onSubmit: (e)=>{\n                                                    e.preventDefault();\n                                                    handleSubmit(appointmentForm.values);\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid gap-3 py-2 pr-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-4 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                                    value: eventTitle,\n                                                                    onChange: (value)=>setEventTitle(value !== null && value !== void 0 ? value : \"\"),\n                                                                    placeholder: \"Titre\",\n                                                                    data: titleOptions,\n                                                                    className: \"w-full\",\n                                                                    leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarPlus_FaMicrophone_FaMicrophoneLines_FaUserDoctor_react_icons_fa6__WEBPACK_IMPORTED_MODULE_12__.FaUserDoctor, {\n                                                                        size: 16\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 516,\n                                                                        columnNumber: 38\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 510,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Menu, {\n                                                                    width: 200,\n                                                                    shadow: \"md\",\n                                                                    closeOnItemClick: false,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Menu.Target, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Avatar, {\n                                                                                color: \"#4BA3D3\",\n                                                                                radius: \"sm\",\n                                                                                h: 36,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCheck_IconColorPicker_IconHexagonPlusFilled_tabler_icons_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                    size: 20\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                    lineNumber: 521,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                lineNumber: 520,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                            lineNumber: 519,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Menu.Dropdown, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.TextInput, {\n                                                                                leftSectionPointerEvents: \"none\",\n                                                                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarPlus_FaMicrophone_FaMicrophoneLines_FaUserDoctor_react_icons_fa6__WEBPACK_IMPORTED_MODULE_12__.FaUserDoctor, {\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                    lineNumber: 527,\n                                                                                    columnNumber: 42\n                                                                                }, void 0),\n                                                                                placeholder: \"Ajouter des titres\",\n                                                                                value: newTitle,\n                                                                                onChange: (e)=>setNewTitle(e.target.value),\n                                                                                rightSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.ActionIcon, {\n                                                                                    size: \"sm\",\n                                                                                    onClick: ()=>{\n                                                                                        if (newTitle.trim()) {\n                                                                                            const newTitleOption = {\n                                                                                                value: newTitle,\n                                                                                                label: newTitle\n                                                                                            };\n                                                                                            setTitleOptions([\n                                                                                                ...titleOptions,\n                                                                                                newTitleOption\n                                                                                            ]);\n                                                                                            setEventTitle(newTitle);\n                                                                                            setNewTitle(\"\");\n                                                                                            _mantine_notifications__WEBPACK_IMPORTED_MODULE_18__.notifications.show({\n                                                                                                title: 'Titre ajouté',\n                                                                                                message: '\"'.concat(newTitle, '\" a \\xe9t\\xe9 ajout\\xe9 \\xe0 la liste des titres'),\n                                                                                                color: 'green',\n                                                                                                autoClose: 2000\n                                                                                            });\n                                                                                        }\n                                                                                    },\n                                                                                    disabled: !newTitle.trim(),\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarPlus_FaMicrophone_FaMicrophoneLines_FaUserDoctor_react_icons_fa6__WEBPACK_IMPORTED_MODULE_12__.FaCalendarPlus, {\n                                                                                        size: 16\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                        lineNumber: 550,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                    lineNumber: 532,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                onKeyDown: (e)=>{\n                                                                                    if (e.key === 'Enter' && newTitle.trim()) {\n                                                                                        const newTitleOption = {\n                                                                                            value: newTitle,\n                                                                                            label: newTitle\n                                                                                        };\n                                                                                        setTitleOptions([\n                                                                                            ...titleOptions,\n                                                                                            newTitleOption\n                                                                                        ]);\n                                                                                        setEventTitle(newTitle);\n                                                                                        setNewTitle(\"\");\n                                                                                        _mantine_notifications__WEBPACK_IMPORTED_MODULE_18__.notifications.show({\n                                                                                            title: 'Titre ajouté',\n                                                                                            message: '\"'.concat(newTitle, '\" a \\xe9t\\xe9 ajout\\xe9 \\xe0 la liste des titres'),\n                                                                                            color: 'green',\n                                                                                            autoClose: 2000\n                                                                                        });\n                                                                                    }\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                lineNumber: 525,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                            lineNumber: 524,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 518,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.TextInput, {\n                                                                    id: \"event-nom\",\n                                                                    placeholder: \"Nom *\",\n                                                                    type: \"text\",\n                                                                    value: patientName,\n                                                                    onChange: (e)=>setPatientName(e.target.value),\n                                                                    required: true,\n                                                                    className: \"input input-bordered w-full\",\n                                                                    leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiUserFollowLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_19__.RiUserFollowLine, {\n                                                                        size: 16\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 578,\n                                                                        columnNumber: 38\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 570,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.TextInput, {\n                                                                    id: \"event-prenom\",\n                                                                    placeholder: \"Pr\\xe9nom *\",\n                                                                    type: \"text\",\n                                                                    value: patientlastName,\n                                                                    onChange: (e)=>setPatientlastName(e.target.value),\n                                                                    required: true,\n                                                                    className: \"input input-bordered w-full\",\n                                                                    leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiUserFollowLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_19__.RiUserFollowLine, {\n                                                                        size: 16\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 588,\n                                                                        columnNumber: 38\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 580,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Avatar, {\n                                                                    color: \"#4BA3D3\",\n                                                                    radius: \"sm\",\n                                                                    h: 36,\n                                                                    onClick: openListDesPatient,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MdOutlineBedroomChild_MdOutlineContentPasteSearch_MdOutlineSocialDistance_react_icons_md__WEBPACK_IMPORTED_MODULE_20__.MdOutlineContentPasteSearch, {\n                                                                        size: 20\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 591,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 590,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 509,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-4 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.TextInput, {\n                                                                    type: \"date\",\n                                                                    placeholder: \"Date de Naissance...\",\n                                                                    id: \"event-dateDeNaissance\",\n                                                                    value: eventDateDeNaissance,\n                                                                    onChange: handleDateChange,\n                                                                    required: true,\n                                                                    className: \"input input-bordered max-w-[278px] w-full\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 597,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.TextInput, {\n                                                                    type: \"text\",\n                                                                    id: \"event-age\",\n                                                                    value: (_eventAge_toString = eventAge === null || eventAge === void 0 ? void 0 : eventAge.toString()) !== null && _eventAge_toString !== void 0 ? _eventAge_toString : \"\",\n                                                                    placeholder: eventAge !== null ? eventAge.toString() : \"Veuillez entrer votre date de naissance\",\n                                                                    readOnly: true,\n                                                                    className: \"input input-bordered max-w-[278px] w-full\",\n                                                                    leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LiaAddressCardSolid_LiaBirthdayCakeSolid_react_icons_lia__WEBPACK_IMPORTED_MODULE_21__.LiaBirthdayCakeSolid, {\n                                                                        size: 16\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 617,\n                                                                        columnNumber: 38\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 606,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Radio.Group, {\n                                                                        value: genderOption,\n                                                                        onChange: handleOptionChange,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Radio, {\n                                                                                    value: \"Homme\",\n                                                                                    label: \"Homme\"\n                                                                                }, \"homme\", false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                    lineNumber: 625,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Radio, {\n                                                                                    value: \"Femme\",\n                                                                                    label: \"Femme\"\n                                                                                }, \"femme\", false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                    lineNumber: 626,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Radio, {\n                                                                                    value: \"Enfant\",\n                                                                                    label: \"Enfant\"\n                                                                                }, \"enfant\", false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                    lineNumber: 627,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                            lineNumber: 624,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 620,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 619,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 596,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                                    value: eventEtatCivil,\n                                                                    onChange: (value)=>setEventEtatCivil(value !== null && value !== void 0 ? value : \"\"),\n                                                                    placeholder: \"\\xc9tat civil\",\n                                                                    data: [\n                                                                        {\n                                                                            value: \"Célibataire\",\n                                                                            label: \"Célibataire\"\n                                                                        },\n                                                                        {\n                                                                            value: \"Marié(e)\",\n                                                                            label: \"Marié(e)\"\n                                                                        },\n                                                                        {\n                                                                            value: \"Divorcé(e)\",\n                                                                            label: \"Divorcé(e)\"\n                                                                        },\n                                                                        {\n                                                                            value: \"Veuf(ve)\",\n                                                                            label: \"Veuf(ve)\"\n                                                                        },\n                                                                        {\n                                                                            value: \"Autre chose\",\n                                                                            label: \"Autre chose\"\n                                                                        }\n                                                                    ],\n                                                                    className: \"select w-full max-w-xs\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 635,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.TextInput, {\n                                                                    placeholder: \"CIN\",\n                                                                    disabled: genderOption === 'Enfant',\n                                                                    value: eventCin,\n                                                                    onChange: (e)=>setEventCin(e.target.value),\n                                                                    styles: {\n                                                                        input: {\n                                                                            backgroundColor: genderOption === 'Enfant' ? '#f5f5f5' : undefined,\n                                                                            color: genderOption === 'Enfant' ? '#999' : undefined\n                                                                        }\n                                                                    },\n                                                                    className: \"input input-bordered mb-2 w-full\",\n                                                                    leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TbNumber_react_icons_tb__WEBPACK_IMPORTED_MODULE_23__.TbNumber, {\n                                                                        size: 16\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 660,\n                                                                        columnNumber: 38\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 648,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.TextInput, {\n                                                                    id: \"Adresse\",\n                                                                    placeholder: \"Adress\\xe9 par\",\n                                                                    value: address,\n                                                                    onChange: (e)=>setAddress(e.target.value),\n                                                                    className: \"input input-bordered mb-2 w-full\",\n                                                                    leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LiaAddressCardSolid_LiaBirthdayCakeSolid_react_icons_lia__WEBPACK_IMPORTED_MODULE_21__.LiaAddressCardSolid, {\n                                                                        size: 16\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 668,\n                                                                        columnNumber: 38\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 662,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 634,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.InputBase, {\n                                                                    id: \"T\\xe9l\\xe9phone\",\n                                                                    component: react_imask__WEBPACK_IMPORTED_MODULE_2__.IMaskInput,\n                                                                    mask: \"00-00-00-00-00\",\n                                                                    placeholder: \"T\\xe9l\\xe9phone\",\n                                                                    value: eventTelephone,\n                                                                    onAccept: (value)=>setEventTelephone(value),\n                                                                    unmask: true,\n                                                                    className: \"input input-bordered mb-2 w-full\",\n                                                                    leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiPhone_react_icons_fi__WEBPACK_IMPORTED_MODULE_25__.FiPhone, {\n                                                                        size: 16\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 683,\n                                                                        columnNumber: 38\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 674,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.TextInput, {\n                                                                    id: \"Email\",\n                                                                    placeholder: \"Email\",\n                                                                    value: email,\n                                                                    onChange: (e)=>setEmail(e.target.value),\n                                                                    className: \"input input-bordered mb-2 w-full\",\n                                                                    leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CiAt_react_icons_ci__WEBPACK_IMPORTED_MODULE_26__.CiAt, {\n                                                                        size: 16\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 691,\n                                                                        columnNumber: 38\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 685,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 673,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                                    value: patientdoctor,\n                                                                    onChange: (value)=>setPatientDocteur(value !== null && value !== void 0 ? value : \"\"),\n                                                                    placeholder: \"Docteur\",\n                                                                    data: [\n                                                                        {\n                                                                            value: \"Docteur\",\n                                                                            label: \"Docteur\"\n                                                                        },\n                                                                        {\n                                                                            value: \"dr.Kader\",\n                                                                            label: \"dr.Kader\"\n                                                                        },\n                                                                        {\n                                                                            value: \"dr.Kaders\",\n                                                                            label: \"dr.Kaders\"\n                                                                        }\n                                                                    ],\n                                                                    className: \"w-full\",\n                                                                    leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarPlus_FaMicrophone_FaMicrophoneLines_FaUserDoctor_react_icons_fa6__WEBPACK_IMPORTED_MODULE_12__.FaUserDoctor, {\n                                                                        size: 16\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 707,\n                                                                        columnNumber: 38\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 697,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                                    value: patientsocialSecurity || 'Aucune',\n                                                                    onChange: (value)=>setSocialSecurity(value || 'Aucune'),\n                                                                    placeholder: \"S\\xe9curit\\xe9 sociale\",\n                                                                    data: [\n                                                                        {\n                                                                            value: \"Aucune\",\n                                                                            label: \"Aucune\"\n                                                                        },\n                                                                        {\n                                                                            value: \"CNSS\",\n                                                                            label: \"CNSS\"\n                                                                        },\n                                                                        {\n                                                                            value: \"AMO\",\n                                                                            label: \"AMO\"\n                                                                        }\n                                                                    ],\n                                                                    className: \"w-full\",\n                                                                    leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MdOutlineBedroomChild_MdOutlineContentPasteSearch_MdOutlineSocialDistance_react_icons_md__WEBPACK_IMPORTED_MODULE_20__.MdOutlineSocialDistance, {\n                                                                        size: 16\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 719,\n                                                                        columnNumber: 38\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 709,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 696,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-4 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                                    label: \"Type de consultation\",\n                                                                    placeholder: \"Rechercher ou saisir...\",\n                                                                    data: consultationTypes,\n                                                                    value: patienttypeConsultation,\n                                                                    onChange: (value)=>{\n                                                                        setPatientTypeConsultation(value !== null && value !== void 0 ? value : \"\");\n                                                                        const selectedLabel = [\n                                                                            {\n                                                                                value: \"Visite de malade\",\n                                                                                eventType: \"visit\"\n                                                                            },\n                                                                            {\n                                                                                value: \"Visitor Counter\",\n                                                                                eventType: \"visitor-counter\"\n                                                                            },\n                                                                            {\n                                                                                value: \"Completed\",\n                                                                                eventType: \"completed\"\n                                                                            }\n                                                                        ].find((item)=>item.value === value);\n                                                                        if (selectedLabel) {\n                                                                            setEventType(selectedLabel.eventType);\n                                                                        }\n                                                                    },\n                                                                    searchable: true,\n                                                                    searchValue: searchValue,\n                                                                    onSearchChange: setSearchValue,\n                                                                    clearable: true,\n                                                                    maxDropdownHeight: 280,\n                                                                    rightSectionWidth: 70,\n                                                                    required: true,\n                                                                    rightSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"bg-[#4CAF50] text-white px-2 py-1 rounded text-xs\",\n                                                                        children: dureeDeLexamen\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 750,\n                                                                        columnNumber: 27\n                                                                    }, void 0),\n                                                                    allowDeselect: true,\n                                                                    className: \"w-full\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 725,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Menu, {\n                                                                    width: 260,\n                                                                    shadow: \"md\",\n                                                                    closeOnItemClick: false,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Menu.Target, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Avatar, {\n                                                                                color: \"#4BA3D3\",\n                                                                                radius: \"sm\",\n                                                                                h: 36,\n                                                                                mt: \"24\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCheck_IconColorPicker_IconHexagonPlusFilled_tabler_icons_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                    size: 20\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                    lineNumber: 758,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                lineNumber: 757,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                            lineNumber: 756,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Menu.Dropdown, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.TextInput, {\n                                                                                        leftSectionPointerEvents: \"none\",\n                                                                                        leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarPlus_FaMicrophone_FaMicrophoneLines_FaUserDoctor_react_icons_fa6__WEBPACK_IMPORTED_MODULE_12__.FaUserDoctor, {\n                                                                                            size: 16\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                            lineNumber: 765,\n                                                                                            columnNumber: 44\n                                                                                        }, void 0),\n                                                                                        placeholder: \"Ajouter des Consultation\",\n                                                                                        value: newConsultationType,\n                                                                                        onChange: (e)=>setNewConsultationType(e.target.value),\n                                                                                        rightSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.ActionIcon, {\n                                                                                            size: \"sm\",\n                                                                                            onClick: ()=>{\n                                                                                                if (newConsultationType.trim()) {\n                                                                                                    const newType = {\n                                                                                                        value: newConsultationType,\n                                                                                                        label: newConsultationType,\n                                                                                                        duration: dureeDeLexamen || \"15 min\"\n                                                                                                    };\n                                                                                                    setConsultationTypes([\n                                                                                                        ...consultationTypes,\n                                                                                                        newType\n                                                                                                    ]);\n                                                                                                    setPatientTypeConsultation(newConsultationType);\n                                                                                                    setNewConsultationType(\"\");\n                                                                                                    _mantine_notifications__WEBPACK_IMPORTED_MODULE_18__.notifications.show({\n                                                                                                        title: 'Type de consultation ajouté',\n                                                                                                        message: '\"'.concat(newConsultationType, '\" a \\xe9t\\xe9 ajout\\xe9'),\n                                                                                                        color: 'green',\n                                                                                                        autoClose: 2000\n                                                                                                    });\n                                                                                                }\n                                                                                            },\n                                                                                            disabled: !newConsultationType.trim(),\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarPlus_FaMicrophone_FaMicrophoneLines_FaUserDoctor_react_icons_fa6__WEBPACK_IMPORTED_MODULE_12__.FaCalendarPlus, {\n                                                                                                size: 16\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                                lineNumber: 792,\n                                                                                                columnNumber: 35\n                                                                                            }, void 0)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                            lineNumber: 770,\n                                                                                            columnNumber: 33\n                                                                                        }, void 0),\n                                                                                        onKeyDown: (e)=>{\n                                                                                            if (e.key === 'Enter' && newConsultationType.trim()) {\n                                                                                                const newType = {\n                                                                                                    value: newConsultationType,\n                                                                                                    label: newConsultationType,\n                                                                                                    duration: dureeDeLexamen || \"15 min\"\n                                                                                                };\n                                                                                                setConsultationTypes([\n                                                                                                    ...consultationTypes,\n                                                                                                    newType\n                                                                                                ]);\n                                                                                                setPatientTypeConsultation(newConsultationType);\n                                                                                                setNewConsultationType(\"\");\n                                                                                                _mantine_notifications__WEBPACK_IMPORTED_MODULE_18__.notifications.show({\n                                                                                                    title: 'Type de consultation ajouté',\n                                                                                                    message: '\"'.concat(newConsultationType, '\" a \\xe9t\\xe9 ajout\\xe9'),\n                                                                                                    color: 'green',\n                                                                                                    autoClose: 2000\n                                                                                                });\n                                                                                            }\n                                                                                        }\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                        lineNumber: 763,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Avatar, {\n                                                                                        color: newConsultationColor,\n                                                                                        radius: \"sm\",\n                                                                                        ml: 4,\n                                                                                        h: 36,\n                                                                                        onClick: openedColorPicker,\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                            fill: \"none\",\n                                                                                            viewBox: \"0 0 200 200\",\n                                                                                            style: {\n                                                                                                width: \"26px\",\n                                                                                                height: \"26px\"\n                                                                                            },\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                fill: \"#FF5178\",\n                                                                                                d: \"M100 0a100 100 0 00-50 13.398l30 51.961A40 40 0 01100 60V0z\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                                lineNumber: 824,\n                                                                                                columnNumber: 33\n                                                                                            }, undefined)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                            lineNumber: 821,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                        lineNumber: 814,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                lineNumber: 762,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                            lineNumber: 761,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 755,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Modal, {\n                                                                    opened: ColorPickeropened,\n                                                                    onClose: closeColorPicker,\n                                                                    size: \"auto\",\n                                                                    yOffset: \"18vh\",\n                                                                    xOffset: 30,\n                                                                    withCloseButton: false,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_27__.ColorPicker, {\n                                                                            defaultValue: newConsultationColor,\n                                                                            value: newConsultationColor,\n                                                                            onChange: setNewConsultationColor,\n                                                                            onChangeEnd: setChangeEndValue,\n                                                                            format: \"hex\",\n                                                                            swatches: [\n                                                                                '#2e2e2e',\n                                                                                '#868e96',\n                                                                                '#fa5252',\n                                                                                '#e64980',\n                                                                                '#be4bdb',\n                                                                                '#7950f2',\n                                                                                '#4c6ef5',\n                                                                                '#228be6',\n                                                                                '#15aabf',\n                                                                                '#12b886',\n                                                                                '#40c057',\n                                                                                '#82c91e',\n                                                                                '#fab005',\n                                                                                '#fd7e14'\n                                                                            ]\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                            lineNumber: 831,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Group, {\n                                                                            justify: \"center\",\n                                                                            mt: 8,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                                                variant: \"filled\",\n                                                                                w: \"100%\",\n                                                                                color: \"\".concat(newConsultationColor),\n                                                                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCheck_IconColorPicker_IconHexagonPlusFilled_tabler_icons_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                                    stroke: 1,\n                                                                                    size: 18\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                    lineNumber: 844,\n                                                                                    columnNumber: 42\n                                                                                }, void 0),\n                                                                                onClick: ()=>{\n                                                                                    setNewConsultationColor(changeEndValue);\n                                                                                    closeColorPicker();\n                                                                                },\n                                                                                children: \"S\\xe9lectionner cette couleur\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                lineNumber: 840,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                            lineNumber: 839,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 830,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                                    label: \"Dur\\xe9e\",\n                                                                    value: dureeDeLexamen,\n                                                                    onChange: (value)=>setDureeDeLexamen(value !== null && value !== void 0 ? value : \"\"),\n                                                                    placeholder: \"15 min\",\n                                                                    data: [\n                                                                        \"10 min\",\n                                                                        \"15 min\",\n                                                                        \"20 min\",\n                                                                        \"25 min\",\n                                                                        \"30 min\",\n                                                                        \"35 min\",\n                                                                        \"40 min\",\n                                                                        \"45 min\"\n                                                                    ],\n                                                                    className: \"select w-full max-w-xs\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 855,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                                    label: \"Agenda\",\n                                                                    value: eventAganda,\n                                                                    onChange: (value)=>setEventAganda(value !== null && value !== void 0 ? value : \"\"),\n                                                                    placeholder: \"Ajouter des Agenda\",\n                                                                    data: agendaTypes,\n                                                                    className: \"w-full\",\n                                                                    leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarPlus_FaMicrophone_FaMicrophoneLines_FaUserDoctor_react_icons_fa6__WEBPACK_IMPORTED_MODULE_12__.FaUserDoctor, {\n                                                                        size: 16\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 870,\n                                                                        columnNumber: 38\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 863,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Menu, {\n                                                                    width: 200,\n                                                                    shadow: \"md\",\n                                                                    closeOnItemClick: false,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Menu.Target, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Avatar, {\n                                                                                color: \"#4BA3D3\",\n                                                                                radius: \"sm\",\n                                                                                h: 36,\n                                                                                mt: \"24\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCheck_IconColorPicker_IconHexagonPlusFilled_tabler_icons_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                    size: 20\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                    lineNumber: 875,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                lineNumber: 874,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                            lineNumber: 873,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Menu.Dropdown, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.TextInput, {\n                                                                                leftSectionPointerEvents: \"none\",\n                                                                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarPlus_FaMicrophone_FaMicrophoneLines_FaUserDoctor_react_icons_fa6__WEBPACK_IMPORTED_MODULE_12__.FaUserDoctor, {\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                    lineNumber: 881,\n                                                                                    columnNumber: 42\n                                                                                }, void 0),\n                                                                                placeholder: \"Ajouter des Agenda\",\n                                                                                value: newAgendaType,\n                                                                                onChange: (e)=>setNewAgendaType(e.target.value),\n                                                                                rightSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.ActionIcon, {\n                                                                                    size: \"sm\",\n                                                                                    onClick: ()=>{\n                                                                                        if (newAgendaType.trim()) {\n                                                                                            const newAgendaOption = {\n                                                                                                value: newAgendaType,\n                                                                                                label: newAgendaType\n                                                                                            };\n                                                                                            setAgendaTypes([\n                                                                                                ...agendaTypes,\n                                                                                                newAgendaOption\n                                                                                            ]);\n                                                                                            setEventAganda(newAgendaType);\n                                                                                            setNewAgendaType(\"\");\n                                                                                            _mantine_notifications__WEBPACK_IMPORTED_MODULE_18__.notifications.show({\n                                                                                                title: 'Agenda ajouté',\n                                                                                                message: '\"'.concat(newAgendaType, '\" a \\xe9t\\xe9 ajout\\xe9 \\xe0 la liste des agendas'),\n                                                                                                color: 'green',\n                                                                                                autoClose: 2000\n                                                                                            });\n                                                                                        }\n                                                                                    },\n                                                                                    disabled: !newAgendaType.trim(),\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarPlus_FaMicrophone_FaMicrophoneLines_FaUserDoctor_react_icons_fa6__WEBPACK_IMPORTED_MODULE_12__.FaCalendarPlus, {\n                                                                                        size: 16\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                        lineNumber: 904,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                    lineNumber: 886,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                onKeyDown: (e)=>{\n                                                                                    if (e.key === 'Enter' && newAgendaType.trim()) {\n                                                                                        const newAgendaOption = {\n                                                                                            value: newAgendaType,\n                                                                                            label: newAgendaType\n                                                                                        };\n                                                                                        setAgendaTypes([\n                                                                                            ...agendaTypes,\n                                                                                            newAgendaOption\n                                                                                        ]);\n                                                                                        setEventAganda(newAgendaType);\n                                                                                        setNewAgendaType(\"\");\n                                                                                        _mantine_notifications__WEBPACK_IMPORTED_MODULE_18__.notifications.show({\n                                                                                            title: 'Agenda ajouté',\n                                                                                            message: '\"'.concat(newAgendaType, '\" a \\xe9t\\xe9 ajout\\xe9 \\xe0 la liste des agendas'),\n                                                                                            color: 'green',\n                                                                                            autoClose: 2000\n                                                                                        });\n                                                                                    }\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                lineNumber: 879,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                            lineNumber: 878,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 872,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 724,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        !isWaitingList && !appointmentForm.values.addToWaitingList && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mx-auto flex gap-4 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                                    size: \"12px\",\n                                                                    className: \"label\",\n                                                                    style: {\n                                                                        marginTop: \"10px\"\n                                                                    },\n                                                                    children: \"Date du RDV\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 929,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.TextInput, {\n                                                                    id: \"event-date\",\n                                                                    type: \"date\",\n                                                                    value: eventDate,\n                                                                    onChange: (e)=>setEventDate(e.target.value),\n                                                                    className: \"input input-bordered mb-2 w-64 max-w-64\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 930,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                                    size: \"12px\",\n                                                                    className: \"label\",\n                                                                    style: {\n                                                                        marginTop: \"10px\"\n                                                                    },\n                                                                    children: \"De*\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 937,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.TextInput, {\n                                                                    id: \"event-time\",\n                                                                    type: \"time\",\n                                                                    value: eventTime,\n                                                                    onChange: (e)=>setEventTime(e.target.value),\n                                                                    className: \"input input-bordered mb-2 w-64 max-w-64\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 938,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                                    size: \"12px\",\n                                                                    className: \"label\",\n                                                                    style: {\n                                                                        marginTop: \"10px\"\n                                                                    },\n                                                                    children: \"\\xe0*\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 945,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.TextInput, {\n                                                                    id: \"event-time-end\",\n                                                                    type: \"text\",\n                                                                    placeholder: eventTime !== null ? moment__WEBPACK_IMPORTED_MODULE_3___default()(eventTime, \"HH:mm\").add(parseInt(eventConsultation), \"minutes\").format(\"HH:mm\") : \"Please enter your date of birth\",\n                                                                    readOnly: true,\n                                                                    className: \"input input-bordered mb-2 w-40\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 946,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Avatar, {\n                                                                    color: \"#4BA3D3\",\n                                                                    radius: \"sm\",\n                                                                    h: 36,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ListPlus_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                                        size: 30,\n                                                                        className: \"text-[#3799CE] cursor-pointer\",\n                                                                        onClick: openListRendezVous\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 960,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 959,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Modal, {\n                                                                    opened: ListRendezVousOpened,\n                                                                    onClose: closeListRendezVous,\n                                                                    size: \"xl\",\n                                                                    centered: true,\n                                                                    withCloseButton: false,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RendezVousSelector, {\n                                                                        onClose: closeListRendezVous\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 972,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 965,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 928,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-4 mb-2 -mt-2 pr-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_30__.Textarea, {\n                                                                    id: \"event-Commentaire\",\n                                                                    value: patientcomment,\n                                                                    onChange: (event)=>{\n                                                                        var _event_currentTarget_value;\n                                                                        return setPatientcomment((_event_currentTarget_value = event.currentTarget.value) !== null && _event_currentTarget_value !== void 0 ? _event_currentTarget_value : \"\");\n                                                                    },\n                                                                    placeholder: \"Commentaire ...\",\n                                                                    className: \"w-full\",\n                                                                    rightSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarPlus_FaMicrophone_FaMicrophoneLines_FaUserDoctor_react_icons_fa6__WEBPACK_IMPORTED_MODULE_12__.FaMicrophoneLines, {\n                                                                        size: 18\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 985,\n                                                                        columnNumber: 39\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 979,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_30__.Textarea, {\n                                                                    id: \"event-Notes\",\n                                                                    value: patientnotes,\n                                                                    onChange: (event)=>{\n                                                                        var _event_currentTarget_value;\n                                                                        return setPatientNotes((_event_currentTarget_value = event.currentTarget.value) !== null && _event_currentTarget_value !== void 0 ? _event_currentTarget_value : \"\");\n                                                                    },\n                                                                    placeholder: \"Notes ...\",\n                                                                    className: \"w-full\",\n                                                                    rightSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarPlus_FaMicrophone_FaMicrophoneLines_FaUserDoctor_react_icons_fa6__WEBPACK_IMPORTED_MODULE_12__.FaMicrophoneLines, {\n                                                                        size: 18\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 993,\n                                                                        columnNumber: 39\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 987,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_30__.Textarea, {\n                                                                    id: \"event-Commentairelistedattente\",\n                                                                    value: patientcommentairelistedattente,\n                                                                    onChange: (event)=>{\n                                                                        var _event_currentTarget_value;\n                                                                        return setPatientCommentairelistedattente((_event_currentTarget_value = event.currentTarget.value) !== null && _event_currentTarget_value !== void 0 ? _event_currentTarget_value : \"\");\n                                                                    },\n                                                                    placeholder: \"Commentaire (liste d'attente)...\",\n                                                                    className: \"w-full\",\n                                                                    rightSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarPlus_FaMicrophone_FaMicrophoneLines_FaUserDoctor_react_icons_fa6__WEBPACK_IMPORTED_MODULE_12__.FaMicrophone, {\n                                                                        size: 18\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 1001,\n                                                                        columnNumber: 39\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 995,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 978,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-base-100 px-[4px] pt-[8px]\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                className: \"text-daisy flex flex-wrap gap-x-4 gap-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-1\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                                            value: eventResourceId ? eventResourceId.toString() : \"\",\n                                                                            onChange: (value)=>{\n                                                                                setEventResourceId(Number(value) || 1);\n                                                                            },\n                                                                            name: \"resourceId\",\n                                                                            placeholder: \"Room\",\n                                                                            data: [\n                                                                                {\n                                                                                    value: \"1\",\n                                                                                    label: \"Room A\"\n                                                                                },\n                                                                                {\n                                                                                    value: \"2\",\n                                                                                    label: \"Room B\"\n                                                                                }\n                                                                            ],\n                                                                            required: true,\n                                                                            className: \"select w-full max-w-xs\",\n                                                                            leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MdOutlineBedroomChild_MdOutlineContentPasteSearch_MdOutlineSocialDistance_react_icons_md__WEBPACK_IMPORTED_MODULE_20__.MdOutlineBedroomChild, {\n                                                                                size: 16\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                lineNumber: 1022,\n                                                                                columnNumber: 42\n                                                                            }, void 0)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                            lineNumber: 1009,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 1008,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                id: \"visit\",\n                                                                                type: \"radio\",\n                                                                                name: \"eventType\",\n                                                                                value: \"visit\",\n                                                                                className: \"peer hidden\",\n                                                                                checked: eventType === \"visit\",\n                                                                                onChange: (e)=>setEventType(e.target.value)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                lineNumber: 1027,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                htmlFor: \"visit\",\n                                                                                className: \"\".concat(eventType === \"visit\" ? \"peer-checked:text-[#34D1BF]\" : \"text-[var(--mantine-color-dark-0)]\", \" inline-flex h-10 w-full items-center justify-center whitespace-nowrap rounded-md px-4 py-2 text-sm font-medium uppercase\"),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    className: \"flex items-center gap-2 text-xs uppercase\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"disk-teal relative z-10 block h-[12px] w-[12px] rounded-full border-2 border-white\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                            lineNumber: 1045,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined),\n                                                                                        \"Visite de malade\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                    lineNumber: 1044,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                lineNumber: 1036,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 1026,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                id: \"visitor-counter\",\n                                                                                type: \"radio\",\n                                                                                name: \"eventType\",\n                                                                                value: \"visitor-counter\",\n                                                                                className: \"peer hidden\",\n                                                                                checked: eventType === \"visitor-counter\",\n                                                                                onChange: (e)=>setEventType(e.target.value)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                lineNumber: 1052,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                htmlFor: \"visitor-counter\",\n                                                                                className: \"\".concat(eventType === \"visitor-counter\" ? \"peer-checked:text-[#F17105]\" : \"text-[var(--mantine-color-dark-0)]\", \" inline-flex h-10 w-full items-center justify-center whitespace-nowrap rounded-md px-4 py-2 text-sm font-medium uppercase\"),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    className: \"flex items-center gap-2 text-xs uppercase\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"disk-orange relative z-10 block h-[12px] w-[12px] rounded-full border-2 border-white\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                            lineNumber: 1070,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined),\n                                                                                        \"Visitor Counter\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                    lineNumber: 1069,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                lineNumber: 1061,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 1051,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                id: \"completed\",\n                                                                                type: \"radio\",\n                                                                                name: \"eventType\",\n                                                                                value: \"completed\",\n                                                                                className: \"peer hidden\",\n                                                                                checked: eventType === \"completed\",\n                                                                                onChange: (e)=>setEventType(e.target.value)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                lineNumber: 1077,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                htmlFor: \"completed\",\n                                                                                className: \"\".concat(eventType === \"completed\" ? \"peer-checked:text-[#3799CE]\" : \"text-[var(--mantine-color-dark-0)]\", \" inline-flex h-10 w-full items-center justify-center whitespace-nowrap px-4 py-2 text-sm font-medium uppercase\"),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    className: \"flex items-center gap-2 text-xs uppercase\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"disk-azure relative z-10 block h-[12px] w-[12px] rounded-full border-2 border-white\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                            lineNumber: 1095,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined),\n                                                                                        \"Completed\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                    lineNumber: 1094,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                lineNumber: 1086,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 1076,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                id: \"diagnosis\",\n                                                                                type: \"radio\",\n                                                                                name: \"eventType\",\n                                                                                value: \"diagnosis\",\n                                                                                checked: eventType === \"diagnosis\",\n                                                                                className: \"peer hidden\",\n                                                                                onChange: (e)=>setEventType(e.target.value)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                lineNumber: 1102,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                htmlFor: \"diagnosis\",\n                                                                                className: \"\".concat(eventType === \"diagnosis\" ? \"peer-checked:text-[#F3124E]\" : \"text-[var(--mantine-color-dark-0)]\", \" inline-flex h-10 w-full items-center justify-center whitespace-nowrap px-4 py-2 text-sm font-medium uppercase\"),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    className: \"flex items-center gap-2 text-xs uppercase\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"disk-red relative z-10 block h-[12px] w-[12px] rounded-full border-2 border-white\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                            lineNumber: 1120,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined),\n                                                                                        \"Re-diagnose\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                    lineNumber: 1119,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                lineNumber: 1111,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 1101,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                lineNumber: 1007,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 1006,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 pr-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Group, {\n                                                                    gap: \"xs\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Switch, {\n                                                                            color: \"teal\",\n                                                                            size: \"xs\",\n                                                                            label: \"Add to Waiting List\",\n                                                                            checked: appointmentForm.values.addToWaitingList,\n                                                                            onChange: (event)=>{\n                                                                                appointmentForm.setFieldValue('addToWaitingList', event.currentTarget.checked);\n                                                                                appointmentForm.setFieldValue('removeFromCalendar', event.currentTarget.checked);\n                                                                            },\n                                                                            thumbIcon: appointmentForm.values.addToWaitingList ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCheck_IconColorPicker_IconHexagonPlusFilled_tabler_icons_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                                                size: 12,\n                                                                                color: \"var(--mantine-color-teal-6)\",\n                                                                                stroke: 3\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                lineNumber: 1142,\n                                                                                columnNumber: 31\n                                                                            }, void 0) : null\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                            lineNumber: 1131,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Switch, {\n                                                                            checked: checkedAppelvideo,\n                                                                            onChange: handleAppelvideoChange,\n                                                                            color: \"teal\",\n                                                                            size: \"xs\",\n                                                                            label: \"Appel video\",\n                                                                            thumbIcon: checkedAppelvideo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCheck_IconColorPicker_IconHexagonPlusFilled_tabler_icons_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                                                size: 12,\n                                                                                color: \"var(--mantine-color-teal-6)\",\n                                                                                stroke: 3\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                lineNumber: 1157,\n                                                                                columnNumber: 31\n                                                                            }, void 0) : null\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                            lineNumber: 1149,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Switch, {\n                                                                            checked: checkedRappelSms,\n                                                                            onChange: handleRappelSmsChange,\n                                                                            color: \"teal\",\n                                                                            size: \"xs\",\n                                                                            label: \"Rappel Sms\",\n                                                                            thumbIcon: checkedRappelSms ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCheck_IconColorPicker_IconHexagonPlusFilled_tabler_icons_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                                                size: 12,\n                                                                                color: \"var(--mantine-color-teal-6)\",\n                                                                                stroke: 3\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                lineNumber: 1171,\n                                                                                columnNumber: 31\n                                                                            }, void 0) : null\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                            lineNumber: 1163,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Switch, {\n                                                                            checked: checkedRappelEmail,\n                                                                            onChange: handleRappelEmailChange,\n                                                                            color: \"teal\",\n                                                                            size: \"xs\",\n                                                                            label: \"Rappel e-mail\",\n                                                                            thumbIcon: checkedRappelEmail ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCheck_IconColorPicker_IconHexagonPlusFilled_tabler_icons_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                                                size: 12,\n                                                                                color: \"var(--mantine-color-teal-6)\",\n                                                                                stroke: 3\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                lineNumber: 1185,\n                                                                                columnNumber: 31\n                                                                            }, void 0) : null\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                            lineNumber: 1177,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 1130,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                                    type: \"submit\",\n                                                                    className: \"btn mb-2 bg-[#03A684] text-[var(--mantine-Button-label-MB)] hover:bg-[#03A684]/90\",\n                                                                    onClick: ()=>{\n                                                                        onClose();\n                                                                    },\n                                                                    children: currentPatient ? \"Enregistrer\" : \"Ajouter\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 1193,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                currentPatient && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                                    color: \"red\",\n                                                                    onClick: ()=>{\n                                                                        if (currentPatient) {\n                                                                            setWaitingList(waitingList.filter((p)=>p.id !== currentPatient.id));\n                                                                            setPatientModalOpen(false);\n                                                                        }\n                                                                    },\n                                                                    className: \"btn mb-2 bg-[#F3124E] text-[var(--mantine-Button-label-MB)] hover:bg-[#F3124E]/90\",\n                                                                    children: \"Supprimer\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 1203,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                                    onClick: ()=>{\n                                                                        onClose();\n                                                                    },\n                                                                    className: \"btn mb-2 bg-[#F5A524] text-[var(--mantine-Button-label-MB)] hover:bg-[#F5A524]/90\",\n                                                                    children: \"Annuler\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 1216,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 1129,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                    lineNumber: 507,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                lineNumber: 506,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                            lineNumber: 505,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                        lineNumber: 504,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                    lineNumber: 503,\n                                    columnNumber: 11\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                lineNumber: 502,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                        lineNumber: 462,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                lineNumber: 456,\n                columnNumber: 5\n            }, undefined),\n            showEditModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Modal.Root, {\n                opened: true,\n                onClose: ()=>{\n                    resetForm();\n                    setShowEditModal(false);\n                },\n                size: \"70%\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Modal.Overlay, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                        lineNumber: 1244,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Modal.Content, {\n                        className: \"overflow-y-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Modal.Header, {\n                                style: {\n                                    height: '60px',\n                                    background: \"#3799CE\",\n                                    padding: \"11px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Modal.Title, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                fw: 600,\n                                                c: \"var(--mantine-color-white)\",\n                                                className: \"mb-2 flex gap-2 text-lg font-semibold leading-none tracking-tight\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        width: \"1em\",\n                                                        height: \"1em\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M21 7.5V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h3.5M16 2v4M8 2v4m-5 4h5m9.5 7.5L16 16.3V14\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 1262,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                    cx: 16,\n                                                                    cy: 16,\n                                                                    r: 6\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 1263,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 1255,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                        lineNumber: 1249,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"Modifier rendez-vous\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                lineNumber: 1248,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-muted-foreground text-sm text-[var(--mantine-color-white)]\",\n                                                children: \"Modifiez les d\\xe9tails de l'\\xe9v\\xe9nement ci-dessous\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                lineNumber: 1268,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                        lineNumber: 1247,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Modal.CloseButton, {\n                                        className: \"mantine-focus-always text-[var(--mantine-color-white)] hover:text-[#868e96]\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                        lineNumber: 1272,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                lineNumber: 1246,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Modal.Body, {\n                                style: {\n                                    padding: '0px'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"py-2 pl-4 h-[600px]\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(simplebar_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"simplebar-scrollable-y h-[calc(100%)]\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"pr-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                onSubmit: handleEditSubmit,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid gap-3 py-2 pr-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-4 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                                    value: eventTitle,\n                                                                    onChange: (value)=>setEventTitle(value !== null && value !== void 0 ? value : \"\"),\n                                                                    placeholder: \"Titre\",\n                                                                    data: [\n                                                                        {\n                                                                            value: \"Titre1\",\n                                                                            label: \"Titre1\"\n                                                                        },\n                                                                        {\n                                                                            value: \"Titre2\",\n                                                                            label: \"Titre2\"\n                                                                        },\n                                                                        {\n                                                                            value: \"Titre3\",\n                                                                            label: \"Titre3\"\n                                                                        }\n                                                                    ],\n                                                                    className: \"w-full\",\n                                                                    leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarPlus_FaMicrophone_FaMicrophoneLines_FaUserDoctor_react_icons_fa6__WEBPACK_IMPORTED_MODULE_12__.FaUserDoctor, {\n                                                                        size: 16\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 1291,\n                                                                        columnNumber: 40\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 1281,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.TextInput, {\n                                                                    id: \"event-nom\",\n                                                                    placeholder: \"Nom *\",\n                                                                    type: \"text\",\n                                                                    value: patientName,\n                                                                    onChange: (e)=>setPatientName(e.target.value),\n                                                                    required: true,\n                                                                    className: \"input input-bordered w-full\",\n                                                                    leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiUserFollowLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_19__.RiUserFollowLine, {\n                                                                        size: 16\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 1301,\n                                                                        columnNumber: 40\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 1293,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.TextInput, {\n                                                                    id: \"event-prenom\",\n                                                                    placeholder: \"Pr\\xe9nom *\",\n                                                                    type: \"text\",\n                                                                    value: patientlastName,\n                                                                    onChange: (e)=>setPatientlastName(e.target.value),\n                                                                    required: true,\n                                                                    className: \"input input-bordered w-full\",\n                                                                    leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiUserFollowLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_19__.RiUserFollowLine, {\n                                                                        size: 16\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 1311,\n                                                                        columnNumber: 40\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 1303,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Avatar, {\n                                                                    color: \"#4BA3D3\",\n                                                                    radius: \"sm\",\n                                                                    h: 36,\n                                                                    onClick: openListDesPatient,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MdOutlineBedroomChild_MdOutlineContentPasteSearch_MdOutlineSocialDistance_react_icons_md__WEBPACK_IMPORTED_MODULE_20__.MdOutlineContentPasteSearch, {\n                                                                        size: 20\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 1314,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 1313,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 1280,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-4 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.TextInput, {\n                                                                    type: \"date\",\n                                                                    placeholder: \"Date de Naissance...\",\n                                                                    id: \"event-birth_date\",\n                                                                    value: eventDateDeNaissance,\n                                                                    onChange: handleDateChange,\n                                                                    required: true,\n                                                                    className: \"input input-bordered max-w-[278px] w-full\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 1319,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.TextInput, {\n                                                                    type: \"text\",\n                                                                    id: \"event-age\",\n                                                                    value: (_eventAge_toString1 = eventAge === null || eventAge === void 0 ? void 0 : eventAge.toString()) !== null && _eventAge_toString1 !== void 0 ? _eventAge_toString1 : \"\",\n                                                                    placeholder: eventAge !== null ? eventAge.toString() : \"Veuillez entrer votre date de naissance\",\n                                                                    readOnly: true,\n                                                                    className: \"input input-bordered max-w-[278px] w-full\",\n                                                                    leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LiaAddressCardSolid_LiaBirthdayCakeSolid_react_icons_lia__WEBPACK_IMPORTED_MODULE_21__.LiaBirthdayCakeSolid, {\n                                                                        size: 16\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 1339,\n                                                                        columnNumber: 40\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 1328,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Radio.Group, {\n                                                                        value: genderOption,\n                                                                        onChange: handleOptionChange,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Radio, {\n                                                                                    value: \"Homme\",\n                                                                                    label: \"Homme\"\n                                                                                }, \"homme\", false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                    lineNumber: 1347,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Radio, {\n                                                                                    value: \"Femme\",\n                                                                                    label: \"Femme\"\n                                                                                }, \"femme\", false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                    lineNumber: 1348,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Radio, {\n                                                                                    value: \"Enfant\",\n                                                                                    label: \"Enfant\"\n                                                                                }, \"enfant\", false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                    lineNumber: 1349,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                            lineNumber: 1346,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 1342,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 1341,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 1318,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                                    value: eventEtatCivil,\n                                                                    onChange: (value)=>setEventEtatCivil(value !== null && value !== void 0 ? value : \"\"),\n                                                                    placeholder: \"Etat civil\",\n                                                                    data: [\n                                                                        {\n                                                                            value: \"Célibataire\",\n                                                                            label: \"Célibataire\"\n                                                                        },\n                                                                        {\n                                                                            value: \"Marié\",\n                                                                            label: \"Marié\"\n                                                                        },\n                                                                        {\n                                                                            value: \"Autre chose\",\n                                                                            label: \"Autre chose\"\n                                                                        }\n                                                                    ],\n                                                                    className: \"select w-full max-w-xs\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 1356,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                genderOption !== \"Enfant\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.TextInput, {\n                                                                    id: \"Cin\",\n                                                                    placeholder: \"Cin\",\n                                                                    value: eventCin,\n                                                                    onChange: (e)=>setEventCin(e.target.value),\n                                                                    className: \"input input-bordered mb-2 w-full\",\n                                                                    leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TbNumber_react_icons_tb__WEBPACK_IMPORTED_MODULE_23__.TbNumber, {\n                                                                        size: 16\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 1374,\n                                                                        columnNumber: 42\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 1368,\n                                                                    columnNumber: 27\n                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.TextInput, {\n                                                                    id: \"Cin\",\n                                                                    placeholder: \"Cin\",\n                                                                    disabled: true,\n                                                                    className: \"input input-bordered mb-2 w-full\",\n                                                                    leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TbNumber_react_icons_tb__WEBPACK_IMPORTED_MODULE_23__.TbNumber, {\n                                                                        size: 16\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 1382,\n                                                                        columnNumber: 42\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 1377,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.TextInput, {\n                                                                    id: \"Adresse\",\n                                                                    placeholder: \"Adress\\xe9 par\",\n                                                                    value: address,\n                                                                    onChange: (e)=>setAddress(e.target.value),\n                                                                    className: \"input input-bordered mb-2 w-full\",\n                                                                    leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LiaAddressCardSolid_LiaBirthdayCakeSolid_react_icons_lia__WEBPACK_IMPORTED_MODULE_21__.LiaAddressCardSolid, {\n                                                                        size: 16\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 1391,\n                                                                        columnNumber: 40\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 1385,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 1355,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.InputBase, {\n                                                                    id: \"T\\xe9l\\xe9phone\",\n                                                                    component: react_imask__WEBPACK_IMPORTED_MODULE_2__.IMaskInput,\n                                                                    mask: \"00-00-00-00-00\",\n                                                                    placeholder: \"T\\xe9l\\xe9phone\",\n                                                                    value: eventTelephone,\n                                                                    onAccept: (value)=>setEventTelephone(value),\n                                                                    unmask: true,\n                                                                    className: \"input input-bordered mb-2 w-full\",\n                                                                    leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiPhone_react_icons_fi__WEBPACK_IMPORTED_MODULE_25__.FiPhone, {\n                                                                        size: 16\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 1405,\n                                                                        columnNumber: 40\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 1396,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.TextInput, {\n                                                                    id: \"Email\",\n                                                                    placeholder: \"Email\",\n                                                                    value: email,\n                                                                    onChange: (e)=>setEmail(e.target.value),\n                                                                    className: \"input input-bordered mb-2 w-full\",\n                                                                    leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CiAt_react_icons_ci__WEBPACK_IMPORTED_MODULE_26__.CiAt, {\n                                                                        size: 16\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 1413,\n                                                                        columnNumber: 40\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 1407,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 1395,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                    lineNumber: 1279,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                lineNumber: 1278,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                            lineNumber: 1277,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                        lineNumber: 1276,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                    lineNumber: 1275,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                lineNumber: 1274,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                        lineNumber: 1245,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                lineNumber: 1236,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_c1 = AjouterUnRendezVous;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AjouterUnRendezVous);\nvar _c, _c1;\n$RefreshReg$(_c, \"RendezVousSelector\");\n$RefreshReg$(_c1, \"AjouterUnRendezVous\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Appointments/overview/AjouterUnRendezVous.tsx\n"));

/***/ })

});