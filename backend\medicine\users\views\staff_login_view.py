from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status, permissions
from django.contrib.auth import get_user_model, authenticate
from rest_framework_simplejwt.tokens import RefreshToken
import logging

# Set up logging
logger = logging.getLogger(__name__)

User = get_user_model()

class StaffLoginView(APIView):
    """
    View to handle staff login specifically.
    This is a specialized endpoint that handles the nuances of staff authentication.
    """
    permission_classes = [permissions.AllowAny]

    def post(self, request, *args, **kwargs):
        email = request.data.get('email')
        password = request.data.get('password')

        if not email or not password:
            return Response(
                {"error": "Email and password are required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        logger.info(f"Staff login attempt for email: {email}")

        # Try to authenticate the user
        user = authenticate(request, email=email, password=password)

        if not user:
            logger.warning(f"Authentication failed for email: {email}")
            return Response(
                {"error": "Invalid email or password"},
                status=status.HTTP_401_UNAUTHORIZED
            )

        # Check if the user is a staff member or can be treated as one
        is_staff = user.user_type == 'staff'
        is_admin = user.user_type == 'admin'
        is_assistant = user.user_type == 'assistant'

        logger.info(f"User type for {email}: {user.user_type}")

        # Allow staff, admin, or assistant users to log in through this endpoint
        if not (is_staff or is_admin or is_assistant):
            logger.warning(f"User {email} is not staff, admin, or assistant")
            return Response(
                {"error": "This login endpoint is only for staff, admin, or assistant accounts"},
                status=status.HTTP_403_FORBIDDEN
            )

        # Generate tokens
        refresh = RefreshToken.for_user(user)

        # Get assigned doctor information if available
        assigned_doctor = None
        assigned_doctor_name = None

        try:
            # Try different ways to get the assigned doctor
            if hasattr(user, 'staff_profile') and hasattr(user.staff_profile, 'assigned_doctor'):
                assigned_doctor = str(user.staff_profile.assigned_doctor.id)
                assigned_doctor_name = f"{user.staff_profile.assigned_doctor.first_name} {user.staff_profile.assigned_doctor.last_name}"
                logger.info(f"Staff {email} is assigned to doctor via staff_profile: {assigned_doctor_name}")
            elif hasattr(user, 'assigned_doctor'):
                assigned_doctor = str(user.assigned_doctor.id)
                assigned_doctor_name = f"{user.assigned_doctor.first_name} {user.assigned_doctor.last_name}"
                logger.info(f"Staff {email} is assigned to doctor via direct field: {assigned_doctor_name}")
            else:
                logger.warning(f"No assigned doctor found for {email}")
        except Exception as e:
            logger.error(f"Error getting assigned doctor for {email}: {e}", exc_info=True)

        # Return the tokens and user information
        return Response({
            'access': str(refresh.access_token),
            'refresh': str(refresh),
            'user_type': 'staff',  # Always return 'staff' regardless of actual user type
            'assigned_doctor': assigned_doctor,
            'assigned_doctor_name': assigned_doctor_name,
            'user_id': str(user.id),
            'email': user.email,
            'first_name': user.first_name,
            'last_name': user.last_name
        })
