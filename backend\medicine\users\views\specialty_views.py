from rest_framework import generics, permissions, status
from rest_framework.response import Response
from rest_framework.views import APIView
from django.contrib.auth import get_user_model
from django.shortcuts import get_object_or_404

from ..models import Specialty
from ..serializers.specialty_serializers import SpecialtySerializer, SpecialtyDetailSerializer

User = get_user_model()

class SpecialtyListView(generics.ListAPIView):
    """
    View to list all active specialties.
    """
    queryset = Specialty.objects.filter(is_active=True)
    serializer_class = SpecialtySerializer
    permission_classes = [permissions.AllowAny]

class SpecialtyDetailView(generics.RetrieveAPIView):
    """
    View to retrieve a specific specialty and its doctors.
    """
    queryset = Specialty.objects.filter(is_active=True)
    serializer_class = SpecialtyDetailSerializer
    permission_classes = [permissions.AllowAny]
    lookup_field = 'slug'

class DoctorsBySpecialtyView(APIView):
    """
    View to list all doctors in a specific specialty.
    """
    permission_classes = [permissions.AllowAny]
    
    def get(self, request, slug):
        specialty = get_object_or_404(Specialty, slug=slug, is_active=True)
        
        # Get all active doctors in this specialty
        doctors = specialty.doctors.filter(
            is_active=True,
            user_type='doctor'
        )
        
        # Import here to avoid circular imports
        from ..views.user_serializers import DoctorSerializer
        
        serializer = DoctorSerializer(doctors, many=True)
        return Response({
            'specialty': SpecialtySerializer(specialty).data,
            'doctors': serializer.data
        })
