"""
Modèles pour les ensembles dentaires (<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>f<PERSON><PERSON>)
avec restrictions d'âge: 13.5 ans, 12 ans, 7.5 ans, 6 ans, moins de 6 ans
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator, MaxValueValidator
from .base import DentistryBaseModel
from .patient import DentistryPatient


class DentalSet(DentistryBaseModel):
    """
    Ensemble dentaire (Général, Supérieur, Inférieur)
    """
    SET_TYPES = [
        ('general', _('Ensemble Général')),
        ('upper', _('Ensemble Supérieur')),
        ('lower', _('Ensemble Inférieur')),
    ]

    AGE_RESTRICTIONS = [
        (13.5, _('13.5 ans et plus')),
        (12.0, _('12 ans à 13.5 ans')),
        (7.5, _('7.5 ans à 12 ans')),
        (6.0, _('6 ans à 7.5 ans')),
        (0.0, _('Moins de 6 ans')),
    ]

    name = models.CharField(
        max_length=100,
        verbose_name=_("Nom de l'ensemble")
    )

    set_type = models.CharField(
        max_length=20,
        choices=SET_TYPES,
        verbose_name=_("Type d'ensemble")
    )

    age_restriction = models.FloatField(
        choices=AGE_RESTRICTIONS,
        default=0.0,
        verbose_name=_("Restriction d'âge"),
        help_text=_("Tranche d'âge pour cet ensemble")
    )

    description = models.TextField(
        blank=True,
        verbose_name=_("Description")
    )

    is_active = models.BooleanField(
        default=True,
        verbose_name=_("Actif")
    )

    class Meta:
        verbose_name = _("Ensemble Dentaire")
        verbose_name_plural = _("Ensembles Dentaires")
        unique_together = ('set_type', 'age_restriction')
        ordering = ['set_type', '-age_restriction']

    def __str__(self):
        return f"{self.get_set_type_display()} - {self.get_age_restriction_display()}"


class ToothButton(DentistryBaseModel):
    """
    Bouton de dent avec nom, numéro et signe +
    """
    dental_set = models.ForeignKey(
        DentalSet,
        on_delete=models.CASCADE,
        related_name="tooth_buttons",
        verbose_name=_("Ensemble dentaire")
    )

    patient = models.ForeignKey(
        DentistryPatient,
        on_delete=models.CASCADE,
        related_name="tooth_buttons",
        verbose_name=_("Patient")
    )

    # Numéro de la dent (système FDI)
    tooth_number = models.PositiveSmallIntegerField(
        validators=[MinValueValidator(11), MaxValueValidator(85)],
        verbose_name=_("Numéro de la dent"),
        help_text=_("Numéro FDI: 11-18, 21-28 (supérieur), 31-38, 41-48 (inférieur), 51-55, 61-65, 71-75, 81-85 (primaires)")
    )

    # Nom de la dent
    tooth_name = models.CharField(
        max_length=200,
        verbose_name=_("Nom de la dent"),
        help_text=_("Ex: Incisive centrale supérieure droite")
    )

    # Position dans le quadrant
    quadrant_position = models.PositiveSmallIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(8)],
        verbose_name=_("Position dans le quadrant")
    )

    # Quadrant
    QUADRANTS = [
        ('upper_right', _('Supérieur droit')),
        ('upper_left', _('Supérieur gauche')),
        ('lower_left', _('Inférieur gauche')),
        ('lower_right', _('Inférieur droit')),
    ]

    quadrant = models.CharField(
        max_length=20,
        choices=QUADRANTS,
        verbose_name=_("Quadrant")
    )

    # État du bouton (+ ou -)
    is_expanded = models.BooleanField(
        default=False,
        verbose_name=_("Bouton développé"),
        help_text=_("True si les options sont affichées")
    )

    # Visibilité
    is_visible = models.BooleanField(
        default=True,
        verbose_name=_("Visible")
    )

    class Meta:
        verbose_name = _("Bouton de Dent")
        verbose_name_plural = _("Boutons de Dents")
        unique_together = ('dental_set', 'patient', 'tooth_number')
        ordering = ['tooth_number']

    def __str__(self):
        return f"Dent {self.tooth_number} - {self.tooth_name}"

    @property
    def is_upper_jaw(self):
        """Retourne True si la dent est dans la mâchoire supérieure"""
        return self.tooth_number in range(11, 29)

    @property
    def is_lower_jaw(self):
        """Retourne True si la dent est dans la mâchoire inférieure"""
        return self.tooth_number in range(31, 49)


class SpecialtyField(DentistryBaseModel):
    """
    Champs de saisie pour les spécialisations
    """
    SPECIALTIES = [
        ('esthetic', _('Dentisterie Esthétique')),
        ('therapeutic', _('Thérapeutique')),
        ('prosthodontics', _('Prothèses')),
        ('surgery', _('Chirurgie')),
        ('orthodontics', _('Orthodontie')),
    ]

    tooth_button = models.ForeignKey(
        ToothButton,
        on_delete=models.CASCADE,
        related_name="specialty_fields",
        verbose_name=_("Bouton de dent")
    )

    specialty_type = models.CharField(
        max_length=20,
        choices=SPECIALTIES,
        verbose_name=_("Type de spécialisation")
    )

    # Restrictions d'âge pour cette spécialisation
    min_age_required = models.FloatField(
        default=6.0,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        verbose_name=_("Âge minimum requis"),
        help_text=_("Âge minimum pour cette spécialisation")
    )

    # Champs de couleur
    color_value = models.CharField(
        max_length=7,
        default="#FFFFFF",
        verbose_name=_("Couleur"),
        help_text=_("Couleur hexadécimale (ex: #FFFFFF)")
    )

    # Options masquer/afficher
    is_hidden = models.BooleanField(
        default=False,
        verbose_name=_("Masqué")
    )

    # Champs spécifiques selon l'âge
    # Options pour 13.5+ ans
    orthodontic_complete = models.BooleanField(
        default=False,
        verbose_name=_("Traitement orthodontique complet"),
        help_text=_("Disponible à partir de 13.5 ans")
    )

    maxillofacial_surgery = models.BooleanField(
        default=False,
        verbose_name=_("Chirurgie maxillo-faciale"),
        help_text=_("Disponible à partir de 13.5 ans")
    )

    dental_implants = models.BooleanField(
        default=False,
        verbose_name=_("Implants dentaires"),
        help_text=_("Disponible à partir de 13.5 ans")
    )

    # Options pour 12-13.5 ans
    preventive_orthodontics = models.BooleanField(
        default=False,
        verbose_name=_("Orthodontie préventive"),
        help_text=_("Disponible de 12 à 13.5 ans")
    )

    groove_sealants = models.BooleanField(
        default=False,
        verbose_name=_("Scellants de sillons"),
        help_text=_("Disponible de 12 à 13.5 ans")
    )

    scheduled_extractions = models.BooleanField(
        default=False,
        verbose_name=_("Extractions programmées"),
        help_text=_("Disponible de 12 à 13.5 ans")
    )

    # Notes
    notes = models.TextField(
        blank=True,
        verbose_name=_("Notes")
    )

    # Priorité
    priority = models.PositiveSmallIntegerField(
        default=1,
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        verbose_name=_("Priorité"),
        help_text=_("Priorité de traitement (1-5)")
    )

    class Meta:
        verbose_name = _("Champ de Spécialisation")
        verbose_name_plural = _("Champs de Spécialisations")
        unique_together = ('tooth_button', 'specialty_type')
        ordering = ['specialty_type']

    def __str__(self):
        return f"{self.tooth_button} - {self.get_specialty_type_display()}"

    def is_available_for_age(self, patient_age):
        """Vérifie si cette spécialisation est disponible pour l'âge du patient"""
        return patient_age >= self.min_age_required

    def get_available_age_options(self, patient_age):
        """Retourne les options disponibles selon l'âge du patient"""
        options = []

        if patient_age >= 13.5:
            options.extend([
                'orthodontic_complete',
                'maxillofacial_surgery',
                'dental_implants'
            ])

        if 12 <= patient_age < 13.5:
            options.extend([
                'preventive_orthodontics',
                'groove_sealants',
                'scheduled_extractions'
            ])

        return options


class ReplacementButton(DentistryBaseModel):
    """
    Boutons de remplacement spécifiques aux spécialisations
    """
    specialty_field = models.ForeignKey(
        SpecialtyField,
        on_delete=models.CASCADE,
        related_name="replacement_buttons",
        verbose_name=_("Champ de spécialisation")
    )

    button_name = models.CharField(
        max_length=100,
        verbose_name=_("Nom du bouton")
    )

    button_action = models.CharField(
        max_length=50,
        verbose_name=_("Action du bouton"),
        help_text=_("Action à effectuer lors du clic")
    )

    # Couleur associée au bouton
    button_color = models.CharField(
        max_length=7,
        default="#007BFF",
        verbose_name=_("Couleur du bouton")
    )

    # Ordre d'affichage
    display_order = models.PositiveSmallIntegerField(
        default=0,
        verbose_name=_("Ordre d'affichage")
    )

    is_active = models.BooleanField(
        default=True,
        verbose_name=_("Actif")
    )

    class Meta:
        verbose_name = _("Bouton de Remplacement")
        verbose_name_plural = _("Boutons de Remplacement")
        ordering = ['display_order', 'button_name']

    def __str__(self):
        return f"{self.specialty_field} - {self.button_name}"


class DentalSetConfiguration(DentistryBaseModel):
    """
    Configuration globale pour les ensembles dentaires
    """
    patient = models.OneToOneField(
        DentistryPatient,
        on_delete=models.CASCADE,
        related_name="dental_set_config",
        verbose_name=_("Patient")
    )

    # Ensemble actif
    active_set_type = models.CharField(
        max_length=20,
        choices=DentalSet.SET_TYPES,
        default='general',
        verbose_name=_("Ensemble actif")
    )

    # Âge du patient pour les restrictions
    patient_age = models.FloatField(
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        verbose_name=_("Âge du patient"),
        help_text=_("Âge en années (ex: 12.5)")
    )

    # Dernière modification
    last_modified = models.DateTimeField(
        auto_now=True,
        verbose_name=_("Dernière modification")
    )

    class Meta:
        verbose_name = _("Configuration d'Ensemble Dentaire")
        verbose_name_plural = _("Configurations d'Ensembles Dentaires")

    def __str__(self):
        return f"{self.patient} - {self.get_active_set_type_display()}"

    def get_available_age_category(self):
        """Retourne la catégorie d'âge appropriée"""
        if self.patient_age >= 13.5:
            return 13.5
        elif self.patient_age >= 12.0:
            return 12.0
        elif self.patient_age >= 7.5:
            return 7.5
        elif self.patient_age >= 6.0:
            return 6.0
        else:
            return 0.0
