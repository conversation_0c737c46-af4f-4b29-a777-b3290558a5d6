from django.core.management.base import BaseCommand
from subscriptions.models import SubscriptionPackage

class Command(BaseCommand):
    help = 'Seeds the database with initial subscription packages'

    def handle(self, *args, **options):
        # Check if packages already exist
        if SubscriptionPackage.objects.exists():
            self.stdout.write(self.style.WARNING('Subscription packages already exist. Skipping seeding.'))
            return

        # Create Basic package
        basic = SubscriptionPackage.objects.create(
            name='Basic',
            description='Essential features for small practices',
            max_assistants=2,
            max_users=3,
            max_specialties=1,
            price_monthly='99.00',
            price_yearly='999.00',
            features=[
                'Patient management',
                'Appointment scheduling',
                'Basic reporting',
                'Email notifications',
                'Online booking'
            ],
            is_active=True
        )

        # Create Standard package
        standard = SubscriptionPackage.objects.create(
            name='Standard',
            description='Comprehensive solution for growing practices',
            max_assistants=5,
            max_users=10,
            max_specialties=3,
            price_monthly='199.00',
            price_yearly='1999.00',
            features=[
                'All Basic features',
                'Advanced reporting',
                'Multiple specialties',
                'SMS notifications',
                'Patient portal access',
                'Electronic prescriptions',
                'Lab results integration'
            ],
            is_active=True
        )

        # Create Premium package
        premium = SubscriptionPackage.objects.create(
            name='Premium',
            description='Complete solution for established practices',
            max_assistants=10,
            max_users=25,
            max_specialties=5,
            price_monthly='299.00',
            price_yearly='2999.00',
            features=[
                'All Standard features',
                'Custom branding',
                'Priority support',
                'Advanced analytics',
                'Telemedicine',
                'Multiple locations',
                'API access',
                'Custom integrations',
                'HIPAA compliant storage'
            ],
            is_active=True
        )

        self.stdout.write(self.style.SUCCESS('Successfully created subscription packages:'))
        self.stdout.write(f'- {basic.name}: ${basic.price_monthly}/month or ${basic.price_yearly}/year')
        self.stdout.write(f'- {standard.name}: ${standard.price_monthly}/month or ${standard.price_yearly}/year')
        self.stdout.write(f'- {premium.name}: ${premium.price_monthly}/month or ${premium.price_yearly}/year')
