# backend/dental_medicine/dentistry/models_estimates.py

from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model
from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType
import uuid
import json

User = get_user_model()

class EstimateSession(models.Model):
    """
    Session d'estimation pour sauvegarder les modifications dentaires
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # NOUVEAU: Liaison directe avec le patient
    patient = models.ForeignKey(
        'DentalPatient',
        on_delete=models.CASCADE,
        null=True,  # Temporaire pour migration
        blank=True,
        related_name='estimate_sessions',
        verbose_name=_('Patient')
    )

    # Identification de la session
    session_name = models.CharField(max_length=200, default='Session par défaut')

    # DEPRECATED: Garder pour compatibilité temporaire
    external_patient_id = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        help_text="ID du patient externe - DEPRECATED: utiliser patient"
    )

    # Métadonnées
    created_by = models.Foreign<PERSON>ey(User, on_delete=models.SET_NULL, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Statut de la session
    is_active = models.BooleanField(default=True)
    is_completed = models.BooleanField(default=False)

    class Meta:
        ordering = ['-updated_at']
        verbose_name = _('Estimate Session')
        verbose_name_plural = _('Estimate Sessions')

    def __str__(self):
        return f"{self.session_name} - {self.created_at.strftime('%Y-%m-%d')}"


class ToothModificationEstimate(models.Model):
    """
    Modifications dentaires sauvegardées pour les estimations
    """
    STATUS_CHOICES = [
        ('not_applied', 'Non Appliqué'),
        ('planned', 'Planifié'),
        ('applied', 'Appliqué'),
    ]

    INDICATION_CHOICES = [
        ('preventive', _('Préventif')),
        ('therapeutic', _('Thérapeutique')),
        ('aesthetic', _('Esthétique')),
        ('emergency', _('Urgence')),
        ('maintenance', _('Maintenance')),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Relations
    session = models.ForeignKey(EstimateSession, on_delete=models.CASCADE, related_name='modifications')

    # NOUVEAU: Liaison directe avec le patient
    patient = models.ForeignKey(
        'DentalPatient',
        on_delete=models.CASCADE,
        null=True,  # Temporaire pour migration
        blank=True,
        related_name='tooth_modifications',
        verbose_name=_('Patient')
    )

    # NOUVEAU: Association avec diagnostic/pathologie (optionnel pour compatibilité)
    diagnosis = models.ForeignKey(
        'PatientDiagnosis',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='treatments',
        verbose_name=_('Diagnostic associé')
    )
    treatment_protocol = models.ForeignKey(
        'TreatmentProtocol',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='applications',
        verbose_name=_('Protocole de traitement')
    )

    # NOUVEAU: Liaison avec traitement spécialisé (GenericForeignKey)
    specialized_treatment_type = models.ForeignKey(
        ContentType,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        limit_choices_to={
            'model__in': [
                'estheticdentistrytreatment',
                'prosthodontictreatment',
                'surgicaltreatment',
                'orthodontictreatment',
                'therapeutictreatment'
            ]
        }
    )
    specialized_treatment_id = models.UUIDField(null=True, blank=True)
    specialized_treatment = GenericForeignKey(
        'specialized_treatment_type',
        'specialized_treatment_id'
    )

    # Identification de la dent et du path
    tooth_number = models.PositiveIntegerField(help_text="Numéro de la dent (1-32)")
    svg_id = models.CharField(max_length=10, help_text="ID du SVG (ex: '1', '2', etc.)")
    path_id = models.CharField(max_length=10, help_text="ID du path SVG (ex: '17', '18', etc.)")

    # Type de modification basé sur le path_id
    modification_type = models.CharField(max_length=50, help_text="Type de modification (ex: 'cleaning', 'whitening', etc.)")

    # NOUVEAU: Indication médicale
    indication = models.CharField(
        max_length=20,
        choices=INDICATION_CHOICES,
        default='therapeutic',
        verbose_name=_('Indication'),
        help_text="Raison médicale de la modification"
    )
    medical_justification = models.TextField(
        blank=True,
        verbose_name=_('Justification médicale'),
        help_text="Explication détaillée de la nécessité du traitement"
    )

    # État de la modification
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='not_applied')
    is_visible = models.BooleanField(default=False, help_text="Si le path est visible (non masqué)")

    # Propriétés visuelles
    color = models.CharField(max_length=7, blank=True, null=True, help_text="Couleur de remplissage")
    stroke = models.CharField(max_length=7, blank=True, null=True, help_text="Couleur du contour")

    # Métadonnées supplémentaires
    metadata = models.JSONField(default=dict, blank=True, help_text="Données supplémentaires (highlighted_paths, etc.)")

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-updated_at']
        unique_together = ['session', 'svg_id', 'path_id']
        verbose_name = _('Tooth Modification Estimate')
        verbose_name_plural = _('Tooth Modification Estimates')
        indexes = [
            models.Index(fields=['session', 'tooth_number']),
            models.Index(fields=['session', 'modification_type']),
            models.Index(fields=['session', 'status']),
        ]

    def __str__(self):
        return f"Dent {self.tooth_number} - Path {self.path_id} - {self.modification_type} ({self.status})"

    @property
    def modification_code(self):
        """Génère un code unique pour la modification"""
        status_suffix = self.status.upper()
        return f"T{self.tooth_number}_P{self.path_id}_{self.modification_type.upper()}_{status_suffix}"

    def save(self, *args, **kwargs):
        # Déterminer le statut basé sur la visibilité
        if self.is_visible:
            self.status = 'applied'
        else:
            self.status = 'not_applied'

        super().save(*args, **kwargs)


class EstimateSessionHistory(models.Model):
    """
    Historique des modifications de session pour traçabilité
    """
    ACTION_CHOICES = [
        ('created', 'Créé'),
        ('modified', 'Modifié'),
        ('deleted', 'Supprimé'),
        ('completed', 'Terminé'),
        ('reset', 'Réinitialisé'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Relations
    session = models.ForeignKey(EstimateSession, on_delete=models.CASCADE, related_name='history')
    modification = models.ForeignKey(ToothModificationEstimate, on_delete=models.SET_NULL, null=True, blank=True)

    # Détails de l'action
    action = models.CharField(max_length=20, choices=ACTION_CHOICES)
    description = models.TextField(blank=True, null=True)

    # Données avant/après
    previous_data = models.JSONField(default=dict, blank=True)
    new_data = models.JSONField(default=dict, blank=True)

    # Métadonnées
    performed_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    performed_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-performed_at']
        verbose_name = _('Estimate Session History')
        verbose_name_plural = _('Estimate Session Histories')

    def __str__(self):
        return f"{self.session.session_name} - {self.action} - {self.performed_at.strftime('%Y-%m-%d %H:%M')}"


class EstimateTemplate(models.Model):
    """
    Modèles d'estimation prédéfinis
    """
    SPECIALTY_CHOICES = [
        ('esthetic', 'Dentisterie Esthétique'),
        ('prosthetic', 'Prothèses Thérapeutiques'),
        ('surgery', 'Chirurgie'),
        ('orthodontics', 'Orthopédie'),
        ('general', 'Général'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Identification du modèle
    name = models.CharField(max_length=200)
    description = models.TextField(blank=True, null=True)
    specialty = models.CharField(max_length=20, choices=SPECIALTY_CHOICES, default='general')

    # Configuration du modèle
    template_data = models.JSONField(default=dict, help_text="Configuration des modifications par défaut")

    # Métadonnées
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Statut
    is_active = models.BooleanField(default=True)
    is_public = models.BooleanField(default=False, help_text="Visible par tous les utilisateurs")

    class Meta:
        ordering = ['specialty', 'name']
        verbose_name = _('Estimate Template')
        verbose_name_plural = _('Estimate Templates')

    def __str__(self):
        return f"{self.name} ({self.specialty})"


class EstimateStatistics(models.Model):
    """
    Statistiques des estimations pour analyse
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Relations
    session = models.OneToOneField(EstimateSession, on_delete=models.CASCADE, related_name='statistics')

    # Statistiques générales
    total_modifications = models.PositiveIntegerField(default=0)
    applied_modifications = models.PositiveIntegerField(default=0)
    planned_modifications = models.PositiveIntegerField(default=0)

    # Statistiques par type
    esthetic_count = models.PositiveIntegerField(default=0)
    prosthetic_count = models.PositiveIntegerField(default=0)
    surgery_count = models.PositiveIntegerField(default=0)
    orthodontics_count = models.PositiveIntegerField(default=0)

    # Statistiques par dent
    modified_teeth_count = models.PositiveIntegerField(default=0)
    most_modified_tooth = models.PositiveIntegerField(null=True, blank=True)

    # Temps de travail
    session_duration_minutes = models.PositiveIntegerField(default=0)
    last_activity = models.DateTimeField(auto_now=True)

    # Métadonnées
    calculated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('Estimate Statistics')
        verbose_name_plural = _('Estimate Statistics')

    def __str__(self):
        return f"Stats - {self.session.session_name}"

    def calculate_statistics(self):
        """Recalcule les statistiques basées sur les modifications"""
        modifications = self.session.modifications.all()

        self.total_modifications = modifications.count()
        self.applied_modifications = modifications.filter(status='applied').count()
        self.planned_modifications = modifications.filter(status='planned').count()

        # Compter par type (basé sur les path_ids)
        esthetic_paths = ['17', '18', '19', '20', '21', '22', '23', '37']  # Cleaning, Fluoride, Sealant, Whitening, Veneer
        prosthetic_paths = ['24', '25', '26', '27', '36', '38', '39', '40', '41', '42', '43', '44', '45', '46', '47']  # Restorations, Crowns
        surgery_paths = ['53', '68']  # Extraction, Resection
        orthodontics_paths = ['69']  # Teeth Crown (orthodontics)

        self.esthetic_count = modifications.filter(path_id__in=esthetic_paths).count()
        self.prosthetic_count = modifications.filter(path_id__in=prosthetic_paths).count()
        self.surgery_count = modifications.filter(path_id__in=surgery_paths).count()
        self.orthodontics_count = modifications.filter(path_id__in=orthodontics_paths).count()

        # Dents modifiées
        modified_teeth = modifications.values('tooth_number').distinct()
        self.modified_teeth_count = modified_teeth.count()

        # Dent la plus modifiée
        if modified_teeth:
            from django.db.models import Count
            most_modified = modifications.values('tooth_number').annotate(
                count=Count('id')
            ).order_by('-count').first()
            self.most_modified_tooth = most_modified['tooth_number'] if most_modified else None

        self.save()
        return self
