"""
Comment and review models for the dentistry application.
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator, MaxValueValidator
from .base import DentistryBaseModel
from .doctor import DentistryDoctor


class DentistryComment(DentistryBaseModel):
    """
    Model for patient comments on dentistry doctors.
    Supports hierarchical comments (replies) and ratings.
    """
    doctor = models.ForeignKey(
        DentistryDoctor,
        on_delete=models.CASCADE,
        related_name='comments',
        verbose_name=_("Doctor")
    )
    
    # Patient information (stored as text for flexibility)
    patient_name = models.CharField(
        max_length=200,
        verbose_name=_("Patient Name")
    )
    
    patient_email = models.EmailField(
        verbose_name=_("Patient Email"),
        null=True,
        blank=True
    )
    
    patient_phone = models.CharField(
        max_length=20,
        verbose_name=_("Patient Phone"),
        null=True,
        blank=True
    )
    
    # Comment content
    content = models.TextField(
        verbose_name=_("Comment Content")
    )
    
    # Hierarchical structure for replies
    parent = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='replies',
        verbose_name=_("Parent Comment")
    )
    
    # Rating (1-5 stars, only for top-level comments)
    rating = models.IntegerField(
        null=True,
        blank=True,
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        verbose_name=_("Rating"),
        help_text=_("Rating from 1 to 5 stars")
    )
    
    # Engagement metrics
    likes = models.IntegerField(
        default=0,
        verbose_name=_("Likes Count")
    )
    
    # Dental-specific fields
    treatment_type = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        verbose_name=_("Treatment Type"),
        help_text=_("Type of dental treatment received")
    )
    
    visit_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_("Visit Date"),
        help_text=_("Date of the dental visit")
    )
    
    # Moderation and status
    is_verified = models.BooleanField(
        default=False,
        verbose_name=_("Is Verified"),
        help_text=_("Whether this comment is from a verified patient")
    )
    
    is_featured = models.BooleanField(
        default=False,
        verbose_name=_("Is Featured"),
        help_text=_("Whether to feature this comment prominently")
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name=_("Is Active")
    )
    
    # Metadata
    ip_address = models.GenericIPAddressField(
        null=True,
        blank=True,
        verbose_name=_("IP Address")
    )
    
    user_agent = models.TextField(
        null=True,
        blank=True,
        verbose_name=_("User Agent")
    )

    class Meta:
        verbose_name = _("Dentistry Comment")
        verbose_name_plural = _("Dentistry Comments")
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['doctor', '-created_at']),
            models.Index(fields=['is_active', '-created_at']),
            models.Index(fields=['rating', '-created_at']),
        ]

    def __str__(self):
        if self.parent:
            return f"Reply by {self.patient_name} to comment on Dr. {self.doctor.full_name}"
        return f"Comment by {self.patient_name} for Dr. {self.doctor.full_name}"

    @property
    def is_reply(self):
        """Check if this is a reply to another comment."""
        return self.parent is not None

    @property
    def reply_count(self):
        """Get the number of replies to this comment."""
        return self.replies.filter(is_active=True).count()

    def clean(self):
        """Custom validation."""
        from django.core.exceptions import ValidationError
        
        # Only top-level comments can have ratings
        if self.parent and self.rating:
            raise ValidationError(_("Replies cannot have ratings"))
        
        # Featured comments must be verified
        if self.is_featured and not self.is_verified:
            raise ValidationError(_("Featured comments must be verified"))


class DentistryReview(DentistryBaseModel):
    """
    Model for comprehensive patient reviews of dentistry doctors.
    More detailed than comments, includes multiple rating categories.
    """
    doctor = models.ForeignKey(
        DentistryDoctor,
        on_delete=models.CASCADE,
        related_name='reviews',
        verbose_name=_("Doctor")
    )
    
    # Patient information
    patient_name = models.CharField(
        max_length=200,
        verbose_name=_("Patient Name")
    )
    
    patient_email = models.EmailField(
        verbose_name=_("Patient Email"),
        null=True,
        blank=True
    )
    
    # Overall rating and review
    overall_rating = models.IntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        verbose_name=_("Overall Rating")
    )
    
    review_text = models.TextField(
        verbose_name=_("Review Text")
    )
    
    # Detailed ratings for dental care
    professionalism_rating = models.IntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        verbose_name=_("Professionalism Rating"),
        null=True,
        blank=True
    )
    
    communication_rating = models.IntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        verbose_name=_("Communication Rating"),
        null=True,
        blank=True
    )
    
    pain_management_rating = models.IntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        verbose_name=_("Pain Management Rating"),
        null=True,
        blank=True
    )
    
    cleanliness_rating = models.IntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        verbose_name=_("Cleanliness Rating"),
        null=True,
        blank=True
    )
    
    wait_time_rating = models.IntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        verbose_name=_("Wait Time Rating"),
        null=True,
        blank=True
    )
    
    # Dental-specific information
    treatment_received = models.CharField(
        max_length=200,
        verbose_name=_("Treatment Received"),
        help_text=_("Description of dental treatment received")
    )
    
    visit_date = models.DateField(
        verbose_name=_("Visit Date")
    )
    
    would_recommend = models.BooleanField(
        default=True,
        verbose_name=_("Would Recommend"),
        help_text=_("Would recommend this dentist to others")
    )
    
    # Verification and moderation
    is_verified = models.BooleanField(
        default=False,
        verbose_name=_("Is Verified")
    )
    
    verification_method = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        choices=[
            ('email', _('Email Verification')),
            ('phone', _('Phone Verification')),
            ('appointment', _('Appointment Verification')),
            ('manual', _('Manual Verification')),
        ],
        verbose_name=_("Verification Method")
    )
    
    is_featured = models.BooleanField(
        default=False,
        verbose_name=_("Is Featured")
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name=_("Is Active")
    )
    
    # Engagement
    helpful_count = models.IntegerField(
        default=0,
        verbose_name=_("Helpful Count"),
        help_text=_("Number of users who found this review helpful")
    )

    class Meta:
        verbose_name = _("Dentistry Review")
        verbose_name_plural = _("Dentistry Reviews")
        ordering = ['-created_at']
        unique_together = ['doctor', 'patient_email', 'visit_date']
        indexes = [
            models.Index(fields=['doctor', '-created_at']),
            models.Index(fields=['overall_rating', '-created_at']),
            models.Index(fields=['is_verified', '-created_at']),
        ]

    def __str__(self):
        return f"Review by {self.patient_name} for Dr. {self.doctor.full_name} - {self.overall_rating}/5"

    @property
    def average_detailed_rating(self):
        """Calculate average of detailed ratings."""
        ratings = [
            self.professionalism_rating,
            self.communication_rating,
            self.pain_management_rating,
            self.cleanliness_rating,
            self.wait_time_rating
        ]
        valid_ratings = [r for r in ratings if r is not None]
        if valid_ratings:
            return sum(valid_ratings) / len(valid_ratings)
        return None


class DentistryDoctorNote(DentistryBaseModel):
    """
    Internal notes about dentistry doctors for administrative purposes.
    """
    doctor = models.ForeignKey(
        DentistryDoctor,
        on_delete=models.CASCADE,
        related_name='internal_notes',
        verbose_name=_("Doctor")
    )
    
    title = models.CharField(
        max_length=200,
        verbose_name=_("Note Title")
    )
    
    content = models.TextField(
        verbose_name=_("Note Content")
    )
    
    note_type = models.CharField(
        max_length=50,
        choices=[
            ('general', _('General Note')),
            ('performance', _('Performance Note')),
            ('training', _('Training Note')),
            ('complaint', _('Complaint Note')),
            ('achievement', _('Achievement Note')),
            ('reminder', _('Reminder Note')),
        ],
        default='general',
        verbose_name=_("Note Type")
    )
    
    priority = models.CharField(
        max_length=20,
        choices=[
            ('low', _('Low')),
            ('medium', _('Medium')),
            ('high', _('High')),
            ('urgent', _('Urgent')),
        ],
        default='medium',
        verbose_name=_("Priority")
    )
    
    is_confidential = models.BooleanField(
        default=False,
        verbose_name=_("Is Confidential")
    )
    
    # Author information
    author_name = models.CharField(
        max_length=200,
        verbose_name=_("Author Name")
    )
    
    author_role = models.CharField(
        max_length=100,
        verbose_name=_("Author Role")
    )

    class Meta:
        verbose_name = _("Dentistry Doctor Note")
        verbose_name_plural = _("Dentistry Doctor Notes")
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['doctor', '-created_at']),
            models.Index(fields=['note_type', '-created_at']),
            models.Index(fields=['priority', '-created_at']),
        ]

    def __str__(self):
        return f"Note: {self.title} - Dr. {self.doctor.full_name}"
