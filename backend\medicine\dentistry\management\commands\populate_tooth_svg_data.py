"""
Management command to populate tooth SVG data from Tdantal.ts structure
"""
from django.core.management.base import BaseCommand
from dentistry.models import Tooth, DentistryPatient
from users.models import User


class Command(BaseCommand):
    help = 'Populate tooth SVG data based on Tdantal.ts structure'

    def add_arguments(self, parser):
        parser.add_argument(
            '--create-sample-patient',
            action='store_true',
            help='Create a sample patient with all teeth',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🦷 Starting tooth SVG data population...'))

        # Sample SVG data based on Tdantal.ts structure
        tooth_svg_data = {
            # Upper Right Quadrant (11-18)
            11: {"svg_id": "1", "width": "59.8625px", "position": "0 0 50.8 172"},
            12: {"svg_id": "2", "width": "55.2px", "position": "0 0 46.9 172"},
            13: {"svg_id": "3", "width": "48.3px", "position": "0 0 41.1 172"},
            14: {"svg_id": "4", "width": "52.8px", "position": "0 0 44.9 172"},
            15: {"svg_id": "5", "width": "54.1px", "position": "0 0 46.0 172"},
            16: {"svg_id": "6", "width": "58.7px", "position": "0 0 49.9 172"},
            17: {"svg_id": "7", "width": "57.2px", "position": "0 0 48.6 172"},
            18: {"svg_id": "8", "width": "55.8px", "position": "0 0 47.4 172"},

            # Upper Left Quadrant (21-28)
            21: {"svg_id": "9", "width": "59.8625px", "position": "0 0 50.8 172"},
            22: {"svg_id": "10", "width": "55.2px", "position": "0 0 46.9 172"},
            23: {"svg_id": "11", "width": "48.3px", "position": "0 0 41.1 172"},
            24: {"svg_id": "12", "width": "52.8px", "position": "0 0 44.9 172"},
            25: {"svg_id": "13", "width": "54.1px", "position": "0 0 46.0 172"},
            26: {"svg_id": "14", "width": "58.7px", "position": "0 0 49.9 172"},
            27: {"svg_id": "15", "width": "57.2px", "position": "0 0 48.6 172"},
            28: {"svg_id": "16", "width": "55.8px", "position": "0 0 47.4 172"},

            # Lower Left Quadrant (31-38)
            31: {"svg_id": "17", "width": "45.2px", "position": "0 0 38.4 172"},
            32: {"svg_id": "18", "width": "48.1px", "position": "0 0 40.9 172"},
            33: {"svg_id": "19", "width": "52.3px", "position": "0 0 44.5 172"},
            34: {"svg_id": "20", "width": "54.8px", "position": "0 0 46.6 172"},
            35: {"svg_id": "21", "width": "56.2px", "position": "0 0 47.8 172"},
            36: {"svg_id": "22", "width": "59.1px", "position": "0 0 50.3 172"},
            37: {"svg_id": "23", "width": "58.4px", "position": "0 0 49.7 172"},
            38: {"svg_id": "24", "width": "57.6px", "position": "0 0 49.0 172"},

            # Lower Right Quadrant (41-48)
            41: {"svg_id": "25", "width": "45.2px", "position": "0 0 38.4 172"},
            42: {"svg_id": "26", "width": "48.1px", "position": "0 0 40.9 172"},
            43: {"svg_id": "27", "width": "52.3px", "position": "0 0 44.5 172"},
            44: {"svg_id": "28", "width": "54.8px", "position": "0 0 46.6 172"},
            45: {"svg_id": "29", "width": "56.2px", "position": "0 0 47.8 172"},
            46: {"svg_id": "30", "width": "59.1px", "position": "0 0 50.3 172"},
            47: {"svg_id": "31", "width": "58.4px", "position": "0 0 49.7 172"},
            48: {"svg_id": "32", "width": "57.6px", "position": "0 0 49.0 172"},

            # Primary teeth (51-85)
            51: {"svg_id": "33", "width": "42.1px", "position": "0 0 35.8 140"},
            52: {"svg_id": "34", "width": "40.3px", "position": "0 0 34.3 140"},
            53: {"svg_id": "35", "width": "38.7px", "position": "0 0 32.9 140"},
            54: {"svg_id": "36", "width": "44.2px", "position": "0 0 37.6 140"},
            55: {"svg_id": "37", "width": "46.8px", "position": "0 0 39.8 140"},

            61: {"svg_id": "38", "width": "42.1px", "position": "0 0 35.8 140"},
            62: {"svg_id": "39", "width": "40.3px", "position": "0 0 34.3 140"},
            63: {"svg_id": "40", "width": "38.7px", "position": "0 0 32.9 140"},
            64: {"svg_id": "41", "width": "44.2px", "position": "0 0 37.6 140"},
            65: {"svg_id": "42", "width": "46.8px", "position": "0 0 39.8 140"},

            71: {"svg_id": "43", "width": "38.9px", "position": "0 0 33.1 140"},
            72: {"svg_id": "44", "width": "40.1px", "position": "0 0 34.1 140"},
            73: {"svg_id": "45", "width": "42.5px", "position": "0 0 36.1 140"},
            74: {"svg_id": "46", "width": "45.3px", "position": "0 0 38.5 140"},
            75: {"svg_id": "47", "width": "47.9px", "position": "0 0 40.7 140"},

            81: {"svg_id": "48", "width": "38.9px", "position": "0 0 33.1 140"},
            82: {"svg_id": "49", "width": "40.1px", "position": "0 0 34.1 140"},
            83: {"svg_id": "50", "width": "42.5px", "position": "0 0 36.1 140"},
            84: {"svg_id": "51", "width": "45.3px", "position": "0 0 38.5 140"},
            85: {"svg_id": "52", "width": "47.9px", "position": "0 0 40.7 140"},
        }

        # Create sample patient if requested
        if options['create_sample_patient']:
            self.create_sample_patient_with_teeth(tooth_svg_data)

        # Update existing teeth with SVG data
        updated_count = 0
        for tooth_number, svg_data in tooth_svg_data.items():
            teeth = Tooth.objects.filter(tooth_number=tooth_number)
            for tooth in teeth:
                tooth.svg_id = svg_data['svg_id']
                tooth.svg_width = svg_data['width']
                tooth.svg_position = svg_data['position']

                # Set default visibility based on tooth type
                if tooth_number >= 51:  # Primary teeth
                    tooth.visible_under_6_years = True
                    tooth.visible_under_7_5_years = True
                    tooth.visible_under_12_years = True
                    tooth.visible_under_13_5_years = False
                    tooth.visible_adult = False
                else:  # Permanent teeth
                    tooth.visible_under_6_years = False
                    tooth.visible_under_7_5_years = True
                    tooth.visible_under_12_years = True
                    tooth.visible_under_13_5_years = True
                    tooth.visible_adult = True

                tooth.save()
                updated_count += 1

        self.stdout.write(
            self.style.SUCCESS(f'✅ Updated {updated_count} teeth with SVG data')
        )

    def create_sample_patient_with_teeth(self, tooth_svg_data):
        """Create a sample patient with all teeth for testing"""
        self.stdout.write('👤 Creating sample patient with all teeth...')

        # Create sample user
        user, created = User.objects.get_or_create(
            email='<EMAIL>',
            defaults={
                'first_name': 'Sample',
                'last_name': 'Patient',
                'phone_number': '+**********',
                'is_active': True
            }
        )

        if created:
            user.set_password('samplepassword123')
            user.save()

        # Create dentistry patient
        patient, created = DentistryPatient.objects.get_or_create(
            patient=user,
            defaults={
                'dental_history': 'Sample patient for testing dental admin interface',
                'dental_insurance_provider': 'Sample Insurance Co.',
                'dental_insurance_policy_number': 'SAMPLE123456'
            }
        )

        # Create all teeth for the patient
        teeth_created = 0
        for tooth_number, svg_data in tooth_svg_data.items():
            tooth, created = Tooth.objects.get_or_create(
                patient=patient,
                tooth_number=tooth_number,
                defaults={
                    'name': dict(Tooth.TOOTH_NUMBERS).get(tooth_number, f'Tooth {tooth_number}'),
                    'svg_id': svg_data['svg_id'],
                    'svg_width': svg_data['width'],
                    'svg_position': svg_data['position'],
                    'status': 'healthy',
                    'visible_under_6_years': tooth_number >= 51,
                    'visible_under_7_5_years': True,
                    'visible_under_12_years': True,
                    'visible_under_13_5_years': tooth_number < 51,
                    'visible_adult': tooth_number < 51,
                }
            )

            if created:
                teeth_created += 1

        self.stdout.write(
            self.style.SUCCESS(f'✅ Created sample patient with {teeth_created} teeth')
        )
        self.stdout.write(
            self.style.WARNING(f'📧 Patient email: {user.email}')
        )
        self.stdout.write(
            self.style.WARNING(f'🔑 Password: samplepassword123')
        )
