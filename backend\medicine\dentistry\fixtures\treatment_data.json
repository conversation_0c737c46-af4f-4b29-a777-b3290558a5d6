[{"model": "dentistry.treatmentcategory", "pk": "550e8400-e29b-41d4-a716-446655440001", "fields": {"name": "esthetic", "display_name": "Dentisterie Esthétique", "description": "Traitements visant à améliorer l'apparence des dents", "icon": "IconSparkles", "color": "#E91E63", "order": 1, "is_active": true}}, {"model": "dentistry.treatmentcategory", "pk": "550e8400-e29b-41d4-a716-446655440002", "fields": {"name": "prosthetic", "display_name": "Prothèses Thérapeutiques", "description": "Remplacement et restauration des dents", "icon": "IconTool", "color": "#2196F3", "order": 2, "is_active": true}}, {"model": "dentistry.treatmentcategory", "pk": "550e8400-e29b-41d4-a716-446655440003", "fields": {"name": "surgery", "display_name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Interventions chirurgicales dentaires", "icon": "IconScalpel", "color": "#FF5722", "order": 3, "is_active": true}}, {"model": "dentistry.treatmentcategory", "pk": "550e8400-e29b-41d4-a716-446655440004", "fields": {"name": "orthodontics", "display_name": "Orthopédie", "description": "Correction de l'alignement des dents", "icon": "IconAdjustments", "color": "#4CAF50", "order": 4, "is_active": true}}, {"model": "dentistry.treatmenttype", "pk": "660e8400-e29b-41d4-a716-446655440001", "fields": {"category": "550e8400-e29b-41d4-a716-446655440001", "code": "EST001", "name": "<PERSON><PERSON><PERSON>", "description": "Éclaircissement de la couleur des dents", "icon": "IconSun", "color": "#FFC107", "duration_minutes": 60, "base_price": "150.00", "complexity_level": "simple", "order": 1, "is_active": true, "requires_anesthesia": false, "requires_followup": true}}, {"model": "dentistry.treatmenttype", "pk": "660e8400-e29b-41d4-a716-446655440002", "fields": {"category": "550e8400-e29b-41d4-a716-446655440001", "code": "EST002", "name": "Facettes Céramique", "description": "Pose de facettes en céramique", "icon": "IconRectangle", "color": "#E1F5FE", "duration_minutes": 120, "base_price": "800.00", "complexity_level": "complex", "order": 2, "is_active": true, "requires_anesthesia": true, "requires_followup": true}}, {"model": "dentistry.treatmenttype", "pk": "660e8400-e29b-41d4-a716-446655440003", "fields": {"category": "550e8400-e29b-41d4-a716-446655440001", "code": "EST003", "name": "Composite Esthétique", "description": "Restauration en composite esthétique", "icon": "IconPalette", "color": "#F3E5F5", "duration_minutes": 45, "base_price": "120.00", "complexity_level": "moderate", "order": 3, "is_active": true, "requires_anesthesia": true, "requires_followup": false}}, {"model": "dentistry.treatmenttype", "pk": "660e8400-e29b-41d4-a716-446655440004", "fields": {"category": "550e8400-e29b-41d4-a716-446655440002", "code": "PRO001", "name": "Couronne Céramique", "description": "Pose d'une couronne en céramique", "icon": "IconCrown", "color": "#FFE0B2", "duration_minutes": 90, "base_price": "600.00", "complexity_level": "complex", "order": 1, "is_active": true, "requires_anesthesia": true, "requires_followup": true}}, {"model": "dentistry.treatmenttype", "pk": "660e8400-e29b-41d4-a716-446655440005", "fields": {"category": "550e8400-e29b-41d4-a716-446655440002", "code": "PRO002", "name": "<PERSON>", "description": "Pose d'un bridge pour remplacer une ou plusieurs dents", "icon": "IconBridge", "color": "#E3F2FD", "duration_minutes": 150, "base_price": "1200.00", "complexity_level": "expert", "order": 2, "is_active": true, "requires_anesthesia": true, "requires_followup": true}}, {"model": "dentistry.treatmenttype", "pk": "660e8400-e29b-41d4-a716-446655440006", "fields": {"category": "550e8400-e29b-41d4-a716-446655440002", "code": "PRO003", "name": "<PERSON><PERSON><PERSON>", "description": "Pose d'un implant dentaire", "icon": "IconScrew", "color": "#F1F8E9", "duration_minutes": 120, "base_price": "1000.00", "complexity_level": "expert", "order": 3, "is_active": true, "requires_anesthesia": true, "requires_followup": true}}, {"model": "dentistry.treatmenttype", "pk": "660e8400-e29b-41d4-a716-446655440007", "fields": {"category": "550e8400-e29b-41d4-a716-446655440003", "code": "CHI001", "name": "Extraction Simple", "description": "Extraction d'une dent simple", "icon": "IconMinus", "color": "#FFEBEE", "duration_minutes": 30, "base_price": "80.00", "complexity_level": "simple", "order": 1, "is_active": true, "requires_anesthesia": true, "requires_followup": true}}, {"model": "dentistry.treatmenttype", "pk": "660e8400-e29b-41d4-a716-446655440008", "fields": {"category": "550e8400-e29b-41d4-a716-446655440003", "code": "CHI002", "name": "Extraction Chirurgicale", "description": "Extraction chirurgicale complexe", "icon": "IconCut", "color": "#FF8A80", "duration_minutes": 60, "base_price": "200.00", "complexity_level": "complex", "order": 2, "is_active": true, "requires_anesthesia": true, "requires_followup": true}}, {"model": "dentistry.treatmenttype", "pk": "660e8400-e29b-41d4-a716-446655440009", "fields": {"category": "550e8400-e29b-41d4-a716-446655440003", "code": "CHI003", "name": "Greffe Osseuse", "description": "Greffe osseuse pour implant", "icon": "IconBone", "color": "#FFF3E0", "duration_minutes": 90, "base_price": "500.00", "complexity_level": "expert", "order": 3, "is_active": true, "requires_anesthesia": true, "requires_followup": true}}, {"model": "dentistry.treatmenttype", "pk": "660e8400-e29b-41d4-a716-446655440010", "fields": {"category": "550e8400-e29b-41d4-a716-446655440004", "code": "ORT001", "name": "Appareil Orthodontique", "description": "Pose d'un appareil orthodontique", "icon": "IconBraces", "color": "#E8F5E8", "duration_minutes": 60, "base_price": "2000.00", "complexity_level": "complex", "order": 1, "is_active": true, "requires_anesthesia": false, "requires_followup": true}}, {"model": "dentistry.treatmenttype", "pk": "660e8400-e29b-41d4-a716-446655440011", "fields": {"category": "550e8400-e29b-41d4-a716-446655440004", "code": "ORT002", "name": "Gouttière Invisalign", "description": "Traitement par gouttières transparentes", "icon": "IconEye", "color": "#F3E5F5", "duration_minutes": 45, "base_price": "3500.00", "complexity_level": "complex", "order": 2, "is_active": true, "requires_anesthesia": false, "requires_followup": true}}]