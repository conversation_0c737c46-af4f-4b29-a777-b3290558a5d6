"use client";

import React, { useState } from 'react';
import {
  Button,
  Tabs,
  TextInput,
  Group,
  Text,
  Card,
  Select,
  Checkbox
} from '@mantine/core';
import {
  IconLungs,
  IconSearch,
  IconChevronLeft,
  IconChevronRight
} from '@tabler/icons-react';
import { DataTable } from 'mantine-datatable';

// Types
interface ChartColor {
  id: string;
  name: string;
  color: string;
}

interface ExamProtocol {
  id: string;
  titre: string;
  charge: string;
  duree: string;
}

interface ExamReason {
  id: string;
  typeExamen: string;
  motif: string;
}

const Oxymetrie = () => {
  // États pour les onglets
  const [activeTab, setActiveTab] = useState('general');

  // États pour la pagination
  const [currentPageProtocols, setCurrentPageProtocols] = useState(1);
  const [itemsPerPageProtocols, setItemsPerPageProtocols] = useState(2);
  const [currentPageReasons, setCurrentPageReasons] = useState(1);
  const [itemsPerPageReasons, setItemsPerPageReasons] = useState(2);

  // Données mockées
  const chartColors: ChartColor[] = [];
  const examProtocols: ExamProtocol[] = [];
  const examReasons: ExamReason[] = [];

  // Gestionnaire pour les onglets
  const handleTabChange = (value: string | null) => {
    if (value) setActiveTab(value);
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <IconLungs size={32} className="text-blue-600" />
          <h1 className="text-2xl font-bold text-gray-800">Oxymétrie</h1>
        </div>
      </div>

      {/* Onglets principaux */}
      <Card shadow="sm" padding="lg" radius="md" withBorder>
        <Tabs value={activeTab} onChange={handleTabChange}>
          <Tabs.List>
            <Tabs.Tab value="general">Général</Tabs.Tab>
            <Tabs.Tab value="couleur-chart">Couleur du chart</Tabs.Tab>
            <Tabs.Tab value="protocoles-examen">Protocoles d&apos;examen</Tabs.Tab>
            <Tabs.Tab value="motifs-examen">Motifs d&apos;examen</Tabs.Tab>
          </Tabs.List>

          {/* Onglet Général */}
          <Tabs.Panel value="general" pt="md">
            <div className="space-y-6">
              <Text size="lg" fw={600} className="text-gray-800 mb-4">
                Examen valeur par défaut
              </Text>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Colonne gauche */}
                <div className="space-y-4">
                  <div>
                    <Text size="sm" fw={500} mb="xs">Type d&apos;examen</Text>
                    <Select
                      placeholder=""
                      data={[]}
                      className="w-full"
                    />
                  </div>

                  <div>
                    <Text size="sm" fw={500} mb="xs">Mètre/tour</Text>
                    <TextInput
                      placeholder="Raison"
                      className="w-full"
                    />
                  </div>

                  <div>
                    <Text size="sm" fw={500} mb="xs">Raison</Text>
                    <TextInput
                      placeholder="Raison"
                      className="w-full"
                    />
                  </div>
                </div>

                {/* Colonne droite */}
                <div className="space-y-4">
                  <div>
                    <Text size="sm" fw={500} mb="xs">Courte pause</Text>
                    <TextInput
                      placeholder=""
                      className="w-full"
                    />
                  </div>

                  <div>
                    <Text size="sm" fw={500} mb="xs">Durée de récupération</Text>
                    <TextInput
                      placeholder=""
                      className="w-full"
                    />
                  </div>

                  <div>
                    <Text size="sm" fw={500} mb="xs">Cause d&apos;arrêt</Text>
                    <TextInput
                      placeholder="Cause d'arrêt"
                      className="w-full"
                    />
                  </div>
                </div>
              </div>

              {/* Checkbox */}
              <div className="mt-6">
                <Checkbox
                  label="Enregistrement automatique à la fin d'examen"
                  className="text-gray-700"
                />
              </div>
            </div>
          </Tabs.Panel>

          {/* Onglet Couleur du chart */}
          <Tabs.Panel value="couleur-chart" pt="md">
            <div className="space-y-4">
              <div className="mb-4">
                <TextInput
                  placeholder="Rechercher..."
                  leftSection={<IconSearch size={16} />}
                  className="max-w-md"
                />
              </div>

              {chartColors.length === 0 ? (
                <div className="bg-yellow-100 p-4 rounded-lg">
                  <Text size="sm" className="text-yellow-800">
                    ⚠️ Aucun élément trouvé.
                  </Text>
                </div>
              ) : (
                <DataTable
                  withTableBorder
                  borderRadius="sm"
                  withColumnBorders
                  striped
                  highlightOnHover
                  records={chartColors}
                  columns={[
                    { accessor: 'name', title: 'Name', width: 300 },
                    { accessor: 'color', title: 'Couleur', width: 200 }
                  ]}
                  minHeight={400}
                  noRecordsText="Aucun élément trouvé."
                />
              )}
            </div>
          </Tabs.Panel>

          {/* Onglet Protocoles d'examen */}
          <Tabs.Panel value="protocoles-examen" pt="md">
            <div className="space-y-4">
              <div className="mb-4">
                <TextInput
                  placeholder="Rechercher..."
                  leftSection={<IconSearch size={16} />}
                  className="max-w-md"
                />
              </div>

              {examProtocols.length === 0 ? (
                <div className="bg-yellow-100 p-4 rounded-lg">
                  <Text size="sm" className="text-yellow-800">
                    ⚠️ Aucun élément trouvé.
                  </Text>
                </div>
              ) : (
                <DataTable
                  withTableBorder
                  borderRadius="sm"
                  withColumnBorders
                  striped
                  highlightOnHover
                  records={examProtocols}
                  columns={[
                    { accessor: 'titre', title: 'Titre', width: 300 },
                    { accessor: 'charge', title: 'Charge', width: 200 },
                    { accessor: 'duree', title: 'Durée', width: 200 }
                  ]}
                  minHeight={400}
                  noRecordsText="Aucun élément trouvé."
                />
              )}

              {/* Pagination pour protocoles */}
              <Group justify="space-between" mt="md">
                <Group gap="sm" align="center">
                  <Text size="sm" className="text-gray-600">Page</Text>
                  <Select
                    value={currentPageProtocols.toString()}
                    onChange={(value) => setCurrentPageProtocols(Number(value) || 1)}
                    data={['1']}
                    size="xs"
                    className="w-16"
                  />
                  <Text size="sm" className="text-gray-600">Lignes par Page</Text>
                  <Select
                    value={itemsPerPageProtocols.toString()}
                    onChange={(value) => setItemsPerPageProtocols(Number(value) || 2)}
                    data={['2', '5', '10', '20']}
                    size="xs"
                    className="w-16"
                  />
                  <Text size="sm" className="text-gray-600">0 - de</Text>
                  <Group gap="xs">
                    <Button variant="subtle" size="xs" disabled>
                      <IconChevronLeft size={16} />
                    </Button>
                    <Button variant="subtle" size="xs" disabled>
                      <IconChevronRight size={16} />
                    </Button>
                  </Group>
                </Group>
              </Group>
            </div>
          </Tabs.Panel>

          {/* Onglet Motifs d'examen */}
          <Tabs.Panel value="motifs-examen" pt="md">
            <div className="space-y-4">
              <div className="mb-4">
                <TextInput
                  placeholder="Rechercher..."
                  leftSection={<IconSearch size={16} />}
                  className="max-w-md"
                />
              </div>

              {examReasons.length === 0 ? (
                <div className="bg-yellow-100 p-4 rounded-lg">
                  <Text size="sm" className="text-yellow-800">
                    ⚠️ Aucun élément trouvé.
                  </Text>
                </div>
              ) : (
                <DataTable
                  withTableBorder
                  borderRadius="sm"
                  withColumnBorders
                  striped
                  highlightOnHover
                  records={examReasons}
                  columns={[
                    { accessor: 'typeExamen', title: 'Type d\'examen', width: 300 },
                    { accessor: 'motif', title: 'Motif', width: 300 }
                  ]}
                  minHeight={400}
                  noRecordsText="Aucun élément trouvé."
                />
              )}

              {/* Pagination pour motifs */}
              <Group justify="space-between" mt="md">
                <Group gap="sm" align="center">
                  <Text size="sm" className="text-gray-600">Page</Text>
                  <Select
                    value={currentPageReasons.toString()}
                    onChange={(value) => setCurrentPageReasons(Number(value) || 1)}
                    data={['1']}
                    size="xs"
                    className="w-16"
                  />
                  <Text size="sm" className="text-gray-600">Lignes par Page</Text>
                  <Select
                    value={itemsPerPageReasons.toString()}
                    onChange={(value) => setItemsPerPageReasons(Number(value) || 2)}
                    data={['2', '5', '10', '20']}
                    size="xs"
                    className="w-16"
                  />
                  <Text size="sm" className="text-gray-600">0 - de</Text>
                  <Group gap="xs">
                    <Button variant="subtle" size="xs" disabled>
                      <IconChevronLeft size={16} />
                    </Button>
                    <Button variant="subtle" size="xs" disabled>
                      <IconChevronRight size={16} />
                    </Button>
                  </Group>
                </Group>
              </Group>
            </div>
          </Tabs.Panel>
        </Tabs>
      </Card>
    </div>
  );
};

export default Oxymetrie;
