from rest_framework import status
from rest_framework.decorators import api_view, parser_classes, permission_classes
from rest_framework.response import Response
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ars<PERSON>
from rest_framework.permissions import IsAuthenticated, IsAdminUser
from ..location_import import import_locations_from_json

@api_view(['POST'])
@parser_classes([MultiPartParser, FormParser])
@permission_classes([IsAuthenticated, IsAdminUser])
def import_locations(request):
    """
    Import location data (countries, regions, cities) from a JSON file.
    Only admin users can access this endpoint.
    """
    if 'file' not in request.FILES:
        return Response({'error': 'No file provided'}, status=status.HTTP_400_BAD_REQUEST)
    
    file = request.FILES['file']
    file_extension = file.name.split('.')[-1].lower()
    
    if file_extension != 'json':
        return Response({'error': 'Only JSON files are supported for location data'}, status=status.HTTP_400_BAD_REQUEST)
    
    try:
        # Import the location data
        result = import_locations_from_json(file)
        
        if result['success']:
            return Response({
                'message': result['message'],
                'stats': result['stats']
            }, status=status.HTTP_200_OK)
        else:
            return Response({
                'error': result['message']
            }, status=status.HTTP_400_BAD_REQUEST)
    
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)
