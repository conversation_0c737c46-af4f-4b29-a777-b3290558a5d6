"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_i18n_locales_ar_Corbeille_researchdoctor_json"],{

/***/ "(app-pages-browser)/./src/i18n/locales/ar/Corbeille/researchdoctor.json":
/*!***********************************************************!*\
  !*** ./src/i18n/locales/ar/Corbeille/researchdoctor.json ***!
  \***********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

module.exports = /*#__PURE__*/JSON.parse('{"researchdoctor":"researchdoctor AR","conditions":"البحث عن طبيب عام","filters":"المرشحات","sort-by":"الترتيب حسب","Ascending-price":"سعر تصاعدي","Descending-price":"سعر تنازلي","Availabl":"متاح في أقرب وقت ممكن","results-found":"العثور على نتائج","View-profile":"عرض الصفحة الشخصية","specialite-title-form-l":": أبحث عن ","specialite-title-form-r":": أبحث عن طبيب ","placeHolder-specialite":"اختر التخصص","placeHolder-city":"اختر مدينة","placeHolder-region":"أختر المنطقة","specialite-form-submit":"بحت","I-research":"أبحث عن","A":"في","Display":"اختر مدينة","DisplayVille":"تخصص ....","Find":"بحت","appointment":"تحديد موعد","Next-availability":"التوقيت المتاح","label-last-name":"* اسم العائلة ","label-first-name":"* الاسم الأول","label-email-star":"* عنوان البريد الإلكتروني","Civility":"الجنس","Mon":" سيد","Mrs":"سيدة","label-phone":"الهاتف / الهاتف المحمول *","label-message-star":"من خلال إرسال هذا النموذج ، فقد قرأت وأوافق على","Privacy-Policy":"سياسة الخصوصية","label-Send":" أرسل","Prendre":"تحديد موعد","Précédente":"السابق","Suivant":"التالي","Submit":"أرسل","Choose":"اختر قيمة الاستشارة","consultation":"متابعة التشاور","Urgence":"الطوارئ","When-would":"متى ترغب في الاستشارة؟","You-can-choose":"يمكنك اختيار ما يصل إلى 3 فترات زمنية. سيتم الاتصال بك من قبل أحد أعضاء فريقنا للتحقق من صحة الشخص الأنسب.","Choose-a-date":"اختر تاريخًا","Choose-a-time-on":"اختر وقتًا","Morning":"الفترة الصباحية","Afternoon":"بعد الظهيرة","Summary-of-your-request":"ملخص طلبك","Reason-for-consultation":"سبب التشاور","Price":"السعر","Your-availability":"تواجدك","Identify-yourself":"عرف عن نفسك","Please-enter-your-identity-below":"الرجاء إدخال هويتك أدناه","Your-choice":"اختيارك","Add-another-slot":"أضف مواعيد أخرى","About":"عن","Availability":"التوفر","Services":"خدمات","First-consultation":"الحجز الاول","Emergency":"الطوارئ","These-prices":"يتم إرسال هذه الأسعار إليك لأغراض المعلومات فقط. وقد تختلف تبعًا للرعاية التي يتم إجراؤها فعليًا في المكتب.","Map":"الخريطة","Address":"العنوان","comments":"التعليقات"}');

/***/ })

}]);