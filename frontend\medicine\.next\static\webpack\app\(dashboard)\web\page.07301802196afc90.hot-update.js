"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/web/page",{

/***/ "(app-pages-browser)/./src/components/Appointments/overview/AjouterUnRendezVous.tsx":
/*!**********************************************************************!*\
  !*** ./src/components/Appointments/overview/AjouterUnRendezVous.tsx ***!
  \**********************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Text/Text.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Select/Select.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/NumberInput/NumberInput.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Button/Button.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Modal/Modal.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Group/Group.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Switch/Switch.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/index.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Menu/Menu.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Avatar/Avatar.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/TextInput/TextInput.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ActionIcon/ActionIcon.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Radio/Radio.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/InputBase/InputBase.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ColorPicker/ColorPicker.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconColorPicker_IconHexagonPlusFilled_tabler_icons_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=IconColorPicker,IconHexagonPlusFilled!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconHexagonPlusFilled.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconColorPicker_IconHexagonPlusFilled_tabler_icons_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=IconColorPicker,IconHexagonPlusFilled!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconColorPicker.mjs\");\n/* harmony import */ var _barrel_optimize_names_FaCalendarPlus_FaUserDoctor_react_icons_fa6__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=FaCalendarPlus,FaUserDoctor!=!react-icons/fa6 */ \"(app-pages-browser)/./node_modules/react-icons/fa6/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_RiUserFollowLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=RiUserFollowLine!=!react-icons/ri */ \"(app-pages-browser)/./node_modules/react-icons/ri/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_LiaAddressCardSolid_LiaBirthdayCakeSolid_react_icons_lia__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=LiaAddressCardSolid,LiaBirthdayCakeSolid!=!react-icons/lia */ \"(app-pages-browser)/./node_modules/react-icons/lia/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_TbNumber_react_icons_tb__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=TbNumber!=!react-icons/tb */ \"(app-pages-browser)/./node_modules/react-icons/tb/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FiPhone_react_icons_fi__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=FiPhone!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_CiAt_react_icons_ci__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=CiAt!=!react-icons/ci */ \"(app-pages-browser)/./node_modules/react-icons/ci/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_MdOutlineContentPasteSearch_MdOutlineSocialDistance_react_icons_md__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=MdOutlineContentPasteSearch,MdOutlineSocialDistance!=!react-icons/md */ \"(app-pages-browser)/./node_modules/react-icons/md/index.mjs\");\n/* harmony import */ var react_imask__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-imask */ \"(app-pages-browser)/./node_modules/react-imask/esm/index.js\");\n/* harmony import */ var _mantine_notifications__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @mantine/notifications */ \"(app-pages-browser)/./node_modules/@mantine/notifications/esm/notifications.store.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst RendezVousSelector = (param)=>{\n    let { onClose } = param;\n    _s();\n    const [selectedPeriod, setSelectedPeriod] = react__WEBPACK_IMPORTED_MODULE_1___default().useState('15days');\n    const [startDate, setStartDate] = react__WEBPACK_IMPORTED_MODULE_1___default().useState('12/06/2025');\n    const [duration, setDuration] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(30);\n    const [numberOfDays, setNumberOfDays] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(3);\n    const [selectedSlots, setSelectedSlots] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(new Set());\n    // Générer les créneaux horaires\n    const generateTimeSlots = ()=>{\n        const slots = [];\n        const startHour = 8;\n        const endHour = 14;\n        for(let hour = startHour; hour < endHour; hour++){\n            for(let minute = 0; minute < 60; minute += 30){\n                const startTime = \"\".concat(hour.toString().padStart(2, '0'), \":\").concat(minute.toString().padStart(2, '0'));\n                const endMinute = minute + 30;\n                const endHour = endMinute >= 60 ? hour + 1 : hour;\n                const adjustedEndMinute = endMinute >= 60 ? endMinute - 60 : endMinute;\n                const endTime = \"\".concat(endHour.toString().padStart(2, '0'), \":\").concat(adjustedEndMinute.toString().padStart(2, '0'));\n                slots.push({\n                    id: \"\".concat(hour, \"-\").concat(minute),\n                    startTime,\n                    endTime\n                });\n            }\n        }\n        return slots;\n    };\n    // Calculer la date selon la période sélectionnée\n    const getDateForPeriod = ()=>{\n        switch(selectedPeriod){\n            case '15days':\n                return '12 juin 2025';\n            case '1month':\n                return '26 juin 2025';\n            case '3months':\n                return '10 juillet 2025';\n            default:\n                return '12 juin 2025';\n        }\n    };\n    // Calculer la date de début selon la période\n    const getStartDateForPeriod = ()=>{\n        switch(selectedPeriod){\n            case '15days':\n                return '12/06/2025';\n            case '1month':\n                return '25/06/2025';\n            case '3months':\n                return '10/07/2025';\n            default:\n                return '12/06/2025';\n        }\n    };\n    // Calculer la date formatée selon la période\n    const getFormattedDateForPeriod = ()=>{\n        switch(selectedPeriod){\n            case '15days':\n                return '12/06/2025';\n            case '1month':\n                return '26/06/2025';\n            case '3months':\n                return '10/07/2025';\n            default:\n                return '12/06/2025';\n        }\n    };\n    const timeSlots = generateTimeSlots();\n    const handleSlotToggle = (slotId)=>{\n        const newSelectedSlots = new Set(selectedSlots);\n        if (newSelectedSlots.has(slotId)) {\n            newSelectedSlots.delete(slotId);\n        } else {\n            newSelectedSlots.add(slotId);\n        }\n        setSelectedSlots(newSelectedSlots);\n    };\n    const isValidateEnabled = selectedSlots.size > 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-12 gap-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"col-span-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 bg-gray-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                        size: \"sm\",\n                                        fw: 500,\n                                        mb: \"xs\",\n                                        children: \"\\xc0 partir de\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Select, {\n                                        value: getStartDateForPeriod(),\n                                        onChange: (value)=>setStartDate(value || ''),\n                                        data: [\n                                            {\n                                                value: '12/06/2025',\n                                                label: '12/06/2025'\n                                            },\n                                            {\n                                                value: '25/06/2025',\n                                                label: '25/06/2025'\n                                            },\n                                            {\n                                                value: '10/07/2025',\n                                                label: '10/07/2025'\n                                            },\n                                            {\n                                                value: '10/09/2025',\n                                                label: '10/09/2025'\n                                            }\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                        size: \"sm\",\n                                        fw: 500,\n                                        mb: \"xs\",\n                                        children: \"Dur\\xe9e (min)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.NumberInput, {\n                                        value: duration,\n                                        onChange: (value)=>setDuration(Number(value)),\n                                        min: 15,\n                                        max: 120,\n                                        step: 15\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                        size: \"sm\",\n                                        fw: 500,\n                                        mb: \"xs\",\n                                        children: \"Nbre des jours\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.NumberInput, {\n                                        value: numberOfDays,\n                                        onChange: (value)=>setNumberOfDays(Number(value)),\n                                        min: 1,\n                                        max: 30\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                    lineNumber: 220,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                lineNumber: 219,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"col-span-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                    size: \"lg\",\n                                    fw: 600,\n                                    children: getDateForPeriod()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                    size: \"sm\",\n                                    color: \"dimmed\",\n                                    children: \"24\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                            lineNumber: 262,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2 max-h-96 overflow-y-auto\",\n                            children: timeSlots.map((slot)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between p-2 hover:bg-gray-50 rounded\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                                    size: \"sm\",\n                                                    children: \"le\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                                    size: \"sm\",\n                                                    color: \"blue\",\n                                                    fw: 500,\n                                                    children: getFormattedDateForPeriod()\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                                    size: \"sm\",\n                                                    children: \"de\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                    lineNumber: 275,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                                    size: \"sm\",\n                                                    color: \"red\",\n                                                    fw: 500,\n                                                    children: slot.startTime\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                                    size: \"sm\",\n                                                    children: \"\\xe0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                                    size: \"sm\",\n                                                    color: \"green\",\n                                                    fw: 500,\n                                                    children: slot.endTime\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: selectedSlots.has(slot.id),\n                                            onChange: ()=>handleSlotToggle(slot.id),\n                                            className: \"form-checkbox h-4 w-4 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, slot.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                            lineNumber: 267,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center mt-6 pt-4 border-t\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                            variant: selectedPeriod === '15days' ? 'filled' : 'outline',\n                                            onClick: ()=>setSelectedPeriod('15days'),\n                                            size: \"sm\",\n                                            children: \"15 jours\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                            lineNumber: 296,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                            variant: selectedPeriod === '1month' ? 'filled' : 'outline',\n                                            onClick: ()=>setSelectedPeriod('1month'),\n                                            size: \"sm\",\n                                            children: \"1 Mois\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                            variant: selectedPeriod === '3months' ? 'filled' : 'outline',\n                                            onClick: ()=>setSelectedPeriod('3months'),\n                                            size: \"sm\",\n                                            children: \"3 Mois\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                            color: isValidateEnabled ? 'blue' : 'gray',\n                                            disabled: !isValidateEnabled,\n                                            onClick: ()=>{\n                                                // Logique de validation ici\n                                                onClose();\n                                            },\n                                            children: \"Valider\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                            variant: \"outline\",\n                                            color: \"red\",\n                                            onClick: onClose,\n                                            children: \"Annuler\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                            lineNumber: 294,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                    lineNumber: 261,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                lineNumber: 260,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n        lineNumber: 218,\n        columnNumber: 5\n    }, undefined);\n};\n_s(RendezVousSelector, \"Koepf+TcS+LiKPzoUnfAyPcfzVM=\");\n_c = RendezVousSelector;\nconst AjouterUnRendezVous = (props)=>{\n    const { opened, onClose, appointmentForm, handleSubmit, eventTitle, setEventTitle, titleOptions, setTitleOptions, newTitle, setNewTitle, patientName, setPatientName, patientlastName, setPatientlastName, openListDesPatient, eventDateDeNaissance, handleDateChange, eventAge, genderOption, handleOptionChange, eventEtatCivil, setEventEtatCivil, eventCin, setEventCin, address, setAddress, eventTelephone, setEventTelephone, email, setEmail, patientdoctor, setPatientDocteur, patientsocialSecurity, setSocialSecurity, consultationTypes, setConsultationTypes, patienttypeConsultation, setPatientTypeConsultation, setEventType, searchValue, setSearchValue, dureeDeLexamen, getEventTypeColor, newConsultationType, setNewConsultationType, newConsultationColor, setNewConsultationColor, ColorPickeropened, openedColorPicker, closeColorPicker, changeEndValue, setChangeEndValue, setDureeDeLexamen, eventAganda, setEventAganda, agendaTypes, setAgendaTypes, newAgendaType, setNewAgendaType, isWaitingList, eventDate, setEventDate, eventTime, setEventTime, eventConsultation, openListRendezVous, ListRendezVousOpened, closeListRendezVous, patientcomment, setPatientcomment, patientnotes, setPatientNotes, patientcommentairelistedattente, setPatientCommentairelistedattente, eventResourceId, setEventResourceId, eventType, checkedAppelvideo, handleAppelvideoChange, checkedRappelSms, handleRappelSmsChange, checkedRappelEmail, handleRappelEmailChange, currentPatient, waitingList, setWaitingList, setPatientModalOpen } = props;\n    var _eventAge_toString;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Modal.Root, {\n        opened: opened,\n        onClose: onClose,\n        size: \"70%\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Modal.Overlay, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                lineNumber: 442,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Modal.Content, {\n                className: \"overflow-y-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Modal.Header, {\n                        style: {\n                            height: '60px',\n                            background: \"#3799CE\",\n                            padding: \"11px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Modal.Title, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                        fw: 600,\n                                        c: \"var(--mantine-color-white)\",\n                                        className: \"mb-2 flex gap-2 text-lg font-semibold leading-none tracking-tight\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                width: \"1em\",\n                                                height: \"1em\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M21 7.5V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h3.5M16 2v4M8 2v4m-5 4h5m9.5 7.5L16 16.3V14\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 460,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                            cx: 16,\n                                                            cy: 16,\n                                                            r: 6\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 461,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                lineNumber: 447,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Ajouter un rendez-vous\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                        lineNumber: 446,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground text-sm text-[var(--mantine-color-white)]\",\n                                        children: \"Remplissez les d\\xe9tails ci-dessous pour ajouter un nouvel \\xe9v\\xe9nement.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                        lineNumber: 466,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                lineNumber: 445,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Group, {\n                                justify: \"flex-end\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Switch, {\n                                        defaultChecked: true,\n                                        color: \"teal\",\n                                        size: \"xs\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                        lineNumber: 471,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground text-sm text-[var(--mantine-color-white)]\",\n                                        children: \"Pause\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                        lineNumber: 476,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Modal.CloseButton, {\n                                        className: \"mantine-focus-always\",\n                                        style: {\n                                            color: \"white\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                        lineNumber: 479,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                lineNumber: 470,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                        lineNumber: 444,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Modal.Body, {\n                        style: {\n                            padding: '0px'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-2 pl-4 h-[600px]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.SimpleBar, {\n                                className: \"simplebar-scrollable-y h-[calc(100%)]\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"pr-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: (e)=>{\n                                            e.preventDefault();\n                                            handleSubmit(appointmentForm.values);\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid gap-3 py-2 pr-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-4 mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Select, {\n                                                            value: eventTitle,\n                                                            onChange: (value)=>setEventTitle(value !== null && value !== void 0 ? value : \"\"),\n                                                            placeholder: \"Titre\",\n                                                            data: titleOptions,\n                                                            className: \"w-full\",\n                                                            leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarPlus_FaUserDoctor_react_icons_fa6__WEBPACK_IMPORTED_MODULE_11__.FaUserDoctor, {\n                                                                size: 16\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                lineNumber: 497,\n                                                                columnNumber: 38\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 491,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Menu, {\n                                                            width: 200,\n                                                            shadow: \"md\",\n                                                            closeOnItemClick: false,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Menu.Target, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Avatar, {\n                                                                        color: \"#4BA3D3\",\n                                                                        radius: \"sm\",\n                                                                        h: 36,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconColorPicker_IconHexagonPlusFilled_tabler_icons_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                            size: 20\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                            lineNumber: 502,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 501,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 500,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Menu.Dropdown, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.TextInput, {\n                                                                        leftSectionPointerEvents: \"none\",\n                                                                        leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarPlus_FaUserDoctor_react_icons_fa6__WEBPACK_IMPORTED_MODULE_11__.FaUserDoctor, {\n                                                                            size: 16\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                            lineNumber: 508,\n                                                                            columnNumber: 42\n                                                                        }, void 0),\n                                                                        placeholder: \"Ajouter des titres\",\n                                                                        value: newTitle,\n                                                                        onChange: (e)=>setNewTitle(e.target.value),\n                                                                        rightSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.ActionIcon, {\n                                                                            size: \"sm\",\n                                                                            onClick: ()=>{\n                                                                                if (newTitle.trim()) {\n                                                                                    const newTitleOption = {\n                                                                                        value: newTitle,\n                                                                                        label: newTitle\n                                                                                    };\n                                                                                    setTitleOptions([\n                                                                                        ...titleOptions,\n                                                                                        newTitleOption\n                                                                                    ]);\n                                                                                    setEventTitle(newTitle);\n                                                                                    setNewTitle(\"\");\n                                                                                    _mantine_notifications__WEBPACK_IMPORTED_MODULE_17__.notifications.show({\n                                                                                        title: 'Titre ajouté',\n                                                                                        message: '\"'.concat(newTitle, '\" a \\xe9t\\xe9 ajout\\xe9 \\xe0 la liste des titres'),\n                                                                                        color: 'green',\n                                                                                        autoClose: 2000\n                                                                                    });\n                                                                                }\n                                                                            },\n                                                                            disabled: !newTitle.trim(),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarPlus_FaUserDoctor_react_icons_fa6__WEBPACK_IMPORTED_MODULE_11__.FaCalendarPlus, {\n                                                                                size: 16\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                lineNumber: 531,\n                                                                                columnNumber: 33\n                                                                            }, void 0)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                            lineNumber: 513,\n                                                                            columnNumber: 31\n                                                                        }, void 0),\n                                                                        onKeyDown: (e)=>{\n                                                                            if (e.key === 'Enter' && newTitle.trim()) {\n                                                                                const newTitleOption = {\n                                                                                    value: newTitle,\n                                                                                    label: newTitle\n                                                                                };\n                                                                                setTitleOptions([\n                                                                                    ...titleOptions,\n                                                                                    newTitleOption\n                                                                                ]);\n                                                                                setEventTitle(newTitle);\n                                                                                setNewTitle(\"\");\n                                                                                _mantine_notifications__WEBPACK_IMPORTED_MODULE_17__.notifications.show({\n                                                                                    title: 'Titre ajouté',\n                                                                                    message: '\"'.concat(newTitle, '\" a \\xe9t\\xe9 ajout\\xe9 \\xe0 la liste des titres'),\n                                                                                    color: 'green',\n                                                                                    autoClose: 2000\n                                                                                });\n                                                                            }\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 506,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 505,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 499,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.TextInput, {\n                                                            id: \"event-nom\",\n                                                            placeholder: \"Nom *\",\n                                                            type: \"text\",\n                                                            value: patientName,\n                                                            onChange: (e)=>setPatientName(e.target.value),\n                                                            required: true,\n                                                            className: \"input input-bordered w-full\",\n                                                            leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiUserFollowLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_18__.RiUserFollowLine, {\n                                                                size: 16\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                lineNumber: 559,\n                                                                columnNumber: 38\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 551,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.TextInput, {\n                                                            id: \"event-prenom\",\n                                                            placeholder: \"Pr\\xe9nom *\",\n                                                            type: \"text\",\n                                                            value: patientlastName,\n                                                            onChange: (e)=>setPatientlastName(e.target.value),\n                                                            required: true,\n                                                            className: \"input input-bordered w-full\",\n                                                            leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiUserFollowLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_18__.RiUserFollowLine, {\n                                                                size: 16\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                lineNumber: 569,\n                                                                columnNumber: 38\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 561,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Avatar, {\n                                                            color: \"#4BA3D3\",\n                                                            radius: \"sm\",\n                                                            h: 36,\n                                                            onClick: openListDesPatient,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MdOutlineContentPasteSearch_MdOutlineSocialDistance_react_icons_md__WEBPACK_IMPORTED_MODULE_19__.MdOutlineContentPasteSearch, {\n                                                                size: 20\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                lineNumber: 572,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 571,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                    lineNumber: 490,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-4 mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.TextInput, {\n                                                            type: \"date\",\n                                                            placeholder: \"Date de Naissance...\",\n                                                            id: \"event-dateDeNaissance\",\n                                                            value: eventDateDeNaissance,\n                                                            onChange: handleDateChange,\n                                                            required: true,\n                                                            className: \"input input-bordered max-w-[278px] w-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 578,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.TextInput, {\n                                                            type: \"text\",\n                                                            id: \"event-age\",\n                                                            value: (_eventAge_toString = eventAge === null || eventAge === void 0 ? void 0 : eventAge.toString()) !== null && _eventAge_toString !== void 0 ? _eventAge_toString : \"\",\n                                                            placeholder: eventAge !== null ? eventAge.toString() : \"Veuillez entrer votre date de naissance\",\n                                                            readOnly: true,\n                                                            className: \"input input-bordered max-w-[278px] w-full\",\n                                                            leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LiaAddressCardSolid_LiaBirthdayCakeSolid_react_icons_lia__WEBPACK_IMPORTED_MODULE_20__.LiaBirthdayCakeSolid, {\n                                                                size: 16\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                lineNumber: 598,\n                                                                columnNumber: 38\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 587,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Radio.Group, {\n                                                                value: genderOption,\n                                                                onChange: handleOptionChange,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Radio, {\n                                                                            value: \"Homme\",\n                                                                            label: \"Homme\"\n                                                                        }, \"homme\", false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                            lineNumber: 606,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Radio, {\n                                                                            value: \"Femme\",\n                                                                            label: \"Femme\"\n                                                                        }, \"femme\", false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                            lineNumber: 607,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Radio, {\n                                                                            value: \"Enfant\",\n                                                                            label: \"Enfant\"\n                                                                        }, \"enfant\", false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                            lineNumber: 608,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 605,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                lineNumber: 601,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 600,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                    lineNumber: 577,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Select, {\n                                                            value: eventEtatCivil,\n                                                            onChange: (value)=>setEventEtatCivil(value !== null && value !== void 0 ? value : \"\"),\n                                                            placeholder: \"\\xc9tat civil\",\n                                                            data: [\n                                                                {\n                                                                    value: \"Célibataire\",\n                                                                    label: \"Célibataire\"\n                                                                },\n                                                                {\n                                                                    value: \"Marié(e)\",\n                                                                    label: \"Marié(e)\"\n                                                                },\n                                                                {\n                                                                    value: \"Divorcé(e)\",\n                                                                    label: \"Divorcé(e)\"\n                                                                },\n                                                                {\n                                                                    value: \"Veuf(ve)\",\n                                                                    label: \"Veuf(ve)\"\n                                                                },\n                                                                {\n                                                                    value: \"Autre chose\",\n                                                                    label: \"Autre chose\"\n                                                                }\n                                                            ],\n                                                            className: \"select w-full max-w-xs\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 616,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.TextInput, {\n                                                            placeholder: \"CIN\",\n                                                            disabled: genderOption === 'Enfant',\n                                                            value: eventCin,\n                                                            onChange: (e)=>setEventCin(e.target.value),\n                                                            styles: {\n                                                                input: {\n                                                                    backgroundColor: genderOption === 'Enfant' ? '#f5f5f5' : undefined,\n                                                                    color: genderOption === 'Enfant' ? '#999' : undefined\n                                                                }\n                                                            },\n                                                            className: \"input input-bordered mb-2 w-full\",\n                                                            leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TbNumber_react_icons_tb__WEBPACK_IMPORTED_MODULE_22__.TbNumber, {\n                                                                size: 16\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                lineNumber: 641,\n                                                                columnNumber: 38\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 629,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.TextInput, {\n                                                            id: \"Adresse\",\n                                                            placeholder: \"Adress\\xe9 par\",\n                                                            value: address,\n                                                            onChange: (e)=>setAddress(e.target.value),\n                                                            className: \"input input-bordered mb-2 w-full\",\n                                                            leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LiaAddressCardSolid_LiaBirthdayCakeSolid_react_icons_lia__WEBPACK_IMPORTED_MODULE_20__.LiaAddressCardSolid, {\n                                                                size: 16\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                lineNumber: 649,\n                                                                columnNumber: 38\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 643,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                    lineNumber: 615,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_23__.InputBase, {\n                                                            id: \"T\\xe9l\\xe9phone\",\n                                                            component: react_imask__WEBPACK_IMPORTED_MODULE_2__.IMaskInput,\n                                                            mask: \"00-00-00-00-00\",\n                                                            placeholder: \"T\\xe9l\\xe9phone\",\n                                                            value: eventTelephone,\n                                                            onAccept: (value)=>setEventTelephone(value),\n                                                            unmask: true,\n                                                            className: \"input input-bordered mb-2 w-full\",\n                                                            leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiPhone_react_icons_fi__WEBPACK_IMPORTED_MODULE_24__.FiPhone, {\n                                                                size: 16\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                lineNumber: 664,\n                                                                columnNumber: 38\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 655,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.TextInput, {\n                                                            id: \"Email\",\n                                                            placeholder: \"Email\",\n                                                            value: email,\n                                                            onChange: (e)=>setEmail(e.target.value),\n                                                            className: \"input input-bordered mb-2 w-full\",\n                                                            leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CiAt_react_icons_ci__WEBPACK_IMPORTED_MODULE_25__.CiAt, {\n                                                                size: 16\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                lineNumber: 672,\n                                                                columnNumber: 38\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 666,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                    lineNumber: 654,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Select, {\n                                                            value: patientdoctor,\n                                                            onChange: (value)=>setPatientDocteur(value !== null && value !== void 0 ? value : \"\"),\n                                                            placeholder: \"Docteur\",\n                                                            data: [\n                                                                {\n                                                                    value: \"Docteur\",\n                                                                    label: \"Docteur\"\n                                                                },\n                                                                {\n                                                                    value: \"dr.Kader\",\n                                                                    label: \"dr.Kader\"\n                                                                },\n                                                                {\n                                                                    value: \"dr.Kaders\",\n                                                                    label: \"dr.Kaders\"\n                                                                }\n                                                            ],\n                                                            className: \"w-full\",\n                                                            leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarPlus_FaUserDoctor_react_icons_fa6__WEBPACK_IMPORTED_MODULE_11__.FaUserDoctor, {\n                                                                size: 16\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                lineNumber: 688,\n                                                                columnNumber: 38\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 678,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Select, {\n                                                            value: patientsocialSecurity || 'Aucune',\n                                                            onChange: (value)=>setSocialSecurity(value || 'Aucune'),\n                                                            placeholder: \"S\\xe9curit\\xe9 sociale\",\n                                                            data: [\n                                                                {\n                                                                    value: \"Aucune\",\n                                                                    label: \"Aucune\"\n                                                                },\n                                                                {\n                                                                    value: \"CNSS\",\n                                                                    label: \"CNSS\"\n                                                                },\n                                                                {\n                                                                    value: \"AMO\",\n                                                                    label: \"AMO\"\n                                                                }\n                                                            ],\n                                                            className: \"w-full\",\n                                                            leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MdOutlineContentPasteSearch_MdOutlineSocialDistance_react_icons_md__WEBPACK_IMPORTED_MODULE_19__.MdOutlineSocialDistance, {\n                                                                size: 16\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                lineNumber: 700,\n                                                                columnNumber: 38\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 690,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                    lineNumber: 677,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-4 mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Select, {\n                                                            label: \"Type de consultation\",\n                                                            placeholder: \"Rechercher ou saisir...\",\n                                                            data: consultationTypes,\n                                                            value: patienttypeConsultation,\n                                                            onChange: (value)=>{\n                                                                setPatientTypeConsultation(value !== null && value !== void 0 ? value : \"\");\n                                                                const selectedLabel = [\n                                                                    {\n                                                                        value: \"Visite de malade\",\n                                                                        eventType: \"visit\"\n                                                                    },\n                                                                    {\n                                                                        value: \"Visitor Counter\",\n                                                                        eventType: \"visitor-counter\"\n                                                                    },\n                                                                    {\n                                                                        value: \"Completed\",\n                                                                        eventType: \"completed\"\n                                                                    }\n                                                                ].find((item)=>item.value === value);\n                                                                if (selectedLabel) {\n                                                                    setEventType(selectedLabel.eventType);\n                                                                }\n                                                            },\n                                                            searchable: true,\n                                                            searchValue: searchValue,\n                                                            onSearchChange: setSearchValue,\n                                                            clearable: true,\n                                                            maxDropdownHeight: 280,\n                                                            rightSectionWidth: 70,\n                                                            required: true,\n                                                            rightSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-[#4CAF50] text-white px-2 py-1 rounded text-xs\",\n                                                                children: dureeDeLexamen\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                lineNumber: 731,\n                                                                columnNumber: 27\n                                                            }, void 0),\n                                                            allowDeselect: true,\n                                                            className: \"w-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 706,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Menu, {\n                                                            width: 260,\n                                                            shadow: \"md\",\n                                                            closeOnItemClick: false,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Menu.Target, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Avatar, {\n                                                                        color: \"#4BA3D3\",\n                                                                        radius: \"sm\",\n                                                                        h: 36,\n                                                                        mt: \"24\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconColorPicker_IconHexagonPlusFilled_tabler_icons_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                            size: 20\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                            lineNumber: 739,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 738,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 737,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Menu.Dropdown, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.TextInput, {\n                                                                                leftSectionPointerEvents: \"none\",\n                                                                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarPlus_FaUserDoctor_react_icons_fa6__WEBPACK_IMPORTED_MODULE_11__.FaUserDoctor, {\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                    lineNumber: 746,\n                                                                                    columnNumber: 44\n                                                                                }, void 0),\n                                                                                placeholder: \"Ajouter des Consultation\",\n                                                                                value: newConsultationType,\n                                                                                onChange: (e)=>setNewConsultationType(e.target.value),\n                                                                                rightSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.ActionIcon, {\n                                                                                    size: \"sm\",\n                                                                                    onClick: ()=>{\n                                                                                        if (newConsultationType.trim()) {\n                                                                                            const newType = {\n                                                                                                value: newConsultationType,\n                                                                                                label: newConsultationType,\n                                                                                                duration: dureeDeLexamen || \"15 min\"\n                                                                                            };\n                                                                                            setConsultationTypes([\n                                                                                                ...consultationTypes,\n                                                                                                newType\n                                                                                            ]);\n                                                                                            setPatientTypeConsultation(newConsultationType);\n                                                                                            setNewConsultationType(\"\");\n                                                                                            _mantine_notifications__WEBPACK_IMPORTED_MODULE_17__.notifications.show({\n                                                                                                title: 'Type de consultation ajouté',\n                                                                                                message: '\"'.concat(newConsultationType, '\" a \\xe9t\\xe9 ajout\\xe9'),\n                                                                                                color: 'green',\n                                                                                                autoClose: 2000\n                                                                                            });\n                                                                                        }\n                                                                                    },\n                                                                                    disabled: !newConsultationType.trim(),\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarPlus_FaUserDoctor_react_icons_fa6__WEBPACK_IMPORTED_MODULE_11__.FaCalendarPlus, {\n                                                                                        size: 16\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                        lineNumber: 773,\n                                                                                        columnNumber: 35\n                                                                                    }, void 0)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                    lineNumber: 751,\n                                                                                    columnNumber: 33\n                                                                                }, void 0),\n                                                                                onKeyDown: (e)=>{\n                                                                                    if (e.key === 'Enter' && newConsultationType.trim()) {\n                                                                                        const newType = {\n                                                                                            value: newConsultationType,\n                                                                                            label: newConsultationType,\n                                                                                            duration: dureeDeLexamen || \"15 min\"\n                                                                                        };\n                                                                                        setConsultationTypes([\n                                                                                            ...consultationTypes,\n                                                                                            newType\n                                                                                        ]);\n                                                                                        setPatientTypeConsultation(newConsultationType);\n                                                                                        setNewConsultationType(\"\");\n                                                                                        _mantine_notifications__WEBPACK_IMPORTED_MODULE_17__.notifications.show({\n                                                                                            title: 'Type de consultation ajouté',\n                                                                                            message: '\"'.concat(newConsultationType, '\" a \\xe9t\\xe9 ajout\\xe9'),\n                                                                                            color: 'green',\n                                                                                            autoClose: 2000\n                                                                                        });\n                                                                                    }\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                lineNumber: 744,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Avatar, {\n                                                                                color: newConsultationColor,\n                                                                                radius: \"sm\",\n                                                                                ml: 4,\n                                                                                h: 36,\n                                                                                onClick: openedColorPicker,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                    fill: \"none\",\n                                                                                    viewBox: \"0 0 200 200\",\n                                                                                    style: {\n                                                                                        width: \"26px\",\n                                                                                        height: \"26px\"\n                                                                                    },\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        fill: \"#FF5178\",\n                                                                                        d: \"M100 0a100 100 0 00-50 13.398l30 51.961A40 40 0 01100 60V0z\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                        lineNumber: 805,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                    lineNumber: 802,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                lineNumber: 795,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 743,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 742,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 736,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Modal, {\n                                                            opened: ColorPickeropened,\n                                                            onClose: closeColorPicker,\n                                                            size: \"auto\",\n                                                            yOffset: \"18vh\",\n                                                            xOffset: 30,\n                                                            withCloseButton: false,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_26__.ColorPicker, {\n                                                                    defaultValue: newConsultationColor,\n                                                                    value: newConsultationColor,\n                                                                    onChange: setNewConsultationColor,\n                                                                    onChangeEnd: setChangeEndValue,\n                                                                    format: \"hex\",\n                                                                    swatches: [\n                                                                        '#2e2e2e',\n                                                                        '#868e96',\n                                                                        '#fa5252',\n                                                                        '#e64980',\n                                                                        '#be4bdb',\n                                                                        '#7950f2',\n                                                                        '#4c6ef5',\n                                                                        '#228be6',\n                                                                        '#15aabf',\n                                                                        '#12b886',\n                                                                        '#40c057',\n                                                                        '#82c91e',\n                                                                        '#fab005',\n                                                                        '#fd7e14'\n                                                                    ]\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 812,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Group, {\n                                                                    justify: \"center\",\n                                                                    mt: 8,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                        variant: \"filled\",\n                                                                        w: \"100%\",\n                                                                        color: \"\".concat(newConsultationColor),\n                                                                        leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconColorPicker_IconHexagonPlusFilled_tabler_icons_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                            stroke: 1,\n                                                                            size: 18\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                            lineNumber: 825,\n                                                                            columnNumber: 42\n                                                                        }, void 0),\n                                                                        onClick: ()=>{\n                                                                            setNewConsultationColor(changeEndValue);\n                                                                            closeColorPicker();\n                                                                        },\n                                                                        children: \"S\\xe9lectionner cette couleur\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 821,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 820,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 811,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Select, {\n                                                            label: \"Dur\\xe9e\",\n                                                            value: dureeDeLexamen,\n                                                            onChange: (value)=>setDureeDeLexamen(value !== null && value !== void 0 ? value : \"\"),\n                                                            placeholder: \"15 min\",\n                                                            data: [\n                                                                \"10 min\",\n                                                                \"15 min\",\n                                                                \"20 min\",\n                                                                \"25 min\",\n                                                                \"30 min\",\n                                                                \"35 min\",\n                                                                \"40 min\",\n                                                                \"45 min\"\n                                                            ],\n                                                            className: \"select w-full max-w-xs\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 836,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Select, {\n                                                            label: \"Agenda\",\n                                                            value: eventAganda,\n                                                            onChange: (value)=>setEventAganda(value !== null && value !== void 0 ? value : \"\"),\n                                                            placeholder: \"Ajouter des Agenda\",\n                                                            data: agendaTypes,\n                                                            className: \"w-full\",\n                                                            leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarPlus_FaUserDoctor_react_icons_fa6__WEBPACK_IMPORTED_MODULE_11__.FaUserDoctor, {\n                                                                size: 16\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                lineNumber: 851,\n                                                                columnNumber: 38\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 844,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Menu, {\n                                                            width: 200,\n                                                            shadow: \"md\",\n                                                            closeOnItemClick: false,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Menu.Target, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Avatar, {\n                                                                        color: \"#4BA3D3\",\n                                                                        radius: \"sm\",\n                                                                        h: 36,\n                                                                        mt: \"24\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconColorPicker_IconHexagonPlusFilled_tabler_icons_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                            size: 20\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                            lineNumber: 856,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 855,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 854,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Menu.Dropdown, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.TextInput, {\n                                                                        leftSectionPointerEvents: \"none\",\n                                                                        leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarPlus_FaUserDoctor_react_icons_fa6__WEBPACK_IMPORTED_MODULE_11__.FaUserDoctor, {\n                                                                            size: 16\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                            lineNumber: 862,\n                                                                            columnNumber: 42\n                                                                        }, void 0),\n                                                                        placeholder: \"Ajouter des Agenda\",\n                                                                        value: newAgendaType,\n                                                                        onChange: (e)=>setNewAgendaType(e.target.value),\n                                                                        rightSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.ActionIcon, {\n                                                                            size: \"sm\",\n                                                                            onClick: ()=>{\n                                                                                if (newAgendaType.trim()) {\n                                                                                    const newAgendaOption = {\n                                                                                        value: newAgendaType,\n                                                                                        label: newAgendaType\n                                                                                    };\n                                                                                    setAgendaTypes([\n                                                                                        ...agendaTypes,\n                                                                                        newAgendaOption\n                                                                                    ]);\n                                                                                    setEventAganda(newAgendaType);\n                                                                                    setNewAgendaType(\"\");\n                                                                                    _mantine_notifications__WEBPACK_IMPORTED_MODULE_17__.notifications.show({\n                                                                                        title: 'Agenda ajouté',\n                                                                                        message: '\"'.concat(newAgendaType, '\" a \\xe9t\\xe9 ajout\\xe9 \\xe0 la liste des agendas'),\n                                                                                        color: 'green',\n                                                                                        autoClose: 2000\n                                                                                    });\n                                                                                }\n                                                                            },\n                                                                            disabled: !newAgendaType.trim(),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarPlus_FaUserDoctor_react_icons_fa6__WEBPACK_IMPORTED_MODULE_11__.FaCalendarPlus, {\n                                                                                size: 16\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                lineNumber: 885,\n                                                                                columnNumber: 33\n                                                                            }, void 0)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                            lineNumber: 867,\n                                                                            columnNumber: 31\n                                                                        }, void 0),\n                                                                        onKeyDown: (e)=>{\n                                                                            if (e.key === 'Enter' && newAgendaType.trim()) {\n                                                                                const newAgendaOption = {\n                                                                                    value: newAgendaType,\n                                                                                    label: newAgendaType\n                                                                                };\n                                                                                setAgendaTypes([\n                                                                                    ...agendaTypes,\n                                                                                    newAgendaOption\n                                                                                ]);\n                                                                                setEventAganda(newAgendaType);\n                                                                                setNewAgendaType(\"\");\n                                                                                _mantine_notifications__WEBPACK_IMPORTED_MODULE_17__.notifications.show({\n                                                                                    title: 'Agenda ajouté',\n                                                                                    message: '\"'.concat(newAgendaType, '\" a \\xe9t\\xe9 ajout\\xe9 \\xe0 la liste des agendas'),\n                                                                                    color: 'green',\n                                                                                    autoClose: 2000\n                                                                                });\n                                                                            }\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 860,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 859,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 853,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                    lineNumber: 705,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                            lineNumber: 488,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                        lineNumber: 487,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                    lineNumber: 486,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                lineNumber: 485,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                            lineNumber: 484,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                        lineNumber: 483,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                lineNumber: 443,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n        lineNumber: 437,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = AjouterUnRendezVous;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AjouterUnRendezVous);\nvar _c, _c1;\n$RefreshReg$(_c, \"RendezVousSelector\");\n$RefreshReg$(_c1, \"AjouterUnRendezVous\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Appointments/overview/AjouterUnRendezVous.tsx\n"));

/***/ })

});