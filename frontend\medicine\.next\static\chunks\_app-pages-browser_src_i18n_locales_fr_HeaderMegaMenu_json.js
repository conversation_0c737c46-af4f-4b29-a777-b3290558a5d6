"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_i18n_locales_fr_HeaderMegaMenu_json"],{

/***/ "(app-pages-browser)/./src/i18n/locales/fr/HeaderMegaMenu.json":
/*!*************************************************!*\
  !*** ./src/i18n/locales/fr/HeaderMegaMenu.json ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

module.exports = /*#__PURE__*/JSON.parse('{"Default":{"Default":"Accueil"},"Agenda":{"Agenda":"Agenda"},"FluxJour":{"FluxJour":"Flux de journaux"},"Visites":{"Visites":"Visites"},"Recettes":{"Recettes":"Recettes"},"Mutuelles":{"Mutuelles":"Mutuelles"},"Facturation":{"MesFacturs":"Facturation","MesDevis":"Mes devis","MesReglements":"Mes réglements","MesDepenses":"Mes dépenses","MesContrats":"Mes contrats"},"Pharmacie":{"Pharmacie":"Pharmacie"},"Etats":{"Etats":"États"}}');

/***/ })

}]);