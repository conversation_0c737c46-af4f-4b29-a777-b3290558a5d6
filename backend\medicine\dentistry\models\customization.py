"""
Customization models for the dentistry application.
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from dentistry.models.base import DentistryBaseModel
from dentistry.models.doctor import DentistryDoctor

class DentistryTemplate(DentistryBaseModel):
    """
    Dentistry-specific template model.
    """
    doctor = models.ForeignKey(
        DentistryDoctor,
        on_delete=models.CASCADE,
        related_name="dentistry_templates",
        verbose_name=_("Doctor")
    )
    name = models.CharField(
        max_length=100,
        verbose_name=_("Template Name")
    )
    description = models.TextField(
        blank=True,
        verbose_name=_("Description")
    )
    TYPE_CHOICES = (
        ('medical_record', _('Medical Record')),
        ('treatment_plan', _('Treatment Plan')),
        ('prescription', _('Prescription')),
        ('referral', _('Referral')),
        ('consent_form', _('Consent Form')),
        ('post_op_instructions', _('Post-Op Instructions')),
        ('lab_order', _('Lab Order')),
        ('patient_education', _('Patient Education')),
    )
    template_type = models.CharField(
        max_length=20,
        choices=TYPE_CHOICES,
        verbose_name=_("Template Type")
    )
    content = models.TextField(
        verbose_name=_("Template Content")
    )
    is_default = models.BooleanField(
        default=False,
        verbose_name=_("Is Default Template")
    )

    class Meta:
        verbose_name = _("Dentistry Template")
        verbose_name_plural = _("Dentistry Templates")
        ordering = ['template_type', 'name']
        unique_together = ['doctor', 'template_type', 'name']

    def __str__(self):
        return f"{self.get_template_type_display()}: {self.name}"

class DentistryCustomField(DentistryBaseModel):
    """
    Dentistry-specific custom field model.
    """
    doctor = models.ForeignKey(
        DentistryDoctor,
        on_delete=models.CASCADE,
        related_name="dentistry_custom_fields",
        verbose_name=_("Doctor")
    )
    name = models.CharField(
        max_length=100,
        verbose_name=_("Field Name")
    )
    description = models.TextField(
        blank=True,
        verbose_name=_("Description")
    )
    FIELD_TYPE_CHOICES = (
        ('text', _('Text')),
        ('number', _('Number')),
        ('date', _('Date')),
        ('boolean', _('Boolean')),
        ('select', _('Select')),
        ('multi_select', _('Multi-Select')),
    )
    field_type = models.CharField(
        max_length=20,
        choices=FIELD_TYPE_CHOICES,
        verbose_name=_("Field Type")
    )
    ENTITY_TYPE_CHOICES = (
        ('patient', _('Patient')),
        ('medical_record', _('Medical Record')),
        ('treatment', _('Treatment')),
        ('procedure', _('Procedure')),
        ('appointment', _('Appointment')),
        ('lab_work', _('Lab Work')),
    )
    entity_type = models.CharField(
        max_length=20,
        choices=ENTITY_TYPE_CHOICES,
        verbose_name=_("Entity Type")
    )
    options = models.JSONField(
        default=list,
        blank=True,
        verbose_name=_("Field Options"),
        help_text=_("Options for select and multi-select fields")
    )
    is_required = models.BooleanField(
        default=False,
        verbose_name=_("Is Required")
    )
    default_value = models.CharField(
        max_length=255,
        blank=True,
        verbose_name=_("Default Value")
    )
    display_order = models.PositiveIntegerField(
        default=0,
        verbose_name=_("Display Order")
    )

    class Meta:
        verbose_name = _("Dentistry Custom Field")
        verbose_name_plural = _("Dentistry Custom Fields")
        ordering = ['entity_type', 'display_order', 'name']
        unique_together = ['doctor', 'entity_type', 'name']

    def __str__(self):
        return f"{self.get_entity_type_display()}: {self.name}"

class DentistryCustomFieldValue(DentistryBaseModel):
    """
    Dentistry-specific custom field value model.
    """
    custom_field = models.ForeignKey(
        DentistryCustomField,
        on_delete=models.CASCADE,
        related_name="values",
        verbose_name=_("Custom Field")
    )
    entity_id = models.PositiveIntegerField(
        verbose_name=_("Entity ID")
    )
    value_text = models.TextField(
        blank=True,
        verbose_name=_("Text Value")
    )
    value_number = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name=_("Number Value")
    )
    value_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_("Date Value")
    )
    value_boolean = models.BooleanField(
        null=True,
        blank=True,
        verbose_name=_("Boolean Value")
    )
    value_json = models.JSONField(
        default=dict,
        blank=True,
        verbose_name=_("JSON Value")
    )

    class Meta:
        verbose_name = _("Dentistry Custom Field Value")
        verbose_name_plural = _("Dentistry Custom Field Values")
        unique_together = ['custom_field', 'entity_id']

    def __str__(self):
        return f"{self.custom_field.name} for {self.custom_field.get_entity_type_display()} #{self.entity_id}"

    @property
    def value(self):
        """
        Get the value based on the field type.
        """
        field_type = self.custom_field.field_type
        if field_type == 'text':
            return self.value_text
        elif field_type == 'number':
            return self.value_number
        elif field_type == 'date':
            return self.value_date
        elif field_type == 'boolean':
            return self.value_boolean
        elif field_type in ['select', 'multi_select']:
            return self.value_json
        return None
