"""
Treatment models for the dentistry application.
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from dentistry.models.base import DentistryBaseModel
from dentistry.models.doctor import DentistryDoctor
from dentistry.models.patient import DentistryPatient

class DentalTreatment(DentistryBaseModel):
    """
    Dental treatment plan model.
    """
    patient = models.ForeignKey(
        DentistryPatient,
        on_delete=models.CASCADE,
        related_name="treatment_plans",
        verbose_name=_("Patient")
    )
    doctor = models.ForeignKey(
        DentistryDoctor,
        on_delete=models.SET_NULL,
        null=True,
        related_name="dental_treatment_plans",
        verbose_name=_("Doctor")
    )

    title = models.CharField(
        max_length=200,
        verbose_name=_("Treatment Plan Title")
    )
    description = models.TextField(
        verbose_name=_("Description")
    )
    start_date = models.DateField(
        verbose_name=_("Start Date")
    )
    estimated_end_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_("Estimated End Date")
    )
    actual_end_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_("Actual End Date")
    )

    STATUS_CHOICES = (
        ('planned', _('Planned')),
        ('in_progress', _('In Progress')),
        ('completed', _('Completed')),
        ('cancelled', _('Cancelled')),
    )
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='planned',
        verbose_name=_("Status")
    )

    estimated_cost = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name=_("Estimated Cost")
    )
    insurance_coverage = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name=_("Insurance Coverage")
    )
    patient_responsibility = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name=_("Patient Responsibility")
    )

    notes = models.TextField(
        blank=True,
        verbose_name=_("Notes")
    )

    class Meta:
        verbose_name = _("Dental Treatment Plan")
        verbose_name_plural = _("Dental Treatment Plans")
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.title} for {self.patient.full_name}"

    @property
    def progress_percentage(self):
        """
        Calculate the progress percentage of the treatment plan.
        """
        total_procedures = self.procedures.count()
        if total_procedures == 0:
            return 0

        completed_procedures = self.procedures.filter(status='completed').count()
        return int((completed_procedures / total_procedures) * 100)

class DentalProcedure(DentistryBaseModel):
    """
    Individual dental procedure within a treatment plan.
    """
    treatment = models.ForeignKey(
        DentalTreatment,
        on_delete=models.CASCADE,
        related_name="procedures",
        verbose_name=_("Treatment Plan")
    )

    PROCEDURE_TYPE_CHOICES = (
        ('diagnostic', _('Diagnostic')),
        ('preventive', _('Preventive')),
        ('restorative', _('Restorative')),
        ('endodontic', _('Endodontic')),
        ('periodontic', _('Periodontic')),
        ('prosthodontic', _('Prosthodontic')),
        ('oral_surgery', _('Oral Surgery')),
        ('orthodontic', _('Orthodontic')),
        ('other', _('Other')),
    )
    procedure_type = models.CharField(
        max_length=20,
        choices=PROCEDURE_TYPE_CHOICES,
        verbose_name=_("Procedure Type")
    )

    name = models.CharField(
        max_length=200,
        verbose_name=_("Procedure Name")
    )
    description = models.TextField(
        blank=True,
        verbose_name=_("Description")
    )
    tooth_numbers = models.CharField(
        max_length=100,
        blank=True,
        verbose_name=_("Tooth Numbers"),
        help_text=_("Comma-separated list of tooth numbers")
    )

    scheduled_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_("Scheduled Date")
    )
    completed_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_("Completed Date")
    )

    STATUS_CHOICES = (
        ('planned', _('Planned')),
        ('scheduled', _('Scheduled')),
        ('in_progress', _('In Progress')),
        ('completed', _('Completed')),
        ('cancelled', _('Cancelled')),
    )
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='planned',
        verbose_name=_("Status")
    )

    cost = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name=_("Cost")
    )

    notes = models.TextField(
        blank=True,
        verbose_name=_("Notes")
    )

    class Meta:
        verbose_name = _("Dental Procedure")
        verbose_name_plural = _("Dental Procedures")
        ordering = ['scheduled_date', 'created_at']

    def __str__(self):
        return f"{self.name} ({self.get_procedure_type_display()})"

    def save(self, *args, **kwargs):
        """
        Override save to update treatment plan status.
        """
        super().save(*args, **kwargs)

        # Update the treatment plan status if needed
        treatment = self.treatment
        if treatment.status == 'planned' and self.status in ['in_progress', 'completed']:
            treatment.status = 'in_progress'
            treatment.save(update_fields=['status'])

        # Check if all procedures are completed
        if treatment.status == 'in_progress':
            all_completed = all(
                p.status == 'completed'
                for p in treatment.procedures.exclude(id=self.id)
            ) and self.status == 'completed'

            if all_completed:
                from datetime import date
                treatment.status = 'completed'
                treatment.actual_end_date = date.today()
                treatment.save(update_fields=['status', 'actual_end_date'])
