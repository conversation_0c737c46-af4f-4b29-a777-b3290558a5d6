from django.core.management.base import BaseCommand
from django.utils import timezone
from django.db import transaction
from django.utils.text import slugify
from users.models import User, DoctorLicense, Specialty
import uuid
import random
from datetime import timedelta

class Command(BaseCommand):
    help = 'Creates test accounts for a doctor and assistants to test registration stages'

    def add_arguments(self, parser):
        parser.add_argument('--count', type=int, default=1, help='Number of doctor accounts to create')
        parser.add_argument('--assistants', type=int, default=2, help='Number of assistants per doctor')
        parser.add_argument('--specialty', type=str, default='cardiology', help='Specialty for the doctors')
        parser.add_argument('--activated', action='store_true', help='Create activated accounts')

    def handle(self, *args, **options):
        count = options['count']
        assistants_count = options['assistants']
        specialty_name = options['specialty']
        create_activated = options['activated']

        # Get or create the specialty
        specialty, created = Specialty.objects.get_or_create(
            name=specialty_name.capitalize(),
            defaults={
                'description': f"Medical specialty focused on {specialty_name.lower()} care",
                'slug': slugify(specialty_name.lower())
            }
        )

        if created:
            self.stdout.write(self.style.SUCCESS(f'Created new specialty: {specialty.name}'))

        doctors_created = 0
        assistants_created = 0

        for i in range(count):
            with transaction.atomic():
                # Create doctor
                doctor_email = f"testdoctor{i+1}_{timezone.now().strftime('%Y%m%d%H%M%S')}@example.com"
                doctor = User.objects.create_user(
                    email=doctor_email,
                    password="Test@123",
                    first_name=f"Doctor{i+1}",
                    last_name=f"Test",
                    user_type="doctor",
                    phone_number=f"+1555{random.randint(1000000, 9999999)}",
                )

                # Associate doctor with specialty
                doctor.specialties.add(specialty)

                # Create license for doctor
                license_number = str(uuid.uuid4())
                issue_date = timezone.now() - timedelta(days=random.randint(1, 30))
                expiry_date = issue_date + timedelta(days=365)

                license_status = 'active' if create_activated else 'pending'
                activation_date = timezone.now() if create_activated else None

                # Create the license without is_active (it's a property)
                doctor_license = DoctorLicense.objects.create(
                    user=doctor,
                    license_number=license_number,
                    status=license_status,
                    issue_date=issue_date,
                    expiry_date=expiry_date,
                    activation_date=activation_date,
                    initial_days=365,
                    days_remaining=365,
                    transaction_id=f"TRANS-{uuid.uuid4().hex[:8].upper()}",
                    payment_method='bank_transfer',
                    payment_status='verified' if create_activated else 'pending_verification',
                    payment_amount='299.00',
                    payment_bank='Medical Bank',
                    payment_account='1234-**************',
                )

                doctors_created += 1
                self.stdout.write(self.style.SUCCESS(
                    f'Created doctor: {doctor.email} with license: {license_number}'
                ))

                # Note: Assistants are no longer created automatically
                # They should be created manually by the doctor through the user management interface
                # This ensures better control over assistant creation and subscription limits
                self.stdout.write(self.style.WARNING(
                    f'Doctor {doctor.email} created without assistants. '
                    f'Assistants should be created manually through the user management interface.'
                ))

        self.stdout.write(self.style.SUCCESS(
            f'Successfully created {doctors_created} doctors. '
            f'Assistants should be created manually through the user management interface.'
        ))

        # Print test credentials
        self.stdout.write("\nTest Credentials:")
        self.stdout.write("----------------")
        self.stdout.write(f"Doctor Email: {doctor_email}")
        self.stdout.write(f"Doctor Password: Test@123")
        self.stdout.write(f"Doctor License: {license_number}")
        self.stdout.write(f"License Status: {license_status}")
        self.stdout.write(f"Transaction ID: {doctor_license.transaction_id}")

        if assistants_count > 0:
            self.stdout.write(f"\nAssistant Email: {assistant_email}")
            self.stdout.write(f"Assistant Password: Test@123")
