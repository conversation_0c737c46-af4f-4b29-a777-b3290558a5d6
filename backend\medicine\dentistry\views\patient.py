"""
Patient views for the dentistry application.
"""
from rest_framework import viewsets, permissions, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from dentistry.models import DentistryPatient
from dentistry.serializers import DentistryPatientSerializer

class DentistryPatientViewSet(viewsets.ModelViewSet):
    """
    API endpoint for dentistry patients.
    """
    queryset = DentistryPatient.objects.all()
    serializer_class = DentistryPatientSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['has_dentures', 'has_implants', 'has_braces', 'teeth_grinding', 'sensitive_teeth']
    search_fields = ['patient__first_name', 'patient__last_name', 'dental_history']
    ordering_fields = ['created_at', 'last_cleaning_date']
    
    @action(detail=True, methods=['post'])
    def update_tooth_status(self, request, pk=None):
        """
        Update the status of a specific tooth.
        """
        patient = self.get_object()
        
        # Validate required fields
        tooth_number = request.data.get('tooth_number')
        status = request.data.get('status')
        
        if not tooth_number or not status:
            return Response(
                {"detail": "Tooth number and status are required."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            tooth_number = int(tooth_number)
            if tooth_number < 1 or tooth_number > 32:
                return Response(
                    {"detail": "Tooth number must be between 1 and 32."},
                    status=status.HTTP_400_BAD_REQUEST
                )
        except ValueError:
            return Response(
                {"detail": "Tooth number must be a valid integer."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        notes = request.data.get('notes', '')
        
        # Update the tooth status
        success = patient.update_tooth_status(tooth_number, status, notes)
        
        if success:
            return Response(
                {"status": "success", "message": f"Tooth {tooth_number} status updated to {status}."},
                status=status.HTTP_200_OK
            )
        else:
            return Response(
                {"detail": "Failed to update tooth status."},
                status=status.HTTP_400_BAD_REQUEST
            )
    
    @action(detail=True, methods=['post'])
    def initialize_teeth_chart(self, request, pk=None):
        """
        Initialize or reset the teeth chart for a patient.
        """
        patient = self.get_object()
        patient.initialize_teeth_chart()
        
        return Response(
            {"status": "success", "message": "Teeth chart initialized."},
            status=status.HTTP_200_OK
        )
