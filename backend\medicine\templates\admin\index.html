{% extends "admin/base_site.html" %}
{% load i18n static admin_urls %}

{% block extrahead %}
    {{ block.super }}
    <style>
        .model-group {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin-bottom: 20px;
            padding: 15px;
        }
        .model-group h3 {
            color: #495057;
            margin-bottom: 15px;
            font-size: 18px;
            border-bottom: 2px solid #007cba;
            padding-bottom: 8px;
        }
        .model-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 10px;
        }
        .model-item {
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            transition: all 0.2s;
        }
        .model-item:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transform: translateY(-1px);
        }
        .model-item a {
            text-decoration: none;
            color: #007cba;
            font-weight: 500;
        }
        .model-item a:hover {
            color: #005a87;
        }
        .model-count {
            color: #6c757d;
            font-size: 12px;
            margin-left: 5px;
        }
        .other-apps {
            margin-top: 30px;
        }
        .stats-summary {
            background: linear-gradient(135deg, #007cba, #005a87);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            text-align: center;
        }
        .stats-summary h2 {
            margin: 0 0 10px 0;
            font-size: 24px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .stat-item {
            background: rgba(255,255,255,0.1);
            padding: 10px;
            border-radius: 4px;
            text-align: center;
        }
        .stat-number {
            font-size: 28px;
            font-weight: bold;
            display: block;
        }
        .stat-label {
            font-size: 14px;
            opacity: 0.9;
        }
    </style>
{% endblock %}

{% block content %}
<div id="content-main">
    
    <!-- Statistics Summary -->
    <div class="stats-summary">
        <h2>🦷 Système de Gestion Dentaire</h2>
        <p>Administration complète avec {{ total_models|default:"65+" }} modèles organisés en 9 groupes fonctionnels</p>
        
        <div class="stats-grid">
            <div class="stat-item">
                <span class="stat-number">9</span>
                <span class="stat-label">Groupes Fonctionnels</span>
            </div>
            <div class="stat-item">
                <span class="stat-number">62+</span>
                <span class="stat-label">Modèles Dentaires</span>
            </div>
            <div class="stat-item">
                <span class="stat-number">3</span>
                <span class="stat-label">Applications</span>
            </div>
            <div class="stat-item">
                <span class="stat-number">✅</span>
                <span class="stat-label">Système Opérationnel</span>
            </div>
        </div>
    </div>

    <!-- Dentistry Models Organized by Groups -->
    {% if app_list %}
        {% for app in app_list %}
            {% if app.app_label == "dentistry" %}
                
                <!-- Group 1: Patients & Doctors -->
                <div class="model-group">
                    <h3>👥 Patients et Médecins</h3>
                    <div class="model-list">
                        {% for model in app.models %}
                            {% if model.object_name in "DentalPatient,DentistryPatient,DentistryPatientInsurance,DentistryDoctor,DentistryDoctorNote,DentistryDoctorSettings,DentistryRole,DentistryStaffProfile,PatientDiagnosis" %}
                                <div class="model-item">
                                    {% if model.admin_url %}<a href="{{ model.admin_url }}">{{ model.name }}</a>{% else %}{{ model.name }}{% endif %}
                                    {% if model.add_url %}<a href="{{ model.add_url }}" class="addlink">{% trans 'Add' %}</a>{% endif %}
                                </div>
                            {% endif %}
                        {% endfor %}
                    </div>
                </div>

                <!-- Group 2: Appointments & Consultations -->
                <div class="model-group">
                    <h3>📅 Rendez-vous et Consultations</h3>
                    <div class="model-list">
                        {% for model in app.models %}
                            {% if model.object_name in "DentistryAppointment,DentalConsultation,Tooth,DentistryAppointmentSettings,ToothModificationEstimate" %}
                                <div class="model-item">
                                    {% if model.admin_url %}<a href="{{ model.admin_url }}">{{ model.name }}</a>{% else %}{{ model.name }}{% endif %}
                                    {% if model.add_url %}<a href="{{ model.add_url }}" class="addlink">{% trans 'Add' %}</a>{% endif %}
                                </div>
                            {% endif %}
                        {% endfor %}
                    </div>
                </div>

                <!-- Group 3: Treatments & Procedures -->
                <div class="model-group">
                    <h3>🦷 Traitements et Procédures</h3>
                    <div class="model-list">
                        {% for model in app.models %}
                            {% if model.object_name in "DentalImaging,DentalProcedure,DentalTreatment,DentalTreatmentTemplate,DentistryDiagnosis,DentistryMaterial,DentistryMedicalRecord,DentistryProcedure,EstheticDentistryTreatment,OrthodonticTreatment,ProsthodonticTreatment,SurgicalTreatment,TherapeuticTreatment,TreatmentProtocol" %}
                                <div class="model-item">
                                    {% if model.admin_url %}<a href="{{ model.admin_url }}">{{ model.name }}</a>{% else %}{{ model.name }}{% endif %}
                                    {% if model.add_url %}<a href="{{ model.add_url }}" class="addlink">{% trans 'Add' %}</a>{% endif %}
                                </div>
                            {% endif %}
                        {% endfor %}
                    </div>
                </div>

                <!-- Group 4: Laboratory & Billing -->
                <div class="model-group">
                    <h3>🧪 Laboratoire et Facturation</h3>
                    <div class="model-list">
                        {% for model in app.models %}
                            {% if model.object_name in "DentalLaboratory,DentistryBillingCode,DentistryInvoice,LabWorkOrder" %}
                                <div class="model-item">
                                    {% if model.admin_url %}<a href="{{ model.admin_url }}">{{ model.name }}</a>{% else %}{{ model.name }}{% endif %}
                                    {% if model.add_url %}<a href="{{ model.add_url }}" class="addlink">{% trans 'Add' %}</a>{% endif %}
                                </div>
                            {% endif %}
                        {% endfor %}
                    </div>
                </div>

                <!-- Group 5: Comments & Reviews -->
                <div class="model-group">
                    <h3>💬 Commentaires et Avis</h3>
                    <div class="model-list">
                        {% for model in app.models %}
                            {% if model.object_name in "DentistryComment,DentistryReview" %}
                                <div class="model-item">
                                    {% if model.admin_url %}<a href="{{ model.admin_url }}">{{ model.name }}</a>{% else %}{{ model.name }}{% endif %}
                                    {% if model.add_url %}<a href="{{ model.add_url }}" class="addlink">{% trans 'Add' %}</a>{% endif %}
                                </div>
                            {% endif %}
                        {% endfor %}
                    </div>
                </div>

                <!-- Group 6: SVG System -->
                <div class="model-group">
                    <h3>🎨 Système SVG Dentaire</h3>
                    <div class="model-list">
                        {% for model in app.models %}
                            {% if model.object_name in "DentalModification,DentalPathology,DentalReminderSetting,DentalSvgConfiguration,DentalSvgData,DentalSvgPath" %}
                                <div class="model-item">
                                    {% if model.admin_url %}<a href="{{ model.admin_url }}">{{ model.name }}</a>{% else %}{{ model.name }}{% endif %}
                                    {% if model.add_url %}<a href="{{ model.add_url }}" class="addlink">{% trans 'Add' %}</a>{% endif %}
                                </div>
                            {% endif %}
                        {% endfor %}
                    </div>
                </div>

                <!-- Group 7: Configuration -->
                <div class="model-group">
                    <h3>⚙️ Configuration et Notifications</h3>
                    <div class="model-list">
                        {% for model in app.models %}
                            {% if model.object_name in "DentistryConfiguration,DentistryCustomField,DentistryCustomFieldValue,DentistryNotification,DentistryNotificationSettings,DentistryTemplate,EstimateTemplate" %}
                                <div class="model-item">
                                    {% if model.admin_url %}<a href="{{ model.admin_url }}">{{ model.name }}</a>{% else %}{{ model.name }}{% endif %}
                                    {% if model.add_url %}<a href="{{ model.add_url }}" class="addlink">{% trans 'Add' %}</a>{% endif %}
                                </div>
                            {% endif %}
                        {% endfor %}
                    </div>
                </div>

                <!-- Group 8: Website -->
                <div class="model-group">
                    <h3>🌐 Site Web et Contenu</h3>
                    <div class="model-list">
                        {% for model in app.models %}
                            {% if model.object_name in "DentistryBeforeAfterCase,DentistryPage,DentistryWebsiteSettings" %}
                                <div class="model-item">
                                    {% if model.admin_url %}<a href="{{ model.admin_url }}">{{ model.name }}</a>{% else %}{{ model.name }}{% endif %}
                                    {% if model.add_url %}<a href="{{ model.add_url }}" class="addlink">{% trans 'Add' %}</a>{% endif %}
                                </div>
                            {% endif %}
                        {% endfor %}
                    </div>
                </div>

                <!-- Group 9: Dashboard & Settings -->
                <div class="model-group">
                    <h3>📊 Tableau de Bord et Paramètres</h3>
                    <div class="model-list">
                        {% for model in app.models %}
                            {% if model.object_name in "DentistryAccessibilitySettings,DentistryAppearanceSettings,DentistryHoliday,DentistryLocationSettings,DentistryPrivacySettings,DentistryService,DentistrySpecialty,DentistryVacationPeriod,DentistryWorkingHours,EstimateSession,EstimateSessionHistory,EstimateStatistics" %}
                                <div class="model-item">
                                    {% if model.admin_url %}<a href="{{ model.admin_url }}">{{ model.name }}</a>{% else %}{{ model.name }}{% endif %}
                                    {% if model.add_url %}<a href="{{ model.add_url }}" class="addlink">{% trans 'Add' %}</a>{% endif %}
                                </div>
                            {% endif %}
                        {% endfor %}
                    </div>
                </div>

            {% endif %}
        {% endfor %}

        <!-- Other Applications -->
        <div class="other-apps">
            <h2>🔧 Autres Applications</h2>
            {% for app in app_list %}
                {% if app.app_label != "dentistry" %}
                    <div class="model-group">
                        <h3>{{ app.name }}</h3>
                        <div class="model-list">
                            {% for model in app.models %}
                                <div class="model-item">
                                    {% if model.admin_url %}<a href="{{ model.admin_url }}">{{ model.name }}</a>{% else %}{{ model.name }}{% endif %}
                                    {% if model.add_url %}<a href="{{ model.add_url }}" class="addlink">{% trans 'Add' %}</a>{% endif %}
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                {% endif %}
            {% endfor %}
        </div>

    {% else %}
        <p>{% trans "You don't have permission to edit anything." %}</p>
    {% endif %}
</div>
{% endblock %}
