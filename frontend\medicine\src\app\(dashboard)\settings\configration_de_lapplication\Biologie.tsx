'use client';

import React, { useState } from 'react';
import {
  Paper,
  Title,
  Group,
  Button,
  TextInput,
  Select,
  Table,
  ActionIcon,
  Modal,
  Text,
  Tabs,
  Card,
  Badge,
  Tooltip,
  Radio,
  NumberInput,
  Container,
  Stack,
  ScrollArea,
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { notifications } from '@mantine/notifications';
import {
  IconPlus,
  IconSearch,
  IconEdit,
  IconTrash,
  IconFlask,
  IconSettings,
  IconList,
} from '@tabler/icons-react';

// Types pour les données
interface BiologyModel {
  id: number;
  title: string;
  unit: string;
  type: string;
  category: string;
}

interface BiologyFamily {
  id: number;
  name: string;
  isActive: boolean;
}

interface BiologyParameter {
  id: number;
  title: string;
  unit: string;
  type: 'float' | 'integer' | 'string' | 'boolean';
  family: string;
  ageMin?: number;
  ageMax?: number;
  valueMin?: number;
  valueMax?: number;
  gender?: 'homme' | 'femme' | 'indefini';
}

const Biologie = () => {
  // États pour les onglets
  const [activeTab, setActiveTab] = useState<string | null>('models');

  // États pour les modals
  const [modelModalOpened, { open: openModelModal, close: closeModelModal }] = useDisclosure(false);
  const [familyModalOpened, { open: openFamilyModal, close: closeFamilyModal }] = useDisclosure(false);
  const [parameterModalOpened, { open: openParameterModal, close: closeParameterModal }] = useDisclosure(false);

  // États pour l'édition
  const [editingModel, setEditingModel] = useState<BiologyModel | null>(null);
  const [editingFamily, setEditingFamily] = useState<BiologyFamily | null>(null);
  const [editingParameter, setEditingParameter] = useState<BiologyParameter | null>(null);

  // États pour la recherche
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFamily, setSelectedFamily] = useState<string>('');

  // Données mockées pour les modèles
  const [models, setModels] = useState<BiologyModel[]>([
    { id: 1, title: 'CK totales', unit: '', type: 'float', category: 'BILAN CARDIAQUE' },
    { id: 2, title: 'CKMM', unit: 'μg', type: 'float', category: 'BILAN CARDIAQUE' },
    { id: 3, title: 'CKMB', unit: '', type: 'float', category: 'BILAN CARDIAQUE' },
    { id: 4, title: 'CKBB', unit: '', type: 'float', category: 'BILAN CARDIAQUE' },
    { id: 5, title: 'NFS', unit: '', type: 'float', category: 'BILAN 28 SA' },
    { id: 6, title: 'Albumine', unit: '', type: 'float', category: 'BILAN PROTIDIQUE' },
    { id: 7, title: 'Alpha 1 globulines', unit: '', type: 'float', category: 'BILAN PROTIDIQUE' },
    { id: 8, title: 'Alpha 2 globulines', unit: '', type: 'float', category: 'BILAN PROTIDIQUE' },
    { id: 9, title: 'Bêta globulines', unit: '', type: 'float', category: 'BILAN PROTIDIQUE' },
    { id: 10, title: 'Gamma globulines', unit: '', type: 'float', category: 'BILAN PROTIDIQUE' },
  ]);

  // Données mockées pour les familles
  const [families, setFamilies] = useState<BiologyFamily[]>([
    { id: 1, name: 'BILAN 28 SA', isActive: true },
    { id: 2, name: 'BILAN CARDIAQUE', isActive: true },
    { id: 3, name: 'BILAN IONIQUE', isActive: true },
    { id: 4, name: 'BILAN PROTIDIQUE', isActive: true },
    { id: 5, name: 'BILAN PULMONAIRE', isActive: true },
    { id: 6, name: 'BILAN RÉNAL', isActive: true },
    { id: 7, name: 'Bilan de la fonction rénale', isActive: true },
    { id: 8, name: 'Bilan glycémique', isActive: true },
    { id: 9, name: 'Bilan hépatique', isActive: true },
    { id: 10, name: 'Bilan thyroïdien', isActive: true },
  ]);

  // Données mockées pour les paramètres
  const [parameters, setParameters] = useState<BiologyParameter[]>([
    {
      id: 1,
      title: "TEST D'O SULLIVAN 50g T0-T1",
      unit: '',
      type: 'float',
      family: 'BILAN 28 SA',
      gender: 'indefini'
    },
    {
      id: 2,
      title: 'NFS',
      unit: '',
      type: 'float',
      family: 'BILAN 28 SA',
      gender: 'indefini'
    },
  ]);

  return (
    <Container size="xl" className="py-6">
      <Paper shadow="sm" radius="md" p="xl" className="bg-white">
        {/* En-tête */}
        <Group justify="space-between" mb="xl">
          <Group>
            <IconFlask size={24} className="text-blue-600" />
            <Title order={2} className="text-gray-800">Biologie</Title>
          </Group>
        </Group>

        {/* Onglets principaux */}
        <Tabs value={activeTab} onChange={setActiveTab} className="w-full">
          <Tabs.List className="mb-6">
            <Tabs.Tab value="models" leftSection={<IconList size={16} />}>
              Modèles
            </Tabs.Tab>
            <Tabs.Tab value="parameters" leftSection={<IconSettings size={16} />}>
              Paramètres
            </Tabs.Tab>
          </Tabs.List>

          {/* Onglet Modèles */}
          <Tabs.Panel value="models">
            <Card shadow="sm" padding="lg" radius="md" className="bg-white">
              {/* Barre d'outils pour les modèles */}
              <Group justify="space-between" mb="md">
                <Button
                  leftSection={<IconPlus size={16} />}
                  onClick={openModelModal}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  Nouveau modèle
                </Button>
                <Group>
                  <TextInput
                    placeholder="Rechercher..."
                    leftSection={<IconSearch size={16} />}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.currentTarget.value)}
                    className="w-64"
                  />
                  <Select
                    placeholder="Filtrer par catégorie"
                    data={[
                      { value: '', label: 'Toutes les catégories' },
                      { value: 'BILAN CARDIAQUE', label: 'BILAN CARDIAQUE' },
                      { value: 'BILAN 28 SA', label: 'BILAN 28 SA' },
                      { value: 'BILAN PROTIDIQUE', label: 'BILAN PROTIDIQUE' },
                    ]}
                    value={selectedFamily}
                    onChange={(value) => setSelectedFamily(value || '')}
                    className="w-48"
                  />
                </Group>
              </Group>

              {/* Tableau des modèles */}
              <ScrollArea>
                <Table striped highlightOnHover>
                  <Table.Thead>
                    <Table.Tr>
                      <Table.Th>Titre</Table.Th>
                      <Table.Th>Unité</Table.Th>
                      <Table.Th>Type</Table.Th>
                      <Table.Th width={100}>Actions</Table.Th>
                    </Table.Tr>
                  </Table.Thead>
                  <Table.Tbody>
                    {models
                      .filter((model) => {
                        const matchesSearch = model.title.toLowerCase().includes(searchTerm.toLowerCase());
                        const matchesCategory = !selectedFamily || model.category === selectedFamily;
                        return matchesSearch && matchesCategory;
                      })
                      .map((model) => (
                        <Table.Tr key={model.id}>
                          <Table.Td>
                            <div>
                              <Text fw={500}>{model.title}</Text>
                              <Text size="xs" c="dimmed">{model.category}</Text>
                            </div>
                          </Table.Td>
                          <Table.Td>{model.unit}</Table.Td>
                          <Table.Td>
                            <Badge variant="light" color="blue">
                              {model.type}
                            </Badge>
                          </Table.Td>
                          <Table.Td>
                            <Group gap="xs">
                              <Tooltip label="Modifier">
                                <ActionIcon
                                  variant="subtle"
                                  color="blue"
                                  size="sm"
                                  onClick={() => {
                                    setEditingModel(model);
                                    openModelModal();
                                  }}
                                >
                                  <IconEdit size={14} />
                                </ActionIcon>
                              </Tooltip>
                              <Tooltip label="Supprimer">
                                <ActionIcon
                                  variant="subtle"
                                  color="red"
                                  size="sm"
                                  onClick={() => {
                                    setModels(models.filter(m => m.id !== model.id));
                                    notifications.show({
                                      title: 'Modèle supprimé',
                                      message: 'Le modèle a été supprimé avec succès',
                                      color: 'green',
                                    });
                                  }}
                                >
                                  <IconTrash size={14} />
                                </ActionIcon>
                              </Tooltip>
                            </Group>
                          </Table.Td>
                        </Table.Tr>
                      ))}
                  </Table.Tbody>
                </Table>
              </ScrollArea>
            </Card>
          </Tabs.Panel>

          {/* Onglet Paramètres */}
          <Tabs.Panel value="parameters">
            <div className="space-y-6">
              {/* Liste des familles */}
              <Card shadow="sm" padding="lg" radius="md" className="bg-white">
                <Group justify="space-between" mb="md">
                  <Title order={4}>Liste des examens</Title>
                  <Group>
                    <Button
                      leftSection={<IconPlus size={16} />}
                      onClick={openFamilyModal}
                      variant="outline"
                      color="blue"
                    >
                      Nouvelle famille
                    </Button>
                    <Button
                      leftSection={<IconPlus size={16} />}
                      onClick={openParameterModal}
                      className="bg-blue-600 hover:bg-blue-700"
                    >
                      Nouveau paramètre
                    </Button>
                  </Group>
                </Group>

                <div className="flex">
                  {/* Sidebar des familles */}
                  <div className="w-64 border-r border-gray-200 pr-4">
                    <TextInput
                      placeholder="Liste des examens"
                      leftSection={<IconSearch size={16} />}
                      mb="md"
                      className="w-full"
                    />
                    <ScrollArea h={400}>
                      <Stack gap="xs">
                        {families.map((family) => (
                          <div
                            key={family.id}
                            className={`p-2 rounded cursor-pointer flex items-center justify-between ${
                              selectedFamily === family.name
                                ? 'bg-blue-50 border border-blue-200'
                                : 'hover:bg-gray-50'
                            }`}
                            onClick={() => setSelectedFamily(family.name)}
                          >
                            <div className="flex items-center gap-2">
                              <div className="w-3 h-3 bg-red-500 rounded-sm"></div>
                              <Text size="sm" fw={selectedFamily === family.name ? 600 : 400}>
                                {family.name}
                              </Text>
                            </div>
                            {selectedFamily === family.name && (
                              <ActionIcon
                                variant="subtle"
                                color="blue"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setEditingFamily(family);
                                  openFamilyModal();
                                }}
                              >
                                <IconEdit size={14} />
                              </ActionIcon>
                            )}
                          </div>
                        ))}
                      </Stack>
                    </ScrollArea>
                  </div>

                  {/* Contenu principal */}
                  <div className="flex-1 pl-6">
                    {selectedFamily && (
                      <>
                        <Group justify="space-between" mb="md">
                          <Title order={5}>{selectedFamily}</Title>
                        </Group>

                        <Table striped highlightOnHover>
                          <Table.Thead>
                            <Table.Tr>
                              <Table.Th>Titre</Table.Th>
                              <Table.Th>Unité</Table.Th>
                              <Table.Th>Type</Table.Th>
                              <Table.Th style={{ width: 100 }}>Actions</Table.Th>
                            </Table.Tr>
                          </Table.Thead>
                          <Table.Tbody>
                            {parameters
                              .filter((param) => param.family === selectedFamily)
                              .map((parameter) => (
                                <Table.Tr key={parameter.id}>
                                  <Table.Td>{parameter.title}</Table.Td>
                                  <Table.Td>{parameter.unit}</Table.Td>
                                  <Table.Td>
                                    <Badge variant="light" color="blue">
                                      {parameter.type}
                                    </Badge>
                                  </Table.Td>
                                  <Table.Td>
                                    <Group gap="xs">
                                      <Tooltip label="Modifier">
                                        <ActionIcon
                                          variant="subtle"
                                          color="blue"
                                          size="sm"
                                          onClick={() => {
                                            setEditingParameter(parameter);
                                            openParameterModal();
                                          }}
                                        >
                                          <IconEdit size={14} />
                                        </ActionIcon>
                                      </Tooltip>
                                      <Tooltip label="Supprimer">
                                        <ActionIcon
                                          variant="subtle"
                                          color="red"
                                          size="sm"
                                          onClick={() => {
                                            setParameters(parameters.filter(p => p.id !== parameter.id));
                                            notifications.show({
                                              title: 'Paramètre supprimé',
                                              message: 'Le paramètre a été supprimé avec succès',
                                              color: 'green',
                                            });
                                          }}
                                        >
                                          <IconTrash size={14} />
                                        </ActionIcon>
                                      </Tooltip>
                                    </Group>
                                  </Table.Td>
                                </Table.Tr>
                              ))}
                          </Table.Tbody>
                        </Table>
                      </>
                    )}
                  </div>
                </div>
              </Card>
            </div>
          </Tabs.Panel>
        </Tabs>

        {/* Modal pour les modèles */}
        <Modal
          opened={modelModalOpened}
          onClose={() => {
            closeModelModal();
            setEditingModel(null);
          }}
          title={
            <Group>
              <IconFlask size={20} />
              <Text fw={600}>
                {editingModel ? 'Modifier le modèle' : 'Nouveau modèle'}
              </Text>
            </Group>
          }
          size="md"
        >
          <ModelForm
            model={editingModel}
            onSubmit={(data) => {
              if (editingModel) {
                setModels(models.map(m => m.id === editingModel.id ? { ...editingModel, ...data } : m));
                notifications.show({
                  title: 'Modèle modifié',
                  message: 'Le modèle a été modifié avec succès',
                  color: 'green',
                });
              } else {
                const newModel = {
                  id: Math.max(...models.map(m => m.id)) + 1,
                  ...data,
                };
                setModels([...models, newModel]);
                notifications.show({
                  title: 'Modèle créé',
                  message: 'Le modèle a été créé avec succès',
                  color: 'green',
                });
              }
              closeModelModal();
              setEditingModel(null);
            }}
            onCancel={() => {
              closeModelModal();
              setEditingModel(null);
            }}
          />
        </Modal>

        {/* Modal pour les familles */}
        <Modal
          opened={familyModalOpened}
          onClose={() => {
            closeFamilyModal();
            setEditingFamily(null);
          }}
          title={
            <Group>
              <IconFlask size={20} />
              <Text fw={600} c="white">
                Gestion d'exam biologique
              </Text>
            </Group>
          }
          size="sm"
          styles={{
            header: {
              backgroundColor: '#3799CE',
              color: 'white',
            },
            title: {
              color: 'white',
            },
          }}
        >
          <FamilyForm
            family={editingFamily}
            onSubmit={(data) => {
              if (editingFamily) {
                setFamilies(families.map(f => f.id === editingFamily.id ? { ...editingFamily, ...data } : f));
                notifications.show({
                  title: 'Famille modifiée',
                  message: 'La famille a été modifiée avec succès',
                  color: 'green',
                });
              } else {
                const newFamily = {
                  id: Math.max(...families.map(f => f.id)) + 1,
                  isActive: true,
                  ...data,
                };
                setFamilies([...families, newFamily]);
                notifications.show({
                  title: 'Famille créée',
                  message: 'La famille a été créée avec succès',
                  color: 'green',
                });
              }
              closeFamilyModal();
              setEditingFamily(null);
            }}
            onCancel={() => {
              closeFamilyModal();
              setEditingFamily(null);
            }}
          />
        </Modal>

        {/* Modal pour les paramètres */}
        <Modal
          opened={parameterModalOpened}
          onClose={() => {
            closeParameterModal();
            setEditingParameter(null);
          }}
          title={
            <Group>
              <IconFlask size={20} />
              <Text fw={600} c="white">
                Gestion d'exam biologique
              </Text>
            </Group>
          }
          size="lg"
          styles={{
            header: {
              backgroundColor: '#3799CE',
              color: 'white',
            },
            title: {
              color: 'white',
            },
          }}
        >
          <ParameterForm
            parameter={editingParameter}
            families={families}
            onSubmit={(data) => {
              if (editingParameter) {
                setParameters(parameters.map(p => p.id === editingParameter.id ? { ...editingParameter, ...data } : p));
                notifications.show({
                  title: 'Paramètre modifié',
                  message: 'Le paramètre a été modifié avec succès',
                  color: 'green',
                });
              } else {
                const newParameter = {
                  id: Math.max(...parameters.map(p => p.id)) + 1,
                  ...data,
                };
                setParameters([...parameters, newParameter]);
                notifications.show({
                  title: 'Paramètre créé',
                  message: 'Le paramètre a été créé avec succès',
                  color: 'green',
                });
              }
              closeParameterModal();
              setEditingParameter(null);
            }}
            onCancel={() => {
              closeParameterModal();
              setEditingParameter(null);
            }}
          />
        </Modal>
      </Paper>
    </Container>
  );
};

// Composant de formulaire pour les modèles
interface ModelFormProps {
  model: BiologyModel | null;
  onSubmit: (data: Partial<BiologyModel>) => void;
  onCancel: () => void;
}

const ModelForm: React.FC<ModelFormProps> = ({ model, onSubmit, onCancel }) => {
  const [title, setTitle] = useState(model?.title || '');
  const [unit, setUnit] = useState(model?.unit || '');
  const [type, setType] = useState(model?.type || 'float');
  const [category, setCategory] = useState(model?.category || '');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!title.trim()) return;

    onSubmit({
      title: title.trim(),
      unit: unit.trim(),
      type,
      category: category.trim(),
    });
  };

  return (
    <form onSubmit={handleSubmit}>
      <Stack gap="md">
        <TextInput
          label="Titre"
          placeholder="Entrez le titre du modèle"
          value={title}
          onChange={(e) => setTitle(e.currentTarget.value)}
          required
          withAsterisk
        />

        <TextInput
          label="Unité"
          placeholder="Entrez l'unité (optionnel)"
          value={unit}
          onChange={(e) => setUnit(e.currentTarget.value)}
        />

        <Select
          label="Type"
          placeholder="Sélectionnez le type"
          data={[
            { value: 'float', label: 'Floton' },
            { value: 'integer', label: 'Entier' },
            { value: 'string', label: 'Chaîne de caractère' },
            { value: 'boolean', label: 'Booléen' },
          ]}
          value={type}
          onChange={(value) => setType(value || 'float')}
          required
          withAsterisk
        />

        <TextInput
          label="Catégorie"
          placeholder="Entrez la catégorie"
          value={category}
          onChange={(e) => setCategory(e.currentTarget.value)}
          required
          withAsterisk
        />

        <Group justify="flex-end" mt="md">
          <Button variant="outline" onClick={onCancel}>
            Annuler
          </Button>
          <Button type="submit" className="bg-blue-600 hover:bg-blue-700">
            {model ? 'Modifier' : 'Créer'}
          </Button>
        </Group>
      </Stack>
    </form>
  );
};

// Composant de formulaire pour les familles
interface FamilyFormProps {
  family: BiologyFamily | null;
  onSubmit: (data: Partial<BiologyFamily>) => void;
  onCancel: () => void;
}

const FamilyForm: React.FC<FamilyFormProps> = ({ family, onSubmit, onCancel }) => {
  const [name, setName] = useState(family?.name || '');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!name.trim()) return;

    onSubmit({
      name: name.trim(),
    });
  };

  return (
    <form onSubmit={handleSubmit}>
      <Stack gap="md">
        <TextInput
          label="Titre"
          placeholder="Entrez le nom de la famille"
          value={name}
          onChange={(e) => setName(e.currentTarget.value)}
          required
          withAsterisk
          styles={{
            label: { color: 'red', fontWeight: 600 },
          }}
        />

        <div className="flex items-center gap-2 mt-4">
          <div className="w-4 h-4 bg-gray-400 rounded"></div>
          <Text size="sm" c="dimmed">Unique</Text>
        </div>

        <Group justify="flex-end" mt="md">
          <Button variant="outline" color="gray">
            Enregistrer
          </Button>
          <Button color="red" onClick={onCancel}>
            Annuler
          </Button>
        </Group>
      </Stack>
    </form>
  );
};

// Composant de formulaire pour les paramètres
interface ParameterFormProps {
  parameter: BiologyParameter | null;
  families: BiologyFamily[];
  onSubmit: (data: Partial<BiologyParameter>) => void;
  onCancel: () => void;
}

const ParameterForm: React.FC<ParameterFormProps> = ({ parameter, families, onSubmit, onCancel }) => {
  const [title, setTitle] = useState(parameter?.title || '');
  const [unit, setUnit] = useState(parameter?.unit || '');
  const [type, setType] = useState<'float' | 'integer' | 'string' | 'boolean'>(parameter?.type || 'float');
  const [family, setFamily] = useState(parameter?.family || '');
  const [ageMin, setAgeMin] = useState<number | undefined>(parameter?.ageMin);
  const [ageMax, setAgeMax] = useState<number | undefined>(parameter?.ageMax);
  const [valueMin, setValueMin] = useState<number | undefined>(parameter?.valueMin);
  const [valueMax, setValueMax] = useState<number | undefined>(parameter?.valueMax);
  const [gender, setGender] = useState<'homme' | 'femme' | 'indefini'>(parameter?.gender || 'indefini');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!title.trim() || !family) return;

    onSubmit({
      title: title.trim(),
      unit: unit.trim(),
      type,
      family,
      ageMin,
      ageMax,
      valueMin,
      valueMax,
      gender,
    });
  };

  return (
    <form onSubmit={handleSubmit}>
      <Stack gap="md">
        <TextInput
          label="Titre"
          placeholder="Entrez le titre du paramètre"
          value={title}
          onChange={(e) => setTitle(e.currentTarget.value)}
          required
          withAsterisk
          styles={{
            label: { color: 'red', fontWeight: 600 },
          }}
        />

        <Select
          label="Unité"
          placeholder="Sélectionnez une unité"
          data={[
            { value: '', label: 'Aucune unité' },
            { value: 'mg/dL', label: 'mg/dL' },
            { value: 'g/L', label: 'g/L' },
            { value: 'μg', label: 'μg' },
            { value: 'UI/L', label: 'UI/L' },
            { value: '%', label: '%' },
          ]}
          value={unit}
          onChange={(value) => setUnit(value || '')}
          rightSection={<IconPlus size={16} />}
        />

        <div>
          <Text size="sm" fw={500} mb="xs">Type</Text>
          <Radio.Group value={type} onChange={(value) => setType(value as 'float' | 'integer' | 'string' | 'boolean')}>
            <Group>
              <Radio value="float" label="Floton" />
              <Radio value="integer" label="Entier" />
              <Radio value="string" label="Chaîne de caractère" />
              <Radio value="boolean" label="Booléen" />
            </Group>
          </Radio.Group>
        </div>

        <Table>
          <Table.Thead>
            <Table.Tr style={{ backgroundColor: '#e3f2fd' }}>
              <Table.Th>Age min</Table.Th>
              <Table.Th>Age max</Table.Th>
              <Table.Th>Valeur min</Table.Th>
              <Table.Th>Valeur max</Table.Th>
              <Table.Th>Sexe</Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            <Table.Tr>
              <Table.Td>
                <NumberInput
                  placeholder="Age min"
                  value={ageMin}
                  onChange={(value) => setAgeMin(typeof value === 'number' ? value : undefined)}
                  min={0}
                  max={120}
                />
              </Table.Td>
              <Table.Td>
                <NumberInput
                  placeholder="Age max"
                  value={ageMax}
                  onChange={(value) => setAgeMax(typeof value === 'number' ? value : undefined)}
                  min={0}
                  max={120}
                />
              </Table.Td>
              <Table.Td>
                <NumberInput
                  placeholder="Valeur min"
                  value={valueMin}
                  onChange={(value) => setValueMin(typeof value === 'number' ? value : undefined)}
                />
              </Table.Td>
              <Table.Td>
                <NumberInput
                  placeholder="Valeur max"
                  value={valueMax}
                  onChange={(value) => setValueMax(typeof value === 'number' ? value : undefined)}
                />
              </Table.Td>
              <Table.Td>
                <Radio.Group value={gender} onChange={(value) => setGender(value as 'homme' | 'femme' | 'indefini')}>
                  <Group>
                    <Radio value="homme" label="Homme" />
                    <Radio value="femme" label="Femme" />
                    <Radio value="indefini" label="Indéfini" />
                  </Group>
                </Radio.Group>
              </Table.Td>
            </Table.Tr>
          </Table.Tbody>
        </Table>

        <Group justify="flex-end" mt="md">
          <Button variant="outline" color="gray">
            Enregistrer
          </Button>
          <Button color="red" onClick={onCancel}>
            Annuler
          </Button>
        </Group>
      </Stack>
    </form>
  );
};

export default Biologie;
