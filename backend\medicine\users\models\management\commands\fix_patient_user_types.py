from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.db.models import Q

User = get_user_model()

class Command(BaseCommand):
    help = 'Fix user_type for patients and ensure they appear in the admin panel'

    def handle(self, *args, **options):
        # Check if there are any users with user_type='patient'
        patient_count = User.objects.filter(user_type='patient').count()
        self.stdout.write(self.style.SUCCESS(f'Found {patient_count} users with user_type="patient"'))

        # Check if there are any users without a user_type
        no_type_count = User.objects.filter(Q(user_type__isnull=True) | Q(user_type='')).count()
        self.stdout.write(self.style.SUCCESS(f'Found {no_type_count} users without a user_type'))

        # Check if there are any users with incorrect user_type
        incorrect_type_count = User.objects.exclude(user_type__in=['doctor', 'assistant', 'patient', 'admin']).count()
        self.stdout.write(self.style.SUCCESS(f'Found {incorrect_type_count} users with incorrect user_type'))

        # Fix users without a user_type
        if no_type_count > 0:
            updated = User.objects.filter(Q(user_type__isnull=True) | Q(user_type='')).update(user_type='patient')
            self.stdout.write(self.style.SUCCESS(f'Updated {updated} users without a user_type to "patient"'))

        # Fix users with incorrect user_type
        if incorrect_type_count > 0:
            updated = User.objects.exclude(user_type__in=['doctor', 'assistant', 'patient', 'admin']).update(user_type='patient')
            self.stdout.write(self.style.SUCCESS(f'Updated {updated} users with incorrect user_type to "patient"'))

        # Check if there are any users that should be patients based on other criteria
        # For example, users that have patient-specific fields filled but wrong user_type
        potential_patients = User.objects.filter(
            ~Q(user_type='patient') &
            (
                ~Q(date_of_birth__isnull=True) |
                ~Q(gender__isnull=True) |
                ~Q(social_security__isnull=True)
            )
        ).count()
        
        self.stdout.write(self.style.SUCCESS(f'Found {potential_patients} potential patients with wrong user_type'))

        # Print final count
        final_count = User.objects.filter(user_type='patient').count()
        self.stdout.write(self.style.SUCCESS(f'Final count: {final_count} users with user_type="patient"'))
