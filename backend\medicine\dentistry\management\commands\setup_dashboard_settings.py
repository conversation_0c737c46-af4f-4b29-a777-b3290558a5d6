"""
Management command to set up default dashboard settings for all dentistry doctors.
"""
from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import time

from dentistry.models import (
    DentistryDoctor, DentistryAppointmentSettings, DentistryWorkingHours,
    DentistryLocationSettings, DentistryNotificationSettings,
    DentistryPrivacySettings, DentistryAccessibilitySettings,
    DentistryAppearanceSettings
)


class Command(BaseCommand):
    help = 'Set up default dashboard settings for all dentistry doctors'

    def add_arguments(self, parser):
        parser.add_argument(
            '--doctor-id',
            type=str,
            help='Set up settings for a specific doctor ID'
        )
        parser.add_argument(
            '--reset',
            action='store_true',
            help='Reset existing settings to defaults'
        )

    def handle(self, *args, **options):
        self.stdout.write('Setting up dashboard settings for dentistry doctors...')

        # Get doctors to process
        if options['doctor_id']:
            try:
                doctors = [DentistryDoctor.objects.get(id=options['doctor_id'])]
            except DentistryDoctor.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'Doctor with ID {options["doctor_id"]} not found')
                )
                return
        else:
            doctors = DentistryDoctor.objects.all()

        if not doctors:
            self.stdout.write(self.style.WARNING('No doctors found'))
            return

        total_doctors = len(doctors)
        processed = 0

        for doctor in doctors:
            self.stdout.write(f'Processing Dr. {doctor.full_name}...')
            
            # 1. Appointment Settings
            appointment_settings, created = DentistryAppointmentSettings.objects.get_or_create(
                doctor=doctor,
                defaults={
                    'allow_online_booking': True,
                    'advance_booking_days': 30,
                    'min_booking_notice_hours': 24,
                    'default_appointment_duration': 30,
                    'consultation_duration': 30,
                    'cleaning_duration': 60,
                    'treatment_duration': 90,
                    'emergency_duration': 45,
                    'buffer_time_before': 10,
                    'buffer_time_after': 10,
                    'allow_patient_cancellation': True,
                    'cancellation_notice_hours': 24,
                    'send_appointment_reminders': True,
                    'reminder_hours_before': 24,
                    'reminder_methods': ['email', 'sms']
                }
            )
            if options['reset'] and not created:
                # Reset to defaults
                appointment_settings.allow_online_booking = True
                appointment_settings.advance_booking_days = 30
                appointment_settings.min_booking_notice_hours = 24
                appointment_settings.default_appointment_duration = 30
                appointment_settings.consultation_duration = 30
                appointment_settings.cleaning_duration = 60
                appointment_settings.treatment_duration = 90
                appointment_settings.emergency_duration = 45
                appointment_settings.buffer_time_before = 10
                appointment_settings.buffer_time_after = 10
                appointment_settings.allow_patient_cancellation = True
                appointment_settings.cancellation_notice_hours = 24
                appointment_settings.send_appointment_reminders = True
                appointment_settings.reminder_hours_before = 24
                appointment_settings.reminder_methods = ['email', 'sms']
                appointment_settings.save()

            # 2. Working Hours (Monday to Friday)
            for weekday in range(7):  # 0=Monday, 6=Sunday
                working_hours, created = DentistryWorkingHours.objects.get_or_create(
                    doctor=doctor,
                    weekday=weekday,
                    defaults={
                        'is_working_day': weekday < 5,  # Monday-Friday
                        'morning_start': time(9, 0) if weekday < 5 else None,
                        'morning_end': time(12, 0) if weekday < 5 else None,
                        'afternoon_start': time(14, 0) if weekday < 5 else None,
                        'afternoon_end': time(18, 0) if weekday < 5 else None,
                        'lunch_break_start': time(12, 0) if weekday < 5 else None,
                        'lunch_break_end': time(14, 0) if weekday < 5 else None,
                        'notes': ''
                    }
                )
                if options['reset'] and not created:
                    working_hours.is_working_day = weekday < 5
                    working_hours.morning_start = time(9, 0) if weekday < 5 else None
                    working_hours.morning_end = time(12, 0) if weekday < 5 else None
                    working_hours.afternoon_start = time(14, 0) if weekday < 5 else None
                    working_hours.afternoon_end = time(18, 0) if weekday < 5 else None
                    working_hours.lunch_break_start = time(12, 0) if weekday < 5 else None
                    working_hours.lunch_break_end = time(14, 0) if weekday < 5 else None
                    working_hours.notes = ''
                    working_hours.save()

            # 3. Location Settings
            location_settings, created = DentistryLocationSettings.objects.get_or_create(
                doctor=doctor,
                defaults={
                    'clinic_name': f'Cabinet Dentaire Dr. {doctor.last_name}',
                    'address_line_1': '',
                    'city': '',
                    'state_province': '',
                    'postal_code': '',
                    'country': 'Morocco',
                    'phone_number': '',
                    'email': doctor.email,
                    'show_on_map': True,
                    'map_zoom_level': 15,
                    'parking_available': True,
                    'parking_instructions': '',
                    'public_transport_info': '',
                    'accessibility_info': ''
                }
            )

            # 4. Notification Settings
            notification_settings, created = DentistryNotificationSettings.objects.get_or_create(
                doctor=doctor,
                defaults={
                    'email_notifications': True,
                    'appointment_reminders': True,
                    'new_appointment_notifications': True,
                    'cancellation_notifications': True,
                    'sms_notifications': True,
                    'emergency_sms': True,
                    'marketing_emails': False,
                    'newsletter_subscription': False,
                    'reminder_hours_before': 24,
                    'preferred_notification_method': 'email'
                }
            )

            # 5. Privacy Settings
            privacy_settings, created = DentistryPrivacySettings.objects.get_or_create(
                doctor=doctor,
                defaults={
                    'show_profile_to_other_doctors': True,
                    'show_profile_to_patients': True,
                    'share_anonymized_data_for_research': False,
                    'allow_patient_feedback': True,
                    'show_phone_number': True,
                    'show_email_address': False,
                    'require_patient_verification': False,
                    'patient_data_retention_months': 60
                }
            )

            # 6. Accessibility Settings
            accessibility_settings, created = DentistryAccessibilitySettings.objects.get_or_create(
                doctor=doctor,
                defaults={
                    'high_contrast': False,
                    'large_text': False,
                    'font_size': 16,
                    'screen_reader': False,
                    'color_blind_friendly': False,
                    'reduce_animations': False,
                    'keyboard_navigation': False
                }
            )

            # 7. Appearance Settings
            appearance_settings, created = DentistryAppearanceSettings.objects.get_or_create(
                doctor=doctor,
                defaults={
                    'theme': 'light',
                    'language': 'fr',  # French for Morocco
                    'time_format': '24h',
                    'date_format': 'DD/MM/YYYY',
                    'calendar_view': 'month',
                    'calendar_start_hour': 8,
                    'calendar_end_hour': 18
                }
            )

            processed += 1
            self.stdout.write(
                self.style.SUCCESS(f'✓ Settings configured for Dr. {doctor.full_name}')
            )

        self.stdout.write(
            self.style.SUCCESS(
                f'\nSuccessfully configured dashboard settings for {processed}/{total_doctors} doctors'
            )
        )

        # Summary
        self.stdout.write('\n' + '='*50)
        self.stdout.write('SUMMARY OF CONFIGURED SETTINGS:')
        self.stdout.write('='*50)
        self.stdout.write('✓ Appointment Settings (Prendre rendez-vous)')
        self.stdout.write('  - Online booking enabled')
        self.stdout.write('  - 30 days advance booking')
        self.stdout.write('  - Various appointment durations')
        self.stdout.write('  - Email & SMS reminders')
        self.stdout.write('')
        self.stdout.write('✓ Working Hours (Horaires de travail)')
        self.stdout.write('  - Monday-Friday: 9:00-12:00, 14:00-18:00')
        self.stdout.write('  - Lunch break: 12:00-14:00')
        self.stdout.write('  - Weekends: Off')
        self.stdout.write('')
        self.stdout.write('✓ Location Settings (Carte des dépliants)')
        self.stdout.write('  - Clinic information')
        self.stdout.write('  - Address and contact details')
        self.stdout.write('  - Map integration')
        self.stdout.write('')
        self.stdout.write('✓ Additional Settings')
        self.stdout.write('  - Notification preferences')
        self.stdout.write('  - Privacy controls')
        self.stdout.write('  - Accessibility options')
        self.stdout.write('  - Appearance customization')
        self.stdout.write('')
        self.stdout.write(self.style.SUCCESS('All dashboard settings are now ready for use!'))
