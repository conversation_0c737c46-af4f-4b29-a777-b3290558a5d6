"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_i18n_locales_en_Corbeille_researchdoctor_json";
exports.ids = ["_ssr_src_i18n_locales_en_Corbeille_researchdoctor_json"];
exports.modules = {

/***/ "(ssr)/./src/i18n/locales/en/Corbeille/researchdoctor.json":
/*!***********************************************************!*\
  !*** ./src/i18n/locales/en/Corbeille/researchdoctor.json ***!
  \***********************************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"researchdoctor":"researchdoctor AN","conditions":"Search for General Practitioner","filters":"المرشحات","sort-by":"sort by","Ascending-price":"Ascending price","Descending-price":"Descending price","Availabl":"Available as soon as possible","results-found":"results found","View-profile":"View profile","specialite-title-form-l":"Look for","specialite-title-form-r":"I am looking for a doctor","placeHolder-specialite":"Choose a specialty","placeHolder-city":"Select city","placeHolder-region":"Select the region","specialite-form-submit":"Find","Civility":"Civility","Mon":"sir","Mrs":"Mrs","I-research":"I research","A":"in","Display":" Choose a city","DisplayVille":"Specialty","Find":"Find","appointment":"Make an appointment","Next-availability":"Next availability","label-first-name":"First Name *","label-your-rating":"Your Rating *","label-email-star":"Email address *","label-phone":"Phone/Mobile *","label-message-star":"By submitting this form, I have read and accept the","Privacy-Policy":"Privacy Policy","label-Send":"Send","Prendre":"Make an appointment","Précédente":"Former","Suivant":"Following","Submit":"Submit","Choose":"Choose the consultation value","consultation":"Follow-up consultation","Urgence":"Emergency","When-would":"When would you like to consult ?","You-can-choose":"You can choose up to 3 time slots. You will be contacted by a member of our team to validate the most suitable one.","Choose-a-date":"Choose a date","Choose-a-time-on":"Choose a time on","Morning":"Morning","Afternoon":"Afternoon","Summary-of-your-request":"Summary of your request","Reason-for-consultation":"Reason for consultation","Price":"Price","Your-availability":"Your availability","Identify-yourself":"Identify yourself","Please-enter-your-identity-below":"Please enter your identity below","Your-choice":"Your choice","Add-another-slot":"Add another slot","About":"About","Availability":"Availability","Services":"Services","First-consultation":"First consultation","Emergency":"Emergency","These-prices":"These prices are communicated to you for information purposes only. They may vary depending on the care actually carried out in the office.","Map":"Map","Address":"Address","comments":"comments"}');

/***/ })

};
;