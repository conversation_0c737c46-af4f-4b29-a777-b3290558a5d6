"""
Commande de gestion pour initialiser les templates de traitement dentaire
"""
from django.core.management.base import BaseCommand
from django.db import transaction
from dentistry.models.dental_svg_system import DentalTreatmentTemplate


class Command(BaseCommand):
    help = 'Initialize dental treatment templates with predefined data'

    def handle(self, *args, **options):
        """
        Initialise les templates de traitement dentaire
        """
        self.stdout.write(self.style.SUCCESS('Initializing dental treatment templates...'))

        with transaction.atomic():
            # Template pour le nettoyage (Cleaning)
            cleaning_template, created = DentalTreatmentTemplate.objects.get_or_create(
                name='cleaning',
                defaults={
                    'description': 'Nettoyage dentaire professionnel',
                    'template_paths': [
                        {
                            'path_id': '17',
                            'code': 'st9',
                            'path': 'M25,50 L35,60 L25,70 Z',  # Exemple de path
                            'style': {'fill': '#03baf2'},
                            'display_order': 17
                        }
                    ],
                    'paths_to_remove': [],
                    'min_age': 3
                }
            )
            if created:
                self.stdout.write(f'✓ Created cleaning template')
            else:
                self.stdout.write(f'- Cleaning template already exists')

            # Template pour le fluorure (Fluoride)
            fluoride_template, created = DentalTreatmentTemplate.objects.get_or_create(
                name='fluoride',
                defaults={
                    'description': 'Application de fluorure',
                    'template_paths': [
                        {
                            'path_id': '18',
                            'code': 'st10',
                            'path': 'M20,45 L40,65 L20,85 Z',  # Exemple de path
                            'style': {'fill': '#00ff00'},
                            'display_order': 18
                        }
                    ],
                    'paths_to_remove': ['17'],  # Supprime le nettoyage
                    'min_age': 2
                }
            )
            if created:
                self.stdout.write(f'✓ Created fluoride template')
            else:
                self.stdout.write(f'- Fluoride template already exists')

            # Template pour le scellant (Sealant)
            sealant_template, created = DentalTreatmentTemplate.objects.get_or_create(
                name='sealant',
                defaults={
                    'description': 'Application de scellant dentaire',
                    'template_paths': [
                        {
                            'path_id': '19',
                            'code': 'st11',
                            'path': 'M22,48 L38,62 L22,78 Z',  # Exemple de path
                            'style': {'fill': '#ffff00'},
                            'display_order': 19
                        }
                    ],
                    'paths_to_remove': [],
                    'min_age': 6
                }
            )
            if created:
                self.stdout.write(f'✓ Created sealant template')
            else:
                self.stdout.write(f'- Sealant template already exists')

            # Template pour le blanchiment (Whitening)
            whitening_template, created = DentalTreatmentTemplate.objects.get_or_create(
                name='whitening',
                defaults={
                    'description': 'Blanchiment dentaire',
                    'template_paths': [
                        {
                            'path_id': '20',
                            'code': 'st12',
                            'path': 'M18,42 L42,66 L18,90 Z',
                            'style': {'fill': '#ffffff'},
                            'display_order': 20
                        },
                        {
                            'path_id': '21',
                            'code': 'st12',
                            'path': 'M19,43 L41,65 L19,89 Z',
                            'style': {'fill': '#ffffff'},
                            'display_order': 21
                        },
                        {
                            'path_id': '22',
                            'code': 'st12',
                            'path': 'M20,44 L40,64 L20,88 Z',
                            'style': {'fill': '#ffffff'},
                            'display_order': 22
                        },
                        {
                            'path_id': '23',
                            'code': 'st12',
                            'path': 'M21,45 L39,63 L21,87 Z',
                            'style': {'fill': '#ffffff'},
                            'display_order': 23
                        }
                    ],
                    'paths_to_remove': [],
                    'min_age': 16
                }
            )
            if created:
                self.stdout.write(f'✓ Created whitening template')
            else:
                self.stdout.write(f'- Whitening template already exists')

            # Template pour restauration amalgame
            restoration_amalgam_template, created = DentalTreatmentTemplate.objects.get_or_create(
                name='restoration_amalgam',
                defaults={
                    'description': 'Restauration à l\'amalgame',
                    'template_paths': [
                        {
                            'path_id': '24',
                            'code': 'st67',
                            'path': 'M24,46 L36,58 L24,70 Z',
                            'style': {'fill': '#c2c2c2'},
                            'display_order': 24
                        }
                    ],
                    'paths_to_remove': [],
                    'min_age': 6
                }
            )
            if created:
                self.stdout.write(f'✓ Created restoration amalgam template')
            else:
                self.stdout.write(f'- Restoration amalgam template already exists')

            # Template pour restauration verre ionomère
            restoration_glass_template, created = DentalTreatmentTemplate.objects.get_or_create(
                name='restoration_glass_ionomer',
                defaults={
                    'description': 'Restauration au verre ionomère',
                    'template_paths': [
                        {
                            'path_id': '25',
                            'code': 'st43',
                            'path': 'M23,47 L37,57 L23,69 Z',
                            'style': {'fill': '#F7D000'},
                            'display_order': 25
                        }
                    ],
                    'paths_to_remove': [],
                    'min_age': 3
                }
            )
            if created:
                self.stdout.write(f'✓ Created restoration glass ionomer template')
            else:
                self.stdout.write(f'- Restoration glass ionomer template already exists')

            # Template pour restauration temporaire
            restoration_temp_template, created = DentalTreatmentTemplate.objects.get_or_create(
                name='restoration_temporary',
                defaults={
                    'description': 'Restauration temporaire',
                    'template_paths': [
                        {
                            'path_id': '26',
                            'code': 'st50',
                            'path': 'M22,46 L38,58 L22,72 Z',
                            'style': {'fill': '#f70808'},
                            'display_order': 26
                        }
                    ],
                    'paths_to_remove': [],
                    'min_age': 3
                }
            )
            if created:
                self.stdout.write(f'✓ Created restoration temporary template')
            else:
                self.stdout.write(f'- Restoration temporary template already exists')

            # Template pour couronne zircone
            crown_zirconia_template, created = DentalTreatmentTemplate.objects.get_or_create(
                name='crown_zirconia',
                defaults={
                    'description': 'Couronne en zircone',
                    'template_paths': [
                        {
                            'path_id': '45',
                            'code': 'st26',
                            'path': 'M15,40 L45,70 L15,100 Z',
                            'style': {'fill': '#E6E9EC'},
                            'display_order': 45
                        },
                        {
                            'path_id': '46',
                            'code': 'st26',
                            'path': 'M16,41 L44,69 L16,99 Z',
                            'style': {'fill': '#E6E9EC'},
                            'display_order': 46
                        },
                        {
                            'path_id': '47',
                            'code': 'st26',
                            'path': 'M34.45,113.91s-8.44-3.2-15.3.5c0,0-19.2,2-13.2,**********,2.51,8.2,13.2,10.9.3-.4,7,2.3,14.6,2,0,0,13.18,1.1,14.8-12.6C48.55,139.81,51.61,117.49,34.45,113.91Z',
                            'style': {'fill': '#E6E9EC'},
                            'display_order': 47
                        }
                    ],
                    'paths_to_remove': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16'],
                    'min_age': 18
                }
            )
            if created:
                self.stdout.write(f'✓ Created crown zirconia template')
            else:
                self.stdout.write(f'- Crown zirconia template already exists')

            # Template pour traitement de canal temporaire (RootTemporary)
            root_temporary_template, created = DentalTreatmentTemplate.objects.get_or_create(
                name='root_temporary',
                defaults={
                    'description': 'Traitement de canal temporaire',
                    'template_paths': [
                        {
                            'path_id': '28',
                            'code': 'st14',
                            'path': 'M20,45 L40,65 L20,85 Z',
                            'style': {'fill': '#3799CE'},
                            'display_order': 28
                        }
                    ],
                    'paths_to_remove': [],
                    'min_age': 6
                }
            )
            if created:
                self.stdout.write(f'✓ Created root temporary template')
            else:
                self.stdout.write(f'- Root temporary template already exists')

            # Template pour traitement de canal au calcium (RootCalcium)
            root_calcium_template, created = DentalTreatmentTemplate.objects.get_or_create(
                name='root_calcium',
                defaults={
                    'description': 'Traitement de canal au calcium',
                    'template_paths': [
                        {
                            'path_id': '30',
                            'code': 'st15',
                            'path': 'M22,47 L38,63 L22,79 Z',
                            'style': {'fill': '#FFFFFF'},
                            'display_order': 30
                        }
                    ],
                    'paths_to_remove': [],
                    'min_age': 8
                }
            )
            if created:
                self.stdout.write(f'✓ Created root calcium template')
            else:
                self.stdout.write(f'- Root calcium template already exists')

            # Template pour traitement de canal gutta-percha (RootGuttaPerchaMode)
            root_gutta_percha_template, created = DentalTreatmentTemplate.objects.get_or_create(
                name='root_gutta_percha',
                defaults={
                    'description': 'Traitement de canal gutta-percha',
                    'template_paths': [
                        {
                            'path_id': '34',
                            'code': 'st16',
                            'path': 'M24,49 L36,61 L24,73 Z',
                            'style': {'fill': '#FF6B6B'},
                            'display_order': 34
                        }
                    ],
                    'paths_to_remove': [],
                    'min_age': 12
                }
            )
            if created:
                self.stdout.write(f'✓ Created root gutta-percha template')
            else:
                self.stdout.write(f'- Root gutta-percha template already exists')

            # Template pour post et soins (PostCare)
            post_care_template, created = DentalTreatmentTemplate.objects.get_or_create(
                name='post_care',
                defaults={
                    'description': 'Post et soins dentaires',
                    'template_paths': [
                        {
                            'path_id': '36',
                            'code': 'st17',
                            'path': 'M26,51 L34,59 L26,67 Z',
                            'style': {'fill': '#4ECDC4'},
                            'display_order': 36
                        }
                    ],
                    'paths_to_remove': [],
                    'min_age': 16
                }
            )
            if created:
                self.stdout.write(f'✓ Created post care template')
            else:
                self.stdout.write(f'- Post care template already exists')

            # Template pour facettes (Veneer)
            veneer_template, created = DentalTreatmentTemplate.objects.get_or_create(
                name='veneer',
                defaults={
                    'description': 'Facettes dentaires',
                    'template_paths': [
                        {
                            'path_id': '37',
                            'code': 'st18',
                            'path': 'M27,52 L33,58 L27,64 Z',
                            'style': {'fill': '#F7DC6F'},
                            'display_order': 37
                        }
                    ],
                    'paths_to_remove': [],
                    'min_age': 18
                }
            )
            if created:
                self.stdout.write(f'✓ Created veneer template')
            else:
                self.stdout.write(f'- Veneer template already exists')

            # Template pour onlay
            onlay_template, created = DentalTreatmentTemplate.objects.get_or_create(
                name='onlay',
                defaults={
                    'description': 'Onlay dentaire',
                    'template_paths': [
                        {
                            'path_id': '39',
                            'code': 'st19',
                            'path': 'M29,54 L31,56 L29,58 Z',
                            'style': {'fill': '#BB8FCE'},
                            'display_order': 39
                        }
                    ],
                    'paths_to_remove': [],
                    'min_age': 16
                }
            )
            if created:
                self.stdout.write(f'✓ Created onlay template')
            else:
                self.stdout.write(f'- Onlay template already exists')

            # Template pour couronne permanente (Crown)
            crown_permanent_template, created = DentalTreatmentTemplate.objects.get_or_create(
                name='crown_permanent',
                defaults={
                    'description': 'Couronne permanente',
                    'template_paths': [
                        {
                            'path_id': '41',
                            'code': 'st20',
                            'path': 'M31,56 L29,54 L31,52 Z',
                            'style': {'fill': '#85C1E9'},
                            'display_order': 41
                        }
                    ],
                    'paths_to_remove': [],
                    'min_age': 18
                }
            )
            if created:
                self.stdout.write(f'✓ Created crown permanent template')
            else:
                self.stdout.write(f'- Crown permanent template already exists')

            # Template pour couronne temporaire (CrownTemporary)
            crown_temporary_template, created = DentalTreatmentTemplate.objects.get_or_create(
                name='crown_temporary',
                defaults={
                    'description': 'Couronne temporaire',
                    'template_paths': [
                        {
                            'path_id': '42',
                            'code': 'st21',
                            'path': 'M32,57 L28,53 L32,49 Z',
                            'style': {'fill': '#F8C471'},
                            'display_order': 42
                        }
                    ],
                    'paths_to_remove': [],
                    'min_age': 12
                }
            )
            if created:
                self.stdout.write(f'✓ Created crown temporary template')
            else:
                self.stdout.write(f'- Crown temporary template already exists')

            # Template pour couronne en or (CrownGold)
            crown_gold_template, created = DentalTreatmentTemplate.objects.get_or_create(
                name='crown_gold',
                defaults={
                    'description': 'Couronne en or',
                    'template_paths': [
                        {
                            'path_id': '44',
                            'code': 'st22',
                            'path': 'M34,59 L26,51 L34,43 Z',
                            'style': {'fill': '#FFD700'},
                            'display_order': 44
                        }
                    ],
                    'paths_to_remove': [],
                    'min_age': 18
                }
            )
            if created:
                self.stdout.write(f'✓ Created crown gold template')
            else:
                self.stdout.write(f'- Crown gold template already exists')

            # Template pour extraction dentaire
            extraction_template, created = DentalTreatmentTemplate.objects.get_or_create(
                name='extraction',
                defaults={
                    'description': 'Extraction dentaire',
                    'template_paths': [
                        {
                            'path_id': '53',
                            'code': 'st23',
                            'path': 'M35,60 L25,50 L35,40 Z',
                            'style': {'fill': '#E74C3C'},
                            'display_order': 53
                        }
                    ],
                    'paths_to_remove': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16'],
                    'min_age': 6
                }
            )
            if created:
                self.stdout.write(f'✓ Created extraction template')
            else:
                self.stdout.write(f'- Extraction template already exists')

            # Template pour prothèse dentaire (Denture)
            denture_template, created = DentalTreatmentTemplate.objects.get_or_create(
                name='denture',
                defaults={
                    'description': 'Prothèse dentaire complète',
                    'template_paths': [
                        {
                            'path_id': '48',
                            'code': 'st24',
                            'path': 'M12,38 L48,72 L12,106 Z',
                            'style': {'fill': '#F8F9FA'},
                            'display_order': 48
                        },
                        {
                            'path_id': '49',
                            'code': 'st24',
                            'path': 'M13,39 L47,71 L13,105 Z',
                            'style': {'fill': '#F8F9FA'},
                            'display_order': 49
                        }
                    ],
                    'paths_to_remove': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16'],
                    'min_age': 50
                }
            )
            if created:
                self.stdout.write(f'✓ Created denture template')
            else:
                self.stdout.write(f'- Denture template already exists')

            # Template pour pont dentaire (Bridge1)
            bridge_template, created = DentalTreatmentTemplate.objects.get_or_create(
                name='bridge',
                defaults={
                    'description': 'Pont dentaire',
                    'template_paths': [
                        {
                            'path_id': '50',
                            'code': 'st25',
                            'path': 'M14,40 L46,70 L14,104 Z',
                            'style': {'fill': '#AED6F1'},
                            'display_order': 50
                        },
                        {
                            'path_id': '51',
                            'code': 'st25',
                            'path': 'M15,41 L45,69 L15,103 Z',
                            'style': {'fill': '#AED6F1'},
                            'display_order': 51
                        }
                    ],
                    'paths_to_remove': [],
                    'min_age': 18
                }
            )
            if created:
                self.stdout.write(f'✓ Created bridge template')
            else:
                self.stdout.write(f'- Bridge template already exists')

            # Template pour implant dentaire (Implant1)
            implant_template, created = DentalTreatmentTemplate.objects.get_or_create(
                name='implant',
                defaults={
                    'description': 'Implant dentaire',
                    'template_paths': [
                        {
                            'path_id': '52',
                            'code': 'st26',
                            'path': 'M16,42 L44,68 L16,102 Z',
                            'style': {'fill': '#95A5A6'},
                            'display_order': 52
                        }
                    ],
                    'paths_to_remove': [],
                    'min_age': 18
                }
            )
            if created:
                self.stdout.write(f'✓ Created implant template')
            else:
                self.stdout.write(f'- Implant template already exists')

            # Template pour greffe osseuse (Bone)
            bone_graft_template, created = DentalTreatmentTemplate.objects.get_or_create(
                name='bone_graft',
                defaults={
                    'description': 'Greffe osseuse dentaire',
                    'template_paths': [
                        {
                            'path_id': '54',
                            'code': 'st27',
                            'path': 'M17,43 L43,67 L17,101 Z',
                            'style': {'fill': '#D5DBDB'},
                            'display_order': 54
                        }
                    ],
                    'paths_to_remove': [],
                    'min_age': 16
                }
            )
            if created:
                self.stdout.write(f'✓ Created bone graft template')
            else:
                self.stdout.write(f'- Bone graft template already exists')

            # Template pour résection (Resection1)
            resection_template, created = DentalTreatmentTemplate.objects.get_or_create(
                name='resection',
                defaults={
                    'description': 'Résection dentaire',
                    'template_paths': [
                        {
                            'path_id': '55',
                            'code': 'st28',
                            'path': 'M18,44 L42,66 L18,100 Z',
                            'style': {'fill': '#F39C12'},
                            'display_order': 55
                        }
                    ],
                    'paths_to_remove': [],
                    'min_age': 14
                }
            )
            if created:
                self.stdout.write(f'✓ Created resection template')
            else:
                self.stdout.write(f'- Resection template already exists')

            # Template pour couronne dentaire spécialisée (TeethCrown1)
            teeth_crown_template, created = DentalTreatmentTemplate.objects.get_or_create(
                name='teeth_crown_specialized',
                defaults={
                    'description': 'Couronne dentaire spécialisée',
                    'template_paths': [
                        {
                            'path_id': '56',
                            'code': 'st29',
                            'path': 'M19,45 L41,65 L19,99 Z',
                            'style': {'fill': '#E8DAEF'},
                            'display_order': 56
                        }
                    ],
                    'paths_to_remove': [],
                    'min_age': 16
                }
            )
            if created:
                self.stdout.write(f'✓ Created teeth crown specialized template')
            else:
                self.stdout.write(f'- Teeth crown specialized template already exists')

        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully initialized {DentalTreatmentTemplate.objects.count()} dental treatment templates'
            )
        )
