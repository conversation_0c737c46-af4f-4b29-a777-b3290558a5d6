# Generated by Django 4.2.7 on 2025-05-28 16:24

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('dentistry', '0005_dentalsvgdata_dentalmodification'),
    ]

    operations = [
        migrations.CreateModel(
            name='DentalSvgConfiguration',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_private', models.BooleanField(default=False, verbose_name='Is private')),
                ('privatized_by', models.UUIDField(blank=True, null=True, verbose_name='Privatized by')),
                ('owner', models.UUIDField(blank=True, null=True, verbose_name='Owner')),
                ('tooth_id', models.PositiveSmallIntegerField(help_text='1-16: Upper jaw, 17-32: Lower jaw', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(32)], verbose_name='Tooth ID')),
                ('width', models.CharField(default='59.8625px', help_text='Width of the SVG element', max_length=50, verbose_name='SVG Width')),
                ('position', models.CharField(default='0 0 50.8 172', help_text='ViewBox position of the SVG', max_length=100, verbose_name='SVG Position')),
                ('age_restriction', models.PositiveSmallIntegerField(blank=True, help_text='Hide this tooth if patient age is less than this value', null=True, verbose_name='Age Restriction')),
                ('is_cleaning_active', models.BooleanField(default=False, verbose_name='Cleaning Active')),
                ('is_fluoride_active', models.BooleanField(default=False, verbose_name='Fluoride Active')),
                ('is_sealant_active', models.BooleanField(default=False, verbose_name='Sealant Active')),
                ('is_whitening_active', models.BooleanField(default=False, verbose_name='Whitening Active')),
                ('is_restoration_amalgam_active', models.BooleanField(default=False, verbose_name='Restoration Amalgam Active')),
                ('is_restoration_glass_ionomer_active', models.BooleanField(default=False, verbose_name='Restoration Glass Ionomer Active')),
                ('is_restoration_temporary_active', models.BooleanField(default=False, verbose_name='Restoration Temporary Active')),
                ('is_crown_zirconia_active', models.BooleanField(default=False, verbose_name='Crown Zirconia Active')),
                ('is_crown_gold_active', models.BooleanField(default=False, verbose_name='Crown Gold Active')),
                ('is_veneer_active', models.BooleanField(default=False, verbose_name='Veneer Active')),
                ('is_onlay_active', models.BooleanField(default=False, verbose_name='Onlay Active')),
                ('is_bridge_active', models.BooleanField(default=False, verbose_name='Bridge Active')),
                ('is_implant_active', models.BooleanField(default=False, verbose_name='Implant Active')),
                ('is_extraction_active', models.BooleanField(default=False, verbose_name='Extraction Active')),
                ('is_bone_graft_active', models.BooleanField(default=False, verbose_name='Bone Graft Active')),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='dental_svg_configurations', to='dentistry.dentistrypatient', verbose_name='Patient')),
            ],
            options={
                'verbose_name': 'Dental SVG Configuration',
                'verbose_name_plural': 'Dental SVG Configurations',
                'ordering': ['tooth_id'],
                'unique_together': {('patient', 'tooth_id')},
            },
        ),
        migrations.CreateModel(
            name='DentalTreatmentTemplate',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_private', models.BooleanField(default=False, verbose_name='Is private')),
                ('privatized_by', models.UUIDField(blank=True, null=True, verbose_name='Privatized by')),
                ('owner', models.UUIDField(blank=True, null=True, verbose_name='Owner')),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='Treatment Name')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('template_paths', models.JSONField(default=list, help_text='List of path configurations for this treatment', verbose_name='Template Paths')),
                ('paths_to_remove', models.JSONField(default=list, help_text='List of path IDs to remove when applying this treatment', verbose_name='Paths to Remove')),
                ('min_age', models.PositiveSmallIntegerField(blank=True, help_text='Minimum patient age for this treatment', null=True, verbose_name='Minimum Age')),
            ],
            options={
                'verbose_name': 'Dental Treatment Template',
                'verbose_name_plural': 'Dental Treatment Templates',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='DentalSvgPath',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_private', models.BooleanField(default=False, verbose_name='Is private')),
                ('privatized_by', models.UUIDField(blank=True, null=True, verbose_name='Privatized by')),
                ('owner', models.UUIDField(blank=True, null=True, verbose_name='Owner')),
                ('path_id', models.CharField(help_text='1-16: Base paths, 17+: Treatment paths', max_length=10, verbose_name='Path ID')),
                ('code', models.CharField(help_text='CSS class for styling (e.g., st0, st26)', max_length=50, verbose_name='CSS Code')),
                ('path', models.TextField(help_text='SVG path data (d attribute)', verbose_name='SVG Path')),
                ('style', models.JSONField(default=dict, help_text="Custom CSS styles as JSON (e.g., {'cursor': 'pointer'})", verbose_name='Custom Style')),
                ('transform', models.CharField(blank=True, help_text='SVG transform attribute', max_length=200, verbose_name='Transform')),
                ('treatment_type', models.CharField(choices=[('base', 'Base Tooth'), ('cleaning', 'Cleaning'), ('fluoride', 'Fluoride'), ('sealant', 'Sealant'), ('whitening', 'Whitening'), ('restoration_amalgam', 'Restoration Amalgam'), ('restoration_glass_ionomer', 'Restoration Glass Ionomer'), ('restoration_temporary', 'Restoration Temporary'), ('crown_zirconia', 'Crown Zirconia'), ('crown_gold', 'Crown Gold'), ('veneer', 'Veneer'), ('onlay', 'Onlay'), ('bridge', 'Bridge'), ('implant', 'Implant'), ('extraction', 'Extraction'), ('bone_graft', 'Bone Graft')], default='base', max_length=50, verbose_name='Treatment Type')),
                ('display_order', models.PositiveSmallIntegerField(default=0, verbose_name='Display Order')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this path should be displayed', verbose_name='Is Active')),
                ('dental_svg_config', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='svg_paths', to='dentistry.dentalsvgconfiguration', verbose_name='Dental SVG Configuration')),
            ],
            options={
                'verbose_name': 'Dental SVG Path',
                'verbose_name_plural': 'Dental SVG Paths',
                'ordering': ['display_order', 'path_id'],
                'unique_together': {('dental_svg_config', 'path_id')},
            },
        ),
    ]
