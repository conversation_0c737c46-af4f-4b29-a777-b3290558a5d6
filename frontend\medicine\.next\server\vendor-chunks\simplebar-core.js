"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/simplebar-core";
exports.ids = ["vendor-chunks/simplebar-core"];
exports.modules = {

/***/ "(ssr)/./node_modules/simplebar-core/dist/index.mjs":
/*!****************************************************!*\
  !*** ./node_modules/simplebar-core/dist/index.mjs ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SimpleBarCore)\n/* harmony export */ });\n/* harmony import */ var lodash_es_debounce_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lodash-es/debounce.js */ \"(ssr)/./node_modules/lodash-es/debounce.js\");\n/* harmony import */ var lodash_es_throttle_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lodash-es/throttle.js */ \"(ssr)/./node_modules/lodash-es/throttle.js\");\n/**\n * simplebar-core - v1.3.1\n * Scrollbars, simpler.\n * https://grsmto.github.io/simplebar/\n *\n * Made by Adrien Denat from a fork by Jonathan Nicol\n * Under MIT License\n */\n\n\n\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n\r\nvar __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\n\nfunction getElementWindow$1(element) {\n    if (!element ||\n        !element.ownerDocument ||\n        !element.ownerDocument.defaultView) {\n        return window;\n    }\n    return element.ownerDocument.defaultView;\n}\nfunction getElementDocument$1(element) {\n    if (!element || !element.ownerDocument) {\n        return document;\n    }\n    return element.ownerDocument;\n}\n// Helper function to retrieve options from element attributes\nvar getOptions$1 = function (obj) {\n    var initialObj = {};\n    var options = Array.prototype.reduce.call(obj, function (acc, attribute) {\n        var option = attribute.name.match(/data-simplebar-(.+)/);\n        if (option) {\n            var key = option[1].replace(/\\W+(.)/g, function (_, chr) { return chr.toUpperCase(); });\n            switch (attribute.value) {\n                case 'true':\n                    acc[key] = true;\n                    break;\n                case 'false':\n                    acc[key] = false;\n                    break;\n                case undefined:\n                    acc[key] = true;\n                    break;\n                default:\n                    acc[key] = attribute.value;\n            }\n        }\n        return acc;\n    }, initialObj);\n    return options;\n};\nfunction addClasses$1(el, classes) {\n    var _a;\n    if (!el)\n        return;\n    (_a = el.classList).add.apply(_a, classes.split(' '));\n}\nfunction removeClasses$1(el, classes) {\n    if (!el)\n        return;\n    classes.split(' ').forEach(function (className) {\n        el.classList.remove(className);\n    });\n}\nfunction classNamesToQuery$1(classNames) {\n    return \".\".concat(classNames.split(' ').join('.'));\n}\nvar canUseDOM = !!(typeof window !== 'undefined' &&\n    window.document &&\n    window.document.createElement);\n\nvar helpers = /*#__PURE__*/Object.freeze({\n    __proto__: null,\n    addClasses: addClasses$1,\n    canUseDOM: canUseDOM,\n    classNamesToQuery: classNamesToQuery$1,\n    getElementDocument: getElementDocument$1,\n    getElementWindow: getElementWindow$1,\n    getOptions: getOptions$1,\n    removeClasses: removeClasses$1\n});\n\nvar cachedScrollbarWidth = null;\nvar cachedDevicePixelRatio = null;\nif (canUseDOM) {\n    window.addEventListener('resize', function () {\n        if (cachedDevicePixelRatio !== window.devicePixelRatio) {\n            cachedDevicePixelRatio = window.devicePixelRatio;\n            cachedScrollbarWidth = null;\n        }\n    });\n}\nfunction scrollbarWidth() {\n    if (cachedScrollbarWidth === null) {\n        if (typeof document === 'undefined') {\n            cachedScrollbarWidth = 0;\n            return cachedScrollbarWidth;\n        }\n        var body = document.body;\n        var box = document.createElement('div');\n        box.classList.add('simplebar-hide-scrollbar');\n        body.appendChild(box);\n        var width = box.getBoundingClientRect().right;\n        body.removeChild(box);\n        cachedScrollbarWidth = width;\n    }\n    return cachedScrollbarWidth;\n}\n\nvar getElementWindow = getElementWindow$1, getElementDocument = getElementDocument$1, getOptions = getOptions$1, addClasses = addClasses$1, removeClasses = removeClasses$1, classNamesToQuery = classNamesToQuery$1;\nvar SimpleBarCore = /** @class */ (function () {\n    function SimpleBarCore(element, options) {\n        if (options === void 0) { options = {}; }\n        var _this = this;\n        this.removePreventClickId = null;\n        this.minScrollbarWidth = 20;\n        this.stopScrollDelay = 175;\n        this.isScrolling = false;\n        this.isMouseEntering = false;\n        this.isDragging = false;\n        this.scrollXTicking = false;\n        this.scrollYTicking = false;\n        this.wrapperEl = null;\n        this.contentWrapperEl = null;\n        this.contentEl = null;\n        this.offsetEl = null;\n        this.maskEl = null;\n        this.placeholderEl = null;\n        this.heightAutoObserverWrapperEl = null;\n        this.heightAutoObserverEl = null;\n        this.rtlHelpers = null;\n        this.scrollbarWidth = 0;\n        this.resizeObserver = null;\n        this.mutationObserver = null;\n        this.elStyles = null;\n        this.isRtl = null;\n        this.mouseX = 0;\n        this.mouseY = 0;\n        this.onMouseMove = function () { };\n        this.onWindowResize = function () { };\n        this.onStopScrolling = function () { };\n        this.onMouseEntered = function () { };\n        /**\n         * On scroll event handling\n         */\n        this.onScroll = function () {\n            var elWindow = getElementWindow(_this.el);\n            if (!_this.scrollXTicking) {\n                elWindow.requestAnimationFrame(_this.scrollX);\n                _this.scrollXTicking = true;\n            }\n            if (!_this.scrollYTicking) {\n                elWindow.requestAnimationFrame(_this.scrollY);\n                _this.scrollYTicking = true;\n            }\n            if (!_this.isScrolling) {\n                _this.isScrolling = true;\n                addClasses(_this.el, _this.classNames.scrolling);\n            }\n            _this.showScrollbar('x');\n            _this.showScrollbar('y');\n            _this.onStopScrolling();\n        };\n        this.scrollX = function () {\n            if (_this.axis.x.isOverflowing) {\n                _this.positionScrollbar('x');\n            }\n            _this.scrollXTicking = false;\n        };\n        this.scrollY = function () {\n            if (_this.axis.y.isOverflowing) {\n                _this.positionScrollbar('y');\n            }\n            _this.scrollYTicking = false;\n        };\n        this._onStopScrolling = function () {\n            removeClasses(_this.el, _this.classNames.scrolling);\n            if (_this.options.autoHide) {\n                _this.hideScrollbar('x');\n                _this.hideScrollbar('y');\n            }\n            _this.isScrolling = false;\n        };\n        this.onMouseEnter = function () {\n            if (!_this.isMouseEntering) {\n                addClasses(_this.el, _this.classNames.mouseEntered);\n                _this.showScrollbar('x');\n                _this.showScrollbar('y');\n                _this.isMouseEntering = true;\n            }\n            _this.onMouseEntered();\n        };\n        this._onMouseEntered = function () {\n            removeClasses(_this.el, _this.classNames.mouseEntered);\n            if (_this.options.autoHide) {\n                _this.hideScrollbar('x');\n                _this.hideScrollbar('y');\n            }\n            _this.isMouseEntering = false;\n        };\n        this._onMouseMove = function (e) {\n            _this.mouseX = e.clientX;\n            _this.mouseY = e.clientY;\n            if (_this.axis.x.isOverflowing || _this.axis.x.forceVisible) {\n                _this.onMouseMoveForAxis('x');\n            }\n            if (_this.axis.y.isOverflowing || _this.axis.y.forceVisible) {\n                _this.onMouseMoveForAxis('y');\n            }\n        };\n        this.onMouseLeave = function () {\n            _this.onMouseMove.cancel();\n            if (_this.axis.x.isOverflowing || _this.axis.x.forceVisible) {\n                _this.onMouseLeaveForAxis('x');\n            }\n            if (_this.axis.y.isOverflowing || _this.axis.y.forceVisible) {\n                _this.onMouseLeaveForAxis('y');\n            }\n            _this.mouseX = -1;\n            _this.mouseY = -1;\n        };\n        this._onWindowResize = function () {\n            // Recalculate scrollbarWidth in case it's a zoom\n            _this.scrollbarWidth = _this.getScrollbarWidth();\n            _this.hideNativeScrollbar();\n        };\n        this.onPointerEvent = function (e) {\n            if (!_this.axis.x.track.el ||\n                !_this.axis.y.track.el ||\n                !_this.axis.x.scrollbar.el ||\n                !_this.axis.y.scrollbar.el)\n                return;\n            var isWithinTrackXBounds, isWithinTrackYBounds;\n            _this.axis.x.track.rect = _this.axis.x.track.el.getBoundingClientRect();\n            _this.axis.y.track.rect = _this.axis.y.track.el.getBoundingClientRect();\n            if (_this.axis.x.isOverflowing || _this.axis.x.forceVisible) {\n                isWithinTrackXBounds = _this.isWithinBounds(_this.axis.x.track.rect);\n            }\n            if (_this.axis.y.isOverflowing || _this.axis.y.forceVisible) {\n                isWithinTrackYBounds = _this.isWithinBounds(_this.axis.y.track.rect);\n            }\n            // If any pointer event is called on the scrollbar\n            if (isWithinTrackXBounds || isWithinTrackYBounds) {\n                // Prevent event leaking\n                e.stopPropagation();\n                if (e.type === 'pointerdown' && e.pointerType !== 'touch') {\n                    if (isWithinTrackXBounds) {\n                        _this.axis.x.scrollbar.rect =\n                            _this.axis.x.scrollbar.el.getBoundingClientRect();\n                        if (_this.isWithinBounds(_this.axis.x.scrollbar.rect)) {\n                            _this.onDragStart(e, 'x');\n                        }\n                        else {\n                            _this.onTrackClick(e, 'x');\n                        }\n                    }\n                    if (isWithinTrackYBounds) {\n                        _this.axis.y.scrollbar.rect =\n                            _this.axis.y.scrollbar.el.getBoundingClientRect();\n                        if (_this.isWithinBounds(_this.axis.y.scrollbar.rect)) {\n                            _this.onDragStart(e, 'y');\n                        }\n                        else {\n                            _this.onTrackClick(e, 'y');\n                        }\n                    }\n                }\n            }\n        };\n        /**\n         * Drag scrollbar handle\n         */\n        this.drag = function (e) {\n            var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l;\n            if (!_this.draggedAxis || !_this.contentWrapperEl)\n                return;\n            var eventOffset;\n            var track = _this.axis[_this.draggedAxis].track;\n            var trackSize = (_b = (_a = track.rect) === null || _a === void 0 ? void 0 : _a[_this.axis[_this.draggedAxis].sizeAttr]) !== null && _b !== void 0 ? _b : 0;\n            var scrollbar = _this.axis[_this.draggedAxis].scrollbar;\n            var contentSize = (_d = (_c = _this.contentWrapperEl) === null || _c === void 0 ? void 0 : _c[_this.axis[_this.draggedAxis].scrollSizeAttr]) !== null && _d !== void 0 ? _d : 0;\n            var hostSize = parseInt((_f = (_e = _this.elStyles) === null || _e === void 0 ? void 0 : _e[_this.axis[_this.draggedAxis].sizeAttr]) !== null && _f !== void 0 ? _f : '0px', 10);\n            e.preventDefault();\n            e.stopPropagation();\n            if (_this.draggedAxis === 'y') {\n                eventOffset = e.pageY;\n            }\n            else {\n                eventOffset = e.pageX;\n            }\n            // Calculate how far the user's mouse is from the top/left of the scrollbar (minus the dragOffset).\n            var dragPos = eventOffset -\n                ((_h = (_g = track.rect) === null || _g === void 0 ? void 0 : _g[_this.axis[_this.draggedAxis].offsetAttr]) !== null && _h !== void 0 ? _h : 0) -\n                _this.axis[_this.draggedAxis].dragOffset;\n            dragPos =\n                _this.draggedAxis === 'x' && _this.isRtl\n                    ? ((_k = (_j = track.rect) === null || _j === void 0 ? void 0 : _j[_this.axis[_this.draggedAxis].sizeAttr]) !== null && _k !== void 0 ? _k : 0) -\n                        scrollbar.size -\n                        dragPos\n                    : dragPos;\n            // Convert the mouse position into a percentage of the scrollbar height/width.\n            var dragPerc = dragPos / (trackSize - scrollbar.size);\n            // Scroll the content by the same percentage.\n            var scrollPos = dragPerc * (contentSize - hostSize);\n            // Fix browsers inconsistency on RTL\n            if (_this.draggedAxis === 'x' && _this.isRtl) {\n                scrollPos = ((_l = SimpleBarCore.getRtlHelpers()) === null || _l === void 0 ? void 0 : _l.isScrollingToNegative)\n                    ? -scrollPos\n                    : scrollPos;\n            }\n            _this.contentWrapperEl[_this.axis[_this.draggedAxis].scrollOffsetAttr] =\n                scrollPos;\n        };\n        /**\n         * End scroll handle drag\n         */\n        this.onEndDrag = function (e) {\n            _this.isDragging = false;\n            var elDocument = getElementDocument(_this.el);\n            var elWindow = getElementWindow(_this.el);\n            e.preventDefault();\n            e.stopPropagation();\n            removeClasses(_this.el, _this.classNames.dragging);\n            _this.onStopScrolling();\n            elDocument.removeEventListener('mousemove', _this.drag, true);\n            elDocument.removeEventListener('mouseup', _this.onEndDrag, true);\n            _this.removePreventClickId = elWindow.setTimeout(function () {\n                // Remove these asynchronously so we still suppress click events\n                // generated simultaneously with mouseup.\n                elDocument.removeEventListener('click', _this.preventClick, true);\n                elDocument.removeEventListener('dblclick', _this.preventClick, true);\n                _this.removePreventClickId = null;\n            });\n        };\n        /**\n         * Handler to ignore click events during drag\n         */\n        this.preventClick = function (e) {\n            e.preventDefault();\n            e.stopPropagation();\n        };\n        this.el = element;\n        this.options = __assign(__assign({}, SimpleBarCore.defaultOptions), options);\n        this.classNames = __assign(__assign({}, SimpleBarCore.defaultOptions.classNames), options.classNames);\n        this.axis = {\n            x: {\n                scrollOffsetAttr: 'scrollLeft',\n                sizeAttr: 'width',\n                scrollSizeAttr: 'scrollWidth',\n                offsetSizeAttr: 'offsetWidth',\n                offsetAttr: 'left',\n                overflowAttr: 'overflowX',\n                dragOffset: 0,\n                isOverflowing: true,\n                forceVisible: false,\n                track: { size: null, el: null, rect: null, isVisible: false },\n                scrollbar: { size: null, el: null, rect: null, isVisible: false }\n            },\n            y: {\n                scrollOffsetAttr: 'scrollTop',\n                sizeAttr: 'height',\n                scrollSizeAttr: 'scrollHeight',\n                offsetSizeAttr: 'offsetHeight',\n                offsetAttr: 'top',\n                overflowAttr: 'overflowY',\n                dragOffset: 0,\n                isOverflowing: true,\n                forceVisible: false,\n                track: { size: null, el: null, rect: null, isVisible: false },\n                scrollbar: { size: null, el: null, rect: null, isVisible: false }\n            }\n        };\n        if (typeof this.el !== 'object' || !this.el.nodeName) {\n            throw new Error(\"Argument passed to SimpleBar must be an HTML element instead of \".concat(this.el));\n        }\n        this.onMouseMove = (0,lodash_es_throttle_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this._onMouseMove, 64);\n        this.onWindowResize = (0,lodash_es_debounce_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(this._onWindowResize, 64, { leading: true });\n        this.onStopScrolling = (0,lodash_es_debounce_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(this._onStopScrolling, this.stopScrollDelay);\n        this.onMouseEntered = (0,lodash_es_debounce_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(this._onMouseEntered, this.stopScrollDelay);\n        this.init();\n    }\n    /**\n     * Helper to fix browsers inconsistency on RTL:\n     *  - Firefox inverts the scrollbar initial position\n     *  - IE11 inverts both scrollbar position and scrolling offset\n     * Directly inspired by @KingSora's OverlayScrollbars https://github.com/KingSora/OverlayScrollbars/blob/master/js/OverlayScrollbars.js#L1634\n     */\n    SimpleBarCore.getRtlHelpers = function () {\n        if (SimpleBarCore.rtlHelpers) {\n            return SimpleBarCore.rtlHelpers;\n        }\n        var dummyDiv = document.createElement('div');\n        dummyDiv.innerHTML =\n            '<div class=\"simplebar-dummy-scrollbar-size\"><div></div></div>';\n        var scrollbarDummyEl = dummyDiv.firstElementChild;\n        var dummyChild = scrollbarDummyEl === null || scrollbarDummyEl === void 0 ? void 0 : scrollbarDummyEl.firstElementChild;\n        if (!dummyChild)\n            return null;\n        document.body.appendChild(scrollbarDummyEl);\n        scrollbarDummyEl.scrollLeft = 0;\n        var dummyContainerOffset = SimpleBarCore.getOffset(scrollbarDummyEl);\n        var dummyChildOffset = SimpleBarCore.getOffset(dummyChild);\n        scrollbarDummyEl.scrollLeft = -999;\n        var dummyChildOffsetAfterScroll = SimpleBarCore.getOffset(dummyChild);\n        document.body.removeChild(scrollbarDummyEl);\n        SimpleBarCore.rtlHelpers = {\n            // determines if the scrolling is responding with negative values\n            isScrollOriginAtZero: dummyContainerOffset.left !== dummyChildOffset.left,\n            // determines if the origin scrollbar position is inverted or not (positioned on left or right)\n            isScrollingToNegative: dummyChildOffset.left !== dummyChildOffsetAfterScroll.left\n        };\n        return SimpleBarCore.rtlHelpers;\n    };\n    SimpleBarCore.prototype.getScrollbarWidth = function () {\n        // Try/catch for FF 56 throwing on undefined computedStyles\n        try {\n            // Detect browsers supporting CSS scrollbar styling and do not calculate\n            if ((this.contentWrapperEl &&\n                getComputedStyle(this.contentWrapperEl, '::-webkit-scrollbar')\n                    .display === 'none') ||\n                'scrollbarWidth' in document.documentElement.style ||\n                '-ms-overflow-style' in document.documentElement.style) {\n                return 0;\n            }\n            else {\n                return scrollbarWidth();\n            }\n        }\n        catch (e) {\n            return scrollbarWidth();\n        }\n    };\n    SimpleBarCore.getOffset = function (el) {\n        var rect = el.getBoundingClientRect();\n        var elDocument = getElementDocument(el);\n        var elWindow = getElementWindow(el);\n        return {\n            top: rect.top +\n                (elWindow.pageYOffset || elDocument.documentElement.scrollTop),\n            left: rect.left +\n                (elWindow.pageXOffset || elDocument.documentElement.scrollLeft)\n        };\n    };\n    SimpleBarCore.prototype.init = function () {\n        // We stop here on server-side\n        if (canUseDOM) {\n            this.initDOM();\n            this.rtlHelpers = SimpleBarCore.getRtlHelpers();\n            this.scrollbarWidth = this.getScrollbarWidth();\n            this.recalculate();\n            this.initListeners();\n        }\n    };\n    SimpleBarCore.prototype.initDOM = function () {\n        var _a, _b;\n        // assume that element has his DOM already initiated\n        this.wrapperEl = this.el.querySelector(classNamesToQuery(this.classNames.wrapper));\n        this.contentWrapperEl =\n            this.options.scrollableNode ||\n                this.el.querySelector(classNamesToQuery(this.classNames.contentWrapper));\n        this.contentEl =\n            this.options.contentNode ||\n                this.el.querySelector(classNamesToQuery(this.classNames.contentEl));\n        this.offsetEl = this.el.querySelector(classNamesToQuery(this.classNames.offset));\n        this.maskEl = this.el.querySelector(classNamesToQuery(this.classNames.mask));\n        this.placeholderEl = this.findChild(this.wrapperEl, classNamesToQuery(this.classNames.placeholder));\n        this.heightAutoObserverWrapperEl = this.el.querySelector(classNamesToQuery(this.classNames.heightAutoObserverWrapperEl));\n        this.heightAutoObserverEl = this.el.querySelector(classNamesToQuery(this.classNames.heightAutoObserverEl));\n        this.axis.x.track.el = this.findChild(this.el, \"\".concat(classNamesToQuery(this.classNames.track)).concat(classNamesToQuery(this.classNames.horizontal)));\n        this.axis.y.track.el = this.findChild(this.el, \"\".concat(classNamesToQuery(this.classNames.track)).concat(classNamesToQuery(this.classNames.vertical)));\n        this.axis.x.scrollbar.el =\n            ((_a = this.axis.x.track.el) === null || _a === void 0 ? void 0 : _a.querySelector(classNamesToQuery(this.classNames.scrollbar))) || null;\n        this.axis.y.scrollbar.el =\n            ((_b = this.axis.y.track.el) === null || _b === void 0 ? void 0 : _b.querySelector(classNamesToQuery(this.classNames.scrollbar))) || null;\n        if (!this.options.autoHide) {\n            addClasses(this.axis.x.scrollbar.el, this.classNames.visible);\n            addClasses(this.axis.y.scrollbar.el, this.classNames.visible);\n        }\n    };\n    SimpleBarCore.prototype.initListeners = function () {\n        var _this = this;\n        var _a;\n        var elWindow = getElementWindow(this.el);\n        // Event listeners\n        this.el.addEventListener('mouseenter', this.onMouseEnter);\n        this.el.addEventListener('pointerdown', this.onPointerEvent, true);\n        this.el.addEventListener('mousemove', this.onMouseMove);\n        this.el.addEventListener('mouseleave', this.onMouseLeave);\n        (_a = this.contentWrapperEl) === null || _a === void 0 ? void 0 : _a.addEventListener('scroll', this.onScroll);\n        // Browser zoom triggers a window resize\n        elWindow.addEventListener('resize', this.onWindowResize);\n        if (!this.contentEl)\n            return;\n        if (window.ResizeObserver) {\n            // Hack for https://github.com/WICG/ResizeObserver/issues/38\n            var resizeObserverStarted_1 = false;\n            var resizeObserver = elWindow.ResizeObserver || ResizeObserver;\n            this.resizeObserver = new resizeObserver(function () {\n                if (!resizeObserverStarted_1)\n                    return;\n                elWindow.requestAnimationFrame(function () {\n                    _this.recalculate();\n                });\n            });\n            this.resizeObserver.observe(this.el);\n            this.resizeObserver.observe(this.contentEl);\n            elWindow.requestAnimationFrame(function () {\n                resizeObserverStarted_1 = true;\n            });\n        }\n        // This is required to detect horizontal scroll. Vertical scroll only needs the resizeObserver.\n        this.mutationObserver = new elWindow.MutationObserver(function () {\n            elWindow.requestAnimationFrame(function () {\n                _this.recalculate();\n            });\n        });\n        this.mutationObserver.observe(this.contentEl, {\n            childList: true,\n            subtree: true,\n            characterData: true\n        });\n    };\n    SimpleBarCore.prototype.recalculate = function () {\n        if (!this.heightAutoObserverEl ||\n            !this.contentEl ||\n            !this.contentWrapperEl ||\n            !this.wrapperEl ||\n            !this.placeholderEl)\n            return;\n        var elWindow = getElementWindow(this.el);\n        this.elStyles = elWindow.getComputedStyle(this.el);\n        this.isRtl = this.elStyles.direction === 'rtl';\n        var contentElOffsetWidth = this.contentEl.offsetWidth;\n        var isHeightAuto = this.heightAutoObserverEl.offsetHeight <= 1;\n        var isWidthAuto = this.heightAutoObserverEl.offsetWidth <= 1 || contentElOffsetWidth > 0;\n        var contentWrapperElOffsetWidth = this.contentWrapperEl.offsetWidth;\n        var elOverflowX = this.elStyles.overflowX;\n        var elOverflowY = this.elStyles.overflowY;\n        this.contentEl.style.padding = \"\".concat(this.elStyles.paddingTop, \" \").concat(this.elStyles.paddingRight, \" \").concat(this.elStyles.paddingBottom, \" \").concat(this.elStyles.paddingLeft);\n        this.wrapperEl.style.margin = \"-\".concat(this.elStyles.paddingTop, \" -\").concat(this.elStyles.paddingRight, \" -\").concat(this.elStyles.paddingBottom, \" -\").concat(this.elStyles.paddingLeft);\n        var contentElScrollHeight = this.contentEl.scrollHeight;\n        var contentElScrollWidth = this.contentEl.scrollWidth;\n        this.contentWrapperEl.style.height = isHeightAuto ? 'auto' : '100%';\n        // Determine placeholder size\n        this.placeholderEl.style.width = isWidthAuto\n            ? \"\".concat(contentElOffsetWidth || contentElScrollWidth, \"px\")\n            : 'auto';\n        this.placeholderEl.style.height = \"\".concat(contentElScrollHeight, \"px\");\n        var contentWrapperElOffsetHeight = this.contentWrapperEl.offsetHeight;\n        this.axis.x.isOverflowing =\n            contentElOffsetWidth !== 0 && contentElScrollWidth > contentElOffsetWidth;\n        this.axis.y.isOverflowing =\n            contentElScrollHeight > contentWrapperElOffsetHeight;\n        // Set isOverflowing to false if user explicitely set hidden overflow\n        this.axis.x.isOverflowing =\n            elOverflowX === 'hidden' ? false : this.axis.x.isOverflowing;\n        this.axis.y.isOverflowing =\n            elOverflowY === 'hidden' ? false : this.axis.y.isOverflowing;\n        this.axis.x.forceVisible =\n            this.options.forceVisible === 'x' || this.options.forceVisible === true;\n        this.axis.y.forceVisible =\n            this.options.forceVisible === 'y' || this.options.forceVisible === true;\n        this.hideNativeScrollbar();\n        // Set isOverflowing to false if scrollbar is not necessary (content is shorter than offset)\n        var offsetForXScrollbar = this.axis.x.isOverflowing\n            ? this.scrollbarWidth\n            : 0;\n        var offsetForYScrollbar = this.axis.y.isOverflowing\n            ? this.scrollbarWidth\n            : 0;\n        this.axis.x.isOverflowing =\n            this.axis.x.isOverflowing &&\n                contentElScrollWidth > contentWrapperElOffsetWidth - offsetForYScrollbar;\n        this.axis.y.isOverflowing =\n            this.axis.y.isOverflowing &&\n                contentElScrollHeight >\n                    contentWrapperElOffsetHeight - offsetForXScrollbar;\n        this.axis.x.scrollbar.size = this.getScrollbarSize('x');\n        this.axis.y.scrollbar.size = this.getScrollbarSize('y');\n        if (this.axis.x.scrollbar.el)\n            this.axis.x.scrollbar.el.style.width = \"\".concat(this.axis.x.scrollbar.size, \"px\");\n        if (this.axis.y.scrollbar.el)\n            this.axis.y.scrollbar.el.style.height = \"\".concat(this.axis.y.scrollbar.size, \"px\");\n        this.positionScrollbar('x');\n        this.positionScrollbar('y');\n        this.toggleTrackVisibility('x');\n        this.toggleTrackVisibility('y');\n    };\n    /**\n     * Calculate scrollbar size\n     */\n    SimpleBarCore.prototype.getScrollbarSize = function (axis) {\n        var _a, _b;\n        if (axis === void 0) { axis = 'y'; }\n        if (!this.axis[axis].isOverflowing || !this.contentEl) {\n            return 0;\n        }\n        var contentSize = this.contentEl[this.axis[axis].scrollSizeAttr];\n        var trackSize = (_b = (_a = this.axis[axis].track.el) === null || _a === void 0 ? void 0 : _a[this.axis[axis].offsetSizeAttr]) !== null && _b !== void 0 ? _b : 0;\n        var scrollbarRatio = trackSize / contentSize;\n        var scrollbarSize;\n        // Calculate new height/position of drag handle.\n        scrollbarSize = Math.max(~~(scrollbarRatio * trackSize), this.options.scrollbarMinSize);\n        if (this.options.scrollbarMaxSize) {\n            scrollbarSize = Math.min(scrollbarSize, this.options.scrollbarMaxSize);\n        }\n        return scrollbarSize;\n    };\n    SimpleBarCore.prototype.positionScrollbar = function (axis) {\n        var _a, _b, _c;\n        if (axis === void 0) { axis = 'y'; }\n        var scrollbar = this.axis[axis].scrollbar;\n        if (!this.axis[axis].isOverflowing ||\n            !this.contentWrapperEl ||\n            !scrollbar.el ||\n            !this.elStyles) {\n            return;\n        }\n        var contentSize = this.contentWrapperEl[this.axis[axis].scrollSizeAttr];\n        var trackSize = ((_a = this.axis[axis].track.el) === null || _a === void 0 ? void 0 : _a[this.axis[axis].offsetSizeAttr]) || 0;\n        var hostSize = parseInt(this.elStyles[this.axis[axis].sizeAttr], 10);\n        var scrollOffset = this.contentWrapperEl[this.axis[axis].scrollOffsetAttr];\n        scrollOffset =\n            axis === 'x' &&\n                this.isRtl &&\n                ((_b = SimpleBarCore.getRtlHelpers()) === null || _b === void 0 ? void 0 : _b.isScrollOriginAtZero)\n                ? -scrollOffset\n                : scrollOffset;\n        if (axis === 'x' && this.isRtl) {\n            scrollOffset = ((_c = SimpleBarCore.getRtlHelpers()) === null || _c === void 0 ? void 0 : _c.isScrollingToNegative)\n                ? scrollOffset\n                : -scrollOffset;\n        }\n        var scrollPourcent = scrollOffset / (contentSize - hostSize);\n        var handleOffset = ~~((trackSize - scrollbar.size) * scrollPourcent);\n        handleOffset =\n            axis === 'x' && this.isRtl\n                ? -handleOffset + (trackSize - scrollbar.size)\n                : handleOffset;\n        scrollbar.el.style.transform =\n            axis === 'x'\n                ? \"translate3d(\".concat(handleOffset, \"px, 0, 0)\")\n                : \"translate3d(0, \".concat(handleOffset, \"px, 0)\");\n    };\n    SimpleBarCore.prototype.toggleTrackVisibility = function (axis) {\n        if (axis === void 0) { axis = 'y'; }\n        var track = this.axis[axis].track.el;\n        var scrollbar = this.axis[axis].scrollbar.el;\n        if (!track || !scrollbar || !this.contentWrapperEl)\n            return;\n        if (this.axis[axis].isOverflowing || this.axis[axis].forceVisible) {\n            track.style.visibility = 'visible';\n            this.contentWrapperEl.style[this.axis[axis].overflowAttr] = 'scroll';\n            this.el.classList.add(\"\".concat(this.classNames.scrollable, \"-\").concat(axis));\n        }\n        else {\n            track.style.visibility = 'hidden';\n            this.contentWrapperEl.style[this.axis[axis].overflowAttr] = 'hidden';\n            this.el.classList.remove(\"\".concat(this.classNames.scrollable, \"-\").concat(axis));\n        }\n        // Even if forceVisible is enabled, scrollbar itself should be hidden\n        if (this.axis[axis].isOverflowing) {\n            scrollbar.style.display = 'block';\n        }\n        else {\n            scrollbar.style.display = 'none';\n        }\n    };\n    SimpleBarCore.prototype.showScrollbar = function (axis) {\n        if (axis === void 0) { axis = 'y'; }\n        if (this.axis[axis].isOverflowing && !this.axis[axis].scrollbar.isVisible) {\n            addClasses(this.axis[axis].scrollbar.el, this.classNames.visible);\n            this.axis[axis].scrollbar.isVisible = true;\n        }\n    };\n    SimpleBarCore.prototype.hideScrollbar = function (axis) {\n        if (axis === void 0) { axis = 'y'; }\n        if (this.isDragging)\n            return;\n        if (this.axis[axis].isOverflowing && this.axis[axis].scrollbar.isVisible) {\n            removeClasses(this.axis[axis].scrollbar.el, this.classNames.visible);\n            this.axis[axis].scrollbar.isVisible = false;\n        }\n    };\n    SimpleBarCore.prototype.hideNativeScrollbar = function () {\n        if (!this.offsetEl)\n            return;\n        this.offsetEl.style[this.isRtl ? 'left' : 'right'] =\n            this.axis.y.isOverflowing || this.axis.y.forceVisible\n                ? \"-\".concat(this.scrollbarWidth, \"px\")\n                : '0px';\n        this.offsetEl.style.bottom =\n            this.axis.x.isOverflowing || this.axis.x.forceVisible\n                ? \"-\".concat(this.scrollbarWidth, \"px\")\n                : '0px';\n    };\n    SimpleBarCore.prototype.onMouseMoveForAxis = function (axis) {\n        if (axis === void 0) { axis = 'y'; }\n        var currentAxis = this.axis[axis];\n        if (!currentAxis.track.el || !currentAxis.scrollbar.el)\n            return;\n        currentAxis.track.rect = currentAxis.track.el.getBoundingClientRect();\n        currentAxis.scrollbar.rect =\n            currentAxis.scrollbar.el.getBoundingClientRect();\n        if (this.isWithinBounds(currentAxis.track.rect)) {\n            this.showScrollbar(axis);\n            addClasses(currentAxis.track.el, this.classNames.hover);\n            if (this.isWithinBounds(currentAxis.scrollbar.rect)) {\n                addClasses(currentAxis.scrollbar.el, this.classNames.hover);\n            }\n            else {\n                removeClasses(currentAxis.scrollbar.el, this.classNames.hover);\n            }\n        }\n        else {\n            removeClasses(currentAxis.track.el, this.classNames.hover);\n            if (this.options.autoHide) {\n                this.hideScrollbar(axis);\n            }\n        }\n    };\n    SimpleBarCore.prototype.onMouseLeaveForAxis = function (axis) {\n        if (axis === void 0) { axis = 'y'; }\n        removeClasses(this.axis[axis].track.el, this.classNames.hover);\n        removeClasses(this.axis[axis].scrollbar.el, this.classNames.hover);\n        if (this.options.autoHide) {\n            this.hideScrollbar(axis);\n        }\n    };\n    /**\n     * on scrollbar handle drag movement starts\n     */\n    SimpleBarCore.prototype.onDragStart = function (e, axis) {\n        var _a;\n        if (axis === void 0) { axis = 'y'; }\n        this.isDragging = true;\n        var elDocument = getElementDocument(this.el);\n        var elWindow = getElementWindow(this.el);\n        var scrollbar = this.axis[axis].scrollbar;\n        // Measure how far the user's mouse is from the top of the scrollbar drag handle.\n        var eventOffset = axis === 'y' ? e.pageY : e.pageX;\n        this.axis[axis].dragOffset =\n            eventOffset - (((_a = scrollbar.rect) === null || _a === void 0 ? void 0 : _a[this.axis[axis].offsetAttr]) || 0);\n        this.draggedAxis = axis;\n        addClasses(this.el, this.classNames.dragging);\n        elDocument.addEventListener('mousemove', this.drag, true);\n        elDocument.addEventListener('mouseup', this.onEndDrag, true);\n        if (this.removePreventClickId === null) {\n            elDocument.addEventListener('click', this.preventClick, true);\n            elDocument.addEventListener('dblclick', this.preventClick, true);\n        }\n        else {\n            elWindow.clearTimeout(this.removePreventClickId);\n            this.removePreventClickId = null;\n        }\n    };\n    SimpleBarCore.prototype.onTrackClick = function (e, axis) {\n        var _this = this;\n        var _a, _b, _c, _d;\n        if (axis === void 0) { axis = 'y'; }\n        var currentAxis = this.axis[axis];\n        if (!this.options.clickOnTrack ||\n            !currentAxis.scrollbar.el ||\n            !this.contentWrapperEl)\n            return;\n        // Preventing the event's default to trigger click underneath\n        e.preventDefault();\n        var elWindow = getElementWindow(this.el);\n        this.axis[axis].scrollbar.rect =\n            currentAxis.scrollbar.el.getBoundingClientRect();\n        var scrollbar = this.axis[axis].scrollbar;\n        var scrollbarOffset = (_b = (_a = scrollbar.rect) === null || _a === void 0 ? void 0 : _a[this.axis[axis].offsetAttr]) !== null && _b !== void 0 ? _b : 0;\n        var hostSize = parseInt((_d = (_c = this.elStyles) === null || _c === void 0 ? void 0 : _c[this.axis[axis].sizeAttr]) !== null && _d !== void 0 ? _d : '0px', 10);\n        var scrolled = this.contentWrapperEl[this.axis[axis].scrollOffsetAttr];\n        var t = axis === 'y'\n            ? this.mouseY - scrollbarOffset\n            : this.mouseX - scrollbarOffset;\n        var dir = t < 0 ? -1 : 1;\n        var scrollSize = dir === -1 ? scrolled - hostSize : scrolled + hostSize;\n        var speed = 40;\n        var scrollTo = function () {\n            if (!_this.contentWrapperEl)\n                return;\n            if (dir === -1) {\n                if (scrolled > scrollSize) {\n                    scrolled -= speed;\n                    _this.contentWrapperEl[_this.axis[axis].scrollOffsetAttr] = scrolled;\n                    elWindow.requestAnimationFrame(scrollTo);\n                }\n            }\n            else {\n                if (scrolled < scrollSize) {\n                    scrolled += speed;\n                    _this.contentWrapperEl[_this.axis[axis].scrollOffsetAttr] = scrolled;\n                    elWindow.requestAnimationFrame(scrollTo);\n                }\n            }\n        };\n        scrollTo();\n    };\n    /**\n     * Getter for content element\n     */\n    SimpleBarCore.prototype.getContentElement = function () {\n        return this.contentEl;\n    };\n    /**\n     * Getter for original scrolling element\n     */\n    SimpleBarCore.prototype.getScrollElement = function () {\n        return this.contentWrapperEl;\n    };\n    SimpleBarCore.prototype.removeListeners = function () {\n        var elWindow = getElementWindow(this.el);\n        // Event listeners\n        this.el.removeEventListener('mouseenter', this.onMouseEnter);\n        this.el.removeEventListener('pointerdown', this.onPointerEvent, true);\n        this.el.removeEventListener('mousemove', this.onMouseMove);\n        this.el.removeEventListener('mouseleave', this.onMouseLeave);\n        if (this.contentWrapperEl) {\n            this.contentWrapperEl.removeEventListener('scroll', this.onScroll);\n        }\n        elWindow.removeEventListener('resize', this.onWindowResize);\n        if (this.mutationObserver) {\n            this.mutationObserver.disconnect();\n        }\n        if (this.resizeObserver) {\n            this.resizeObserver.disconnect();\n        }\n        // Cancel all debounced functions\n        this.onMouseMove.cancel();\n        this.onWindowResize.cancel();\n        this.onStopScrolling.cancel();\n        this.onMouseEntered.cancel();\n    };\n    /**\n     * Remove all listeners from DOM nodes\n     */\n    SimpleBarCore.prototype.unMount = function () {\n        this.removeListeners();\n    };\n    /**\n     * Check if mouse is within bounds\n     */\n    SimpleBarCore.prototype.isWithinBounds = function (bbox) {\n        return (this.mouseX >= bbox.left &&\n            this.mouseX <= bbox.left + bbox.width &&\n            this.mouseY >= bbox.top &&\n            this.mouseY <= bbox.top + bbox.height);\n    };\n    /**\n     * Find element children matches query\n     */\n    SimpleBarCore.prototype.findChild = function (el, query) {\n        var matches = el.matches ||\n            el.webkitMatchesSelector ||\n            el.mozMatchesSelector ||\n            el.msMatchesSelector;\n        return Array.prototype.filter.call(el.children, function (child) {\n            return matches.call(child, query);\n        })[0];\n    };\n    SimpleBarCore.rtlHelpers = null;\n    SimpleBarCore.defaultOptions = {\n        forceVisible: false,\n        clickOnTrack: true,\n        scrollbarMinSize: 25,\n        scrollbarMaxSize: 0,\n        ariaLabel: 'scrollable content',\n        tabIndex: 0,\n        classNames: {\n            contentEl: 'simplebar-content',\n            contentWrapper: 'simplebar-content-wrapper',\n            offset: 'simplebar-offset',\n            mask: 'simplebar-mask',\n            wrapper: 'simplebar-wrapper',\n            placeholder: 'simplebar-placeholder',\n            scrollbar: 'simplebar-scrollbar',\n            track: 'simplebar-track',\n            heightAutoObserverWrapperEl: 'simplebar-height-auto-observer-wrapper',\n            heightAutoObserverEl: 'simplebar-height-auto-observer',\n            visible: 'simplebar-visible',\n            horizontal: 'simplebar-horizontal',\n            vertical: 'simplebar-vertical',\n            hover: 'simplebar-hover',\n            dragging: 'simplebar-dragging',\n            scrolling: 'simplebar-scrolling',\n            scrollable: 'simplebar-scrollable',\n            mouseEntered: 'simplebar-mouse-entered'\n        },\n        scrollableNode: null,\n        contentNode: null,\n        autoHide: true\n    };\n    /**\n     * Static functions\n     */\n    SimpleBarCore.getOptions = getOptions;\n    SimpleBarCore.helpers = helpers;\n    return SimpleBarCore;\n}());\n\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/simplebar-core/dist/index.mjs\n");

/***/ })

};
;