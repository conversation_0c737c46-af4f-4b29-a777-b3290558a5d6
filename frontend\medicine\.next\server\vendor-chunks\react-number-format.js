"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-number-format";
exports.ids = ["vendor-chunks/react-number-format"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-number-format/dist/react-number-format.es.js":
/*!*************************************************************************!*\
  !*** ./node_modules/react-number-format/dist/react-number-format.es.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NumberFormatBase: () => (/* binding */ NumberFormatBase),\n/* harmony export */   NumericFormat: () => (/* binding */ NumericFormat),\n/* harmony export */   PatternFormat: () => (/* binding */ PatternFormat),\n/* harmony export */   getNumericCaretBoundary: () => (/* binding */ getCaretBoundary),\n/* harmony export */   getPatternCaretBoundary: () => (/* binding */ getCaretBoundary$1),\n/* harmony export */   numericFormatter: () => (/* binding */ format),\n/* harmony export */   patternFormatter: () => (/* binding */ format$1),\n/* harmony export */   removeNumericFormat: () => (/* binding */ removeFormatting),\n/* harmony export */   removePatternFormat: () => (/* binding */ removeFormatting$1),\n/* harmony export */   useNumericFormat: () => (/* binding */ useNumericFormat),\n/* harmony export */   usePatternFormat: () => (/* binding */ usePatternFormat)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/**\n * react-number-format - 5.4.4\n * Author : Sudhanshu Yadav\n * Copyright (c) 2016, 2025 to Sudhanshu Yadav, released under the MIT license.\n * https://github.com/s-yadav/react-number-format\n */\n\n\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n\r\nfunction __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) { if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        { t[p] = s[p]; } }\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        { for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                { t[p[i]] = s[p[i]]; }\r\n        } }\r\n    return t;\r\n}\n\nvar SourceType;\n(function (SourceType) {\n    SourceType[\"event\"] = \"event\";\n    SourceType[\"props\"] = \"prop\";\n})(SourceType || (SourceType = {}));\n\n// basic noop function\nfunction noop() { }\nfunction memoizeOnce(cb) {\n    var lastArgs;\n    var lastValue = undefined;\n    return function () {\n        var args = [], len = arguments.length;\n        while ( len-- ) args[ len ] = arguments[ len ];\n\n        if (lastArgs &&\n            args.length === lastArgs.length &&\n            args.every(function (value, index) { return value === lastArgs[index]; })) {\n            return lastValue;\n        }\n        lastArgs = args;\n        lastValue = cb.apply(void 0, args);\n        return lastValue;\n    };\n}\nfunction charIsNumber(char) {\n    return !!(char || '').match(/\\d/);\n}\nfunction isNil(val) {\n    return val === null || val === undefined;\n}\nfunction isNanValue(val) {\n    return typeof val === 'number' && isNaN(val);\n}\nfunction isNotValidValue(val) {\n    return isNil(val) || isNanValue(val) || (typeof val === 'number' && !isFinite(val));\n}\nfunction escapeRegExp(str) {\n    return str.replace(/[-[\\]/{}()*+?.\\\\^$|]/g, '\\\\$&');\n}\nfunction getThousandsGroupRegex(thousandsGroupStyle) {\n    switch (thousandsGroupStyle) {\n        case 'lakh':\n            return /(\\d+?)(?=(\\d\\d)+(\\d)(?!\\d))(\\.\\d+)?/g;\n        case 'wan':\n            return /(\\d)(?=(\\d{4})+(?!\\d))/g;\n        case 'thousand':\n        default:\n            return /(\\d)(?=(\\d{3})+(?!\\d))/g;\n    }\n}\nfunction applyThousandSeparator(str, thousandSeparator, thousandsGroupStyle) {\n    var thousandsGroupRegex = getThousandsGroupRegex(thousandsGroupStyle);\n    var index = str.search(/[1-9]/);\n    index = index === -1 ? str.length : index;\n    return (str.substring(0, index) +\n        str.substring(index, str.length).replace(thousandsGroupRegex, '$1' + thousandSeparator));\n}\nfunction usePersistentCallback(cb) {\n    var callbackRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(cb);\n    // keep the callback ref upto date\n    callbackRef.current = cb;\n    /**\n     * initialize a persistent callback which never changes\n     * through out the component lifecycle\n     */\n    var persistentCbRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(function () {\n        var args = [], len = arguments.length;\n        while ( len-- ) args[ len ] = arguments[ len ];\n\n        return callbackRef.current.apply(callbackRef, args);\n    });\n    return persistentCbRef.current;\n}\n//spilt a float number into different parts beforeDecimal, afterDecimal, and negation\nfunction splitDecimal(numStr, allowNegative) {\n    if ( allowNegative === void 0 ) allowNegative = true;\n\n    var hasNegation = numStr[0] === '-';\n    var addNegation = hasNegation && allowNegative;\n    numStr = numStr.replace('-', '');\n    var parts = numStr.split('.');\n    var beforeDecimal = parts[0];\n    var afterDecimal = parts[1] || '';\n    return {\n        beforeDecimal: beforeDecimal,\n        afterDecimal: afterDecimal,\n        hasNegation: hasNegation,\n        addNegation: addNegation,\n    };\n}\nfunction fixLeadingZero(numStr) {\n    if (!numStr)\n        { return numStr; }\n    var isNegative = numStr[0] === '-';\n    if (isNegative)\n        { numStr = numStr.substring(1, numStr.length); }\n    var parts = numStr.split('.');\n    var beforeDecimal = parts[0].replace(/^0+/, '') || '0';\n    var afterDecimal = parts[1] || '';\n    return (\"\" + (isNegative ? '-' : '') + beforeDecimal + (afterDecimal ? (\".\" + afterDecimal) : ''));\n}\n/**\n * limit decimal numbers to given scale\n * Not used .fixedTo because that will break with big numbers\n */\nfunction limitToScale(numStr, scale, fixedDecimalScale) {\n    var str = '';\n    var filler = fixedDecimalScale ? '0' : '';\n    for (var i = 0; i <= scale - 1; i++) {\n        str += numStr[i] || filler;\n    }\n    return str;\n}\nfunction repeat(str, count) {\n    return Array(count + 1).join(str);\n}\nfunction toNumericString(num) {\n    var _num = num + ''; // typecast number to string\n    // store the sign and remove it from the number.\n    var sign = _num[0] === '-' ? '-' : '';\n    if (sign)\n        { _num = _num.substring(1); }\n    // split the number into cofficient and exponent\n    var ref = _num.split(/[eE]/g);\n    var coefficient = ref[0];\n    var exponent = ref[1];\n    // covert exponent to number;\n    exponent = Number(exponent);\n    // if there is no exponent part or its 0, return the coffiecient with sign\n    if (!exponent)\n        { return sign + coefficient; }\n    coefficient = coefficient.replace('.', '');\n    /**\n     * for scientific notation the current decimal index will be after first number (index 0)\n     * So effective decimal index will always be 1 + exponent value\n     */\n    var decimalIndex = 1 + exponent;\n    var coffiecientLn = coefficient.length;\n    if (decimalIndex < 0) {\n        // if decimal index is less then 0 add preceding 0s\n        // add 1 as join will have\n        coefficient = '0.' + repeat('0', Math.abs(decimalIndex)) + coefficient;\n    }\n    else if (decimalIndex >= coffiecientLn) {\n        // if decimal index is less then 0 add leading 0s\n        coefficient = coefficient + repeat('0', decimalIndex - coffiecientLn);\n    }\n    else {\n        // else add decimal point at proper index\n        coefficient =\n            (coefficient.substring(0, decimalIndex) || '0') + '.' + coefficient.substring(decimalIndex);\n    }\n    return sign + coefficient;\n}\n/**\n * This method is required to round prop value to given scale.\n * Not used .round or .fixedTo because that will break with big numbers\n */\nfunction roundToPrecision(numStr, scale, fixedDecimalScale) {\n    //if number is empty don't do anything return empty string\n    if (['', '-'].indexOf(numStr) !== -1)\n        { return numStr; }\n    var shouldHaveDecimalSeparator = (numStr.indexOf('.') !== -1 || fixedDecimalScale) && scale;\n    var ref = splitDecimal(numStr);\n    var beforeDecimal = ref.beforeDecimal;\n    var afterDecimal = ref.afterDecimal;\n    var hasNegation = ref.hasNegation;\n    var floatValue = parseFloat((\"0.\" + (afterDecimal || '0')));\n    var floatValueStr = afterDecimal.length <= scale ? (\"0.\" + afterDecimal) : floatValue.toFixed(scale);\n    var roundedDecimalParts = floatValueStr.split('.');\n    var intPart = beforeDecimal;\n    // if we have cary over from rounding decimal part, add that on before decimal\n    if (beforeDecimal && Number(roundedDecimalParts[0])) {\n        intPart = beforeDecimal\n            .split('')\n            .reverse()\n            .reduce(function (roundedStr, current, idx) {\n            if (roundedStr.length > idx) {\n                return ((Number(roundedStr[0]) + Number(current)).toString() +\n                    roundedStr.substring(1, roundedStr.length));\n            }\n            return current + roundedStr;\n        }, roundedDecimalParts[0]);\n    }\n    var decimalPart = limitToScale(roundedDecimalParts[1] || '', scale, fixedDecimalScale);\n    var negation = hasNegation ? '-' : '';\n    var decimalSeparator = shouldHaveDecimalSeparator ? '.' : '';\n    return (\"\" + negation + intPart + decimalSeparator + decimalPart);\n}\n/** set the caret positon in an input field **/\nfunction setCaretPosition(el, caretPos) {\n    el.value = el.value;\n    // ^ this is used to not only get 'focus', but\n    // to make sure we don't have it everything -selected-\n    // (it causes an issue in chrome, and having it doesn't hurt any other browser)\n    if (el !== null) {\n        /* @ts-ignore */\n        if (el.createTextRange) {\n            /* @ts-ignore */\n            var range = el.createTextRange();\n            range.move('character', caretPos);\n            range.select();\n            return true;\n        }\n        // (el.selectionStart === 0 added for Firefox bug)\n        if (el.selectionStart || el.selectionStart === 0) {\n            el.focus();\n            el.setSelectionRange(caretPos, caretPos);\n            return true;\n        }\n        // fail city, fortunately this never happens (as far as I've tested) :)\n        el.focus();\n        return false;\n    }\n}\n/**\n * TODO: remove dependency of findChangeRange, findChangedRangeFromCaretPositions is better way to find what is changed\n * currently this is mostly required by test and isCharacterSame util\n * Given previous value and newValue it returns the index\n * start - end to which values have changed.\n * This function makes assumption about only consecutive\n * characters are changed which is correct assumption for caret input.\n */\nvar findChangeRange = memoizeOnce(function (prevValue, newValue) {\n    var i = 0, j = 0;\n    var prevLength = prevValue.length;\n    var newLength = newValue.length;\n    while (prevValue[i] === newValue[i] && i < prevLength)\n        { i++; }\n    //check what has been changed from last\n    while (prevValue[prevLength - 1 - j] === newValue[newLength - 1 - j] &&\n        newLength - j > i &&\n        prevLength - j > i) {\n        j++;\n    }\n    return {\n        from: { start: i, end: prevLength - j },\n        to: { start: i, end: newLength - j },\n    };\n});\nvar findChangedRangeFromCaretPositions = function (lastCaretPositions, currentCaretPosition) {\n    var startPosition = Math.min(lastCaretPositions.selectionStart, currentCaretPosition);\n    return {\n        from: { start: startPosition, end: lastCaretPositions.selectionEnd },\n        to: { start: startPosition, end: currentCaretPosition },\n    };\n};\n/*\n  Returns a number whose value is limited to the given range\n*/\nfunction clamp(num, min, max) {\n    return Math.min(Math.max(num, min), max);\n}\nfunction geInputCaretPosition(el) {\n    /*Max of selectionStart and selectionEnd is taken for the patch of pixel and other mobile device caret bug*/\n    return Math.max(el.selectionStart, el.selectionEnd);\n}\nfunction addInputMode() {\n    return (typeof navigator !== 'undefined' &&\n        !(navigator.platform && /iPhone|iPod/.test(navigator.platform)));\n}\nfunction getDefaultChangeMeta(value) {\n    return {\n        from: {\n            start: 0,\n            end: 0,\n        },\n        to: {\n            start: 0,\n            end: value.length,\n        },\n        lastValue: '',\n    };\n}\nfunction getMaskAtIndex(mask, index) {\n    if ( mask === void 0 ) mask = ' ';\n\n    if (typeof mask === 'string') {\n        return mask;\n    }\n    return mask[index] || ' ';\n}\nfunction defaultIsCharacterSame(ref) {\n    var currentValue = ref.currentValue;\n    var formattedValue = ref.formattedValue;\n    var currentValueIndex = ref.currentValueIndex;\n    var formattedValueIndex = ref.formattedValueIndex;\n\n    return currentValue[currentValueIndex] === formattedValue[formattedValueIndex];\n}\nfunction getCaretPosition(newFormattedValue, lastFormattedValue, curValue, curCaretPos, boundary, isValidInputCharacter, \n/**\n * format function can change the character, the caret engine relies on mapping old value and new value\n * In such case if character is changed, parent can tell which chars are equivalent\n * Some example, all allowedDecimalCharacters are updated to decimalCharacters, 2nd case if user is coverting\n * number to different numeric system.\n */\nisCharacterSame) {\n    if ( isCharacterSame === void 0 ) isCharacterSame = defaultIsCharacterSame;\n\n    /**\n     * if something got inserted on empty value, add the formatted character before the current value,\n     * This is to avoid the case where typed character is present on format characters\n     */\n    var firstAllowedPosition = boundary.findIndex(function (b) { return b; });\n    var prefixFormat = newFormattedValue.slice(0, firstAllowedPosition);\n    if (!lastFormattedValue && !curValue.startsWith(prefixFormat)) {\n        lastFormattedValue = prefixFormat;\n        curValue = prefixFormat + curValue;\n        curCaretPos = curCaretPos + prefixFormat.length;\n    }\n    var curValLn = curValue.length;\n    var formattedValueLn = newFormattedValue.length;\n    // create index map\n    var addedIndexMap = {};\n    var indexMap = new Array(curValLn);\n    for (var i = 0; i < curValLn; i++) {\n        indexMap[i] = -1;\n        for (var j = 0, jLn = formattedValueLn; j < jLn; j++) {\n            var isCharSame = isCharacterSame({\n                currentValue: curValue,\n                lastValue: lastFormattedValue,\n                formattedValue: newFormattedValue,\n                currentValueIndex: i,\n                formattedValueIndex: j,\n            });\n            if (isCharSame && addedIndexMap[j] !== true) {\n                indexMap[i] = j;\n                addedIndexMap[j] = true;\n                break;\n            }\n        }\n    }\n    /**\n     * For current caret position find closest characters (left and right side)\n     * which are properly mapped to formatted value.\n     * The idea is that the new caret position will exist always in the boundary of\n     * that mapped index\n     */\n    var pos = curCaretPos;\n    while (pos < curValLn && (indexMap[pos] === -1 || !isValidInputCharacter(curValue[pos]))) {\n        pos++;\n    }\n    // if the caret position is on last keep the endIndex as last for formatted value\n    var endIndex = pos === curValLn || indexMap[pos] === -1 ? formattedValueLn : indexMap[pos];\n    pos = curCaretPos - 1;\n    while (pos > 0 && indexMap[pos] === -1)\n        { pos--; }\n    var startIndex = pos === -1 || indexMap[pos] === -1 ? 0 : indexMap[pos] + 1;\n    /**\n     * case where a char is added on suffix and removed from middle, example 2sq345 becoming $2,345 sq\n     * there is still a mapping but the order of start index and end index is changed\n     */\n    if (startIndex > endIndex)\n        { return endIndex; }\n    /**\n     * given the current caret position if it closer to startIndex\n     * keep the new caret position on start index or keep it closer to endIndex\n     */\n    return curCaretPos - startIndex < endIndex - curCaretPos ? startIndex : endIndex;\n}\n/* This keeps the caret within typing area so people can't type in between prefix or suffix or format characters */\nfunction getCaretPosInBoundary(value, caretPos, boundary, direction) {\n    var valLn = value.length;\n    // clamp caret position to [0, value.length]\n    caretPos = clamp(caretPos, 0, valLn);\n    if (direction === 'left') {\n        while (caretPos >= 0 && !boundary[caretPos])\n            { caretPos--; }\n        // if we don't find any suitable caret position on left, set it on first allowed position\n        if (caretPos === -1)\n            { caretPos = boundary.indexOf(true); }\n    }\n    else {\n        while (caretPos <= valLn && !boundary[caretPos])\n            { caretPos++; }\n        // if we don't find any suitable caret position on right, set it on last allowed position\n        if (caretPos > valLn)\n            { caretPos = boundary.lastIndexOf(true); }\n    }\n    // if we still don't find caret position, set it at the end of value\n    if (caretPos === -1)\n        { caretPos = valLn; }\n    return caretPos;\n}\nfunction caretUnknownFormatBoundary(formattedValue) {\n    var boundaryAry = Array.from({ length: formattedValue.length + 1 }).map(function () { return true; });\n    for (var i = 0, ln = boundaryAry.length; i < ln; i++) {\n        // consider caret to be in boundary if it is before or after numeric value\n        boundaryAry[i] = Boolean(charIsNumber(formattedValue[i]) || charIsNumber(formattedValue[i - 1]));\n    }\n    return boundaryAry;\n}\nfunction useInternalValues(value, defaultValue, valueIsNumericString, format, removeFormatting, onValueChange) {\n    if ( onValueChange === void 0 ) onValueChange = noop;\n\n    var getValues = usePersistentCallback(function (value, valueIsNumericString) {\n        var formattedValue, numAsString;\n        if (isNotValidValue(value)) {\n            numAsString = '';\n            formattedValue = '';\n        }\n        else if (typeof value === 'number' || valueIsNumericString) {\n            numAsString = typeof value === 'number' ? toNumericString(value) : value;\n            formattedValue = format(numAsString);\n        }\n        else {\n            numAsString = removeFormatting(value, undefined);\n            formattedValue = format(numAsString);\n        }\n        return { formattedValue: formattedValue, numAsString: numAsString };\n    });\n    var ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(function () {\n        return getValues(isNil(value) ? defaultValue : value, valueIsNumericString);\n    });\n    var values = ref[0];\n    var setValues = ref[1];\n    var _onValueChange = function (newValues, sourceInfo) {\n        if (newValues.formattedValue !== values.formattedValue) {\n            setValues({\n                formattedValue: newValues.formattedValue,\n                numAsString: newValues.value,\n            });\n        }\n        // call parent on value change if only if formatted value is changed\n        onValueChange(newValues, sourceInfo);\n    };\n    // if value is switch from controlled to uncontrolled, use the internal state's value to format with new props\n    var _value = value;\n    var _valueIsNumericString = valueIsNumericString;\n    if (isNil(value)) {\n        _value = values.numAsString;\n        _valueIsNumericString = true;\n    }\n    var newValues = getValues(_value, _valueIsNumericString);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function () {\n        setValues(newValues);\n    }, [newValues.formattedValue]);\n    return [values, _onValueChange];\n}\n\nfunction defaultRemoveFormatting(value) {\n    return value.replace(/[^0-9]/g, '');\n}\nfunction defaultFormat(value) {\n    return value;\n}\nfunction NumberFormatBase(props) {\n    var type = props.type; if ( type === void 0 ) type = 'text';\n    var displayType = props.displayType; if ( displayType === void 0 ) displayType = 'input';\n    var customInput = props.customInput;\n    var renderText = props.renderText;\n    var getInputRef = props.getInputRef;\n    var format = props.format; if ( format === void 0 ) format = defaultFormat;\n    var removeFormatting = props.removeFormatting; if ( removeFormatting === void 0 ) removeFormatting = defaultRemoveFormatting;\n    var defaultValue = props.defaultValue;\n    var valueIsNumericString = props.valueIsNumericString;\n    var onValueChange = props.onValueChange;\n    var isAllowed = props.isAllowed;\n    var onChange = props.onChange; if ( onChange === void 0 ) onChange = noop;\n    var onKeyDown = props.onKeyDown; if ( onKeyDown === void 0 ) onKeyDown = noop;\n    var onMouseUp = props.onMouseUp; if ( onMouseUp === void 0 ) onMouseUp = noop;\n    var onFocus = props.onFocus; if ( onFocus === void 0 ) onFocus = noop;\n    var onBlur = props.onBlur; if ( onBlur === void 0 ) onBlur = noop;\n    var propValue = props.value;\n    var getCaretBoundary = props.getCaretBoundary; if ( getCaretBoundary === void 0 ) getCaretBoundary = caretUnknownFormatBoundary;\n    var isValidInputCharacter = props.isValidInputCharacter; if ( isValidInputCharacter === void 0 ) isValidInputCharacter = charIsNumber;\n    var isCharacterSame = props.isCharacterSame;\n    var otherProps = __rest(props, [\"type\", \"displayType\", \"customInput\", \"renderText\", \"getInputRef\", \"format\", \"removeFormatting\", \"defaultValue\", \"valueIsNumericString\", \"onValueChange\", \"isAllowed\", \"onChange\", \"onKeyDown\", \"onMouseUp\", \"onFocus\", \"onBlur\", \"value\", \"getCaretBoundary\", \"isValidInputCharacter\", \"isCharacterSame\"]);\n    var ref = useInternalValues(propValue, defaultValue, Boolean(valueIsNumericString), format, removeFormatting, onValueChange);\n    var ref_0 = ref[0];\n    var formattedValue = ref_0.formattedValue;\n    var numAsString = ref_0.numAsString;\n    var onFormattedValueChange = ref[1];\n    var caretPositionBeforeChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n    var lastUpdatedValue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({ formattedValue: formattedValue, numAsString: numAsString });\n    var _onValueChange = function (values, source) {\n        lastUpdatedValue.current = { formattedValue: values.formattedValue, numAsString: values.value };\n        onFormattedValueChange(values, source);\n    };\n    var ref$1 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    var mounted = ref$1[0];\n    var setMounted = ref$1[1];\n    var focusedElm = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    var timeout = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n        setCaretTimeout: null,\n        focusTimeout: null,\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {\n        setMounted(true);\n        return function () {\n            clearTimeout(timeout.current.setCaretTimeout);\n            clearTimeout(timeout.current.focusTimeout);\n        };\n    }, []);\n    var _format = format;\n    var getValueObject = function (formattedValue, numAsString) {\n        var floatValue = parseFloat(numAsString);\n        return {\n            formattedValue: formattedValue,\n            value: numAsString,\n            floatValue: isNaN(floatValue) ? undefined : floatValue,\n        };\n    };\n    var setPatchedCaretPosition = function (el, caretPos, currentValue) {\n        // don't reset the caret position when the whole input content is selected\n        if (el.selectionStart === 0 && el.selectionEnd === el.value.length)\n            { return; }\n        /* setting caret position within timeout of 0ms is required for mobile chrome,\n        otherwise browser resets the caret position after we set it\n        We are also setting it without timeout so that in normal browser we don't see the flickering */\n        setCaretPosition(el, caretPos);\n        timeout.current.setCaretTimeout = setTimeout(function () {\n            if (el.value === currentValue && el.selectionStart !== caretPos) {\n                setCaretPosition(el, caretPos);\n            }\n        }, 0);\n    };\n    /* This keeps the caret within typing area so people can't type in between prefix or suffix */\n    var correctCaretPosition = function (value, caretPos, direction) {\n        return getCaretPosInBoundary(value, caretPos, getCaretBoundary(value), direction);\n    };\n    var getNewCaretPosition = function (inputValue, newFormattedValue, caretPos) {\n        var caretBoundary = getCaretBoundary(newFormattedValue);\n        var updatedCaretPos = getCaretPosition(newFormattedValue, formattedValue, inputValue, caretPos, caretBoundary, isValidInputCharacter, isCharacterSame);\n        //correct caret position if its outside of editable area\n        updatedCaretPos = getCaretPosInBoundary(newFormattedValue, updatedCaretPos, caretBoundary);\n        return updatedCaretPos;\n    };\n    var updateValueAndCaretPosition = function (params) {\n        var newFormattedValue = params.formattedValue; if ( newFormattedValue === void 0 ) newFormattedValue = '';\n        var input = params.input;\n        var source = params.source;\n        var event = params.event;\n        var numAsString = params.numAsString;\n        var caretPos;\n        if (input) {\n            var inputValue = params.inputValue || input.value;\n            var currentCaretPosition = geInputCaretPosition(input);\n            /**\n             * set the value imperatively, this is required for IE fix\n             * This is also required as if new caret position is beyond the previous value.\n             * Caret position will not be set correctly\n             */\n            input.value = newFormattedValue;\n            //get the caret position\n            caretPos = getNewCaretPosition(inputValue, newFormattedValue, currentCaretPosition);\n            //set caret position imperatively\n            if (caretPos !== undefined) {\n                setPatchedCaretPosition(input, caretPos, newFormattedValue);\n            }\n        }\n        if (newFormattedValue !== formattedValue) {\n            // trigger onValueChange synchronously, so parent is updated along with the number format. Fix for #277, #287\n            _onValueChange(getValueObject(newFormattedValue, numAsString), { event: event, source: source });\n        }\n    };\n    /**\n     * if the formatted value is not synced to parent, or if the formatted value is different from last synced value sync it\n     * if the formatting props is removed, in which case last formatted value will be different from the numeric string value\n     * in such case we need to inform the parent.\n     */\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {\n        var ref = lastUpdatedValue.current;\n        var lastFormattedValue = ref.formattedValue;\n        var lastNumAsString = ref.numAsString;\n        if (formattedValue !== lastFormattedValue || numAsString !== lastNumAsString) {\n            _onValueChange(getValueObject(formattedValue, numAsString), {\n                event: undefined,\n                source: SourceType.props,\n            });\n        }\n    }, [formattedValue, numAsString]);\n    // also if formatted value is changed from the props, we need to update the caret position\n    // keep the last caret position if element is focused\n    var currentCaretPosition = focusedElm.current\n        ? geInputCaretPosition(focusedElm.current)\n        : undefined;\n    // needed to prevent warning with useLayoutEffect on server\n    var useIsomorphicLayoutEffect = typeof window !== 'undefined' ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\n    useIsomorphicLayoutEffect(function () {\n        var input = focusedElm.current;\n        if (formattedValue !== lastUpdatedValue.current.formattedValue && input) {\n            var caretPos = getNewCaretPosition(lastUpdatedValue.current.formattedValue, formattedValue, currentCaretPosition);\n            /**\n             * set the value imperatively, as we set the caret position as well imperatively.\n             * This is to keep value and caret position in sync\n             */\n            input.value = formattedValue;\n            setPatchedCaretPosition(input, caretPos, formattedValue);\n        }\n    }, [formattedValue]);\n    var formatInputValue = function (inputValue, event, source) {\n        var input = event.target;\n        var changeRange = caretPositionBeforeChange.current\n            ? findChangedRangeFromCaretPositions(caretPositionBeforeChange.current, input.selectionEnd)\n            : findChangeRange(formattedValue, inputValue);\n        var changeMeta = Object.assign(Object.assign({}, changeRange), { lastValue: formattedValue });\n        var _numAsString = removeFormatting(inputValue, changeMeta);\n        var _formattedValue = _format(_numAsString);\n        // formatting can remove some of the number chars, so we need to fine number string again\n        _numAsString = removeFormatting(_formattedValue, undefined);\n        if (isAllowed && !isAllowed(getValueObject(_formattedValue, _numAsString))) {\n            //reset the caret position\n            var input$1 = event.target;\n            var currentCaretPosition = geInputCaretPosition(input$1);\n            var caretPos = getNewCaretPosition(inputValue, formattedValue, currentCaretPosition);\n            input$1.value = formattedValue;\n            setPatchedCaretPosition(input$1, caretPos, formattedValue);\n            return false;\n        }\n        updateValueAndCaretPosition({\n            formattedValue: _formattedValue,\n            numAsString: _numAsString,\n            inputValue: inputValue,\n            event: event,\n            source: source,\n            input: event.target,\n        });\n        return true;\n    };\n    var setCaretPositionInfoBeforeChange = function (el, endOffset) {\n        if ( endOffset === void 0 ) endOffset = 0;\n\n        var selectionStart = el.selectionStart;\n        var selectionEnd = el.selectionEnd;\n        caretPositionBeforeChange.current = { selectionStart: selectionStart, selectionEnd: selectionEnd + endOffset };\n    };\n    var _onChange = function (e) {\n        var el = e.target;\n        var inputValue = el.value;\n        var changed = formatInputValue(inputValue, e, SourceType.event);\n        if (changed)\n            { onChange(e); }\n        // reset the position, as we have already handled the caret position\n        caretPositionBeforeChange.current = undefined;\n    };\n    var _onKeyDown = function (e) {\n        var el = e.target;\n        var key = e.key;\n        var selectionStart = el.selectionStart;\n        var selectionEnd = el.selectionEnd;\n        var value = el.value; if ( value === void 0 ) value = '';\n        var expectedCaretPosition;\n        //Handle backspace and delete against non numerical/decimal characters or arrow keys\n        if (key === 'ArrowLeft' || key === 'Backspace') {\n            expectedCaretPosition = Math.max(selectionStart - 1, 0);\n        }\n        else if (key === 'ArrowRight') {\n            expectedCaretPosition = Math.min(selectionStart + 1, value.length);\n        }\n        else if (key === 'Delete') {\n            expectedCaretPosition = selectionStart;\n        }\n        // if key is delete and text is not selected keep the end offset to 1, as it deletes one character\n        // this is required as selection is not changed on delete case, which changes the change range calculation\n        var endOffset = 0;\n        if (key === 'Delete' && selectionStart === selectionEnd) {\n            endOffset = 1;\n        }\n        var isArrowKey = key === 'ArrowLeft' || key === 'ArrowRight';\n        //if expectedCaretPosition is not set it means we don't want to Handle keyDown\n        // also if multiple characters are selected don't handle\n        if (expectedCaretPosition === undefined || (selectionStart !== selectionEnd && !isArrowKey)) {\n            onKeyDown(e);\n            // keep information of what was the caret position before keyDown\n            // set it after onKeyDown, in case parent updates the position manually\n            setCaretPositionInfoBeforeChange(el, endOffset);\n            return;\n        }\n        var newCaretPosition = expectedCaretPosition;\n        if (isArrowKey) {\n            var direction = key === 'ArrowLeft' ? 'left' : 'right';\n            newCaretPosition = correctCaretPosition(value, expectedCaretPosition, direction);\n            // arrow left or right only moves the caret, so no need to handle the event, if we are handling it manually\n            if (newCaretPosition !== expectedCaretPosition) {\n                e.preventDefault();\n            }\n        }\n        else if (key === 'Delete' && !isValidInputCharacter(value[expectedCaretPosition])) {\n            // in case of delete go to closest caret boundary on the right side\n            newCaretPosition = correctCaretPosition(value, expectedCaretPosition, 'right');\n        }\n        else if (key === 'Backspace' && !isValidInputCharacter(value[expectedCaretPosition])) {\n            // in case of backspace go to closest caret boundary on the left side\n            newCaretPosition = correctCaretPosition(value, expectedCaretPosition, 'left');\n        }\n        if (newCaretPosition !== expectedCaretPosition) {\n            setPatchedCaretPosition(el, newCaretPosition, value);\n        }\n        onKeyDown(e);\n        setCaretPositionInfoBeforeChange(el, endOffset);\n    };\n    /** required to handle the caret position when click anywhere within the input **/\n    var _onMouseUp = function (e) {\n        var el = e.target;\n        /**\n         * NOTE: we have to give default value for value as in case when custom input is provided\n         * value can come as undefined when nothing is provided on value prop.\n         */\n        var correctCaretPositionIfRequired = function () {\n            var selectionStart = el.selectionStart;\n            var selectionEnd = el.selectionEnd;\n            var value = el.value; if ( value === void 0 ) value = '';\n            if (selectionStart === selectionEnd) {\n                var caretPosition = correctCaretPosition(value, selectionStart);\n                if (caretPosition !== selectionStart) {\n                    setPatchedCaretPosition(el, caretPosition, value);\n                }\n            }\n        };\n        correctCaretPositionIfRequired();\n        // try to correct after selection has updated by browser\n        // this case is required when user clicks on some position while a text is selected on input\n        requestAnimationFrame(function () {\n            correctCaretPositionIfRequired();\n        });\n        onMouseUp(e);\n        setCaretPositionInfoBeforeChange(el);\n    };\n    var _onFocus = function (e) {\n        // Workaround Chrome and Safari bug https://bugs.chromium.org/p/chromium/issues/detail?id=779328\n        // (onFocus event target selectionStart is always 0 before setTimeout)\n        if (e.persist)\n            { e.persist(); }\n        var el = e.target;\n        var currentTarget = e.currentTarget;\n        focusedElm.current = el;\n        timeout.current.focusTimeout = setTimeout(function () {\n            var selectionStart = el.selectionStart;\n            var selectionEnd = el.selectionEnd;\n            var value = el.value; if ( value === void 0 ) value = '';\n            var caretPosition = correctCaretPosition(value, selectionStart);\n            //setPatchedCaretPosition only when everything is not selected on focus (while tabbing into the field)\n            if (caretPosition !== selectionStart &&\n                !(selectionStart === 0 && selectionEnd === value.length)) {\n                setPatchedCaretPosition(el, caretPosition, value);\n            }\n            onFocus(Object.assign(Object.assign({}, e), { currentTarget: currentTarget }));\n        }, 0);\n    };\n    var _onBlur = function (e) {\n        focusedElm.current = null;\n        clearTimeout(timeout.current.focusTimeout);\n        clearTimeout(timeout.current.setCaretTimeout);\n        onBlur(e);\n    };\n    // add input mode on element based on format prop and device once the component is mounted\n    var inputMode = mounted && addInputMode() ? 'numeric' : undefined;\n    var inputProps = Object.assign({ inputMode: inputMode }, otherProps, {\n        type: type,\n        value: formattedValue,\n        onChange: _onChange,\n        onKeyDown: _onKeyDown,\n        onMouseUp: _onMouseUp,\n        onFocus: _onFocus,\n        onBlur: _onBlur,\n    });\n    if (displayType === 'text') {\n        return renderText ? (react__WEBPACK_IMPORTED_MODULE_0___default().createElement((react__WEBPACK_IMPORTED_MODULE_0___default().Fragment), null, renderText(formattedValue, otherProps) || null)) : (react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"span\", Object.assign({}, otherProps, { ref: getInputRef }), formattedValue));\n    }\n    else if (customInput) {\n        var CustomInput = customInput;\n        /* @ts-ignore */\n        return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(CustomInput, Object.assign({}, inputProps, { ref: getInputRef }));\n    }\n    return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"input\", Object.assign({}, inputProps, { ref: getInputRef }));\n}\n\nfunction format(numStr, props) {\n    var decimalScale = props.decimalScale;\n    var fixedDecimalScale = props.fixedDecimalScale;\n    var prefix = props.prefix; if ( prefix === void 0 ) prefix = '';\n    var suffix = props.suffix; if ( suffix === void 0 ) suffix = '';\n    var allowNegative = props.allowNegative;\n    var thousandsGroupStyle = props.thousandsGroupStyle; if ( thousandsGroupStyle === void 0 ) thousandsGroupStyle = 'thousand';\n    // don't apply formatting on empty string or '-'\n    if (numStr === '' || numStr === '-') {\n        return numStr;\n    }\n    var ref = getSeparators(props);\n    var thousandSeparator = ref.thousandSeparator;\n    var decimalSeparator = ref.decimalSeparator;\n    /**\n     * Keep the decimal separator\n     * when decimalScale is not defined or non zero and the numStr has decimal in it\n     * Or if decimalScale is > 0 and fixeDecimalScale is true (even if numStr has no decimal)\n     */\n    var hasDecimalSeparator = (decimalScale !== 0 && numStr.indexOf('.') !== -1) || (decimalScale && fixedDecimalScale);\n    var ref$1 = splitDecimal(numStr, allowNegative);\n    var beforeDecimal = ref$1.beforeDecimal;\n    var afterDecimal = ref$1.afterDecimal;\n    var addNegation = ref$1.addNegation; // eslint-disable-line prefer-const\n    //apply decimal precision if its defined\n    if (decimalScale !== undefined) {\n        afterDecimal = limitToScale(afterDecimal, decimalScale, !!fixedDecimalScale);\n    }\n    if (thousandSeparator) {\n        beforeDecimal = applyThousandSeparator(beforeDecimal, thousandSeparator, thousandsGroupStyle);\n    }\n    //add prefix and suffix when there is a number present\n    if (prefix)\n        { beforeDecimal = prefix + beforeDecimal; }\n    if (suffix)\n        { afterDecimal = afterDecimal + suffix; }\n    //restore negation sign\n    if (addNegation)\n        { beforeDecimal = '-' + beforeDecimal; }\n    numStr = beforeDecimal + ((hasDecimalSeparator && decimalSeparator) || '') + afterDecimal;\n    return numStr;\n}\nfunction getSeparators(props) {\n    var decimalSeparator = props.decimalSeparator; if ( decimalSeparator === void 0 ) decimalSeparator = '.';\n    var thousandSeparator = props.thousandSeparator;\n    var allowedDecimalSeparators = props.allowedDecimalSeparators;\n    if (thousandSeparator === true) {\n        thousandSeparator = ',';\n    }\n    if (!allowedDecimalSeparators) {\n        allowedDecimalSeparators = [decimalSeparator, '.'];\n    }\n    return {\n        decimalSeparator: decimalSeparator,\n        thousandSeparator: thousandSeparator,\n        allowedDecimalSeparators: allowedDecimalSeparators,\n    };\n}\nfunction handleNegation(value, allowNegative) {\n    if ( value === void 0 ) value = '';\n\n    var negationRegex = new RegExp('(-)');\n    var doubleNegationRegex = new RegExp('(-)(.)*(-)');\n    // Check number has '-' value\n    var hasNegation = negationRegex.test(value);\n    // Check number has 2 or more '-' values\n    var removeNegation = doubleNegationRegex.test(value);\n    //remove negation\n    value = value.replace(/-/g, '');\n    if (hasNegation && !removeNegation && allowNegative) {\n        value = '-' + value;\n    }\n    return value;\n}\nfunction getNumberRegex(decimalSeparator, global) {\n    return new RegExp((\"(^-)|[0-9]|\" + (escapeRegExp(decimalSeparator))), global ? 'g' : undefined);\n}\nfunction isNumericString(val, prefix, suffix) {\n    // for empty value we can always treat it as numeric string\n    if (val === '')\n        { return true; }\n    return (!(prefix === null || prefix === void 0 ? void 0 : prefix.match(/\\d/)) && !(suffix === null || suffix === void 0 ? void 0 : suffix.match(/\\d/)) && typeof val === 'string' && !isNaN(Number(val)));\n}\nfunction removeFormatting(value, changeMeta, props) {\n    var assign;\n\n    if ( changeMeta === void 0 ) changeMeta = getDefaultChangeMeta(value);\n    var allowNegative = props.allowNegative;\n    var prefix = props.prefix; if ( prefix === void 0 ) prefix = '';\n    var suffix = props.suffix; if ( suffix === void 0 ) suffix = '';\n    var decimalScale = props.decimalScale;\n    var from = changeMeta.from;\n    var to = changeMeta.to;\n    var start = to.start;\n    var end = to.end;\n    var ref = getSeparators(props);\n    var allowedDecimalSeparators = ref.allowedDecimalSeparators;\n    var decimalSeparator = ref.decimalSeparator;\n    var isBeforeDecimalSeparator = value[end] === decimalSeparator;\n    /**\n     * If only a number is added on empty input which matches with the prefix or suffix,\n     * then don't remove it, just return the same\n     */\n    if (charIsNumber(value) &&\n        (value === prefix || value === suffix) &&\n        changeMeta.lastValue === '') {\n        return value;\n    }\n    /** Check for any allowed decimal separator is added in the numeric format and replace it with decimal separator */\n    if (end - start === 1 && allowedDecimalSeparators.indexOf(value[start]) !== -1) {\n        var separator = decimalScale === 0 ? '' : decimalSeparator;\n        value = value.substring(0, start) + separator + value.substring(start + 1, value.length);\n    }\n    var stripNegation = function (value, start, end) {\n        /**\n         * if prefix starts with - we don't allow negative number to avoid confusion\n         * if suffix starts with - and the value length is same as suffix length, then the - sign is from the suffix\n         * In other cases, if the value starts with - then it is a negation\n         */\n        var hasNegation = false;\n        var hasDoubleNegation = false;\n        if (prefix.startsWith('-')) {\n            hasNegation = false;\n        }\n        else if (value.startsWith('--')) {\n            hasNegation = false;\n            hasDoubleNegation = true;\n        }\n        else if (suffix.startsWith('-') && value.length === suffix.length) {\n            hasNegation = false;\n        }\n        else if (value[0] === '-') {\n            hasNegation = true;\n        }\n        var charsToRemove = hasNegation ? 1 : 0;\n        if (hasDoubleNegation)\n            { charsToRemove = 2; }\n        // remove negation/double negation from start to simplify prefix logic as negation comes before prefix\n        if (charsToRemove) {\n            value = value.substring(charsToRemove);\n            // account for the removal of the negation for start and end index\n            start -= charsToRemove;\n            end -= charsToRemove;\n        }\n        return { value: value, start: start, end: end, hasNegation: hasNegation };\n    };\n    var toMetadata = stripNegation(value, start, end);\n    var hasNegation = toMetadata.hasNegation;\n    ((assign = toMetadata, value = assign.value, start = assign.start, end = assign.end));\n    var ref$1 = stripNegation(changeMeta.lastValue, from.start, from.end);\n    var fromStart = ref$1.start;\n    var fromEnd = ref$1.end;\n    var lastValue = ref$1.value;\n    // if only prefix and suffix part is updated reset the value to last value\n    // if the changed range is from suffix in the updated value, and the the suffix starts with the same characters, allow the change\n    var updatedSuffixPart = value.substring(start, end);\n    if (value.length &&\n        lastValue.length &&\n        (fromStart > lastValue.length - suffix.length || fromEnd < prefix.length) &&\n        !(updatedSuffixPart && suffix.startsWith(updatedSuffixPart))) {\n        value = lastValue;\n    }\n    /**\n     * remove prefix\n     * Remove whole prefix part if its present on the value\n     * If the prefix is partially deleted (in which case change start index will be less the prefix length)\n     * Remove only partial part of prefix.\n     */\n    var startIndex = 0;\n    if (value.startsWith(prefix))\n        { startIndex += prefix.length; }\n    else if (start < prefix.length)\n        { startIndex = start; }\n    value = value.substring(startIndex);\n    // account for deleted prefix for end\n    end -= startIndex;\n    /**\n     * Remove suffix\n     * Remove whole suffix part if its present on the value\n     * If the suffix is partially deleted (in which case change end index will be greater than the suffixStartIndex)\n     * remove the partial part of suffix\n     */\n    var endIndex = value.length;\n    var suffixStartIndex = value.length - suffix.length;\n    if (value.endsWith(suffix))\n        { endIndex = suffixStartIndex; }\n    // if the suffix is removed from the end\n    else if (end > suffixStartIndex)\n        { endIndex = end; }\n    // if the suffix is removed from start\n    else if (end > value.length - suffix.length)\n        { endIndex = end; }\n    value = value.substring(0, endIndex);\n    // add the negation back and handle for double negation\n    value = handleNegation(hasNegation ? (\"-\" + value) : value, allowNegative);\n    // remove non numeric characters\n    value = (value.match(getNumberRegex(decimalSeparator, true)) || []).join('');\n    // replace the decimalSeparator with ., and only keep the first separator, ignore following ones\n    var firstIndex = value.indexOf(decimalSeparator);\n    value = value.replace(new RegExp(escapeRegExp(decimalSeparator), 'g'), function (match, index) {\n        return index === firstIndex ? '.' : '';\n    });\n    //check if beforeDecimal got deleted and there is nothing after decimal,\n    //clear all numbers in such case while keeping the - sign\n    var ref$2 = splitDecimal(value, allowNegative);\n    var beforeDecimal = ref$2.beforeDecimal;\n    var afterDecimal = ref$2.afterDecimal;\n    var addNegation = ref$2.addNegation; // eslint-disable-line prefer-const\n    //clear only if something got deleted before decimal (cursor is before decimal)\n    if (to.end - to.start < from.end - from.start &&\n        beforeDecimal === '' &&\n        isBeforeDecimalSeparator &&\n        !parseFloat(afterDecimal)) {\n        value = addNegation ? '-' : '';\n    }\n    return value;\n}\nfunction getCaretBoundary(formattedValue, props) {\n    var prefix = props.prefix; if ( prefix === void 0 ) prefix = '';\n    var suffix = props.suffix; if ( suffix === void 0 ) suffix = '';\n    var boundaryAry = Array.from({ length: formattedValue.length + 1 }).map(function () { return true; });\n    var hasNegation = formattedValue[0] === '-';\n    // fill for prefix and negation\n    boundaryAry.fill(false, 0, prefix.length + (hasNegation ? 1 : 0));\n    // fill for suffix\n    var valLn = formattedValue.length;\n    boundaryAry.fill(false, valLn - suffix.length + 1, valLn + 1);\n    return boundaryAry;\n}\nfunction validateAndUpdateProps(props) {\n    var ref = getSeparators(props);\n    var thousandSeparator = ref.thousandSeparator;\n    var decimalSeparator = ref.decimalSeparator;\n    // eslint-disable-next-line prefer-const\n    var prefix = props.prefix; if ( prefix === void 0 ) prefix = '';\n    var allowNegative = props.allowNegative; if ( allowNegative === void 0 ) allowNegative = true;\n    if (thousandSeparator === decimalSeparator) {\n        throw new Error((\"\\n        Decimal separator can't be same as thousand separator.\\n        thousandSeparator: \" + thousandSeparator + \" (thousandSeparator = {true} is same as thousandSeparator = \\\",\\\")\\n        decimalSeparator: \" + decimalSeparator + \" (default value for decimalSeparator is .)\\n     \"));\n    }\n    if (prefix.startsWith('-') && allowNegative) {\n        // TODO: throw error in next major version\n        console.error((\"\\n      Prefix can't start with '-' when allowNegative is true.\\n      prefix: \" + prefix + \"\\n      allowNegative: \" + allowNegative + \"\\n    \"));\n        allowNegative = false;\n    }\n    return Object.assign(Object.assign({}, props), { allowNegative: allowNegative });\n}\nfunction useNumericFormat(props) {\n    // validate props\n    props = validateAndUpdateProps(props);\n    var _decimalSeparator = props.decimalSeparator;\n    var _allowedDecimalSeparators = props.allowedDecimalSeparators;\n    var thousandsGroupStyle = props.thousandsGroupStyle;\n    var suffix = props.suffix;\n    var allowNegative = props.allowNegative;\n    var allowLeadingZeros = props.allowLeadingZeros;\n    var onKeyDown = props.onKeyDown; if ( onKeyDown === void 0 ) onKeyDown = noop;\n    var onBlur = props.onBlur; if ( onBlur === void 0 ) onBlur = noop;\n    var thousandSeparator = props.thousandSeparator;\n    var decimalScale = props.decimalScale;\n    var fixedDecimalScale = props.fixedDecimalScale;\n    var prefix = props.prefix; if ( prefix === void 0 ) prefix = '';\n    var defaultValue = props.defaultValue;\n    var value = props.value;\n    var valueIsNumericString = props.valueIsNumericString;\n    var onValueChange = props.onValueChange;\n    var restProps = __rest(props, [\"decimalSeparator\", \"allowedDecimalSeparators\", \"thousandsGroupStyle\", \"suffix\", \"allowNegative\", \"allowLeadingZeros\", \"onKeyDown\", \"onBlur\", \"thousandSeparator\", \"decimalScale\", \"fixedDecimalScale\", \"prefix\", \"defaultValue\", \"value\", \"valueIsNumericString\", \"onValueChange\"]);\n    // get derived decimalSeparator and allowedDecimalSeparators\n    var ref = getSeparators(props);\n    var decimalSeparator = ref.decimalSeparator;\n    var allowedDecimalSeparators = ref.allowedDecimalSeparators;\n    var _format = function (numStr) { return format(numStr, props); };\n    var _removeFormatting = function (inputValue, changeMeta) { return removeFormatting(inputValue, changeMeta, props); };\n    var _value = isNil(value) ? defaultValue : value;\n    // try to figure out isValueNumericString based on format prop and value\n    var _valueIsNumericString = valueIsNumericString !== null && valueIsNumericString !== void 0 ? valueIsNumericString : isNumericString(_value, prefix, suffix);\n    if (!isNil(value)) {\n        _valueIsNumericString = _valueIsNumericString || typeof value === 'number';\n    }\n    else if (!isNil(defaultValue)) {\n        _valueIsNumericString = _valueIsNumericString || typeof defaultValue === 'number';\n    }\n    var roundIncomingValueToPrecision = function (value) {\n        if (isNotValidValue(value))\n            { return value; }\n        if (typeof value === 'number') {\n            value = toNumericString(value);\n        }\n        /**\n         * only round numeric or float string values coming through props,\n         * we don't need to do it for onChange events, as we want to prevent typing there\n         */\n        if (_valueIsNumericString && typeof decimalScale === 'number') {\n            return roundToPrecision(value, decimalScale, Boolean(fixedDecimalScale));\n        }\n        return value;\n    };\n    var ref$1 = useInternalValues(roundIncomingValueToPrecision(value), roundIncomingValueToPrecision(defaultValue), Boolean(_valueIsNumericString), _format, _removeFormatting, onValueChange);\n    var ref$1_0 = ref$1[0];\n    var numAsString = ref$1_0.numAsString;\n    var formattedValue = ref$1_0.formattedValue;\n    var _onValueChange = ref$1[1];\n    var _onKeyDown = function (e) {\n        var el = e.target;\n        var key = e.key;\n        var selectionStart = el.selectionStart;\n        var selectionEnd = el.selectionEnd;\n        var value = el.value; if ( value === void 0 ) value = '';\n        // if user tries to delete partial prefix then ignore it\n        if ((key === 'Backspace' || key === 'Delete') && selectionEnd < prefix.length) {\n            e.preventDefault();\n            return;\n        }\n        // if multiple characters are selected and user hits backspace, no need to handle anything manually\n        if (selectionStart !== selectionEnd) {\n            onKeyDown(e);\n            return;\n        }\n        // if user hits backspace, while the cursor is before prefix, and the input has negation, remove the negation\n        if (key === 'Backspace' &&\n            value[0] === '-' &&\n            selectionStart === prefix.length + 1 &&\n            allowNegative) {\n            // bring the cursor to after negation\n            setCaretPosition(el, 1);\n        }\n        // don't allow user to delete decimal separator when decimalScale and fixedDecimalScale is set\n        if (decimalScale && fixedDecimalScale) {\n            if (key === 'Backspace' && value[selectionStart - 1] === decimalSeparator) {\n                setCaretPosition(el, selectionStart - 1);\n                e.preventDefault();\n            }\n            else if (key === 'Delete' && value[selectionStart] === decimalSeparator) {\n                e.preventDefault();\n            }\n        }\n        // if user presses the allowed decimal separator before the separator, move the cursor after the separator\n        if ((allowedDecimalSeparators === null || allowedDecimalSeparators === void 0 ? void 0 : allowedDecimalSeparators.includes(key)) && value[selectionStart] === decimalSeparator) {\n            setCaretPosition(el, selectionStart + 1);\n        }\n        var _thousandSeparator = thousandSeparator === true ? ',' : thousandSeparator;\n        // move cursor when delete or backspace is pressed before/after thousand separator\n        if (key === 'Backspace' && value[selectionStart - 1] === _thousandSeparator) {\n            setCaretPosition(el, selectionStart - 1);\n        }\n        if (key === 'Delete' && value[selectionStart] === _thousandSeparator) {\n            setCaretPosition(el, selectionStart + 1);\n        }\n        onKeyDown(e);\n    };\n    var _onBlur = function (e) {\n        var _value = numAsString;\n        // if there no no numeric value, clear the input\n        if (!_value.match(/\\d/g)) {\n            _value = '';\n        }\n        // clear leading 0s\n        if (!allowLeadingZeros) {\n            _value = fixLeadingZero(_value);\n        }\n        // apply fixedDecimalScale on blur event\n        if (fixedDecimalScale && decimalScale) {\n            _value = roundToPrecision(_value, decimalScale, fixedDecimalScale);\n        }\n        if (_value !== numAsString) {\n            var formattedValue = format(_value, props);\n            _onValueChange({\n                formattedValue: formattedValue,\n                value: _value,\n                floatValue: parseFloat(_value),\n            }, {\n                event: e,\n                source: SourceType.event,\n            });\n        }\n        onBlur(e);\n    };\n    var isValidInputCharacter = function (inputChar) {\n        if (inputChar === decimalSeparator)\n            { return true; }\n        return charIsNumber(inputChar);\n    };\n    var isCharacterSame = function (ref) {\n        var currentValue = ref.currentValue;\n        var lastValue = ref.lastValue;\n        var formattedValue = ref.formattedValue;\n        var currentValueIndex = ref.currentValueIndex;\n        var formattedValueIndex = ref.formattedValueIndex;\n\n        var curChar = currentValue[currentValueIndex];\n        var newChar = formattedValue[formattedValueIndex];\n        /**\n         * NOTE: as thousand separator and allowedDecimalSeparators can be same, we need to check on\n         * typed range if we have typed any character from allowedDecimalSeparators, in that case we\n         * consider different characters like , and . same within the range of updated value.\n         */\n        var typedRange = findChangeRange(lastValue, currentValue);\n        var to = typedRange.to;\n        // handle corner case where if we user types a decimal separator with fixedDecimalScale\n        // and pass back float value the cursor jumps. #851\n        var getDecimalSeparatorIndex = function (value) {\n            return _removeFormatting(value).indexOf('.') + prefix.length;\n        };\n        if (value === 0 &&\n            fixedDecimalScale &&\n            decimalScale &&\n            currentValue[to.start] === decimalSeparator &&\n            getDecimalSeparatorIndex(currentValue) < currentValueIndex &&\n            getDecimalSeparatorIndex(formattedValue) > formattedValueIndex) {\n            return false;\n        }\n        if (currentValueIndex >= to.start &&\n            currentValueIndex < to.end &&\n            allowedDecimalSeparators &&\n            allowedDecimalSeparators.includes(curChar) &&\n            newChar === decimalSeparator) {\n            return true;\n        }\n        return curChar === newChar;\n    };\n    return Object.assign(Object.assign({}, restProps), { value: formattedValue, valueIsNumericString: false, isValidInputCharacter: isValidInputCharacter,\n        isCharacterSame: isCharacterSame, onValueChange: _onValueChange, format: _format, removeFormatting: _removeFormatting, getCaretBoundary: function (formattedValue) { return getCaretBoundary(formattedValue, props); }, onKeyDown: _onKeyDown, onBlur: _onBlur });\n}\nfunction NumericFormat(props) {\n    var numericFormatProps = useNumericFormat(props);\n    return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(NumberFormatBase, Object.assign({}, numericFormatProps));\n}\n\nfunction format$1(numStr, props) {\n    var format = props.format;\n    var allowEmptyFormatting = props.allowEmptyFormatting;\n    var mask = props.mask;\n    var patternChar = props.patternChar; if ( patternChar === void 0 ) patternChar = '#';\n    if (numStr === '' && !allowEmptyFormatting)\n        { return ''; }\n    var hashCount = 0;\n    var formattedNumberAry = format.split('');\n    for (var i = 0, ln = format.length; i < ln; i++) {\n        if (format[i] === patternChar) {\n            formattedNumberAry[i] = numStr[hashCount] || getMaskAtIndex(mask, hashCount);\n            hashCount += 1;\n        }\n    }\n    return formattedNumberAry.join('');\n}\nfunction removeFormatting$1(value, changeMeta, props) {\n    if ( changeMeta === void 0 ) changeMeta = getDefaultChangeMeta(value);\n\n    var format = props.format;\n    var patternChar = props.patternChar; if ( patternChar === void 0 ) patternChar = '#';\n    var from = changeMeta.from;\n    var to = changeMeta.to;\n    var lastValue = changeMeta.lastValue; if ( lastValue === void 0 ) lastValue = '';\n    var isNumericSlot = function (caretPos) { return format[caretPos] === patternChar; };\n    var removeFormatChar = function (string, startIndex) {\n        var str = '';\n        for (var i = 0; i < string.length; i++) {\n            if (isNumericSlot(startIndex + i) && charIsNumber(string[i])) {\n                str += string[i];\n            }\n        }\n        return str;\n    };\n    var extractNumbers = function (str) { return str.replace(/[^0-9]/g, ''); };\n    // if format doesn't have any number, remove all the non numeric characters\n    if (!format.match(/\\d/)) {\n        return extractNumbers(value);\n    }\n    /**\n     * if user paste the whole formatted text in an empty input or doing select all and paste, check if matches to the pattern\n     * and remove the format characters, if there is a mismatch on the pattern, do plane number extract\n     */\n    if ((lastValue === '' || from.end - from.start === lastValue.length) &&\n        value.length === format.length) {\n        var str = '';\n        for (var i = 0; i < value.length; i++) {\n            if (isNumericSlot(i)) {\n                if (charIsNumber(value[i])) {\n                    str += value[i];\n                }\n            }\n            else if (value[i] !== format[i]) {\n                // if there is a mismatch on the pattern, do plane number extract\n                return extractNumbers(value);\n            }\n        }\n        return str;\n    }\n    /**\n     * For partial change,\n     * where ever there is a change on the input, we can break the number in three parts\n     * 1st: left part which is unchanged\n     * 2nd: middle part which is changed\n     * 3rd: right part which is unchanged\n     *\n     * The first and third section will be same as last value, only the middle part will change\n     * We can consider on the change part all the new characters are non format characters.\n     * And on the first and last section it can have partial format characters.\n     *\n     * We pick first and last section from the lastValue (as that has 1-1 mapping with format)\n     * and middle one from the update value.\n     */\n    var firstSection = lastValue.substring(0, from.start);\n    var middleSection = value.substring(to.start, to.end);\n    var lastSection = lastValue.substring(from.end);\n    return (\"\" + (removeFormatChar(firstSection, 0)) + (extractNumbers(middleSection)) + (removeFormatChar(lastSection, from.end)));\n}\nfunction getCaretBoundary$1(formattedValue, props) {\n    var format = props.format;\n    var mask = props.mask;\n    var patternChar = props.patternChar; if ( patternChar === void 0 ) patternChar = '#';\n    var boundaryAry = Array.from({ length: formattedValue.length + 1 }).map(function () { return true; });\n    var hashCount = 0;\n    var firstEmptySlot = -1;\n    var maskAndIndexMap = {};\n    format.split('').forEach(function (char, index) {\n        var maskAtIndex = undefined;\n        if (char === patternChar) {\n            hashCount++;\n            maskAtIndex = getMaskAtIndex(mask, hashCount - 1);\n            if (firstEmptySlot === -1 && formattedValue[index] === maskAtIndex) {\n                firstEmptySlot = index;\n            }\n        }\n        maskAndIndexMap[index] = maskAtIndex;\n    });\n    var isPosAllowed = function (pos) {\n        // the position is allowed if the position is not masked and valid number area\n        return format[pos] === patternChar && formattedValue[pos] !== maskAndIndexMap[pos];\n    };\n    for (var i = 0, ln = boundaryAry.length; i < ln; i++) {\n        // consider caret to be in boundary if it is before or after numeric value\n        // Note: on pattern based format its denoted by patternCharacter\n        // we should also allow user to put cursor on first empty slot\n        boundaryAry[i] = i === firstEmptySlot || isPosAllowed(i) || isPosAllowed(i - 1);\n    }\n    // the first patternChar position is always allowed\n    boundaryAry[format.indexOf(patternChar)] = true;\n    return boundaryAry;\n}\nfunction validateProps(props) {\n    var mask = props.mask;\n    if (mask) {\n        var maskAsStr = mask === 'string' ? mask : mask.toString();\n        if (maskAsStr.match(/\\d/g)) {\n            throw new Error((\"Mask \" + mask + \" should not contain numeric character;\"));\n        }\n    }\n}\nfunction isNumericString$1(val, format) {\n    //we can treat empty string as numeric string\n    if (val === '')\n        { return true; }\n    return !(format === null || format === void 0 ? void 0 : format.match(/\\d/)) && typeof val === 'string' && (!!val.match(/^\\d+$/) || val === '');\n}\nfunction usePatternFormat(props) {\n    var mask = props.mask;\n    var allowEmptyFormatting = props.allowEmptyFormatting;\n    var formatProp = props.format;\n    var inputMode = props.inputMode; if ( inputMode === void 0 ) inputMode = 'numeric';\n    var onKeyDown = props.onKeyDown; if ( onKeyDown === void 0 ) onKeyDown = noop;\n    var patternChar = props.patternChar; if ( patternChar === void 0 ) patternChar = '#';\n    var value = props.value;\n    var defaultValue = props.defaultValue;\n    var valueIsNumericString = props.valueIsNumericString;\n    var restProps = __rest(props, [\"mask\", \"allowEmptyFormatting\", \"format\", \"inputMode\", \"onKeyDown\", \"patternChar\", \"value\", \"defaultValue\", \"valueIsNumericString\"]);\n    // validate props\n    validateProps(props);\n    var _getCaretBoundary = function (formattedValue) {\n        return getCaretBoundary$1(formattedValue, props);\n    };\n    var _onKeyDown = function (e) {\n        var key = e.key;\n        var el = e.target;\n        var selectionStart = el.selectionStart;\n        var selectionEnd = el.selectionEnd;\n        var value = el.value;\n        // if multiple characters are selected and user hits backspace, no need to handle anything manually\n        if (selectionStart !== selectionEnd) {\n            onKeyDown(e);\n            return;\n        }\n        // bring the cursor to closest numeric section\n        var caretPos = selectionStart;\n        // if backspace is pressed after the format characters, bring it to numeric section\n        // if delete is pressed before the format characters, bring it to numeric section\n        if (key === 'Backspace' || key === 'Delete') {\n            var direction = 'right';\n            if (key === 'Backspace') {\n                while (caretPos > 0 && formatProp[caretPos - 1] !== patternChar) {\n                    caretPos--;\n                }\n                direction = 'left';\n            }\n            else {\n                var formatLn = formatProp.length;\n                while (caretPos < formatLn && formatProp[caretPos] !== patternChar) {\n                    caretPos++;\n                }\n                direction = 'right';\n            }\n            caretPos = getCaretPosInBoundary(value, caretPos, _getCaretBoundary(value), direction);\n        }\n        else if (formatProp[caretPos] !== patternChar &&\n            key !== 'ArrowLeft' &&\n            key !== 'ArrowRight') {\n            // if user is typing on format character position, bring user to next allowed caret position\n            caretPos = getCaretPosInBoundary(value, caretPos + 1, _getCaretBoundary(value), 'right');\n        }\n        // if we changing caret position, set the caret position\n        if (caretPos !== selectionStart) {\n            setCaretPosition(el, caretPos);\n        }\n        onKeyDown(e);\n    };\n    // try to figure out isValueNumericString based on format prop and value\n    var _value = isNil(value) ? defaultValue : value;\n    var isValueNumericString = valueIsNumericString !== null && valueIsNumericString !== void 0 ? valueIsNumericString : isNumericString$1(_value, formatProp);\n    var _props = Object.assign(Object.assign({}, props), { valueIsNumericString: isValueNumericString });\n    return Object.assign(Object.assign({}, restProps), { value: value,\n        defaultValue: defaultValue, valueIsNumericString: isValueNumericString, inputMode: inputMode, format: function (numStr) { return format$1(numStr, _props); }, removeFormatting: function (inputValue, changeMeta) { return removeFormatting$1(inputValue, changeMeta, _props); }, getCaretBoundary: _getCaretBoundary, onKeyDown: _onKeyDown });\n}\nfunction PatternFormat(props) {\n    var patternFormatProps = usePatternFormat(props);\n    return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(NumberFormatBase, Object.assign({}, patternFormatProps));\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-number-format/dist/react-number-format.es.js\n");

/***/ })

};
;