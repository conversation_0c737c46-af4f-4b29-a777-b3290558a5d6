"""
JSON utilities for handling custom serialization.
"""

import json
import uuid
from datetime import datetime, date

class UUIDEncoder(json.JSONEncoder):
    """
    Custom JSON encoder that can handle UUID objects.
    """
    def default(self, obj):
        if isinstance(obj, uuid.UUID):
            # Convert UUID to string
            return str(obj)
        if isinstance(obj, (datetime, date)):
            # Convert datetime to ISO format
            return obj.isoformat()
        # Let the base class handle other types
        return super().default(obj)

def dumps(obj, **kwargs):
    """
    Serialize obj to a JSON formatted string using the UUIDEncoder.
    """
    return json.dumps(obj, cls=UUIDEncoder, **kwargs)
