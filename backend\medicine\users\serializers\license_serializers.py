from rest_framework import serializers
from users.models import DoctorLicense

class DoctorLicenseSerializer(serializers.ModelSerializer):
    """
    Serializer for the DoctorLicense model.
    """
    days_until_expiry = serializers.IntegerField(read_only=True)
    formatted_license_number = serializers.CharField(read_only=True)
    is_active = serializers.BooleanField(read_only=True)

    class Meta:
        model = DoctorLicense
        fields = [
            'id', 'license_number', 'formatted_license_number', 'status',
            'issue_date', 'expiry_date', 'days_until_expiry', 'is_active',
            'activation_date', 'is_renewed', 'days_remaining', 'initial_days',
            'transaction_id', 'payment_method', 'payment_status', 'payment_date',
            'payment_amount', 'payment_reference', 'payment_bank', 'payment_account'
        ]
        read_only_fields = [
            'id', 'license_number', 'formatted_license_number', 'status',
            'issue_date', 'expiry_date', 'days_until_expiry', 'is_active',
            'activation_date', 'is_renewed', 'days_remaining', 'initial_days'
        ]

class LicenseActivationSerializer(serializers.Serializer):
    """
    Serializer for license activation.
    """
    license_number = serializers.CharField(required=True)
    activation_code = serializers.CharField(required=True)

    def validate_license_number(self, value):
        try:
            license_obj = DoctorLicense.objects.get(license_number=value)
            if license_obj.status != 'pending':
                raise serializers.ValidationError(f"License is already {license_obj.status}")
        except DoctorLicense.DoesNotExist:
            raise serializers.ValidationError("License number not found")
        return value

class LicenseRenewalSerializer(serializers.Serializer):
    """
    Serializer for license renewal.
    """
    license_number = serializers.CharField(required=True)
    duration_days = serializers.IntegerField(required=False, default=365)

    def validate_license_number(self, value):
        try:
            license_obj = DoctorLicense.objects.get(license_number=value)
            if license_obj.status not in ['active', 'expired']:
                raise serializers.ValidationError(f"Only active or expired licenses can be renewed")
            if license_obj.is_renewed:
                raise serializers.ValidationError("This license has already been renewed")
        except DoctorLicense.DoesNotExist:
            raise serializers.ValidationError("License number not found")
        return value

    def validate_duration_days(self, value):
        if value not in [180, 365, 730]:  # 6 months, 1 year, 2 years
            raise serializers.ValidationError("Duration must be 180, 365, or 730 days")
        return value
