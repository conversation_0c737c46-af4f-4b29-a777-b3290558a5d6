from rest_framework import generics, permissions, status
from rest_framework.response import Response
from rest_framework.views import APIView
from django.utils import timezone
from django.shortcuts import get_object_or_404
from django.core.mail import send_mail
from django.conf import settings

from users.models import DoctorLicense
from users.serializers.license_serializers import (
    DoctorLicenseSerializer,
    LicenseActivationSerializer,
    LicenseRenewalSerializer
)
from users.utils import LicenseGenerator

class DoctorLicenseView(generics.RetrieveAPIView):
    """
    View to retrieve a doctor's license information.
    """
    serializer_class = DoctorLicenseSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self):
        try:
            return DoctorLicense.objects.get(user=self.request.user)
        except DoctorLicense.DoesNotExist:
            return None

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        if not instance:
            return Response(
                {"detail": "No license found for this user."},
                status=status.HTTP_404_NOT_FOUND
            )
        serializer = self.get_serializer(instance)
        return Response(serializer.data)

class GenerateLicenseView(APIView):
    """
    View to generate a new license for a doctor.
    Admin only.
    """
    permission_classes = [permissions.IsAdminUser]

    def post(self, request, *args, **kwargs):
        user_id = request.data.get('user_id')
        specialization = request.data.get('specialization')
        duration_days = int(request.data.get('duration_days', 180))  # Default to 6 months (180 days)

        if not user_id:
            return Response(
                {"detail": "User ID is required."},
                status=status.HTTP_400_BAD_REQUEST
            )

        from django.contrib.auth import get_user_model
        User = get_user_model()

        try:
            user = User.objects.get(id=user_id)
            if user.user_type != 'doctor':
                return Response(
                    {"detail": "User is not a doctor."},
                    status=status.HTTP_400_BAD_REQUEST
                )
        except User.DoesNotExist:
            return Response(
                {"detail": "User not found."},
                status=status.HTTP_404_NOT_FOUND
            )

        # Generate a new license
        license_number, raw_data = LicenseGenerator.generate_license(
            user_id=str(user.id),  # Convert UUID to string
            specialization=specialization or user.specialization,
            expiry_days=duration_days
        )

        # Generate an activation code
        activation_code = LicenseGenerator.generate_activation_code()

        # Create a new license record
        license_obj = DoctorLicense.objects.create(
            user=user,
            license_number=license_number,
            status='pending',
            license_data=raw_data,
            expiry_date=timezone.now() + timezone.timedelta(days=duration_days),
            activation_code=activation_code,
            days_remaining=duration_days,
            initial_days=duration_days
        )

        # Update the user's license number
        user.license_number = license_number
        user.license_verified = False
        user.save()

        return Response({
            "license": DoctorLicenseSerializer(license_obj).data,
            "activation_code": activation_code
        })

class ActivateLicenseView(APIView):
    """
    View to activate a license.
    Public access allowed for initial activation.
    """
    permission_classes = [permissions.AllowAny]

    def post(self, request, *args, **kwargs):
        serializer = LicenseActivationSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        license_number = serializer.validated_data['license_number']
        activation_code = serializer.validated_data['activation_code']

        try:
            license_obj = DoctorLicense.objects.get(license_number=license_number)

            # For authenticated users, check if this license belongs to them
            if request.user.is_authenticated:
                if license_obj.user != request.user and not request.user.is_staff:
                    return Response(
                        {"detail": "You do not have permission to activate this license."},
                        status=status.HTTP_403_FORBIDDEN
                    )

            # Activate the license
            success, message = license_obj.activate(activation_code)

            if success:
                # Update the user's license verification status
                license_obj.user.license_verified = True
                license_obj.user.save()

                return Response({
                    "success": True,
                    "message": message,
                    "license": DoctorLicenseSerializer(license_obj).data
                })
            else:
                return Response(
                    {
                        "success": False,
                        "message": message
                    },
                    status=status.HTTP_400_BAD_REQUEST
                )

        except DoctorLicense.DoesNotExist:
            return Response(
                {
                    "success": False,
                    "message": "License not found. Please check the license number and try again."
                },
                status=status.HTTP_404_NOT_FOUND
            )

class RenewLicenseView(APIView):
    """
    View to renew a license.
    """
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, *args, **kwargs):
        serializer = LicenseRenewalSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        license_number = serializer.validated_data['license_number']
        duration_days = serializer.validated_data['duration_days']

        try:
            license_obj = DoctorLicense.objects.get(license_number=license_number)

            # Check if this license belongs to the current user
            if license_obj.user != request.user and not request.user.is_staff:
                return Response(
                    {"detail": "You do not have permission to renew this license."},
                    status=status.HTTP_403_FORBIDDEN
                )

            # Renew the license
            new_license = license_obj.renew(duration_days)

            # Generate an activation code
            activation_code = LicenseGenerator.generate_activation_code()
            new_license.activation_code = activation_code
            new_license.save()

            return Response({
                "detail": "License renewed successfully.",
                "old_license": DoctorLicenseSerializer(license_obj).data,
                "new_license": DoctorLicenseSerializer(new_license).data,
                "activation_code": activation_code
            })

        except DoctorLicense.DoesNotExist:
            return Response(
                {"detail": "License not found."},
                status=status.HTTP_404_NOT_FOUND
            )

class SendLicenseEmailView(APIView):
    """
    View to send license details to the doctor's email.
    """
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, license_number, *args, **kwargs):
        try:
            # Get the license
            license_obj = get_object_or_404(DoctorLicense, license_number=license_number)

            # Check if this license belongs to the current user
            if license_obj.user != request.user and not request.user.is_staff:
                return Response(
                    {"detail": "You do not have permission to access this license."},
                    status=status.HTTP_403_FORBIDDEN
                )

            # Send the license details to email
            success, message = license_obj.send_to_email()

            if success:
                return Response({
                    "success": True,
                    "message": message
                })
            else:
                return Response({
                    "success": False,
                    "message": message
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        except DoctorLicense.DoesNotExist:
            return Response(
                {"detail": "License not found."},
                status=status.HTTP_404_NOT_FOUND
            )

class UpdateTransactionIDView(APIView):
    """
    View to update the transaction ID of a license.
    """
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, *args, **kwargs):
        license_number = request.data.get('license_number')
        transaction_id = request.data.get('transaction_id')

        if not license_number or not transaction_id:
            return Response(
                {"detail": "License number and transaction ID are required."},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            license_obj = DoctorLicense.objects.get(license_number=license_number)

            # Check if this license belongs to the current user
            if license_obj.user != request.user and not request.user.is_staff:
                return Response(
                    {"detail": "You do not have permission to update this license."},
                    status=status.HTTP_403_FORBIDDEN
                )

            # Format the transaction ID if it doesn't already have the TXN- prefix
            if not transaction_id.startswith('TXN-'):
                from datetime import datetime
                date_str = datetime.now().strftime('%Y%m%d')
                transaction_id = f"TXN-{date_str}-{transaction_id.upper()}"
            else:
                transaction_id = transaction_id.upper()

            # Update the transaction ID
            license_obj.transaction_id = transaction_id
            license_obj.save()

            return Response({
                "success": True,
                "message": "Transaction ID updated successfully.",
                "license": DoctorLicenseSerializer(license_obj).data
            })

        except DoctorLicense.DoesNotExist:
            return Response(
                {
                    "success": False,
                    "message": "License not found. Please check the license number and try again."
                },
                status=status.HTTP_404_NOT_FOUND
            )