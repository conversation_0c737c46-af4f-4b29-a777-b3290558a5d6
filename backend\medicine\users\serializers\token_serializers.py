from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from rest_framework import serializers
from django.contrib.auth import authenticate
from django.utils.translation import gettext_lazy as _

class CustomTokenObtainPairSerializer(TokenObtainPairSerializer):
    user_type = serializers.CharField(required=False)

    def validate(self, attrs):
        # Extract credentials
        email = attrs.get('email')
        password = attrs.get('password')
        user_type = attrs.get('user_type')

        # Remove user_type from attrs to avoid validation errors in parent class
        if 'user_type' in attrs:
            attrs.pop('user_type')

        # Authenticate user with credentials
        user = authenticate(self.context['request'], email=email, password=password)

        if user is None:
            raise serializers.ValidationError(
                _('No active account found with the given credentials'),
                code='authorization'
            )

        # If user_type is provided, check if it matches the user's type
        if user_type and user.user_type != user_type:
            # Special case for staff users - they might be labeled as 'admin' in the database
            if user_type == 'staff' and user.user_type == 'admin':
                # Allow staff login for admin users
                pass
            # Special case for assistant users - they might be trying to log in as staff
            elif user_type == 'staff' and user.user_type == 'assistant':
                # Allow assistants to log in as staff
                pass
            # Special case for staff users - they might be trying to log in as assistant
            elif user_type == 'assistant' and user.user_type == 'staff':
                # Allow staff to log in as assistant
                pass
            else:
                # For all other mismatches, raise an error
                raise serializers.ValidationError(
                    _('Invalid user type. Please select the correct user type.'),
                    code='authorization'
                )

        # Get token data from parent class
        data = super().validate(attrs)

        # Add user type to response
        data['user_type'] = user.user_type

        return data
