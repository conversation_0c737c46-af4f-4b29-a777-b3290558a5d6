"""
Billing views for the dentistry application.
"""
from rest_framework import viewsets, permissions, filters
from django_filters.rest_framework import DjangoFilterBackend
from dentistry.models import (
    DentistryBillingCode, DentistryInvoice, DentistryPatientInsurance
)
from dentistry.serializers import (
    DentistryBillingCodeSerializer, DentistryInvoiceSerializer,
    DentistryPatientInsuranceSerializer
)

class DentistryBillingCodeViewSet(viewsets.ModelViewSet):
    """
    API endpoint for dentistry billing codes.
    """
    queryset = DentistryBillingCode.objects.all()
    serializer_class = DentistryBillingCodeSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = [
        'base_code__code_type', 'is_preventive', 'is_restorative',
        'is_endodontic', 'is_periodontic', 'is_prosthodontic',
        'is_orthodontic', 'is_surgical'
    ]
    search_fields = ['ada_code', 'base_code__code', 'base_code__description', 'dentistry_specific_details']
    ordering_fields = ['ada_code', 'base_code__code', 'created_at']

class DentistryInvoiceViewSet(viewsets.ModelViewSet):
    """
    API endpoint for dentistry invoices.
    """
    queryset = DentistryInvoice.objects.all()
    serializer_class = DentistryInvoiceSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = [
        'dentistry_patient', 'base_invoice__status', 'x_rays_taken',
        'cleaning_performed', 'lab_work_required'
    ]
    search_fields = [
        'base_invoice__invoice_number', 'base_invoice__notes',
        'teeth_treated', 'lab_work_description'
    ]
    ordering_fields = ['base_invoice__date_issued', 'base_invoice__due_date', 'created_at']
    
    def get_queryset(self):
        """
        Filter invoices based on query parameters.
        """
        queryset = super().get_queryset()
        
        # Filter by date range
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        
        if start_date:
            queryset = queryset.filter(base_invoice__date_issued__gte=start_date)
        if end_date:
            queryset = queryset.filter(base_invoice__date_issued__lte=end_date)
        
        # Filter by amount range
        min_amount = self.request.query_params.get('min_amount')
        max_amount = self.request.query_params.get('max_amount')
        
        if min_amount:
            queryset = queryset.filter(base_invoice__total_amount__gte=min_amount)
        if max_amount:
            queryset = queryset.filter(base_invoice__total_amount__lte=max_amount)
        
        # Filter by tooth number
        tooth_number = self.request.query_params.get('tooth_number')
        if tooth_number:
            queryset = queryset.filter(teeth_treated__contains=tooth_number)
        
        return queryset

class DentistryPatientInsuranceViewSet(viewsets.ModelViewSet):
    """
    API endpoint for dentistry patient insurance.
    """
    queryset = DentistryPatientInsurance.objects.all()
    serializer_class = DentistryPatientInsuranceSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = [
        'dentistry_patient', 'base_insurance__is_primary',
        'covers_preventive', 'covers_basic', 'covers_major', 'covers_orthodontic'
    ]
    search_fields = [
        'base_insurance__policy_number', 'base_insurance__group_number',
        'base_insurance__subscriber_name', 'waiting_period_details'
    ]
    ordering_fields = [
        'base_insurance__is_primary', 'annual_maximum', 'remaining_benefit',
        'base_insurance__effective_date', 'base_insurance__expiration_date', 'created_at'
    ]
