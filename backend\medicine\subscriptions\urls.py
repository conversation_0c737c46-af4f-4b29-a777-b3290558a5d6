from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON><PERSON><PERSON>
from .views import (
    SubscriptionPackageViewSet, 
    DoctorSubscriptionViewSet, 
    CouponViewSet,
    SubscriptionTransactionViewSet
)

router = DefaultRouter()
router.register(r'packages', SubscriptionPackageViewSet)
router.register(r'subscriptions', DoctorSubscriptionViewSet, basename='subscription')
router.register(r'coupons', CouponViewSet)
router.register(r'transactions', SubscriptionTransactionViewSet, basename='transaction')

urlpatterns = [
    path('', include(router.urls)),
]
