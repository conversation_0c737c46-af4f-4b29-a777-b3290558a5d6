"""
Vues API pour le système SVG dentaire dynamique
"""
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from django.db import transaction, models
from ..models.dental_svg_system import (
    DentalSvgConfiguration, DentalSvgPath, DentalTreatmentTemplate
)
from ..models.patient import DentistryPatient
from ..serializers.dental_svg_system import (
    DentalSvgConfigurationSerializer, DentalSvgPathSerializer,
    DentalTreatmentTemplateSerializer
)


class DentalSvgConfigurationViewSet(viewsets.ModelViewSet):
    """
    ViewSet pour la configuration SVG des dents
    """
    serializer_class = DentalSvgConfigurationSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return DentalSvgConfiguration.objects.filter(
            patient__patient=self.request.user
        ).prefetch_related('svg_paths')

    @action(detail=False, methods=['post'])
    def initialize_patient_teeth(self, request):
        """
        Initialise toutes les dents (1-32) pour un patient
        """
        try:
            patient = DentistryPatient.objects.get(patient=request.user)

            with transaction.atomic():
                # Créer les configurations pour les 32 dents
                configurations = []
                for tooth_id in range(1, 33):
                    config, created = DentalSvgConfiguration.objects.get_or_create(
                        patient=patient,
                        tooth_id=tooth_id,
                        defaults={
                            'width': '59.8625px',
                            'position': '0 0 50.8 172',
                        }
                    )

                    if created:
                        # Créer les paths de base (1-16) pour chaque dent
                        for path_id in range(1, 17):
                            DentalSvgPath.objects.create(
                                dental_svg_config=config,
                                path_id=str(path_id),
                                code='st0',  # Code par défaut
                                path='',  # Path vide par défaut
                                treatment_type='base',
                                display_order=path_id
                            )

                    configurations.append(config)

                serializer = self.get_serializer(configurations, many=True)
                return Response({
                    'message': 'Patient teeth initialized successfully',
                    'configurations': serializer.data
                }, status=status.HTTP_201_CREATED)

        except DentistryPatient.DoesNotExist:
            return Response({
                'error': 'Patient profile not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=True, methods=['post'])
    def apply_treatment(self, request, pk=None):
        """
        Applique un traitement à une dent spécifique
        """
        config = self.get_object()
        treatment_name = request.data.get('treatment_name')

        if not treatment_name:
            return Response({
                'error': 'treatment_name is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            template = DentalTreatmentTemplate.objects.get(name=treatment_name)

            with transaction.atomic():
                # Supprimer les paths spécifiés
                if template.paths_to_remove:
                    config.svg_paths.filter(
                        path_id__in=template.paths_to_remove
                    ).delete()

                # Ajouter les nouveaux paths
                for path_data in template.template_paths:
                    DentalSvgPath.objects.update_or_create(
                        dental_svg_config=config,
                        path_id=path_data['path_id'],
                        defaults={
                            'code': path_data.get('code', 'st0'),
                            'path': path_data.get('path', ''),
                            'style': path_data.get('style', {}),
                            'transform': path_data.get('transform', ''),
                            'treatment_type': treatment_name,
                            'display_order': path_data.get('display_order', 0),
                            'is_active': True
                        }
                    )

                # Mettre à jour l'état du traitement dans la configuration
                treatment_field = f'is_{treatment_name}_active'
                if hasattr(config, treatment_field):
                    setattr(config, treatment_field, True)
                    config.save()

                serializer = self.get_serializer(config)
                return Response({
                    'message': f'Treatment {treatment_name} applied successfully',
                    'configuration': serializer.data
                }, status=status.HTTP_200_OK)

        except DentalTreatmentTemplate.DoesNotExist:
            return Response({
                'error': f'Treatment template {treatment_name} not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=True, methods=['post'])
    def remove_treatment(self, request, pk=None):
        """
        Supprime un traitement d'une dent spécifique
        """
        config = self.get_object()
        treatment_name = request.data.get('treatment_name')

        if not treatment_name:
            return Response({
                'error': 'treatment_name is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            with transaction.atomic():
                # Supprimer les paths du traitement
                config.svg_paths.filter(treatment_type=treatment_name).delete()

                # Mettre à jour l'état du traitement dans la configuration
                treatment_field = f'is_{treatment_name}_active'
                if hasattr(config, treatment_field):
                    setattr(config, treatment_field, False)
                    config.save()

                serializer = self.get_serializer(config)
                return Response({
                    'message': f'Treatment {treatment_name} removed successfully',
                    'configuration': serializer.data
                }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'])
    def get_patient_teeth_by_age(self, request):
        """
        Récupère les dents d'un patient en fonction de son âge
        """
        try:
            patient = DentistryPatient.objects.get(patient=request.user)
            patient_age = request.query_params.get('age')

            if not patient_age:
                return Response({
                    'error': 'age parameter is required'
                }, status=status.HTTP_400_BAD_REQUEST)

            patient_age = int(patient_age)

            # Filtrer les dents selon l'âge
            configurations = DentalSvgConfiguration.objects.filter(
                patient=patient
            ).filter(
                models.Q(age_restriction__isnull=True) |
                models.Q(age_restriction__lte=patient_age)
            ).prefetch_related('svg_paths')

            serializer = self.get_serializer(configurations, many=True)
            return Response({
                'patient_age': patient_age,
                'configurations': serializer.data
            }, status=status.HTTP_200_OK)

        except DentistryPatient.DoesNotExist:
            return Response({
                'error': 'Patient profile not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except ValueError:
            return Response({
                'error': 'Invalid age parameter'
            }, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class DentalSvgPathViewSet(viewsets.ModelViewSet):
    """
    ViewSet pour les paths SVG individuels
    """
    serializer_class = DentalSvgPathSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return DentalSvgPath.objects.filter(
            dental_svg_config__patient__patient=self.request.user
        )


class DentalTreatmentTemplateViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet pour les templates de traitement (lecture seule)
    """
    queryset = DentalTreatmentTemplate.objects.all()
    serializer_class = DentalTreatmentTemplateSerializer
    permission_classes = [IsAuthenticated]

    @action(detail=False, methods=['get'])
    def get_by_age(self, request):
        """
        Récupère les templates de traitement disponibles selon l'âge
        """
        patient_age = request.query_params.get('age')

        if not patient_age:
            return Response({
                'error': 'age parameter is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            patient_age = int(patient_age)

            templates = DentalTreatmentTemplate.objects.filter(
                models.Q(min_age__isnull=True) |
                models.Q(min_age__lte=patient_age)
            )

            serializer = self.get_serializer(templates, many=True)
            return Response({
                'patient_age': patient_age,
                'templates': serializer.data
            }, status=status.HTTP_200_OK)

        except ValueError:
            return Response({
                'error': 'Invalid age parameter'
            }, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
