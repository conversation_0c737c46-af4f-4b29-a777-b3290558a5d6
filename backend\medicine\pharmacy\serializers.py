from rest_framework import serializers
from .models import (
    Supplier, Depot, ProductCategory, Product, 
    PurchaseRequest, PurchaseRequestItem, 
    Inventory, StockMovement
)


class SupplierSerializer(serializers.ModelSerializer):
    """Serializer for Supplier model"""
    
    class Meta:
        model = Supplier
        fields = [
            'id', 'raison_sociale', 'email', 'ville', 'tel_gestionnaire',
            'tel_fixe', 'fax', 'adresse', 'commentaire', 'mode_paiement',
            'condition_paiement', 'ice', 'rc', 'directeur_commercial',
            'gestionnaire_vente', 'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class DepotSerializer(serializers.ModelSerializer):
    """Serializer for Depot model"""
    
    class Meta:
        model = Depot
        fields = [
            'id', 'name', 'code', 'address', 'manager', 'phone',
            'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class ProductCategorySerializer(serializers.ModelSerializer):
    """Serializer for ProductCategory model"""
    
    class Meta:
        model = ProductCategory
        fields = [
            'id', 'name', 'code', 'description',
            'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class ProductSerializer(serializers.ModelSerializer):
    """Serializer for Product model"""
    category_name = serializers.CharField(source='category.name', read_only=True)
    
    class Meta:
        model = Product
        fields = [
            'id', 'code', 'designation', 'category', 'category_name',
            'unit', 'price', 'barcode', 'description', 'min_stock',
            'max_stock', 'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class PurchaseRequestItemSerializer(serializers.ModelSerializer):
    """Serializer for PurchaseRequestItem model"""
    product_code = serializers.CharField(source='product.code', read_only=True)
    product_designation = serializers.CharField(source='product.designation', read_only=True)
    depot_name = serializers.CharField(source='depot.name', read_only=True)
    
    class Meta:
        model = PurchaseRequestItem
        fields = [
            'id', 'product', 'product_code', 'product_designation',
            'depot', 'depot_name', 'quantity', 'unit_price', 'total_price'
        ]
        read_only_fields = ['id', 'total_price']


class PurchaseRequestSerializer(serializers.ModelSerializer):
    """Serializer for PurchaseRequest model"""
    items = PurchaseRequestItemSerializer(many=True, read_only=True)
    supplier_name = serializers.CharField(source='supplier.raison_sociale', read_only=True)
    
    class Meta:
        model = PurchaseRequest
        fields = [
            'id', 'numero', 'date', 'date_echeance', 'urgent',
            'supplier', 'supplier_name', 'commentaire', 'status',
            'items', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class PurchaseRequestCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating PurchaseRequest with items"""
    items = PurchaseRequestItemSerializer(many=True)
    
    class Meta:
        model = PurchaseRequest
        fields = [
            'numero', 'date', 'date_echeance', 'urgent',
            'supplier', 'commentaire', 'status', 'items'
        ]
    
    def create(self, validated_data):
        items_data = validated_data.pop('items')
        purchase_request = PurchaseRequest.objects.create(**validated_data)
        
        for item_data in items_data:
            PurchaseRequestItem.objects.create(
                purchase_request=purchase_request,
                **item_data
            )
        
        return purchase_request


class InventorySerializer(serializers.ModelSerializer):
    """Serializer for Inventory model"""
    product_code = serializers.CharField(source='product.code', read_only=True)
    product_designation = serializers.CharField(source='product.designation', read_only=True)
    depot_name = serializers.CharField(source='depot.name', read_only=True)
    available_quantity = serializers.ReadOnlyField()
    
    class Meta:
        model = Inventory
        fields = [
            'id', 'product', 'product_code', 'product_designation',
            'depot', 'depot_name', 'quantity', 'reserved_quantity',
            'available_quantity', 'last_movement_date'
        ]
        read_only_fields = ['id', 'last_movement_date']


class StockMovementSerializer(serializers.ModelSerializer):
    """Serializer for StockMovement model"""
    product_code = serializers.CharField(source='product.code', read_only=True)
    product_designation = serializers.CharField(source='product.designation', read_only=True)
    depot_name = serializers.CharField(source='depot.name', read_only=True)
    
    class Meta:
        model = StockMovement
        fields = [
            'id', 'product', 'product_code', 'product_designation',
            'depot', 'depot_name', 'movement_type', 'quantity',
            'reference', 'reason', 'date'
        ]
        read_only_fields = ['id', 'date']


# Simplified serializers for dropdown/select options
class SupplierSimpleSerializer(serializers.ModelSerializer):
    """Simple serializer for supplier dropdown"""
    
    class Meta:
        model = Supplier
        fields = ['id', 'raison_sociale']


class DepotSimpleSerializer(serializers.ModelSerializer):
    """Simple serializer for depot dropdown"""
    
    class Meta:
        model = Depot
        fields = ['id', 'name', 'code']


class ProductSimpleSerializer(serializers.ModelSerializer):
    """Simple serializer for product dropdown"""
    
    class Meta:
        model = Product
        fields = ['id', 'code', 'designation', 'unit', 'price']
