# backend/dental_medicine/dentistry/models_anatomy.py

from django.db import models
from django.utils.translation import gettext_lazy as _
import uuid
import json

class ToothAnatomicalPart(models.Model):
    """
    Parties anatomiques de chaque dent (9 parties par dent)
    """
    SECTION_CHOICES = [
        ('crown', 'Section Coronaire'),
        ('middle', 'Section Médiane'),
        ('root', 'Section Basale (Racines)'),
    ]
    
    PART_CHOICES = [
        # Section Coronaire
        ('crown_mesial', 'Couronne Mésiale'),
        ('crown_distal', 'Couronne Distale'),
        ('crown_central', 'Couronne Centrale'),
        
        # Section Médiane
        ('middle_cervical', 'Médiane Cervicale'),
        ('middle_body', 'Corps Médian'),
        ('middle_gingival', 'Médiane Gingivale'),
        
        # Section Basale
        ('root_mesial', 'Racine <PERSON>ial<PERSON>'),
        ('root_distal', 'Racine Distale'),
        ('root_central', '<PERSON>cine Centrale'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    tooth = models.ForeignKey('Tooth', on_delete=models.CASCADE, related_name='anatomical_parts')
    
    # Identification anatomique
    anatomical_code = models.CharField(max_length=50, unique=True, help_text="Ex: T11_CR_M (Tooth 11, Crown, Mesial)")
    section = models.CharField(max_length=20, choices=SECTION_CHOICES)
    part_type = models.CharField(max_length=30, choices=PART_CHOICES)
    part_name = models.CharField(max_length=100)
    
    # Propriétés visuelles
    default_color = models.CharField(max_length=7, default='#FFFFFF')
    current_color = models.CharField(max_length=7, default='#FFFFFF')
    
    # État de modification
    is_modified = models.BooleanField(default=False)
    modification_count = models.PositiveIntegerField(default=0)
    
    # Métadonnées
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['tooth__tooth_number', 'section', 'part_type']
        verbose_name = _('Tooth Anatomical Part')
        verbose_name_plural = _('Tooth Anatomical Parts')
    
    def __str__(self):
        return f"{self.tooth.tooth_number} - {self.part_name}"


class ToothWall(models.Model):
    """
    Parois de chaque partie anatomique (buccale, linguale, occlusale, etc.)
    """
    WALL_CHOICES = [
        ('buccal', 'Buccale'),
        ('lingual', 'Linguale'),
        ('mesial', 'Mésiale'),
        ('distal', 'Distale'),
        ('occlusal', 'Occlusale'),
        ('cervical', 'Cervicale'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    anatomical_part = models.ForeignKey(ToothAnatomicalPart, on_delete=models.CASCADE, related_name='walls')
    
    # Identification de la paroi
    wall_code = models.CharField(max_length=60, unique=True, help_text="Ex: T11_CR_M_BUC (Tooth 11, Crown, Mesial, Buccal)")
    wall_type = models.CharField(max_length=20, choices=WALL_CHOICES)
    wall_name = models.CharField(max_length=100)
    
    # État de remplissage
    is_filled = models.BooleanField(default=False)
    filling_material = models.CharField(max_length=100, blank=True, null=True)
    filling_color = models.CharField(max_length=7, blank=True, null=True)
    
    # Métadonnées
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['anatomical_part', 'wall_type']
        verbose_name = _('Tooth Wall')
        verbose_name_plural = _('Tooth Walls')
    
    def __str__(self):
        return f"{self.anatomical_part} - {self.wall_name}"


class ToothModificationStatus(models.Model):
    """
    Statuts de modifications possibles pour chaque spécialité
    """
    SPECIALTY_CHOICES = [
        ('esthetic', 'Dentisterie Esthétique'),
        ('prosthetic', 'Prothèses Thérapeutiques'),
        ('surgery', 'Chirurgie'),
        ('orthodontics', 'Orthopédie'),
    ]
    
    MODIFICATION_CHOICES = [
        # Dentisterie Esthétique
        ('color_change', 'Changement de Couleur'),
        ('whitening', 'Blanchiment'),
        ('veneer', 'Facette'),
        ('composite', 'Composite Esthétique'),
        
        # Prothèses Thérapeutiques
        ('crown', 'Couronne'),
        ('bridge', 'Bridge'),
        ('implant', 'Implant'),
        ('filling', 'Obturation'),
        
        # Chirurgie
        ('extraction', 'Extraction'),
        ('surgery', 'Chirurgie'),
        ('bone_graft', 'Greffe Osseuse'),
        ('cavity', 'Carie'),
        
        # Orthopédie
        ('braces', 'Appareil Orthodontique'),
        ('aligners', 'Gouttières'),
        ('movement', 'Déplacement'),
    ]
    
    STATUS_CHOICES = [
        ('not_applied', 'Non Appliqué'),
        ('planned', 'Planifié'),
        ('in_progress', 'En Cours'),
        ('applied', 'Appliqué'),
        ('completed', 'Terminé'),
        ('cancelled', 'Annulé'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    
    # Identification du statut
    status_code = models.CharField(max_length=100, unique=True, help_text="Ex: T11_CR_M_FILLING_APPLIED")
    specialty = models.CharField(max_length=20, choices=SPECIALTY_CHOICES)
    modification_type = models.CharField(max_length=30, choices=MODIFICATION_CHOICES)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='not_applied')
    
    # Description
    name = models.CharField(max_length=200)
    description = models.TextField(blank=True, null=True)
    
    # Propriétés visuelles
    color = models.CharField(max_length=7, default='#2196F3')
    icon = models.CharField(max_length=50, blank=True, null=True)
    
    # Métadonnées
    order = models.PositiveIntegerField(default=0)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['specialty', 'modification_type', 'order']
        verbose_name = _('Tooth Modification Status')
        verbose_name_plural = _('Tooth Modification Statuses')
    
    def __str__(self):
        return f"{self.specialty} - {self.name}"


class ToothModification(models.Model):
    """
    Modifications appliquées à une partie anatomique ou paroi spécifique
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    
    # Relations
    tooth = models.ForeignKey('Tooth', on_delete=models.CASCADE, related_name='modifications')
    anatomical_part = models.ForeignKey(ToothAnatomicalPart, on_delete=models.CASCADE, null=True, blank=True)
    wall = models.ForeignKey(ToothWall, on_delete=models.CASCADE, null=True, blank=True)
    modification_status = models.ForeignKey(ToothModificationStatus, on_delete=models.CASCADE)
    
    # Identification unique
    modification_code = models.CharField(max_length=150, unique=True, help_text="Code unique de la modification")
    
    # État de la modification
    is_applied = models.BooleanField(default=False)
    application_date = models.DateTimeField(null=True, blank=True)
    
    # Détails de la modification
    color = models.CharField(max_length=7, blank=True, null=True)
    material = models.CharField(max_length=100, blank=True, null=True)
    notes = models.TextField(blank=True, null=True)
    
    # Données personnalisées (JSON)
    custom_data = models.JSONField(default=dict, blank=True)
    
    # Métadonnées
    created_by = models.ForeignKey('users.User', on_delete=models.SET_NULL, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-created_at']
        verbose_name = _('Tooth Modification')
        verbose_name_plural = _('Tooth Modifications')
    
    def __str__(self):
        return f"{self.tooth.tooth_number} - {self.modification_status.name} ({'Appliqué' if self.is_applied else 'Non appliqué'})"
    
    def save(self, *args, **kwargs):
        # Générer automatiquement le code de modification
        if not self.modification_code:
            base_code = ""
            if self.anatomical_part:
                base_code = self.anatomical_part.anatomical_code
            elif self.wall:
                base_code = self.wall.wall_code
            
            status_suffix = f"{self.modification_status.modification_type.upper()}_{'APPLIED' if self.is_applied else 'NOT_APPLIED'}"
            self.modification_code = f"{base_code}_{status_suffix}"
        
        super().save(*args, **kwargs)


class ToothModificationHistory(models.Model):
    """
    Historique des modifications pour traçabilité
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    modification = models.ForeignKey(ToothModification, on_delete=models.CASCADE, related_name='history')
    
    # Changement d'état
    previous_status = models.CharField(max_length=20, blank=True, null=True)
    new_status = models.CharField(max_length=20)
    change_reason = models.TextField(blank=True, null=True)
    
    # Données avant/après
    previous_data = models.JSONField(default=dict, blank=True)
    new_data = models.JSONField(default=dict, blank=True)
    
    # Métadonnées
    changed_by = models.ForeignKey('users.User', on_delete=models.SET_NULL, null=True, blank=True)
    changed_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['-changed_at']
        verbose_name = _('Tooth Modification History')
        verbose_name_plural = _('Tooth Modification Histories')
    
    def __str__(self):
        return f"{self.modification} - {self.changed_at.strftime('%Y-%m-%d %H:%M')}"
