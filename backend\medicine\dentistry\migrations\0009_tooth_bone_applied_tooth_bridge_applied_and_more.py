# Generated by Django 4.2.7 on 2025-05-29 16:15

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('dentistry', '0008_populate_tooth_fields'),
    ]

    operations = [
        migrations.AddField(
            model_name='tooth',
            name='bone_applied',
            field=models.BooleanField(default=False, help_text='Bone - IDs 61, 62, 63', verbose_name='Bone Applied'),
        ),
        migrations.AddField(
            model_name='tooth',
            name='bridge_applied',
            field=models.BooleanField(default=False, help_text='Bridge - IDs 51, 52', verbose_name='Bridge Applied'),
        ),
        migrations.AddField(
            model_name='tooth',
            name='cleaning_applied',
            field=models.BooleanField(default=False, help_text='Nettoyage - ID 17', verbose_name='Cleaning Applied'),
        ),
        migrations.AddField(
            model_name='tooth',
            name='crown_applied',
            field=models.BooleanField(default=False, help_text='Couronne - IDs 40, 41', verbose_name='Crown Applied'),
        ),
        migrations.AddField(
            model_name='tooth',
            name='crown_gold_applied',
            field=models.BooleanField(default=False, help_text='Couronne Or - IDs 43, 44', verbose_name='Gold Crown Applied'),
        ),
        migrations.AddField(
            model_name='tooth',
            name='crown_zirconia_applied',
            field=models.BooleanField(default=False, help_text='Couronne Zirconia - IDs 45, 46, 47', verbose_name='Zirconia Crown Applied'),
        ),
        migrations.AddField(
            model_name='tooth',
            name='current_color',
            field=models.CharField(blank=True, help_text='Current applied color for the tooth', max_length=20, null=True, verbose_name='Current Color'),
        ),
        migrations.AddField(
            model_name='tooth',
            name='denture_applied',
            field=models.BooleanField(default=False, help_text='Prothèse - IDs 48, 49, 50', verbose_name='Denture Applied'),
        ),
        migrations.AddField(
            model_name='tooth',
            name='fluoride_applied',
            field=models.BooleanField(default=False, help_text='Fluorure - ID 18', verbose_name='Fluoride Applied'),
        ),
        migrations.AddField(
            model_name='tooth',
            name='implant_applied',
            field=models.BooleanField(default=False, help_text='Implant - IDs 54-60', verbose_name='Implant Applied'),
        ),
        migrations.AddField(
            model_name='tooth',
            name='is_hidden',
            field=models.BooleanField(default=False, help_text='Hide this tooth from display', verbose_name='Is Hidden'),
        ),
        migrations.AddField(
            model_name='tooth',
            name='onlay_applied',
            field=models.BooleanField(default=False, help_text='Onlay - IDs 38, 39', verbose_name='Onlay Applied'),
        ),
        migrations.AddField(
            model_name='tooth',
            name='post_care_applied',
            field=models.BooleanField(default=False, help_text='Post Care - ID 36', verbose_name='Post Care Applied'),
        ),
        migrations.AddField(
            model_name='tooth',
            name='resection_applied',
            field=models.BooleanField(default=False, help_text='Résection - Various IDs', verbose_name='Resection Applied'),
        ),
        migrations.AddField(
            model_name='tooth',
            name='restoration_amalgam_applied',
            field=models.BooleanField(default=False, help_text='Restauration Amalgame - ID 26', verbose_name='Amalgam Restoration Applied'),
        ),
        migrations.AddField(
            model_name='tooth',
            name='restoration_glass_ionomer_applied',
            field=models.BooleanField(default=False, help_text='Restauration Verre Ionomère - ID 27', verbose_name='Glass Ionomer Restoration Applied'),
        ),
        migrations.AddField(
            model_name='tooth',
            name='restoration_temporary_applied',
            field=models.BooleanField(default=False, help_text='Restauration Temporaire - IDs 24, 25', verbose_name='Temporary Restoration Applied'),
        ),
        migrations.AddField(
            model_name='tooth',
            name='root_calcium_applied',
            field=models.BooleanField(default=False, help_text='Root Calcium - IDs 30-33', verbose_name='Root Calcium Applied'),
        ),
        migrations.AddField(
            model_name='tooth',
            name='root_gutta_percha_applied',
            field=models.BooleanField(default=False, help_text='Root Gutta Percha Mode - IDs 34, 35', verbose_name='Root Gutta Percha Applied'),
        ),
        migrations.AddField(
            model_name='tooth',
            name='root_temporary_applied',
            field=models.BooleanField(default=False, help_text='Root Temporaire - IDs 28, 29', verbose_name='Root Temporary Applied'),
        ),
        migrations.AddField(
            model_name='tooth',
            name='sealant_applied',
            field=models.BooleanField(default=False, help_text='Scellant - ID 19', verbose_name='Sealant Applied'),
        ),
        migrations.AddField(
            model_name='tooth',
            name='svg_id',
            field=models.CharField(blank=True, help_text='SVG identifier from Tdantal.ts', max_length=10, null=True, verbose_name='SVG ID'),
        ),
        migrations.AddField(
            model_name='tooth',
            name='svg_position',
            field=models.CharField(blank=True, help_text="SVG viewBox position (e.g., '0 0 50.8 172')", max_length=50, null=True, verbose_name='SVG Position'),
        ),
        migrations.AddField(
            model_name='tooth',
            name='svg_width',
            field=models.CharField(blank=True, help_text="SVG width (e.g., '59.8625px')", max_length=20, null=True, verbose_name='SVG Width'),
        ),
        migrations.AddField(
            model_name='tooth',
            name='veneer_applied',
            field=models.BooleanField(default=False, help_text='Facettes - ID 37', verbose_name='Veneer Applied'),
        ),
        migrations.AddField(
            model_name='tooth',
            name='visible_adult',
            field=models.BooleanField(default=True, help_text='Show this tooth for adult patients', verbose_name='Visible Adult'),
        ),
        migrations.AddField(
            model_name='tooth',
            name='visible_under_12_years',
            field=models.BooleanField(default=True, help_text='Show this tooth for patients under 12 years', verbose_name='Visible Under 12 Years'),
        ),
        migrations.AddField(
            model_name='tooth',
            name='visible_under_13_5_years',
            field=models.BooleanField(default=True, help_text='Show this tooth for patients under 13.5 years', verbose_name='Visible Under 13.5 Years'),
        ),
        migrations.AddField(
            model_name='tooth',
            name='visible_under_6_years',
            field=models.BooleanField(default=True, help_text='Show this tooth for patients under 6 years', verbose_name='Visible Under 6 Years'),
        ),
        migrations.AddField(
            model_name='tooth',
            name='visible_under_7_5_years',
            field=models.BooleanField(default=True, help_text='Show this tooth for patients under 7.5 years', verbose_name='Visible Under 7.5 Years'),
        ),
        migrations.AddField(
            model_name='tooth',
            name='whitening_applied',
            field=models.BooleanField(default=False, help_text='Blanchiment - IDs 20, 21, 22, 23', verbose_name='Whitening Applied'),
        ),
    ]
