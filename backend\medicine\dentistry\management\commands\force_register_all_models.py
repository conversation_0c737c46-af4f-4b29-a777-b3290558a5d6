"""
Commande pour forcer l'enregistrement de tous les modèles dentistry dans l'administration
"""
from django.core.management.base import BaseCommand
from django.contrib import admin
from django.apps import apps


class Command(BaseCommand):
    help = 'Force register all dentistry models in admin'

    def handle(self, *args, **options):
        """
        Force l'enregistrement de tous les modèles dentistry dans l'administration
        """
        self.stdout.write(self.style.SUCCESS('Force registering all dentistry models...'))
        
        # Obtenir tous les modèles de l'application dentistry
        try:
            dentistry_models = list(apps.get_app_config('dentistry').get_models())
            self.stdout.write(f'Found {len(dentistry_models)} models in dentistry app')
            
            registered_count = 0
            already_registered_count = 0
            error_count = 0
            
            for model in dentistry_models:
                model_name = model.__name__
                try:
                    if not admin.site.is_registered(model):
                        admin.site.register(model)
                        registered_count += 1
                        self.stdout.write(f'✅ Registered: {model_name}')
                    else:
                        already_registered_count += 1
                        self.stdout.write(f'⚠️  Already registered: {model_name}')
                except Exception as e:
                    error_count += 1
                    self.stdout.write(f'❌ Error registering {model_name}: {e}')
            
            # Résumé
            self.stdout.write(f'\n📊 Registration Summary:')
            self.stdout.write(f'✅ Newly registered: {registered_count}')
            self.stdout.write(f'⚠️  Already registered: {already_registered_count}')
            self.stdout.write(f'❌ Errors: {error_count}')
            self.stdout.write(f'📋 Total models: {len(dentistry_models)}')
            
            # Vérifier l'état final
            final_registered = sum(1 for model in dentistry_models if admin.site.is_registered(model))
            self.stdout.write(f'🎯 Final registered count: {final_registered}/{len(dentistry_models)}')
            
            # Lister tous les modèles enregistrés
            self.stdout.write(f'\n📋 All registered models:')
            all_registered = []
            for model in admin.site._registry:
                all_registered.append(model.__name__)
            
            for model_name in sorted(all_registered):
                self.stdout.write(f'   - {model_name}')
            
            self.stdout.write(f'\nTotal registered models in admin: {len(all_registered)}')
            
            if final_registered == len(dentistry_models):
                self.stdout.write(
                    self.style.SUCCESS('🎉 All dentistry models are now registered!')
                )
            else:
                missing = len(dentistry_models) - final_registered
                self.stdout.write(
                    self.style.WARNING(f'⚠️  {missing} dentistry models are still not registered')
                )
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error: {e}'))
        
        self.stdout.write(self.style.SUCCESS('\n✨ Force registration completed!'))
