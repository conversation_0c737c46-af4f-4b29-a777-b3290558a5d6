"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-textarea-autosize";
exports.ids = ["vendor-chunks/react-textarea-autosize"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-textarea-autosize/dist/react-textarea-autosize.development.esm.js":
/*!**********************************************************************************************!*\
  !*** ./node_modules/react-textarea-autosize/dist/react-textarea-autosize.development.esm.js ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ index)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutPropertiesLoose */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var use_latest__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! use-latest */ \"(ssr)/./node_modules/use-latest/dist/use-latest.esm.js\");\n/* harmony import */ var use_composed_ref__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! use-composed-ref */ \"(ssr)/./node_modules/use-composed-ref/dist/use-composed-ref.esm.js\");\n\n\n\n\n\n\nvar isBrowser = typeof document !== 'undefined';\n\nvar HIDDEN_TEXTAREA_STYLE = {\n  'min-height': '0',\n  'max-height': 'none',\n  height: '0',\n  visibility: 'hidden',\n  overflow: 'hidden',\n  position: 'absolute',\n  'z-index': '-1000',\n  top: '0',\n  right: '0',\n  display: 'block'\n};\nvar forceHiddenStyles = function forceHiddenStyles(node) {\n  Object.keys(HIDDEN_TEXTAREA_STYLE).forEach(function (key) {\n    node.style.setProperty(key, HIDDEN_TEXTAREA_STYLE[key], 'important');\n  });\n};\nvar forceHiddenStyles$1 = forceHiddenStyles;\n\nvar hiddenTextarea = null;\nvar getHeight = function getHeight(node, sizingData) {\n  var height = node.scrollHeight;\n  if (sizingData.sizingStyle.boxSizing === 'border-box') {\n    // border-box: add border, since height = content + padding + border\n    return height + sizingData.borderSize;\n  }\n\n  // remove padding, since height = content\n  return height - sizingData.paddingSize;\n};\nfunction calculateNodeHeight(sizingData, value, minRows, maxRows) {\n  if (minRows === void 0) {\n    minRows = 1;\n  }\n  if (maxRows === void 0) {\n    maxRows = Infinity;\n  }\n  if (!hiddenTextarea) {\n    hiddenTextarea = document.createElement('textarea');\n    hiddenTextarea.setAttribute('tabindex', '-1');\n    hiddenTextarea.setAttribute('aria-hidden', 'true');\n    forceHiddenStyles$1(hiddenTextarea);\n  }\n  if (hiddenTextarea.parentNode === null) {\n    document.body.appendChild(hiddenTextarea);\n  }\n  var paddingSize = sizingData.paddingSize,\n    borderSize = sizingData.borderSize,\n    sizingStyle = sizingData.sizingStyle;\n  var boxSizing = sizingStyle.boxSizing;\n  Object.keys(sizingStyle).forEach(function (_key) {\n    var key = _key;\n    hiddenTextarea.style[key] = sizingStyle[key];\n  });\n  forceHiddenStyles$1(hiddenTextarea);\n  hiddenTextarea.value = value;\n  var height = getHeight(hiddenTextarea, sizingData);\n  // Double set and calc due to Firefox bug: https://bugzilla.mozilla.org/show_bug.cgi?id=1795904\n  hiddenTextarea.value = value;\n  height = getHeight(hiddenTextarea, sizingData);\n\n  // measure height of a textarea with a single row\n  hiddenTextarea.value = 'x';\n  var rowHeight = hiddenTextarea.scrollHeight - paddingSize;\n  var minHeight = rowHeight * minRows;\n  if (boxSizing === 'border-box') {\n    minHeight = minHeight + paddingSize + borderSize;\n  }\n  height = Math.max(minHeight, height);\n  var maxHeight = rowHeight * maxRows;\n  if (boxSizing === 'border-box') {\n    maxHeight = maxHeight + paddingSize + borderSize;\n  }\n  height = Math.min(maxHeight, height);\n  return [height, rowHeight];\n}\n\nvar noop = function noop() {};\nvar pick = function pick(props, obj) {\n  return props.reduce(function (acc, prop) {\n    acc[prop] = obj[prop];\n    return acc;\n  }, {});\n};\n\nvar SIZING_STYLE = ['borderBottomWidth', 'borderLeftWidth', 'borderRightWidth', 'borderTopWidth', 'boxSizing', 'fontFamily', 'fontSize', 'fontStyle', 'fontWeight', 'letterSpacing', 'lineHeight', 'paddingBottom', 'paddingLeft', 'paddingRight', 'paddingTop',\n// non-standard\n'tabSize', 'textIndent',\n// non-standard\n'textRendering', 'textTransform', 'width', 'wordBreak', 'wordSpacing', 'scrollbarGutter'];\nvar isIE = isBrowser ? !!document.documentElement.currentStyle : false;\nvar getSizingData = function getSizingData(node) {\n  var style = window.getComputedStyle(node);\n  if (style === null) {\n    return null;\n  }\n  var sizingStyle = pick(SIZING_STYLE, style);\n  var boxSizing = sizingStyle.boxSizing;\n\n  // probably node is detached from DOM, can't read computed dimensions\n  if (boxSizing === '') {\n    return null;\n  }\n\n  // IE (Edge has already correct behaviour) returns content width as computed width\n  // so we need to add manually padding and border widths\n  if (isIE && boxSizing === 'border-box') {\n    sizingStyle.width = parseFloat(sizingStyle.width) + parseFloat(sizingStyle.borderRightWidth) + parseFloat(sizingStyle.borderLeftWidth) + parseFloat(sizingStyle.paddingRight) + parseFloat(sizingStyle.paddingLeft) + 'px';\n  }\n  var paddingSize = parseFloat(sizingStyle.paddingBottom) + parseFloat(sizingStyle.paddingTop);\n  var borderSize = parseFloat(sizingStyle.borderBottomWidth) + parseFloat(sizingStyle.borderTopWidth);\n  return {\n    sizingStyle: sizingStyle,\n    paddingSize: paddingSize,\n    borderSize: borderSize\n  };\n};\nvar getSizingData$1 = getSizingData;\n\nfunction useListener(target, type, listener) {\n  var latestListener = (0,use_latest__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(listener);\n  react__WEBPACK_IMPORTED_MODULE_2__.useLayoutEffect(function () {\n    var handler = function handler(ev) {\n      return latestListener.current(ev);\n    };\n    // might happen if document.fonts is not defined, for instance\n    if (!target) {\n      return;\n    }\n    target.addEventListener(type, handler);\n    return function () {\n      return target.removeEventListener(type, handler);\n    };\n  }, []);\n}\nvar useFormResetListener = function useFormResetListener(libRef, listener) {\n  useListener(document.body, 'reset', function (ev) {\n    if (libRef.current.form === ev.target) {\n      listener(ev);\n    }\n  });\n};\nvar useWindowResizeListener = function useWindowResizeListener(listener) {\n  useListener(window, 'resize', listener);\n};\nvar useFontsLoadedListener = function useFontsLoadedListener(listener) {\n  useListener(document.fonts, 'loadingdone', listener);\n};\n\nvar _excluded = [\"cacheMeasurements\", \"maxRows\", \"minRows\", \"onChange\", \"onHeightChange\"];\nvar TextareaAutosize = function TextareaAutosize(_ref, userRef) {\n  var cacheMeasurements = _ref.cacheMeasurements,\n    maxRows = _ref.maxRows,\n    minRows = _ref.minRows,\n    _ref$onChange = _ref.onChange,\n    onChange = _ref$onChange === void 0 ? noop : _ref$onChange,\n    _ref$onHeightChange = _ref.onHeightChange,\n    onHeightChange = _ref$onHeightChange === void 0 ? noop : _ref$onHeightChange,\n    props = (0,_babel_runtime_helpers_esm_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_ref, _excluded);\n  if (props.style) {\n    if ('maxHeight' in props.style) {\n      throw new Error('Using `style.maxHeight` for <TextareaAutosize/> is not supported. Please use `maxRows`.');\n    }\n    if ('minHeight' in props.style) {\n      throw new Error('Using `style.minHeight` for <TextareaAutosize/> is not supported. Please use `minRows`.');\n    }\n  }\n  var isControlled = props.value !== undefined;\n  var libRef = react__WEBPACK_IMPORTED_MODULE_2__.useRef(null);\n  var ref = (0,use_composed_ref__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(libRef, userRef);\n  var heightRef = react__WEBPACK_IMPORTED_MODULE_2__.useRef(0);\n  var measurementsCacheRef = react__WEBPACK_IMPORTED_MODULE_2__.useRef();\n  var resizeTextarea = function resizeTextarea() {\n    var node = libRef.current;\n    var nodeSizingData = cacheMeasurements && measurementsCacheRef.current ? measurementsCacheRef.current : getSizingData$1(node);\n    if (!nodeSizingData) {\n      return;\n    }\n    measurementsCacheRef.current = nodeSizingData;\n    var _calculateNodeHeight = calculateNodeHeight(nodeSizingData, node.value || node.placeholder || 'x', minRows, maxRows),\n      height = _calculateNodeHeight[0],\n      rowHeight = _calculateNodeHeight[1];\n    if (heightRef.current !== height) {\n      heightRef.current = height;\n      node.style.setProperty('height', height + \"px\", 'important');\n      onHeightChange(height, {\n        rowHeight: rowHeight\n      });\n    }\n  };\n  var handleChange = function handleChange(event) {\n    if (!isControlled) {\n      resizeTextarea();\n    }\n    onChange(event);\n  };\n  if (isBrowser) {\n    react__WEBPACK_IMPORTED_MODULE_2__.useLayoutEffect(resizeTextarea);\n    useFormResetListener(libRef, function () {\n      if (!isControlled) {\n        var currentValue = libRef.current.value;\n        requestAnimationFrame(function () {\n          var node = libRef.current;\n          if (node && currentValue !== node.value) {\n            resizeTextarea();\n          }\n        });\n      }\n    });\n    useWindowResizeListener(resizeTextarea);\n    useFontsLoadedListener(resizeTextarea);\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(\"textarea\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, props, {\n      onChange: handleChange,\n      ref: ref\n    }));\n  }\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(\"textarea\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, props, {\n    onChange: onChange,\n    ref: ref\n  }));\n};\nvar index = /* #__PURE__ */react__WEBPACK_IMPORTED_MODULE_2__.forwardRef(TextareaAutosize);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-textarea-autosize/dist/react-textarea-autosize.development.esm.js\n");

/***/ })

};
;