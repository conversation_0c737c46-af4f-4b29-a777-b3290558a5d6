"""
Patient models for the dentistry application.
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from users.models import User
from django.db.models import Model

class DentistryPatient(Model):
    """
    Dentistry-specific patient model.
    """
    patient = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name="dentistry_profile",
        verbose_name=_("Patient")
    )

    # Dentistry-specific fields
    dental_history = models.TextField(
        verbose_name=_("Dental History"),
        blank=True
    )
    last_cleaning_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_("Last Cleaning Date")
    )
    has_dentures = models.BooleanField(
        default=False,
        verbose_name=_("Has Dentures")
    )
    has_implants = models.BooleanField(
        default=False,
        verbose_name=_("Has Implants")
    )
    has_braces = models.BooleanField(
        default=False,
        verbose_name=_("Has Braces/Orthodontics")
    )
    teeth_grinding = models.BooleanField(
        default=False,
        verbose_name=_("Teeth Grinding/Bruxism")
    )
    sensitive_teeth = models.BooleanField(
        default=False,
        verbose_name=_("Sensitive Teeth")
    )
    teeth_chart = models.JSONField(
        default=dict,
        verbose_name=_("Teeth Chart"),
        help_text=_("JSON representation of the patient's teeth chart")
    )

    # Dental insurance information
    dental_insurance_provider = models.CharField(
        max_length=100,
        blank=True,
        verbose_name=_("Dental Insurance Provider")
    )
    dental_insurance_policy_number = models.CharField(
        max_length=100,
        blank=True,
        verbose_name=_("Dental Insurance Policy Number")
    )

    class Meta:
        verbose_name = _("Dentistry Patient")
        verbose_name_plural = _("Dentistry Patients")

    def __str__(self):
        return f"Dentistry Profile: {self.patient.get_full_name()}"

    @property
    def full_name(self):
        """
        Returns the patient's full name.
        """
        return self.patient.get_full_name()

    def initialize_teeth_chart(self):
        """
        Initialize an empty teeth chart for the patient.
        """
        # Standard adult teeth chart (32 teeth)
        adult_teeth = {
            str(i): {
                "status": "normal",
                "procedures": [],
                "notes": ""
            } for i in range(1, 33)
        }

        self.teeth_chart = {
            "adult": adult_teeth,
            "last_updated": None
        }
        self.save(update_fields=['teeth_chart'])

    def update_tooth_status(self, tooth_number, status, notes=None):
        """
        Update the status of a specific tooth.

        Args:
            tooth_number: The number of the tooth (1-32 for adult)
            status: The new status (normal, cavity, filling, crown, missing, etc.)
            notes: Optional notes about the tooth
        """
        if not self.teeth_chart:
            self.initialize_teeth_chart()

        tooth_key = str(tooth_number)

        # Ensure the tooth exists in the chart
        if tooth_key not in self.teeth_chart.get("adult", {}):
            return False

        # Update the tooth status
        self.teeth_chart["adult"][tooth_key]["status"] = status

        # Update notes if provided
        if notes:
            self.teeth_chart["adult"][tooth_key]["notes"] = notes

        # Update the last updated timestamp
        from datetime import datetime
        self.teeth_chart["last_updated"] = datetime.now().isoformat()

        self.save(update_fields=['teeth_chart'])
        return True
