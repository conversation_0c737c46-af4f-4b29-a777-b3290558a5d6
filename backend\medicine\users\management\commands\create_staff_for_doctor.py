"""
Management command to create assistants and staff for a specific doctor.
This command respects subscription limits and provides better control over staff creation.

Usage:
    python manage.py create_staff_for_doctor --doctor-email <EMAIL> --assistants 2 --staff 1
"""

from django.core.management.base import BaseCommand, CommandError
from django.contrib.auth import get_user_model
from django.utils import timezone
import random

User = get_user_model()


class Command(BaseCommand):
    help = 'Create assistants and staff for a specific doctor'

    def add_arguments(self, parser):
        parser.add_argument(
            '--doctor-email',
            type=str,
            required=True,
            help='Email of the doctor for whom to create staff'
        )
        parser.add_argument(
            '--assistants',
            type=int,
            default=0,
            help='Number of assistants to create (default: 0)'
        )
        parser.add_argument(
            '--staff',
            type=int,
            default=0,
            help='Number of staff members to create (default: 0)'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be created without actually creating anything'
        )

    def handle(self, *args, **options):
        doctor_email = options['doctor_email']
        assistants_count = options['assistants']
        staff_count = options['staff']
        dry_run = options['dry_run']

        # Validate inputs
        if assistants_count < 0 or staff_count < 0:
            raise CommandError('Number of assistants and staff must be non-negative')

        total_staff = assistants_count + staff_count
        if total_staff == 0:
            self.stdout.write(self.style.WARNING('No staff to create. Use --assistants or --staff options.'))
            return

        # Find the doctor
        try:
            doctor = User.objects.get(email=doctor_email, user_type='doctor')
        except User.DoesNotExist:
            raise CommandError(f'Doctor with email {doctor_email} not found')

        self.stdout.write(f'Found doctor: {doctor.first_name} {doctor.last_name} ({doctor.email})')

        # Check subscription limits
        from subscriptions.models import DoctorSubscription
        
        try:
            subscription = DoctorSubscription.objects.get(doctor=doctor, status='active')
            self.stdout.write(f'Active subscription: {subscription.package.name}')
            self.stdout.write(f'Max assistants allowed: {subscription.package.max_assistants}')
            
            # Count current staff
            current_staff = User.objects.filter(
                assigned_doctor=doctor,
                user_type__in=['assistant', 'staff']
            ).count()
            
            self.stdout.write(f'Current staff count: {current_staff}')
            
            # Check if we can create the requested staff
            if current_staff + total_staff > subscription.package.max_assistants:
                raise CommandError(
                    f'Cannot create {total_staff} staff members. '
                    f'Current: {current_staff}, Requested: {total_staff}, '
                    f'Max allowed: {subscription.package.max_assistants}'
                )
                
        except DoctorSubscription.DoesNotExist:
            raise CommandError(f'No active subscription found for doctor {doctor_email}')

        if dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN - No actual changes will be made'))
            self.stdout.write(f'Would create:')
            self.stdout.write(f'  - {assistants_count} assistants')
            self.stdout.write(f'  - {staff_count} staff members')
            return

        # Create assistants
        assistants_created = 0
        for i in range(assistants_count):
            assistant_email = f"assistant{i+1}_for_{doctor.first_name.lower()}_{timezone.now().strftime('%Y%m%d%H%M%S')}@example.com"
            assistant = User.objects.create_user(
                email=assistant_email,
                password="Assistant@123",
                first_name=f"Assistant{i+1}",
                last_name=f"For{doctor.first_name}",
                user_type="assistant",
                phone_number=f"+1555{random.randint(1000000, 9999999)}",
                assigned_doctor=doctor
            )
            assistants_created += 1
            self.stdout.write(self.style.SUCCESS(
                f'Created assistant: {assistant.email}'
            ))

        # Create staff members
        staff_created = 0
        for i in range(staff_count):
            staff_email = f"staff{i+1}_for_{doctor.first_name.lower()}_{timezone.now().strftime('%Y%m%d%H%M%S')}@example.com"
            staff_member = User.objects.create_user(
                email=staff_email,
                password="Staff@123",
                first_name=f"Staff{i+1}",
                last_name=f"For{doctor.first_name}",
                user_type="staff",
                phone_number=f"+1555{random.randint(1000000, 9999999)}",
                assigned_doctor=doctor
            )
            staff_created += 1
            self.stdout.write(self.style.SUCCESS(
                f'Created staff member: {staff_member.email}'
            ))

        # Update subscription counts
        subscription.current_assistant_count = current_staff + assistants_created + staff_created
        subscription.save(update_fields=['current_assistant_count'])

        self.stdout.write(self.style.SUCCESS(
            f'\nSuccessfully created {assistants_created} assistants and {staff_created} staff members '
            f'for doctor {doctor.email}'
        ))
        self.stdout.write(f'Updated subscription assistant count to: {subscription.current_assistant_count}')

        # Print credentials
        self.stdout.write("\nNew Staff Credentials:")
        self.stdout.write("----------------------")
        self.stdout.write("Default password for all staff: Assistant@123 (assistants) / Staff@123 (staff)")
        self.stdout.write("Please ask staff members to change their passwords on first login.")
