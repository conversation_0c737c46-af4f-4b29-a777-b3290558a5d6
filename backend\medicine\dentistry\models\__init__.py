"""
Models for the dentistry application.
"""
from .base import DentistryBaseModel, DentistrySpecialty, DentistryService
from .patient import DentistryPatient
from .medical_record import DentistryMedicalRecord, DentalImaging
from .appointment import DentistryAppointment
from .doctor import DentistryDoctor, DentistryDoctorSettings
from .treatment import DentalTreatment, DentalProcedure
from .laboratory import DentalLaboratory, LabWorkOrder
from .authentication import DentistryRole, DentistryStaffProfile
from .billing import DentistryBillingCode, DentistryInvoice, DentistryPatientInsurance
from .notification import DentistryNotification, DentalReminderSetting
from .configuration import DentistryConfiguration
from .customization import (
    DentistryTemplate, DentistryCustomField, DentistryCustomFieldValue
)
from .dictionary import DentistryDiagnosis, DentistryProcedure, DentistryMaterial
from .website import DentistryWebsiteSettings, DentistryPage, DentistryBeforeAfterCase
from .consultation import DentalConsultation, Tooth
from .comment import DentistryComment, DentistryReview, DentistryDoctorNote
from .dental_svg import DentalSvgData, DentalModification
from .dental_svg_system import DentalSvgConfiguration, DentalSvgPath, DentalTreatmentTemplate
from .dental_sets import DentalSet, ToothButton, SpecialtyField, ReplacementButton, DentalSetConfiguration
from .dashboard_settings import (
    DentistryAppointmentSettings, DentistryWorkingHours,
    DentistryHoliday, DentistryLocationSettings,
    DentistryNotificationSettings, DentistryPrivacySettings,
    DentistryAccessibilitySettings, DentistryVacationPeriod,
    DentistryAppearanceSettings
)

# Import estimate models
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))
from ..models_estimates import (
    EstimateSession, ToothModificationEstimate, EstimateSessionHistory,
    EstimateTemplate, EstimateStatistics
)

# Import pathology models
from ..models_pathologies import (
    DentalPathology, PatientDiagnosis, TreatmentProtocol
)

# Import specialized treatment models
from ..models_specialized import (
    DentalPatient, BaseSpecializedTreatment,
    EstheticDentistryTreatment, ProsthodonticTreatment,
    SurgicalTreatment, OrthodonticTreatment, TherapeuticTreatment
)

__all__ = [
    # Base models
    'DentistryBaseModel',
    'DentistrySpecialty',
    'DentistryService',

    # Patient models
    'DentistryPatient',
    'DentistryMedicalRecord',
    'DentalImaging',

    # Appointment and doctor models
    'DentistryAppointment',
    'DentistryDoctor',
    'DentistryDoctorSettings',

    # Treatment models
    'DentalTreatment',
    'DentalProcedure',

    # Laboratory models
    'DentalLaboratory',
    'LabWorkOrder',

    # Authentication models
    'DentistryRole',
    'DentistryStaffProfile',

    # Billing models
    'DentistryBillingCode',
    'DentistryInvoice',
    'DentistryPatientInsurance',

    # Notification models
    'DentistryNotification',
    'DentalReminderSetting',

    # Configuration models
    'DentistryConfiguration',

    # Customization models
    'DentistryTemplate',
    'DentistryCustomField',
    'DentistryCustomFieldValue',

    # Dictionary models
    'DentistryDiagnosis',
    'DentistryProcedure',
    'DentistryMaterial',

    # Website models
    'DentistryWebsiteSettings',
    'DentistryPage',
    'DentistryBeforeAfterCase',

    # Consultation models
    'DentalConsultation',
    'Tooth',

    # Comment and review models
    'DentistryComment',
    'DentistryReview',
    'DentistryDoctorNote',

    # Dental SVG models with age restrictions
    'DentalSvgData',
    'DentalModification',

    # Dental SVG System models
    'DentalSvgConfiguration',
    'DentalSvgPath',
    'DentalTreatmentTemplate',

    # Dashboard settings models
    'DentistryAppointmentSettings',
    'DentistryWorkingHours',
    'DentistryHoliday',
    'DentistryLocationSettings',
    'DentistryNotificationSettings',
    'DentistryPrivacySettings',
    'DentistryAccessibilitySettings',
    'DentistryVacationPeriod',
    'DentistryAppearanceSettings',

    # Estimate models
    'EstimateSession',
    'ToothModificationEstimate',
    'EstimateSessionHistory',
    'EstimateTemplate',
    'EstimateStatistics',

    # Pathology models
    'DentalPathology',
    'PatientDiagnosis',
    'TreatmentProtocol',

    # Specialized treatment models
    'DentalPatient',
    'BaseSpecializedTreatment',
    'EstheticDentistryTreatment',
    'ProsthodonticTreatment',
    'SurgicalTreatment',
    'OrthodonticTreatment',
    'TherapeuticTreatment',
]
