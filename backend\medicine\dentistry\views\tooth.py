"""
Views for tooth management in the dentistry application.
"""
from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db.models import Q
from rest_framework import serializers

from dentistry.models import Tooth, DentistryPatient


class ToothSerializer(serializers.ModelSerializer):
    """
    Serializer for Tooth model.
    """
    patient_name = serializers.CharField(source='patient.get_full_name', read_only=True)
    tooth_display = serializers.CharField(source='get_tooth_number_display', read_only=True)

    # Frontend compatibility fields - now using model fields directly
    number = serializers.SerializerMethodField()

    class Meta:
        model = Tooth
        fields = [
            'id', 'patient', 'patient_name', 'tooth_number', 'tooth_display',
            'status', 'notes', 'created_at', 'updated_at',
            # Frontend compatibility fields - now using model fields directly
            'number', 'name', 'tooth_type', 'quadrant', 'position',
            'is_permanent', 'description'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'patient_name', 'tooth_display']

    def get_number(self, obj):
        """Return tooth number as string for frontend compatibility."""
        return str(obj.tooth_number)


class ToothViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing teeth.
    """
    queryset = Tooth.objects.all()
    serializer_class = ToothSerializer
    permission_classes = [permissions.AllowAny]  # Allow public access for now

    def get_queryset(self):
        queryset = super().get_queryset()

        # Filter by patient
        patient_id = self.request.query_params.get('patient_id')
        if patient_id:
            queryset = queryset.filter(patient_id=patient_id)

        # Filter by quadrant - now using model field
        quadrant = self.request.query_params.get('quadrant')
        if quadrant:
            queryset = queryset.filter(quadrant=quadrant)

        # Filter by tooth type - now using model field
        tooth_type = self.request.query_params.get('tooth_type')
        if tooth_type:
            queryset = queryset.filter(tooth_type=tooth_type)

        # Filter by status
        status = self.request.query_params.get('status')
        if status:
            queryset = queryset.filter(status=status)

        # Ordering
        ordering = self.request.query_params.get('ordering', 'tooth_number')
        if ordering:
            queryset = queryset.order_by(ordering)

        return queryset

    @action(detail=False, methods=['get'])
    def by_patient(self, request):
        """Get all teeth for a specific patient."""
        patient_id = request.query_params.get('patient_id')
        if not patient_id:
            return Response({'error': 'patient_id is required'}, status=400)

        try:
            patient = DentistryPatient.objects.get(id=patient_id)
        except DentistryPatient.DoesNotExist:
            return Response({'error': 'Patient not found'}, status=404)

        teeth = self.get_queryset().filter(patient_id=patient_id)
        serializer = self.get_serializer(teeth, many=True)

        return Response({
            'patient': {
                'id': patient.id,
                'name': patient.get_full_name(),
                'email': patient.email
            },
            'teeth': serializer.data,
            'count': teeth.count()
        })

    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """Get teeth statistics."""
        queryset = self.get_queryset()

        # Overall statistics
        total_teeth = queryset.count()

        # Status distribution
        status_stats = {}
        for status_code, status_name in Tooth._meta.get_field('status').choices:
            count = queryset.filter(status=status_code).count()
            status_stats[status_code] = {
                'name': status_name,
                'count': count,
                'percentage': round((count / total_teeth * 100) if total_teeth > 0 else 0, 2)
            }

        # Quadrant distribution - now using model field
        quadrant_stats = {}
        for quadrant in ['upper_right', 'upper_left', 'lower_left', 'lower_right']:
            count = queryset.filter(quadrant=quadrant).count()
            quadrant_stats[quadrant] = {
                'count': count,
                'percentage': round((count / total_teeth * 100) if total_teeth > 0 else 0, 2)
            }

        return Response({
            'total_teeth': total_teeth,
            'status_distribution': status_stats,
            'quadrant_distribution': quadrant_stats
        })

    @action(detail=False, methods=['post'])
    def create_full_set(self, request):
        """Create a full set of teeth for a patient."""
        patient_id = request.data.get('patient_id')
        if not patient_id:
            return Response({'error': 'patient_id is required'}, status=400)

        try:
            patient = DentistryPatient.objects.get(id=patient_id)
        except DentistryPatient.DoesNotExist:
            return Response({'error': 'Patient not found'}, status=404)

        # Standard adult teeth numbers
        adult_teeth = [
            11, 12, 13, 14, 15, 16, 17, 18,  # Upper right
            21, 22, 23, 24, 25, 26, 27, 28,  # Upper left
            31, 32, 33, 34, 35, 36, 37, 38,  # Lower left
            41, 42, 43, 44, 45, 46, 47, 48   # Lower right
        ]

        created_teeth = []
        for tooth_number in adult_teeth:
            tooth, created = Tooth.objects.get_or_create(
                patient=patient,
                tooth_number=tooth_number,
                defaults={'status': 'healthy'}
            )
            if created:
                created_teeth.append(tooth)

        serializer = self.get_serializer(created_teeth, many=True)
        return Response({
            'message': f'Created {len(created_teeth)} teeth for patient {patient.get_full_name()}',
            'created_teeth': serializer.data
        })
