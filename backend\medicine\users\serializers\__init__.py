# Import serializers from license_serializers.py
from .license_serializers import (
    DoctorLicenseSerializer,
    LicenseActivationSerializer,
    LicenseRenewalSerializer
)

# Import serializers from specialty_serializers.py
from .specialty_serializers import (
    SpecialtySerializer,
    SpecialtyDetailSerializer
)

# Import serializers from location_serializers.py
from .location_serializers import (
    CountrySerializer,
    RegionSerializer,
    CitySerializer,
    CountryWithRegionsSerializer,
    RegionWithCitiesSerializer
)

# Import serializers from site_settings_serializers.py
from .site_settings_serializers import SiteSettingsSerializer

# We'll import the user serializers directly in the views/__init__.py file
# to avoid circular imports