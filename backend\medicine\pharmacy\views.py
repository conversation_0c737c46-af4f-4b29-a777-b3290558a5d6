from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q

from .models import (
    Supplier, Depot, ProductCategory, Product,
    PurchaseRequest, PurchaseRequestItem,
    Inventory, StockMovement
)
from .serializers import (
    SupplierSerializer, DepotSerializer, ProductCategorySerializer,
    ProductSerializer, PurchaseRequestSerializer, PurchaseRequestCreateSerializer,
    PurchaseRequestItemSerializer, InventorySerializer, StockMovementSerializer,
    SupplierSimpleSerializer, DepotSimpleSerializer, ProductSimpleSerializer
)


class SupplierViewSet(viewsets.ModelViewSet):
    """ViewSet for managing suppliers"""
    queryset = Supplier.objects.filter(is_active=True)
    serializer_class = SupplierSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['raison_sociale', 'email', 'tel_gestionnaire', 'ville']
    filterset_fields = ['ville', 'mode_paiement']
    ordering_fields = ['raison_sociale', 'created_at']
    ordering = ['raison_sociale']

    @action(detail=False, methods=['get'])
    def simple_list(self, request):
        """Get simplified list for dropdowns"""
        suppliers = self.get_queryset()
        serializer = SupplierSimpleSerializer(suppliers, many=True)
        return Response(serializer.data)

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)


class DepotViewSet(viewsets.ModelViewSet):
    """ViewSet for managing depots"""
    queryset = Depot.objects.filter(is_active=True)
    serializer_class = DepotSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'code', 'manager']
    ordering_fields = ['name', 'created_at']
    ordering = ['name']

    @action(detail=False, methods=['get'])
    def simple_list(self, request):
        """Get simplified list for dropdowns"""
        depots = self.get_queryset()
        serializer = DepotSimpleSerializer(depots, many=True)
        return Response(serializer.data)

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)


class ProductCategoryViewSet(viewsets.ModelViewSet):
    """ViewSet for managing product categories"""
    queryset = ProductCategory.objects.filter(is_active=True)
    serializer_class = ProductCategorySerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'code']
    ordering_fields = ['name', 'created_at']
    ordering = ['name']

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)


class ProductViewSet(viewsets.ModelViewSet):
    """ViewSet for managing products"""
    queryset = Product.objects.filter(is_active=True)
    serializer_class = ProductSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['code', 'designation', 'barcode']
    filterset_fields = ['category', 'unit']
    ordering_fields = ['designation', 'code', 'price', 'created_at']
    ordering = ['designation']

    @action(detail=False, methods=['get'])
    def simple_list(self, request):
        """Get simplified list for dropdowns"""
        products = self.get_queryset()
        serializer = ProductSimpleSerializer(products, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def inventory(self, request, pk=None):
        """Get inventory for a specific product"""
        product = self.get_object()
        inventory_items = Inventory.objects.filter(product=product)
        serializer = InventorySerializer(inventory_items, many=True)
        return Response(serializer.data)

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)


class PurchaseRequestViewSet(viewsets.ModelViewSet):
    """ViewSet for managing purchase requests"""
    queryset = PurchaseRequest.objects.all()
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['numero', 'supplier__raison_sociale']
    filterset_fields = ['status', 'urgent', 'supplier']
    ordering_fields = ['numero', 'date', 'created_at']
    ordering = ['-date', '-numero']

    def get_serializer_class(self):
        if self.action == 'create':
            return PurchaseRequestCreateSerializer
        return PurchaseRequestSerializer

    @action(detail=True, methods=['post'])
    def approve(self, request, pk=None):
        """Approve a purchase request"""
        purchase_request = self.get_object()
        if purchase_request.status == 'pending':
            purchase_request.status = 'approved'
            purchase_request.save()
            return Response({'status': 'approved'})
        return Response(
            {'error': 'Purchase request cannot be approved'},
            status=status.HTTP_400_BAD_REQUEST
        )

    @action(detail=True, methods=['post'])
    def reject(self, request, pk=None):
        """Reject a purchase request"""
        purchase_request = self.get_object()
        if purchase_request.status == 'pending':
            purchase_request.status = 'rejected'
            purchase_request.save()
            return Response({'status': 'rejected'})
        return Response(
            {'error': 'Purchase request cannot be rejected'},
            status=status.HTTP_400_BAD_REQUEST
        )

    @action(detail=True, methods=['post'])
    def submit(self, request, pk=None):
        """Submit a purchase request for approval"""
        purchase_request = self.get_object()
        if purchase_request.status == 'draft':
            purchase_request.status = 'pending'
            purchase_request.save()
            return Response({'status': 'pending'})
        return Response(
            {'error': 'Purchase request cannot be submitted'},
            status=status.HTTP_400_BAD_REQUEST
        )

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)


class PurchaseRequestItemViewSet(viewsets.ModelViewSet):
    """ViewSet for managing purchase request items"""
    queryset = PurchaseRequestItem.objects.all()
    serializer_class = PurchaseRequestItemSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['purchase_request', 'product', 'depot']

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)


class InventoryViewSet(viewsets.ModelViewSet):
    """ViewSet for managing inventory"""
    queryset = Inventory.objects.all()
    serializer_class = InventorySerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['product__code', 'product__designation', 'depot__name']
    filterset_fields = ['depot', 'product__category']
    ordering_fields = ['product__designation', 'quantity', 'last_movement_date']
    ordering = ['product__designation']

    @action(detail=False, methods=['get'])
    def low_stock(self, request):
        """Get products with low stock"""
        low_stock_items = []
        for item in self.get_queryset():
            if item.quantity <= item.product.min_stock:
                low_stock_items.append(item)
        
        serializer = self.get_serializer(low_stock_items, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def by_depot(self, request):
        """Get inventory grouped by depot"""
        depot_id = request.query_params.get('depot_id')
        if depot_id:
            inventory_items = self.get_queryset().filter(depot_id=depot_id)
        else:
            inventory_items = self.get_queryset()
        
        serializer = self.get_serializer(inventory_items, many=True)
        return Response(serializer.data)


class StockMovementViewSet(viewsets.ModelViewSet):
    """ViewSet for managing stock movements"""
    queryset = StockMovement.objects.all()
    serializer_class = StockMovementSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['product__code', 'product__designation', 'reference']
    filterset_fields = ['movement_type', 'depot', 'product']
    ordering_fields = ['date', 'product__designation']
    ordering = ['-date']

    @action(detail=False, methods=['get'])
    def by_product(self, request):
        """Get movements for a specific product"""
        product_id = request.query_params.get('product_id')
        if product_id:
            movements = self.get_queryset().filter(product_id=product_id)
            serializer = self.get_serializer(movements, many=True)
            return Response(serializer.data)
        return Response({'error': 'product_id parameter required'}, status=status.HTTP_400_BAD_REQUEST)

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)
