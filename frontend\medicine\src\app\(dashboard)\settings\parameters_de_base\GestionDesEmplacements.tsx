import React, { useState } from 'react';
import {
  Stack,
  Title,
  Tabs,
  Paper,
  Text,
  TextInput,
  Button,
  Modal,
  Table,
  ActionIcon,
  Group,
  Tooltip,
  ScrollArea,
  Select,
} from '@mantine/core';
import {
  IconPlus,
  IconEdit,
  IconTrash,
  IconSearch,
  IconMapPin,
} from '@tabler/icons-react';

// Types pour les données
interface Pays {
  id: number;
  abre: string;
  nom: string;
}

interface Ville {
  id: number;
  abre: string;
  nom: string;
  pays: string;
}

interface Prefecture {
  id: number;
  abre: string;
  nom: string;
  ville: string;
}

const GestionDesEmplacements = () => {
  // États pour les onglets
  const [activeTab, setActiveTab] = useState<string>('pays');

  // États pour les modales
  const [paysModalOpened, setPaysModalOpened] = useState(false);
  const [villeModalOpened, setVilleModalOpened] = useState(false);
  const [prefectureModalOpened, setPrefectureModalOpened] = useState(false);

  // États pour l'édition
  const [editingPays, setEditingPays] = useState<Pays | null>(null);
  const [editingVille, setEditingVille] = useState<Ville | null>(null);
  const [editingPrefecture, setEditingPrefecture] = useState<Prefecture | null>(null);

  // États pour les formulaires
  const [paysForm, setPaysForm] = useState({
    abre: '',
    nom: '',
  });

  const [villeForm, setVilleForm] = useState({
    abre: '',
    nom: '',
    pays: '',
  });

  const [prefectureForm, setPrefectureForm] = useState({
    abre: '',
    nom: '',
    ville: '',
  });

  // État pour la recherche
  const [searchTerm, setSearchTerm] = useState('');

  // Données de test
  const [paysList, setPaysList] = useState<Pays[]>([
    { id: 1, abre: 'Mar', nom: 'MAROC' },
    { id: 2, abre: 'Fr', nom: 'FRANCE' },
  ]);

  const [villesList, setVillesList] = useState<Ville[]>([
    { id: 1, abre: 'AL HAJEB', nom: 'AL HAJEB', pays: 'MAROC' },
    { id: 2, abre: 'AGADIR', nom: 'AGADIR', pays: 'MAROC' },
    { id: 3, abre: 'AL HOCEIMA', nom: 'AL HOCEIMA', pays: 'MAROC' },
    { id: 4, abre: 'ASSA ZAG', nom: 'ASSA ZAG', pays: 'MAROC' },
    { id: 5, abre: 'AZILAL', nom: 'AZILAL', pays: 'MAROC' },
    { id: 6, abre: 'BENI MELLAL', nom: 'BENI MELLAL', pays: 'MAROC' },
    { id: 7, abre: 'BENSLIMANE', nom: 'BENSLIMANE', pays: 'MAROC' },
    { id: 8, abre: 'BOUJDOUR', nom: 'BOUJDOUR', pays: 'MAROC' },
    { id: 9, abre: 'BOULEMANE', nom: 'BOULEMANE', pays: 'MAROC' },
    { id: 10, abre: 'BERRECHID', nom: 'BERRECHID', pays: 'MAROC' },
    { id: 11, abre: 'CASABLANCA', nom: 'CASABLANCA', pays: 'MAROC' },
    { id: 12, abre: 'CHEFCHAOUEN', nom: 'CHEFCHAOUEN', pays: 'MAROC' },
    { id: 13, abre: 'CHTOUKA', nom: 'CHTOUKA', pays: 'MAROC' },
    { id: 14, abre: 'AIT BAHA', nom: 'AIT BAHA', pays: 'MAROC' },
    { id: 15, abre: 'CHICHAOUA', nom: 'CHICHAOUA', pays: 'MAROC' },
    { id: 16, abre: 'EL JADIDA', nom: 'EL JADIDA', pays: 'MAROC' },
    { id: 17, abre: 'EL KELAA DES SRAGHNAS', nom: 'EL KELAA DES SRAGHNAS', pays: 'MAROC' },
  ]);

  const [prefecturesList, setPrefecturesList] = useState<Prefecture[]>([
    { id: 1, abre: 'AIK', nom: 'Ain Chock', ville: 'CASABLANCA' },
    { id: 2, abre: 'ASH', nom: 'Ain Sebaâ-Hay Mohammadi', ville: 'CASABLANCA' },
    { id: 3, abre: 'AFS', nom: 'Al Fida-Mers Sultan', ville: 'CASABLANCA' },
    { id: 4, abre: 'BMS', nom: 'Ben M\'sick', ville: 'CASABLANCA' },
    { id: 5, abre: 'CAF', nom: 'Casablanca-Anfa', ville: 'CASABLANCA' },
    { id: 6, abre: 'HAY', nom: 'Hay Hassani', ville: 'CASABLANCA' },
    { id: 7, abre: 'MRC', nom: 'Moulay Rachid', ville: 'CASABLANCA' },
    { id: 8, abre: 'BER', nom: 'Sidi Bernoussi', ville: 'CASABLANCA' },
    { id: 9, abre: 'AGR', nom: 'Agdal-Ryad', ville: 'RABAT' },
    { id: 10, abre: 'YOS', nom: 'El Youssoufia', ville: 'RABAT' },
    { id: 11, abre: 'HAS', nom: 'Hassan', ville: 'RABAT' },
    { id: 12, abre: 'SOS', nom: 'Souissi', ville: 'RABAT' },
    { id: 13, abre: 'YEM', nom: 'Yacoub El Mansour', ville: 'RABAT' },
    { id: 14, abre: 'MKA', nom: 'Mechouar Kasba', ville: 'MARRAKECH' },
    { id: 15, abre: 'MAR', nom: 'Marrakech-Medina', ville: 'MARRAKECH' },
    { id: 16, abre: 'GLZ', nom: 'Guéliz', ville: 'MARRAKECH' },
    { id: 17, abre: 'MEN', nom: 'Ménara', ville: 'MARRAKECH' },
  ]);

  // Fonctions pour filtrer les données selon la recherche
  const getFilteredData = () => {
    const term = searchTerm.toLowerCase();
    switch (activeTab) {
      case 'pays':
        return paysList.filter(item =>
          item.abre.toLowerCase().includes(term) ||
          item.nom.toLowerCase().includes(term)
        );
      case 'ville':
        return villesList.filter(item =>
          item.abre.toLowerCase().includes(term) ||
          item.nom.toLowerCase().includes(term) ||
          item.pays.toLowerCase().includes(term)
        );
      case 'prefecture':
        return prefecturesList.filter(item =>
          item.abre.toLowerCase().includes(term) ||
          item.nom.toLowerCase().includes(term) ||
          item.ville.toLowerCase().includes(term)
        );
      default:
        return [];
    }
  };

  // Fonctions pour gérer les modales - Pays
  const openPaysModal = (pays?: Pays) => {
    if (pays) {
      setEditingPays(pays);
      setPaysForm({
        abre: pays.abre,
        nom: pays.nom,
      });
    } else {
      setEditingPays(null);
      setPaysForm({
        abre: '',
        nom: '',
      });
    }
    setPaysModalOpened(true);
  };

  const closePaysModal = () => {
    setPaysModalOpened(false);
    setEditingPays(null);
  };

  const handleSavePays = () => {
    if (!paysForm.nom.trim()) return;

    const newPays: Pays = {
      id: editingPays ? editingPays.id : Date.now(),
      abre: paysForm.abre,
      nom: paysForm.nom,
    };

    if (editingPays) {
      setPaysList(prev => prev.map(p => p.id === editingPays.id ? newPays : p));
    } else {
      setPaysList(prev => [...prev, newPays]);
    }

    closePaysModal();
  };

  const handleDeletePays = (id: number) => {
    setPaysList(prev => prev.filter(p => p.id !== id));
  };

  // Fonctions pour gérer les modales - Ville
  const openVilleModal = (ville?: Ville) => {
    if (ville) {
      setEditingVille(ville);
      setVilleForm({
        abre: ville.abre,
        nom: ville.nom,
        pays: ville.pays,
      });
    } else {
      setEditingVille(null);
      setVilleForm({
        abre: '',
        nom: '',
        pays: paysList.length > 0 ? paysList[0].nom : '',
      });
    }
    setVilleModalOpened(true);
  };

  const closeVilleModal = () => {
    setVilleModalOpened(false);
    setEditingVille(null);
  };

  const handleSaveVille = () => {
    if (!villeForm.nom.trim()) return;

    const newVille: Ville = {
      id: editingVille ? editingVille.id : Date.now(),
      abre: villeForm.abre,
      nom: villeForm.nom,
      pays: villeForm.pays,
    };

    if (editingVille) {
      setVillesList(prev => prev.map(v => v.id === editingVille.id ? newVille : v));
    } else {
      setVillesList(prev => [...prev, newVille]);
    }

    closeVilleModal();
  };

  const handleDeleteVille = (id: number) => {
    setVillesList(prev => prev.filter(v => v.id !== id));
  };

  // Fonctions pour gérer les modales - Prefecture
  const openPrefectureModal = (prefecture?: Prefecture) => {
    if (prefecture) {
      setEditingPrefecture(prefecture);
      setPrefectureForm({
        abre: prefecture.abre,
        nom: prefecture.nom,
        ville: prefecture.ville,
      });
    } else {
      setEditingPrefecture(null);
      setPrefectureForm({
        abre: '',
        nom: '',
        ville: villesList.length > 0 ? villesList[0].nom : '',
      });
    }
    setPrefectureModalOpened(true);
  };

  const closePrefectureModal = () => {
    setPrefectureModalOpened(false);
    setEditingPrefecture(null);
  };

  const handleSavePrefecture = () => {
    if (!prefectureForm.nom.trim()) return;

    const newPrefecture: Prefecture = {
      id: editingPrefecture ? editingPrefecture.id : Date.now(),
      abre: prefectureForm.abre,
      nom: prefectureForm.nom,
      ville: prefectureForm.ville,
    };

    if (editingPrefecture) {
      setPrefecturesList(prev => prev.map(p => p.id === editingPrefecture.id ? newPrefecture : p));
    } else {
      setPrefecturesList(prev => [...prev, newPrefecture]);
    }

    closePrefectureModal();
  };

  const handleDeletePrefecture = (id: number) => {
    setPrefecturesList(prev => prev.filter(p => p.id !== id));
  };

  return (
    <Stack gap="lg" className="w-full">
      <Title order={2} className="text-gray-800 flex items-center gap-2">
        <IconMapPin size={24} className="text-blue-600" />
        Gestion des emplacements
      </Title>

      <Tabs value={activeTab} onChange={(value) => setActiveTab(value || 'pays')} variant="outline">
        <Tabs.List>
          <Tabs.Tab value="pays" leftSection={<IconMapPin size={16} />}>
            Pays
          </Tabs.Tab>
          <Tabs.Tab value="ville" leftSection={<IconMapPin size={16} />}>
            Ville
          </Tabs.Tab>
          <Tabs.Tab value="prefecture" leftSection={<IconMapPin size={16} />}>
            Préfecture
          </Tabs.Tab>
        </Tabs.List>

        {/* Onglet Pays */}
        <Tabs.Panel value="pays" pt="md">
          <Paper p="md" withBorder>
            <div className="flex justify-between items-center mb-4">
              <TextInput
                placeholder="Rechercher"
                leftSection={<IconSearch size={16} />}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-64"
              />
              <Button
                leftSection={<IconPlus size={16} />}
                onClick={() => openPaysModal()}
                className="bg-blue-500 hover:bg-blue-600"
              >
                Ajouter pays
              </Button>
            </div>

            <ScrollArea h={400}>
              <Table striped highlightOnHover>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>Abré</Table.Th>
                    <Table.Th>Nom</Table.Th>
                    <Table.Th className="text-center w-20">Actions</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {(getFilteredData() as Pays[]).map((pays) => (
                    <Table.Tr key={pays.id}>
                      <Table.Td>
                        <Text fw={500}>{pays.abre}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Text>{pays.nom}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Group gap="xs" justify="center">
                          <Tooltip label="Modifier">
                            <ActionIcon
                              variant="subtle"
                              color="blue"
                              onClick={() => openPaysModal(pays)}
                            >
                              <IconEdit size={16} />
                            </ActionIcon>
                          </Tooltip>
                          <Tooltip label="Supprimer">
                            <ActionIcon
                              variant="subtle"
                              color="red"
                              onClick={() => handleDeletePays(pays.id)}
                            >
                              <IconTrash size={16} />
                            </ActionIcon>
                          </Tooltip>
                        </Group>
                      </Table.Td>
                    </Table.Tr>
                  ))}
                </Table.Tbody>
              </Table>
            </ScrollArea>
          </Paper>
        </Tabs.Panel>

        {/* Onglet Ville */}
        <Tabs.Panel value="ville" pt="md">
          <Paper p="md" withBorder>
            <div className="flex justify-between items-center mb-4">
              <TextInput
                placeholder="Rechercher"
                leftSection={<IconSearch size={16} />}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-64"
              />
              <Button
                leftSection={<IconPlus size={16} />}
                onClick={() => openVilleModal()}
                className="bg-blue-500 hover:bg-blue-600"
              >
                Ajouter ville
              </Button>
            </div>

            <ScrollArea h={400}>
              <Table striped highlightOnHover>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>Abré</Table.Th>
                    <Table.Th>Nom</Table.Th>
                    <Table.Th>Pays</Table.Th>
                    <Table.Th className="text-center w-20">Actions</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {(getFilteredData() as Ville[]).map((ville) => (
                    <Table.Tr key={ville.id}>
                      <Table.Td>
                        <Text fw={500}>{ville.abre}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Text>{ville.nom}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Text>{ville.pays}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Group gap="xs" justify="center">
                          <Tooltip label="Modifier">
                            <ActionIcon
                              variant="subtle"
                              color="blue"
                              onClick={() => openVilleModal(ville)}
                            >
                              <IconEdit size={16} />
                            </ActionIcon>
                          </Tooltip>
                          <Tooltip label="Supprimer">
                            <ActionIcon
                              variant="subtle"
                              color="red"
                              onClick={() => handleDeleteVille(ville.id)}
                            >
                              <IconTrash size={16} />
                            </ActionIcon>
                          </Tooltip>
                        </Group>
                      </Table.Td>
                    </Table.Tr>
                  ))}
                </Table.Tbody>
              </Table>
            </ScrollArea>
          </Paper>
        </Tabs.Panel>

        {/* Onglet Préfecture */}
        <Tabs.Panel value="prefecture" pt="md">
          <Paper p="md" withBorder>
            <div className="flex justify-between items-center mb-4">
              <TextInput
                placeholder="Rechercher"
                leftSection={<IconSearch size={16} />}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-64"
              />
              <Button
                leftSection={<IconPlus size={16} />}
                onClick={() => openPrefectureModal()}
                className="bg-blue-500 hover:bg-blue-600"
              >
                Ajouter préfecture
              </Button>
            </div>

            <ScrollArea h={400}>
              <Table striped highlightOnHover>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>Abré</Table.Th>
                    <Table.Th>Nom</Table.Th>
                    <Table.Th>Ville</Table.Th>
                    <Table.Th className="text-center w-20">Actions</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {(getFilteredData() as Prefecture[]).map((prefecture) => (
                    <Table.Tr key={prefecture.id}>
                      <Table.Td>
                        <Text fw={500}>{prefecture.abre}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Text>{prefecture.nom}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Text>{prefecture.ville}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Group gap="xs" justify="center">
                          <Tooltip label="Modifier">
                            <ActionIcon
                              variant="subtle"
                              color="blue"
                              onClick={() => openPrefectureModal(prefecture)}
                            >
                              <IconEdit size={16} />
                            </ActionIcon>
                          </Tooltip>
                          <Tooltip label="Supprimer">
                            <ActionIcon
                              variant="subtle"
                              color="red"
                              onClick={() => handleDeletePrefecture(prefecture.id)}
                            >
                              <IconTrash size={16} />
                            </ActionIcon>
                          </Tooltip>
                        </Group>
                      </Table.Td>
                    </Table.Tr>
                  ))}
                </Table.Tbody>
              </Table>
            </ScrollArea>
          </Paper>
        </Tabs.Panel>
      </Tabs>

      {/* Modal pour Pays */}
      <Modal
        opened={paysModalOpened}
        onClose={closePaysModal}
        title={
          <div className="bg-blue-500 text-white px-4 py-2 -m-4 mb-4 flex items-center gap-2">
            <IconMapPin size={20} />
            <Text fw={600}>
              Ajouter Pays
            </Text>
          </div>
        }
        size="md"
        withCloseButton={false}
      >
        <Stack gap="md">
          <TextInput
            label="Nom court"
            value={paysForm.abre}
            onChange={(e) => setPaysForm(prev => ({ ...prev, abre: e.target.value }))}
          />

          <TextInput
            label={<span>Nom complet <span className="text-red-500">*</span></span>}
            value={paysForm.nom}
            onChange={(e) => setPaysForm(prev => ({ ...prev, nom: e.target.value }))}
            required
          />

          <div className="flex items-center justify-end pt-4 border-t">
            <Group>
              <Button
                variant="filled"
                color="gray"
                onClick={closePaysModal}
              >
                Annuler
              </Button>
              <Button
                variant="filled"
                color="blue"
                onClick={handleSavePays}
                disabled={!paysForm.nom.trim()}
              >
                Enregistrer
              </Button>
            </Group>
          </div>
        </Stack>
      </Modal>

      {/* Modal pour Ville */}
      <Modal
        opened={villeModalOpened}
        onClose={closeVilleModal}
        title={
          <div className="bg-blue-500 text-white px-4 py-2 -m-4 mb-4 flex items-center gap-2">
            <IconMapPin size={20} />
            <Text fw={600}>
              Ajouter Ville
            </Text>
          </div>
        }
        size="md"
        withCloseButton={false}
      >
        <Stack gap="md">
          <TextInput
            label="Nom court"
            value={villeForm.abre}
            onChange={(e) => setVilleForm(prev => ({ ...prev, abre: e.target.value }))}
          />

          <TextInput
            label={<span>Nom complet <span className="text-red-500">*</span></span>}
            value={villeForm.nom}
            onChange={(e) => setVilleForm(prev => ({ ...prev, nom: e.target.value }))}
            required
          />

          <Select
            label={<span>Pays <span className="text-red-500">*</span></span>}
            value={villeForm.pays}
            onChange={(value) => setVilleForm(prev => ({ ...prev, pays: value || '' }))}
            data={paysList.map(p => ({ value: p.nom, label: p.nom }))}
            required
          />

          <div className="flex items-center justify-end pt-4 border-t">
            <Group>
              <Button
                variant="filled"
                color="gray"
                onClick={closeVilleModal}
              >
                Annuler
              </Button>
              <Button
                variant="filled"
                color="blue"
                onClick={handleSaveVille}
                disabled={!villeForm.nom.trim()}
              >
                Enregistrer
              </Button>
            </Group>
          </div>
        </Stack>
      </Modal>

      {/* Modal pour Préfecture */}
      <Modal
        opened={prefectureModalOpened}
        onClose={closePrefectureModal}
        title={
          <div className="bg-blue-500 text-white px-4 py-2 -m-4 mb-4 flex items-center gap-2">
            <IconMapPin size={20} />
            <Text fw={600}>
              Ajouter Préfecture
            </Text>
          </div>
        }
        size="md"
        withCloseButton={false}
      >
        <Stack gap="md">
          <TextInput
            label="Nom court"
            value={prefectureForm.abre}
            onChange={(e) => setPrefectureForm(prev => ({ ...prev, abre: e.target.value }))}
          />

          <TextInput
            label={<span>Nom complet <span className="text-red-500">*</span></span>}
            value={prefectureForm.nom}
            onChange={(e) => setPrefectureForm(prev => ({ ...prev, nom: e.target.value }))}
            required
          />

          <Select
            label={<span>Ville <span className="text-red-500">*</span></span>}
            value={prefectureForm.ville}
            onChange={(value) => setPrefectureForm(prev => ({ ...prev, ville: value || '' }))}
            data={villesList.map(v => ({ value: v.nom, label: v.nom }))}
            required
          />

          <div className="flex items-center justify-end pt-4 border-t">
            <Group>
              <Button
                variant="filled"
                color="gray"
                onClick={closePrefectureModal}
              >
                Annuler
              </Button>
              <Button
                variant="filled"
                color="blue"
                onClick={handleSavePrefecture}
                disabled={!prefectureForm.nom.trim()}
              >
                Enregistrer
              </Button>
            </Group>
          </div>
        </Stack>
      </Modal>
    </Stack>
  );
};

export default GestionDesEmplacements;
