"""
Dictionary models for the dentistry application.
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from dentistry.models.base import DentistryBaseModel
from dentistry.models.doctor import DentistryDoctor

class DentistryDiagnosis(DentistryBaseModel):
    """
    Dentistry-specific diagnosis dictionary model.
    """
    code = models.CharField(
        max_length=20,
        verbose_name=_("Diagnosis Code")
    )
    name = models.CharField(
        max_length=200,
        verbose_name=_("Diagnosis Name")
    )
    description = models.TextField(
        blank=True,
        verbose_name=_("Description")
    )
    icd10_code = models.Char<PERSON>ield(
        max_length=20,
        blank=True,
        verbose_name=_("ICD-10 Code")
    )
    CATEGORY_CHOICES = (
        ('caries', _('Caries')),
        ('periodontal', _('Periodontal Disease')),
        ('endodontic', _('Endodontic')),
        ('orthodontic', _('Orthodontic')),
        ('oral_pathology', _('Oral Pathology')),
        ('tmj', _('TMJ Disorders')),
        ('other', _('Other')),
    )
    category = models.Char<PERSON>ield(
        max_length=20,
        choices=CATEGORY_CHOICES,
        default='other',
        verbose_name=_("Category")
    )
    is_common = models.BooleanField(
        default=False,
        verbose_name=_("Is Common Diagnosis")
    )
    doctor = models.ForeignKey(
       DentistryDoctor,
        on_delete=models.CASCADE,
        related_name="dentistry_diagnoses",
        verbose_name=_("Doctor"),
        null=True,
        blank=True,
        help_text=_("If null, this is a system-wide diagnosis")
    )
    
    class Meta:
        verbose_name = _("Dentistry Diagnosis")
        verbose_name_plural = _("Dentistry Diagnoses")
        ordering = ['code']
        unique_together = ['code', 'doctor']
    
    def __str__(self):
        return f"{self.code} - {self.name}"

class DentistryProcedure(DentistryBaseModel):
    """
    Dentistry-specific procedure dictionary model.
    """
    code = models.CharField(
        max_length=20,
        verbose_name=_("Procedure Code")
    )
    name = models.CharField(
        max_length=200,
        verbose_name=_("Procedure Name")
    )
    description = models.TextField(
        blank=True,
        verbose_name=_("Description")
    )
    ada_code = models.CharField(
        max_length=20,
        blank=True,
        verbose_name=_("ADA Code")
    )
    CATEGORY_CHOICES = (
        ('diagnostic', _('Diagnostic')),
        ('preventive', _('Preventive')),
        ('restorative', _('Restorative')),
        ('endodontic', _('Endodontic')),
        ('periodontic', _('Periodontic')),
        ('prosthodontic', _('Prosthodontic')),
        ('oral_surgery', _('Oral Surgery')),
        ('orthodontic', _('Orthodontic')),
        ('other', _('Other')),
    )
    category = models.CharField(
        max_length=20,
        choices=CATEGORY_CHOICES,
        default='other',
        verbose_name=_("Category")
    )
    default_duration = models.PositiveIntegerField(
        default=30,
        verbose_name=_("Default Duration (minutes)")
    )
    default_fee = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name=_("Default Fee")
    )
    preparation_instructions = models.TextField(
        blank=True,
        verbose_name=_("Preparation Instructions")
    )
    aftercare_instructions = models.TextField(
        blank=True,
        verbose_name=_("Aftercare Instructions")
    )
    is_common = models.BooleanField(
        default=False,
        verbose_name=_("Is Common Procedure")
    )
    requires_lab_work = models.BooleanField(
        default=False,
        verbose_name=_("Requires Lab Work")
    )
    requires_anesthesia = models.BooleanField(
        default=False,
        verbose_name=_("Requires Anesthesia")
    )
    doctor = models.ForeignKey(
       DentistryDoctor,
        on_delete=models.CASCADE,
        related_name="dentistry_procedures",
        verbose_name=_("Doctor"),
        null=True,
        blank=True,
        help_text=_("If null, this is a system-wide procedure")
    )
    
    class Meta:
        verbose_name = _("Dentistry Procedure")
        verbose_name_plural = _("Dentistry Procedures")
        ordering = ['code']
        unique_together = ['code', 'doctor']
    
    def __str__(self):
        return f"{self.code} - {self.name}"

class DentistryMaterial(DentistryBaseModel):
    """
    Dentistry-specific material dictionary model.
    """
    name = models.CharField(
        max_length=200,
        verbose_name=_("Material Name")
    )
    description = models.TextField(
        blank=True,
        verbose_name=_("Description")
    )
    CATEGORY_CHOICES = (
        ('restorative', _('Restorative')),
        ('impression', _('Impression')),
        ('cement', _('Cement')),
        ('temporary', _('Temporary')),
        ('endodontic', _('Endodontic')),
        ('surgical', _('Surgical')),
        ('other', _('Other')),
    )
    category = models.CharField(
        max_length=20,
        choices=CATEGORY_CHOICES,
        default='other',
        verbose_name=_("Category")
    )
    manufacturer = models.CharField(
        max_length=100,
        blank=True,
        verbose_name=_("Manufacturer")
    )
    instructions = models.TextField(
        blank=True,
        verbose_name=_("Instructions")
    )
    contraindications = models.TextField(
        blank=True,
        verbose_name=_("Contraindications")
    )
    is_common = models.BooleanField(
        default=False,
        verbose_name=_("Is Common Material")
    )
    doctor = models.ForeignKey(
       DentistryDoctor,
        on_delete=models.CASCADE,
        related_name="dentistry_materials",
        verbose_name=_("Doctor"),
        null=True,
        blank=True,
        help_text=_("If null, this is a system-wide material")
    )
    
    class Meta:
        verbose_name = _("Dentistry Material")
        verbose_name_plural = _("Dentistry Materials")
        ordering = ['name']
        unique_together = ['name', 'doctor']
    
    def __str__(self):
        return self.name
