# Generated by Django 4.2.7 on 2025-05-25 09:30

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('dentistry', '0002_estimatesession_toothmodificationestimate_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='DentalPathology',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200, verbose_name='Nom de la pathologie')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='Code pathologie')),
                ('category', models.CharField(choices=[('caries', 'Caries'), ('periodontal', 'Maladie Parodontale'), ('endodontic', 'Problème Endodontique'), ('trauma', 'Traumatisme'), ('malformation', 'Malformation'), ('infection', 'Infection'), ('wear', 'Usure Dentaire'), ('aesthetic', 'Problème Esthétique'), ('orthodontic', 'Problème Orthodontique'), ('other', 'Autre')], max_length=20, verbose_name='Catégorie')),
                ('description', models.TextField(verbose_name='Description')),
                ('symptoms', models.TextField(blank=True, verbose_name='Symptômes')),
                ('causes', models.TextField(blank=True, verbose_name='Causes')),
                ('severity_default', models.CharField(choices=[('mild', 'Légère'), ('moderate', 'Modérée'), ('severe', 'Sévère'), ('critical', 'Critique')], default='mild', max_length=20)),
                ('is_contagious', models.BooleanField(default=False, verbose_name='Contagieux')),
                ('requires_urgent_care', models.BooleanField(default=False, verbose_name='Soins urgents requis')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'verbose_name': 'Dental Pathology',
                'verbose_name_plural': 'Dental Pathologies',
                'ordering': ['category', 'name'],
            },
        ),
        migrations.AddField(
            model_name='toothmodificationestimate',
            name='indication',
            field=models.CharField(choices=[('preventive', 'Préventif'), ('therapeutic', 'Thérapeutique'), ('aesthetic', 'Esthétique'), ('emergency', 'Urgence'), ('maintenance', 'Maintenance')], default='therapeutic', help_text='Raison médicale de la modification', max_length=20, verbose_name='Indication'),
        ),
        migrations.AddField(
            model_name='toothmodificationestimate',
            name='medical_justification',
            field=models.TextField(blank=True, help_text='Explication détaillée de la nécessité du traitement', verbose_name='Justification médicale'),
        ),
        migrations.CreateModel(
            name='TreatmentProtocol',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200, verbose_name='Nom du protocole')),
                ('description', models.TextField(verbose_name='Description')),
                ('treatment_steps', models.JSONField(default=list, verbose_name='Étapes de traitement')),
                ('estimated_duration', models.PositiveIntegerField(help_text='Durée estimée en minutes')),
                ('urgency_level', models.CharField(choices=[('low', 'Faible'), ('medium', 'Moyenne'), ('high', 'Élevée'), ('emergency', 'Urgence')], default='medium', max_length=20)),
                ('required_modifications', models.JSONField(default=list, help_text='Liste des path_ids de modifications requises')),
                ('estimated_cost', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('required_materials', models.JSONField(default=list, verbose_name='Matériaux requis')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('pathology', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='protocols', to='dentistry.dentalpathology')),
            ],
            options={
                'verbose_name': 'Treatment Protocol',
                'verbose_name_plural': 'Treatment Protocols',
                'ordering': ['pathology', 'urgency_level', 'name'],
            },
        ),
        migrations.CreateModel(
            name='PatientDiagnosis',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('patient_id', models.CharField(max_length=100, verbose_name='ID Patient')),
                ('tooth_number', models.PositiveIntegerField(verbose_name='Numéro de dent')),
                ('tooth_surface', models.CharField(blank=True, max_length=50, verbose_name='Surface de la dent')),
                ('diagnosis_date', models.DateTimeField(auto_now_add=True)),
                ('status', models.CharField(choices=[('suspected', 'Suspecté'), ('confirmed', 'Confirmé'), ('treated', 'Traité'), ('resolved', 'Résolu'), ('chronic', 'Chronique')], default='suspected', max_length=20)),
                ('severity', models.CharField(choices=[('mild', 'Légère'), ('moderate', 'Modérée'), ('severe', 'Sévère'), ('critical', 'Critique')], max_length=20)),
                ('clinical_notes', models.TextField(blank=True, verbose_name='Notes cliniques')),
                ('pain_level', models.PositiveIntegerField(blank=True, help_text='Échelle 1-10', null=True)),
                ('treatment_plan', models.TextField(blank=True, verbose_name='Plan de traitement')),
                ('follow_up_required', models.BooleanField(default=True)),
                ('next_appointment', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('diagnosed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('pathology', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='diagnoses', to='dentistry.dentalpathology')),
            ],
            options={
                'verbose_name': 'Patient Diagnosis',
                'verbose_name_plural': 'Patient Diagnoses',
                'ordering': ['-diagnosis_date'],
                'unique_together': {('patient_id', 'tooth_number', 'pathology', 'diagnosis_date')},
            },
        ),
        migrations.AddField(
            model_name='toothmodificationestimate',
            name='diagnosis',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='treatments', to='dentistry.patientdiagnosis', verbose_name='Diagnostic associé'),
        ),
        migrations.AddField(
            model_name='toothmodificationestimate',
            name='treatment_protocol',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='applications', to='dentistry.treatmentprotocol', verbose_name='Protocole de traitement'),
        ),
    ]
