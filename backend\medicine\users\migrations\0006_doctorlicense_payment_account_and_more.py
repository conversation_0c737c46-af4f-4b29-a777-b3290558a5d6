# Generated by Django 5.1.3 on 2025-04-30 16:25

import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0005_patient_fields'),
    ]

    operations = [
        migrations.AddField(
            model_name='doctorlicense',
            name='payment_account',
            field=models.CharField(blank=True, help_text='Account number used for payment', max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='doctorlicense',
            name='payment_amount',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Amount paid', max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='doctorlicense',
            name='payment_bank',
            field=models.CharField(blank=True, help_text='Bank used for payment', max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='doctorlicense',
            name='payment_currency',
            field=models.Char<PERSON>ield(default='USD', help_text='Currency of payment', max_length=3),
        ),
        migrations.AddField(
            model_name='doctorlicense',
            name='payment_date',
            field=models.DateTimeField(blank=True, help_text='Date when payment was made', null=True),
        ),
        migrations.AddField(
            model_name='doctorlicense',
            name='payment_method',
            field=models.CharField(blank=True, help_text='Payment method used', max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='doctorlicense',
            name='payment_reference',
            field=models.CharField(blank=True, help_text='Payment reference (e.g. email)', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='doctorlicense',
            name='payment_status',
            field=models.CharField(blank=True, help_text='Status of the payment', max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='doctorlicense',
            name='transaction_id',
            field=models.CharField(blank=True, help_text='Transaction ID for payment tracking', max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='slug',
            field=models.SlugField(blank=True, help_text='URL-friendly identifier', max_length=255, null=True, unique=True),
        ),
        migrations.AlterField(
            model_name='user',
            name='billing_cycle',
            field=models.CharField(blank=True, help_text='Billing cycle (monthly or annual)', max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='user',
            name='city_name',
            field=models.CharField(blank=True, help_text='City name (legacy)', max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='user',
            name='country_name',
            field=models.CharField(blank=True, help_text='Country name (legacy)', max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='user',
            name='email',
            field=models.EmailField(max_length=254, unique=True, verbose_name='email address'),
        ),
        migrations.AlterField(
            model_name='user',
            name='id',
            field=models.UUIDField(default=uuid.uuid4, editable=False, help_text='Unique identifier for the user', primary_key=True, serialize=False),
        ),
        migrations.AlterField(
            model_name='user',
            name='is_trial',
            field=models.BooleanField(default=False, help_text='Whether the user is in a trial period'),
        ),
        migrations.AlterField(
            model_name='user',
            name='package_id',
            field=models.CharField(blank=True, help_text='ID of the subscription package', max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='user',
            name='package_name',
            field=models.CharField(blank=True, help_text='Name of the subscription package', max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='user',
            name='package_price',
            field=models.CharField(blank=True, help_text='Price of the subscription package', max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='user',
            name='region_name',
            field=models.CharField(blank=True, help_text='Region name (legacy)', max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='user',
            name='subscription_period',
            field=models.CharField(blank=True, help_text='Subscription period (6months or 1year)', max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='user',
            name='trial_duration_months',
            field=models.PositiveSmallIntegerField(blank=True, help_text='Duration of the trial period in months', null=True),
        ),
        migrations.AlterField(
            model_name='user',
            name='trial_end_date',
            field=models.DateTimeField(blank=True, help_text='End date of the trial period', null=True),
        ),
        migrations.AlterField(
            model_name='user',
            name='trial_notification_sent',
            field=models.BooleanField(default=False, help_text='Whether a trial expiration notification has been sent'),
        ),
        migrations.AlterField(
            model_name='user',
            name='trial_request_approved',
            field=models.BooleanField(default=False, help_text='Whether the trial request has been approved'),
        ),
        migrations.AlterField(
            model_name='user',
            name='trial_request_approved_by',
            field=models.CharField(blank=True, help_text='Admin who approved the trial request', max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='user',
            name='trial_request_approved_date',
            field=models.DateTimeField(blank=True, help_text='Date when the trial request was approved', null=True),
        ),
        migrations.AlterField(
            model_name='user',
            name='trial_request_date',
            field=models.DateTimeField(blank=True, help_text='Date when the trial was requested', null=True),
        ),
        migrations.AlterField(
            model_name='user',
            name='trial_request_duration_months',
            field=models.PositiveSmallIntegerField(blank=True, help_text='Requested duration of the trial period in months', null=True),
        ),
        migrations.AlterField(
            model_name='user',
            name='trial_request_notes',
            field=models.TextField(blank=True, help_text='Notes about the trial request', null=True),
        ),
        migrations.AlterField(
            model_name='user',
            name='trial_requested',
            field=models.BooleanField(default=False, help_text='Whether the user has requested a trial period'),
        ),
        migrations.AlterField(
            model_name='user',
            name='trial_start_date',
            field=models.DateTimeField(blank=True, help_text='Start date of the trial period', null=True),
        ),
        migrations.AlterField(
            model_name='user',
            name='user_type',
            field=models.CharField(choices=[('doctor', 'Doctor'), ('assistant', 'Assistant'), ('patient', 'Patient'), ('admin', 'Admin')], default='patient', max_length=10),
        ),
    ]
