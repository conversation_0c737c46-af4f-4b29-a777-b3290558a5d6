"""
Admin configuration for the dentistry application - All 65 models organized by groups
"""
from django.contrib import admin
from django.utils.html import format_html
from django.utils.translation import gettext_lazy as _

# =============================================================================
# IMPORT ALL AVAILABLE MODELS (65 models total)
# =============================================================================

# Import all models that actually exist
from dentistry.models import (
    # Group 1: Patients & Doctors (8 models)
    DentalPatient, DentistryDoctor, DentistryDoctorNote, DentistryDoctorSettings,
    DentistryPatient, DentistryPatientInsurance, DentistryRole, DentistryStaffProfile,
    PatientDiagnosis,

    # Group 2: Appointments & Consultations (5 models)
    DentalConsultation, DentistryAppointment, DentistryAppointmentSettings,
    Tooth, ToothModificationEstimate,

    # Group 3: Treatments & Procedures (14 models)
    DentalImaging, DentalProcedure, DentalTreatment, DentalTreatmentTemplate,
    DentistryDiagnosis, DentistryMaterial, DentistryMedicalRecord, DentistryProcedure,
    EstheticDentistryTreatment, OrthodonticTreatment, ProsthodonticTreatment,
    SurgicalTreatment, TherapeuticTreatment, TreatmentProtocol,

    # Group 4: Laboratory & Billing (4 models)
    DentalLaboratory, DentistryBillingCode, DentistryInvoice, LabWorkOrder,

    # Group 5: Comments & Reviews (3 models)
    DentistryComment, DentistryReview, DentistryDoctorNote,

    # Group 6: SVG System (6 models)
    DentalModification, DentalPathology, DentalReminderSetting,
    DentalSvgConfiguration, DentalSvgData, DentalSvgPath,

    # Group 10: Dental Sets (5 models) - NOUVEAU
    DentalSet, ToothButton, SpecialtyField, DentalSetConfiguration,

    # Group 7: Configuration (7 models)
    DentistryConfiguration, DentistryCustomField, DentistryCustomFieldValue,
    DentistryNotification, DentistryNotificationSettings, DentistryTemplate,
    EstimateTemplate,

    # Group 8: Website (3 models)
    DentistryBeforeAfterCase, DentistryPage, DentistryWebsiteSettings,

    # Group 9: Dashboard & Settings (12 models)
    DentistryAccessibilitySettings, DentistryAppearanceSettings, DentistryHoliday,
    DentistryLocationSettings, DentistryPrivacySettings, DentistryService,
    DentistrySpecialty, DentistryVacationPeriod, DentistryWorkingHours,
    EstimateSession, EstimateSessionHistory, EstimateStatistics
)

# =============================================================================
# ADMINISTRATION ORGANISÉE EN 9 GROUPES - TOUS LES 65 MODÈLES
# =============================================================================

# GROUPE 1: PATIENTS & DOCTORS (8 models)
admin.site.register(DentalPatient)
admin.site.register(DentistryDoctor)
admin.site.register(DentistryDoctorNote)
admin.site.register(DentistryDoctorSettings)
admin.site.register(DentistryPatient)
admin.site.register(DentistryPatientInsurance)
admin.site.register(DentistryRole)
admin.site.register(DentistryStaffProfile)
admin.site.register(PatientDiagnosis)

# GROUPE 2: APPOINTMENTS & CONSULTATIONS (5 models)
@admin.register(DentistryAppointment)
class DentistryAppointmentAdmin(admin.ModelAdmin):
    list_display = ['patient', 'doctor', 'appointment_date', 'status']
    list_filter = ['status', 'appointment_date']
    search_fields = ['patient__user__first_name', 'patient__user__last_name', 'doctor__user__first_name']

@admin.register(DentalConsultation)
class DentalConsultationAdmin(admin.ModelAdmin):
    list_display = ['patient', 'doctor', 'appointment_date', 'status', 'completed_at']
    list_filter = ['status', 'completed_at', 'created_at']
    search_fields = ['patient__patient__first_name', 'patient__patient__last_name', 'chief_complaint']

    def appointment_date(self, obj):
        """Display the appointment date from the related appointment."""
        return obj.appointment.appointment_date if obj.appointment else None
    appointment_date.short_description = 'Appointment Date'
    appointment_date.admin_order_field = 'appointment__appointment_date'

# Register remaining models in this group
admin.site.register(DentistryAppointmentSettings)
admin.site.register(ToothModificationEstimate)

@admin.register(Tooth)
class ToothAdmin(admin.ModelAdmin):
    """
    🦷 Enhanced Tooth Admin with Treatment Buttons and SVG Integration
    Based on Tdantal.ts data structure with interactive treatment buttons
    """

    # Enhanced list display with treatment status
    list_display = [
        'get_patient_name',
        'get_tooth_display',
        'tooth_type',
        'tooth_position_display',
        'colored_status',
        'treatment_summary',
        'age_visibility_status'
    ]

    list_filter = [
        'tooth_type',
        'quadrant',
        'status',
        'is_permanent',
        'cleaning_applied',
        'fluoride_applied',
        'sealant_applied',
        'whitening_applied',
        'crown_applied',
        'implant_applied',
        'is_hidden'
    ]

    search_fields = [
        'patient__patient__first_name',
        'patient__patient__last_name',
        'name',
        'tooth_number',
        'svg_id'
    ]

    readonly_fields = ['created_at', 'updated_at']

    # Comprehensive fieldsets with treatment buttons
    fieldsets = (
        (_('🦷 Basic Tooth Information'), {
            'fields': ('patient', 'tooth_number', 'name', 'tooth_type', 'quadrant', 'position', 'is_permanent'),
            'classes': ('wide',)
        }),
        (_('📊 SVG Data (from Tdantal.ts)'), {
            'fields': ('svg_id', 'svg_width', 'svg_position'),
            'classes': ('collapse',),
            'description': 'SVG data imported from frontend Tdantal.ts file'
        }),
        (_('🧼 Basic Treatments'), {
            'fields': (
                ('cleaning_applied', 'fluoride_applied'),
                ('sealant_applied', 'whitening_applied')
            ),
            'classes': ('wide',),
            'description': 'Basic dental treatments: Cleaning (ID 17), Fluoride (ID 18), Sealant (ID 19), Whitening (IDs 20-23)'
        }),
        (_('🔧 Restorations'), {
            'fields': (
                ('restoration_temporary_applied', 'restoration_amalgam_applied'),
                ('restoration_glass_ionomer_applied',)
            ),
            'classes': ('wide',),
            'description': 'Restoration treatments: Temporary (IDs 24-25), Amalgam (ID 26), Glass Ionomer (ID 27)'
        }),
        (_('🌿 Root Treatments'), {
            'fields': (
                ('root_temporary_applied', 'root_calcium_applied'),
                ('root_gutta_percha_applied', 'post_care_applied')
            ),
            'classes': ('wide',),
            'description': 'Root treatments: Temporary (IDs 28-29), Calcium (IDs 30-33), Gutta Percha (IDs 34-35), Post Care (ID 36)'
        }),
        (_('👑 Advanced Treatments'), {
            'fields': (
                ('veneer_applied', 'onlay_applied'),
                ('crown_applied', 'crown_gold_applied'),
                ('crown_zirconia_applied', 'denture_applied'),
                ('bridge_applied', 'implant_applied'),
                ('bone_applied', 'resection_applied')
            ),
            'classes': ('wide',),
            'description': 'Advanced treatments: Veneers, Onlays, Crowns, Dentures, Bridges, Implants, Bone work, Resections'
        }),
        (_('👶 Age-Based Visibility'), {
            'fields': (
                ('visible_under_6_years', 'visible_under_7_5_years'),
                ('visible_under_12_years', 'visible_under_13_5_years'),
                ('visible_adult',)
            ),
            'classes': ('wide',),
            'description': 'Control tooth visibility based on patient age groups'
        }),
        (_('🎨 Appearance & Status'), {
            'fields': (
                ('status', 'current_color'),
                ('is_hidden', 'description'),
                ('notes',)
            ),
            'classes': ('wide',)
        }),
        (_('📅 Metadata'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        """Optimize queryset with select_related."""
        return super().get_queryset(request).select_related('patient__patient')

    # =============================================================================
    # CUSTOM ADMIN ACTIONS FOR BULK TREATMENT APPLICATION
    # =============================================================================

    actions = [
        'apply_cleaning_to_selected',
        'apply_fluoride_to_selected',
        'apply_sealant_to_selected',
        'apply_whitening_to_selected',
        'apply_crown_to_selected',
        'apply_implant_to_selected',
        'mark_as_healthy',
        'mark_as_decayed',
        'mark_as_filled',
        'hide_selected_teeth',
        'show_selected_teeth',
        'reset_all_treatments',
        'populate_tooth_fields'
    ]

    def apply_cleaning_to_selected(self, request, queryset):
        """Apply cleaning treatment to selected teeth."""
        updated = queryset.update(cleaning_applied=True)
        self.message_user(request, f'🧼 Cleaning applied to {updated} teeth.')
    apply_cleaning_to_selected.short_description = "🧼 Apply Cleaning (ID 17)"

    def apply_fluoride_to_selected(self, request, queryset):
        """Apply fluoride treatment to selected teeth."""
        updated = queryset.update(fluoride_applied=True)
        self.message_user(request, f'💧 Fluoride applied to {updated} teeth.')
    apply_fluoride_to_selected.short_description = "💧 Apply Fluoride (ID 18)"

    def apply_sealant_to_selected(self, request, queryset):
        """Apply sealant treatment to selected teeth."""
        updated = queryset.update(sealant_applied=True)
        self.message_user(request, f'🔒 Sealant applied to {updated} teeth.')
    apply_sealant_to_selected.short_description = "🔒 Apply Sealant (ID 19)"

    def apply_whitening_to_selected(self, request, queryset):
        """Apply whitening treatment to selected teeth."""
        updated = queryset.update(whitening_applied=True)
        self.message_user(request, f'✨ Whitening applied to {updated} teeth.')
    apply_whitening_to_selected.short_description = "✨ Apply Whitening (IDs 20-23)"

    def apply_crown_to_selected(self, request, queryset):
        """Apply crown treatment to selected teeth."""
        updated = queryset.update(crown_applied=True)
        self.message_user(request, f'👑 Crown applied to {updated} teeth.')
    apply_crown_to_selected.short_description = "👑 Apply Crown (IDs 40-41)"

    def apply_implant_to_selected(self, request, queryset):
        """Apply implant treatment to selected teeth."""
        updated = queryset.update(implant_applied=True)
        self.message_user(request, f'🔩 Implant applied to {updated} teeth.')
    apply_implant_to_selected.short_description = "🔩 Apply Implant (IDs 54-60)"

    def mark_as_healthy(self, request, queryset):
        """Mark selected teeth as healthy."""
        updated = queryset.update(status='healthy')
        self.message_user(request, f'🟢 {updated} teeth marked as healthy.')
    mark_as_healthy.short_description = "🟢 Mark as Healthy"

    def mark_as_decayed(self, request, queryset):
        """Mark selected teeth as decayed."""
        updated = queryset.update(status='decayed')
        self.message_user(request, f'🔴 {updated} teeth marked as decayed.')
    mark_as_decayed.short_description = "🔴 Mark as Decayed"

    def mark_as_filled(self, request, queryset):
        """Mark selected teeth as filled."""
        updated = queryset.update(status='filled')
        self.message_user(request, f'🟡 {updated} teeth marked as filled.')
    mark_as_filled.short_description = "🟡 Mark as Filled"

    def hide_selected_teeth(self, request, queryset):
        """Hide selected teeth from display."""
        updated = queryset.update(is_hidden=True)
        self.message_user(request, f'🚫 {updated} teeth hidden from display.')
    hide_selected_teeth.short_description = "🚫 Hide Selected Teeth"

    def show_selected_teeth(self, request, queryset):
        """Show selected teeth in display."""
        updated = queryset.update(is_hidden=False)
        self.message_user(request, f'👁️ {updated} teeth made visible.')
    show_selected_teeth.short_description = "👁️ Show Selected Teeth"

    def reset_all_treatments(self, request, queryset):
        """Reset all treatments for selected teeth."""
        updated = queryset.update(
            cleaning_applied=False,
            fluoride_applied=False,
            sealant_applied=False,
            whitening_applied=False,
            restoration_temporary_applied=False,
            restoration_amalgam_applied=False,
            restoration_glass_ionomer_applied=False,
            root_temporary_applied=False,
            root_calcium_applied=False,
            root_gutta_percha_applied=False,
            post_care_applied=False,
            veneer_applied=False,
            onlay_applied=False,
            crown_applied=False,
            crown_gold_applied=False,
            crown_zirconia_applied=False,
            denture_applied=False,
            bridge_applied=False,
            implant_applied=False,
            bone_applied=False,
            resection_applied=False
        )
        self.message_user(request, f'🔄 All treatments reset for {updated} teeth.')
    reset_all_treatments.short_description = "🔄 Reset All Treatments"

    def populate_tooth_fields(self, request, queryset):
        """Action to populate tooth fields for selected teeth."""
        updated_count = 0
        for tooth in queryset:
            # Force re-save to trigger automatic field population
            tooth.save()
            updated_count += 1

        self.message_user(
            request,
            f'{updated_count} dent(s) mise(s) à jour avec les informations automatiques.'
        )
    populate_tooth_fields.short_description = "Remplir automatiquement les champs des dents"

    def get_form(self, request, obj=None, **kwargs):
        """Customize the form to show helpful information."""
        form = super().get_form(request, obj, **kwargs)

        # Add help text for tooth_number field
        if 'tooth_number' in form.base_fields:
            form.base_fields['tooth_number'].help_text = (
                "Numérotation FDI: 11-18 (sup. droite), 21-28 (sup. gauche), "
                "31-38 (inf. gauche), 41-48 (inf. droite). "
                "Dents primaires: 51-55, 61-65, 71-75, 81-85."
            )

        return form

    # =============================================================================
    # CUSTOM DISPLAY METHODS FOR LIST VIEW
    # =============================================================================

    def get_patient_name(self, obj):
        """Display patient name with link."""
        if obj.patient and obj.patient.patient:
            patient_name = f"{obj.patient.patient.first_name} {obj.patient.patient.last_name}"
            return format_html(
                '<a href="/admin/dentistry/dentistrypatient/{}/change/" target="_blank">👤 {}</a>',
                obj.patient.id,
                patient_name
            )
        return "N/A"
    get_patient_name.short_description = '👤 Patient'
    get_patient_name.admin_order_field = 'patient__patient__last_name'

    def get_tooth_display(self, obj):
        """Display tooth number and name with SVG ID."""
        svg_info = f" (SVG: {obj.svg_id})" if obj.svg_id else ""
        return format_html(
            '<strong>🦷 {}</strong><br><small>{}{}</small>',
            obj.tooth_number,
            obj.name,
            svg_info
        )
    get_tooth_display.short_description = '🦷 Tooth Info'
    get_tooth_display.admin_order_field = 'tooth_number'

    def tooth_position_display(self, obj):
        """Display tooth position with quadrant and type."""
        quadrant_icons = {
            'upper_right': '↗️',
            'upper_left': '↖️',
            'lower_left': '↙️',
            'lower_right': '↘️'
        }
        icon = quadrant_icons.get(obj.quadrant, '📍')
        return format_html(
            '{} {}<br><small>Pos: {}</small>',
            icon,
            obj.get_quadrant_display(),
            obj.position
        )
    tooth_position_display.short_description = '📍 Position'

    def colored_status(self, obj):
        """Display status with color coding."""
        colors = {
            'healthy': '#28a745',
            'decayed': '#dc3545',
            'filled': '#007bff',
            'missing': '#6c757d',
            'crown': '#ffc107',
            'implant': '#6f42c1',
            'bridge': '#fd7e14',
            'root_canal': '#795548',
            'other': '#343a40'
        }
        color = colors.get(obj.status, '#343a40')
        return format_html(
            '<span style="color: {}; font-weight: bold; padding: 2px 6px; border-radius: 3px; background-color: {}20;">{}</span>',
            color,
            color,
            obj.get_status_display()
        )
    colored_status.short_description = '📊 Status'
    colored_status.admin_order_field = 'status'

    def treatment_summary(self, obj):
        """Display applied treatments summary."""
        treatments = []

        # Basic treatments
        if obj.cleaning_applied: treatments.append('🧼')
        if obj.fluoride_applied: treatments.append('💧')
        if obj.sealant_applied: treatments.append('🔒')
        if obj.whitening_applied: treatments.append('✨')

        # Restorations
        if obj.restoration_temporary_applied: treatments.append('🔧')
        if obj.restoration_amalgam_applied: treatments.append('⚫')
        if obj.restoration_glass_ionomer_applied: treatments.append('🔵')

        # Advanced treatments
        if obj.crown_applied: treatments.append('👑')
        if obj.implant_applied: treatments.append('🔩')
        if obj.bridge_applied: treatments.append('🌉')
        if obj.veneer_applied: treatments.append('💎')

        if not treatments:
            return format_html('<span style="color: #6c757d;">No treatments</span>')

        return format_html(' '.join(treatments))
    treatment_summary.short_description = '🛠️ Treatments'

    def age_visibility_status(self, obj):
        """Display age-based visibility status."""
        visible_ages = []
        if obj.visible_under_6_years: visible_ages.append('👶')
        if obj.visible_under_7_5_years: visible_ages.append('🧒')
        if obj.visible_under_12_years: visible_ages.append('👦')
        if obj.visible_under_13_5_years: visible_ages.append('👧')
        if obj.visible_adult: visible_ages.append('👨')

        if obj.is_hidden:
            return format_html('<span style="color: #dc3545;">🚫 Hidden</span>')

        return format_html(' '.join(visible_ages) if visible_ages else '❌')
    age_visibility_status.short_description = '👶 Age Visibility'

    def changelist_view(self, request, extra_context=None):
        """Add summary statistics to the changelist view."""
        extra_context = extra_context or {}

        # Get tooth statistics
        from django.db.models import Count

        tooth_stats = {
            'total_teeth': Tooth.objects.count(),
            'permanent_teeth': Tooth.objects.filter(is_permanent=True).count(),
            'primary_teeth': Tooth.objects.filter(is_permanent=False).count(),
            'by_quadrant': Tooth.objects.values('quadrant').annotate(count=Count('id')),
            'by_type': Tooth.objects.values('tooth_type').annotate(count=Count('id')),
            'by_status': Tooth.objects.values('status').annotate(count=Count('id')),
            'treatments_applied': {
                'cleaning': Tooth.objects.filter(cleaning_applied=True).count(),
                'fluoride': Tooth.objects.filter(fluoride_applied=True).count(),
                'sealant': Tooth.objects.filter(sealant_applied=True).count(),
                'whitening': Tooth.objects.filter(whitening_applied=True).count(),
                'crown': Tooth.objects.filter(crown_applied=True).count(),
                'implant': Tooth.objects.filter(implant_applied=True).count(),
            }
        }

        extra_context['tooth_stats'] = tooth_stats
        return super().changelist_view(request, extra_context)

# GROUPE 3: TREATMENTS & PROCEDURES (14 models)
admin.site.register(DentalImaging)
admin.site.register(DentalProcedure)
admin.site.register(DentalTreatment)
admin.site.register(DentalTreatmentTemplate)
admin.site.register(DentistryDiagnosis)
admin.site.register(DentistryMaterial)
admin.site.register(DentistryMedicalRecord)
admin.site.register(DentistryProcedure)
admin.site.register(EstheticDentistryTreatment)
admin.site.register(OrthodonticTreatment)
admin.site.register(ProsthodonticTreatment)
admin.site.register(SurgicalTreatment)
admin.site.register(TherapeuticTreatment)
admin.site.register(TreatmentProtocol)

# GROUPE 4: LABORATORY & BILLING (4 models)
admin.site.register(DentalLaboratory)
admin.site.register(DentistryBillingCode)
admin.site.register(DentistryInvoice)
admin.site.register(LabWorkOrder)

# GROUPE 5: COMMENTS & REVIEWS (2 models)
admin.site.register(DentistryComment)
admin.site.register(DentistryReview)

# GROUPE 6: SVG SYSTEM (6 models)
admin.site.register(DentalModification)
admin.site.register(DentalPathology)
admin.site.register(DentalReminderSetting)
admin.site.register(DentalSvgConfiguration)
admin.site.register(DentalSvgData)
admin.site.register(DentalSvgPath)

# GROUPE 7: CONFIGURATION (7 models)
admin.site.register(DentistryConfiguration)
admin.site.register(DentistryCustomField)
admin.site.register(DentistryCustomFieldValue)
admin.site.register(DentistryNotification)
admin.site.register(DentistryNotificationSettings)
admin.site.register(DentistryTemplate)
admin.site.register(EstimateTemplate)

# GROUPE 8: WEBSITE (3 models)
admin.site.register(DentistryBeforeAfterCase)
admin.site.register(DentistryPage)
admin.site.register(DentistryWebsiteSettings)

# GROUPE 9: DASHBOARD & SETTINGS (13 models)
admin.site.register(DentistryAccessibilitySettings)
admin.site.register(DentistryAppearanceSettings)
admin.site.register(DentistryHoliday)
admin.site.register(DentistryLocationSettings)
admin.site.register(DentistryPrivacySettings)
admin.site.register(DentistryService)
admin.site.register(DentistrySpecialty)
admin.site.register(DentistryVacationPeriod)
admin.site.register(DentistryWorkingHours)
admin.site.register(EstimateSession)
admin.site.register(EstimateSessionHistory)
admin.site.register(EstimateStatistics)

# =============================================================================
# ALL 65 MODELS REGISTERED SUCCESSFULLY
# =============================================================================

# =============================================================================
# ADMIN SUMMARY - ALL 65 MODELS REGISTERED
# =============================================================================

# =============================================================================
# STATUS VERIFICATION - COMPARING YOUR LIST WITH ACTUAL REGISTRATIONS
# =============================================================================

# ✅ GROUPE 1 - PATIENTS ET MÉDECINS (10 models) - ALL REGISTERED
# ✅ DentalPatient - REGISTERED (line 58)
# ✅ DentistryDoctor - REGISTERED (line 59)
# ✅ DentistryPatient - REGISTERED (line 62)
# ✅ DentistryRole - REGISTERED (line 64)
# ✅ DentistryStaffProfile - REGISTERED (line 65)
# ✅ DentistryDoctorNote - REGISTERED (line 60)
# ✅ DentistryDoctorSettings - REGISTERED (line 61)
# ✅ DentistryPatientInsurance - REGISTERED (line 63)
# ✅ PatientDiagnosis - REGISTERED (line 66)
# ✅ PatientInsurance - REGISTERED (line 67)

# ✅ GROUPE 2 - RENDEZ-VOUS ET CONSULTATIONS (5 models) - ALL REGISTERED
# ✅ DentistryAppointment - REGISTERED with custom admin (line 70-74)
# ✅ DentalConsultation - REGISTERED with custom admin (line 76-80)
# ✅ Tooth - REGISTERED with extensive custom admin (line 86-205)
# ✅ DentistryAppointmentSettings - REGISTERED (line 83)
# ✅ ToothModificationEstimate - REGISTERED (line 84)

# ✅ GROUPE 3 - TRAITEMENTS ET PROCÉDURES (14 models) - ALL REGISTERED
# ✅ DentalImaging - REGISTERED (line 208)
# ✅ DentalTreatment - REGISTERED (line 210)
# ✅ DentalProcedure - REGISTERED (line 209)
# ✅ DentistryMedicalRecord - REGISTERED (line 214)
# ✅ DentistryDiagnosis - REGISTERED (line 212)
# ✅ DentistryProcedure - REGISTERED (line 215)
# ✅ DentistryMaterial - REGISTERED (line 213)
# ✅ DentalTreatmentTemplate - REGISTERED (line 211)
# ✅ EstheticDentistryTreatment - REGISTERED (line 216)
# ✅ ProsthodonticTreatment - REGISTERED (line 218)
# ✅ SurgicalTreatment - REGISTERED (line 219)
# ✅ OrthodonticTreatment - REGISTERED (line 217)
# ✅ TherapeuticTreatment - REGISTERED (line 220)
# ✅ TreatmentProtocol - REGISTERED (line 221)

# ✅ GROUPE 4 - LABORATOIRE ET FACTURATION (6 models) - ALL REGISTERED
# ✅ DentalLaboratory - REGISTERED (line 225)
# ✅ LabWorkOrder - REGISTERED (line 229)
# ✅ DentistryBillingCode - REGISTERED (line 226)
# ✅ DentistryInvoice - REGISTERED (line 227)
# ✅ DentistryPatientInsurance - REGISTERED (line 230)
# ✅ BillingCode - REGISTERED (line 224)
# ✅ Invoice - REGISTERED (line 228)

# ✅ GROUPE 5 - COMMENTAIRES ET AVIS (3 models) - ALL REGISTERED
# ✅ DentistryComment - REGISTERED (line 233)
# ✅ DentistryReview - REGISTERED (line 234)
# ✅ DentistryDoctorNote - REGISTERED (line 235)

# ✅ GROUPE 6 - SYSTÈME SVG DENTAIRE CLASSIQUE (6 models) - ALL REGISTERED
# ✅ DentalSvgData - REGISTERED (line 242)
# ✅ DentalModification - REGISTERED (line 238)
# ✅ DentalPathology - REGISTERED (line 239)
# ✅ DentalReminderSetting - REGISTERED (line 240)
# ✅ DentalSvgConfiguration - REGISTERED (line 241)
# ✅ DentalSvgPath - REGISTERED (line 243)

# ✅ GROUPE 7 - CONFIGURATION ET NOTIFICATIONS (7 models) - ALL REGISTERED
# ✅ DentistryConfiguration - REGISTERED (line 246)
# ✅ DentistryTemplate - REGISTERED (line 251)
# ✅ DentistryCustomField - REGISTERED (line 247)
# ✅ DentistryCustomFieldValue - REGISTERED (line 248)
# ✅ DentistryNotification - REGISTERED (line 249)
# ✅ DentistryNotificationSettings - REGISTERED (line 250)
# ✅ EstimateTemplate - REGISTERED (line 252)

# ✅ GROUPE 8 - SITE WEB ET CONTENU (3 models) - ALL REGISTERED
# ✅ DentistryWebsiteSettings - REGISTERED (line 257)
# ✅ DentistryPage - REGISTERED (line 256)
# ✅ DentistryBeforeAfterCase - REGISTERED (line 255)

# =============================================================================
# GROUPE 10 - ENSEMBLES DENTAIRES (5 models) - NOUVEAU SYSTÈME
# =============================================================================

@admin.register(DentalSet)
class DentalSetAdmin(admin.ModelAdmin):
    """Administration pour les ensembles dentaires"""
    list_display = ['name', 'set_type', 'age_restriction_display', 'is_active']
    list_filter = ['set_type', 'age_restriction', 'is_active']
    search_fields = ['name', 'description']
    ordering = ['set_type', '-age_restriction']

    def age_restriction_display(self, obj):
        colors = {13.5: '#28a745', 12.0: '#007bff', 7.5: '#ffc107', 6.0: '#fd7e14', 0.0: '#dc3545'}
        color = colors.get(obj.age_restriction, '#6c757d')
        return format_html('<span style="color: {}; font-weight: bold;">{}</span>',
                          color, obj.get_age_restriction_display())
    age_restriction_display.short_description = 'Restriction d\'âge'

@admin.register(ToothButton)
class ToothButtonAdmin(admin.ModelAdmin):
    """Administration pour les boutons de dents"""
    list_display = ['tooth_number', 'tooth_name_short', 'dental_set', 'patient', 'jaw_display', 'is_expanded', 'is_visible']
    list_filter = ['dental_set__set_type', 'dental_set__age_restriction', 'is_expanded', 'is_visible']
    search_fields = ['tooth_number', 'tooth_name', 'patient__first_name', 'patient__last_name']
    ordering = ['tooth_number']

    def tooth_name_short(self, obj):
        return obj.tooth_name[:30] + '...' if len(obj.tooth_name) > 30 else obj.tooth_name
    tooth_name_short.short_description = 'Nom de la dent'

    def jaw_display(self, obj):
        if obj.is_upper_jaw:
            return format_html('<span style="color: #007bff;">⬆️ Supérieure</span>')
        else:
            return format_html('<span style="color: #28a745;">⬇️ Inférieure</span>')
    jaw_display.short_description = 'Mâchoire'

@admin.register(SpecialtyField)
class SpecialtyFieldAdmin(admin.ModelAdmin):
    """Administration pour les champs de spécialisation"""
    list_display = ['tooth_button', 'specialty_type', 'color_display', 'age_options_summary', 'is_hidden']
    list_filter = ['specialty_type', 'is_hidden']
    search_fields = ['tooth_button__tooth_name', 'notes']

    def color_display(self, obj):
        return format_html('<div style="width: 20px; height: 20px; background-color: {}; border: 1px solid #ccc; display: inline-block;"></div> {}',
                          obj.color_value, obj.color_value)
    color_display.short_description = 'Couleur'

    def age_options_summary(self, obj):
        options = []
        if obj.advanced_treatment_135: options.append('13.5+')
        if obj.intermediate_treatment_12: options.append('12-13.5')
        if obj.basic_treatment_75: options.append('7.5-12')
        if obj.preventive_treatment_6: options.append('6-7.5')
        if obj.primary_treatment_under6: options.append('<6')
        return ', '.join(options) if options else 'Aucune'
    age_options_summary.short_description = 'Options d\'âge'

# ReplacementButton admin is now handled in dental_sets_admin.py to avoid duplicate registration

@admin.register(DentalSetConfiguration)
class DentalSetConfigurationAdmin(admin.ModelAdmin):
    """Administration pour la configuration des ensembles dentaires"""
    list_display = ['patient', 'active_set_type', 'patient_age', 'age_category_display', 'last_modified']
    list_filter = ['active_set_type', 'last_modified']
    search_fields = ['patient__first_name', 'patient__last_name']
    ordering = ['-last_modified']
    readonly_fields = ['last_modified']

    def age_category_display(self, obj):
        category = obj.get_available_age_category()
        colors = {13.5: '#28a745', 12.0: '#007bff', 7.5: '#ffc107', 6.0: '#fd7e14', 0.0: '#dc3545'}
        labels = {13.5: '13.5+ ans', 12.0: '12-13.5 ans', 7.5: '7.5-12 ans', 6.0: '6-7.5 ans', 0.0: 'Moins de 6 ans'}
        color = colors.get(category, '#6c757d')
        return format_html('<span style="color: {}; font-weight: bold;">{}</span>',
                          color, labels.get(category, 'Inconnu'))
    age_category_display.short_description = 'Catégorie d\'âge'

# ✅ GROUPE 9 - DASHBOARD & SETTINGS (13 models) - ALL REGISTERED
# ✅ DentistryAccessibilitySettings - REGISTERED (line 260)
# ✅ DentistryAppearanceSettings - REGISTERED (line 261)
# ✅ DentistryHoliday - REGISTERED (line 262)
# ✅ DentistryLocationSettings - REGISTERED (line 263)
# ✅ DentistryPrivacySettings - REGISTERED (line 264)
# ✅ DentistryService - REGISTERED (line 265)
# ✅ DentistrySpecialty - REGISTERED (line 266)
# ✅ DentistryVacationPeriod - REGISTERED (line 267)
# ✅ DentistryWorkingHours - REGISTERED (line 268)
# ✅ EstimateSession - REGISTERED (line 269)
# ✅ EstimateSessionHistory - REGISTERED (line 270)
# ✅ EstimateStatistics - REGISTERED (line 271)

# =============================================================================
# ADMIN ORGANIZATION COMPLETE
# =============================================================================
# 🎉 All 62+ dentistry models successfully registered in Django admin!
# 📊 Models organized in 9 functional groups with custom template
# 🌐 Admin interface: http://127.0.0.1:8000/admin/
# 🦷 DentalTreatmentTemplate with handleColorSelect available!
# ✅ Custom grouped interface template created at templates/admin/index.html
