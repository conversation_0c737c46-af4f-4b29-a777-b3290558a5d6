"""
Laboratory views for the dentistry application.
"""
from rest_framework import viewsets, permissions, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from dentistry.models import DentalLaboratory, LabWorkOrder
from dentistry.serializers import (
    DentalLaboratorySerializer, LabWorkOrderSerializer
)

class DentalLaboratoryViewSet(viewsets.ModelViewSet):
    """
    API endpoint for dental laboratories.
    """
    queryset = DentalLaboratory.objects.all()
    serializer_class = DentalLaboratorySerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['is_preferred', 'specializations']
    search_fields = ['name', 'address', 'contact_person', 'notes']
    ordering_fields = ['name', 'quality_rating', 'average_turnaround_days', 'created_at']
    
    @action(detail=True, methods=['get'])
    def work_orders(self, request, pk=None):
        """
        Get all work orders for a laboratory.
        """
        laboratory = self.get_object()
        work_orders = laboratory.work_orders.all()
        
        page = self.paginate_queryset(work_orders)
        if page is not None:
            serializer = LabWorkOrderSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = LabWorkOrderSerializer(work_orders, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def preferred(self, request):
        """
        Get all preferred laboratories.
        """
        preferred_labs = self.queryset.filter(is_preferred=True)
        
        page = self.paginate_queryset(preferred_labs)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(preferred_labs, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def by_specialization(self, request):
        """
        Get laboratories by specialization.
        """
        specialization = request.query_params.get('specialization')
        
        if not specialization:
            return Response(
                {"detail": "Specialization parameter is required."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Filter labs that have the requested specialization
        # This assumes specializations is a JSONField with a list of specializations
        labs = self.queryset.filter(specializations__contains=[specialization])
        
        page = self.paginate_queryset(labs)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(labs, many=True)
        return Response(serializer.data)

class LabWorkOrderViewSet(viewsets.ModelViewSet):
    """
    API endpoint for laboratory work orders.
    """
    queryset = LabWorkOrder.objects.all()
    serializer_class = LabWorkOrderSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['laboratory', 'patient', 'doctor', 'work_type', 'material', 'status', 'is_invoiced', 'is_paid']
    search_fields = ['order_number', 'description', 'tooth_numbers', 'special_instructions', 'lab_notes', 'internal_notes']
    ordering_fields = ['date_sent', 'requested_return_date', 'actual_return_date', 'created_at']
    
    def get_queryset(self):
        """
        Filter work orders based on query parameters.
        """
        queryset = super().get_queryset()
        
        # Filter by date range for date_sent
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        
        if start_date:
            queryset = queryset.filter(date_sent__gte=start_date)
        if end_date:
            queryset = queryset.filter(date_sent__lte=end_date)
        
        # Filter by tooth number
        tooth_number = self.request.query_params.get('tooth_number')
        if tooth_number:
            queryset = queryset.filter(tooth_numbers__contains=tooth_number)
        
        return queryset
    
    @action(detail=True, methods=['post'])
    def update_status(self, request, pk=None):
        """
        Update the status of a work order.
        """
        work_order = self.get_object()
        
        status_value = request.data.get('status')
        if not status_value:
            return Response(
                {"detail": "Status is required."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Validate the status value
        valid_statuses = [choice[0] for choice in LabWorkOrder.STATUS_CHOICES]
        if status_value not in valid_statuses:
            return Response(
                {"detail": f"Invalid status. Must be one of: {', '.join(valid_statuses)}"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Update the status
        work_order.status = status_value
        
        # If status is 'returned', set the actual_return_date
        if status_value == 'returned' and not work_order.actual_return_date:
            from datetime import date
            work_order.actual_return_date = date.today()
        
        # Add notes if provided
        notes = request.data.get('notes')
        if notes:
            if status_value in ['received_by_lab', 'in_progress', 'completed_by_lab']:
                # These are lab notes
                work_order.lab_notes += f"\n\n{notes}" if work_order.lab_notes else notes
            else:
                # These are internal notes
                work_order.internal_notes += f"\n\n{notes}" if work_order.internal_notes else notes
        
        work_order.save()
        
        serializer = self.get_serializer(work_order)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def pending(self, request):
        """
        Get all pending work orders (sent but not returned).
        """
        pending_orders = self.queryset.filter(
            status__in=['sent', 'received_by_lab', 'in_progress', 'completed_by_lab']
        )
        
        page = self.paginate_queryset(pending_orders)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(pending_orders, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def overdue(self, request):
        """
        Get all overdue work orders.
        """
        from datetime import date
        today = date.today()
        
        overdue_orders = self.queryset.filter(
            requested_return_date__lt=today,
            status__in=['sent', 'received_by_lab', 'in_progress', 'completed_by_lab']
        )
        
        page = self.paginate_queryset(overdue_orders)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(overdue_orders, many=True)
        return Response(serializer.data)
