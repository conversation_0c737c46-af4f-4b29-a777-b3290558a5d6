{% extends "admin/change_list.html" %}
{% load i18n admin_urls static admin_list %}

{% block content_title %}
    <h1>{{ title }}</h1>
    
    {% if tooth_stats %}
    <div class="module" style="margin-bottom: 20px;">
        <h2>Statistiques des Dents</h2>
        <div style="display: flex; gap: 20px; flex-wrap: wrap;">
            <div class="form-row">
                <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; min-width: 200px;">
                    <h3 style="margin: 0 0 10px 0; color: #333;">R<PERSON><PERSON><PERSON></h3>
                    <p><strong>Total des dents:</strong> {{ tooth_stats.total_teeth }}</p>
                    <p><strong>Dents permanentes:</strong> {{ tooth_stats.permanent_teeth }}</p>
                    <p><strong>Dents primaires:</strong> {{ tooth_stats.primary_teeth }}</p>
                </div>
            </div>
            
            <div class="form-row">
                <div style="background: #e3f2fd; padding: 15px; border-radius: 5px; min-width: 200px;">
                    <h3 style="margin: 0 0 10px 0; color: #333;">Par Quadrant</h3>
                    {% for quadrant in tooth_stats.by_quadrant %}
                        <p><strong>{{ quadrant.quadrant|capfirst }}:</strong> {{ quadrant.count }}</p>
                    {% endfor %}
                </div>
            </div>
            
            <div class="form-row">
                <div style="background: #e8f5e8; padding: 15px; border-radius: 5px; min-width: 200px;">
                    <h3 style="margin: 0 0 10px 0; color: #333;">Par Type</h3>
                    {% for type in tooth_stats.by_type %}
                        <p><strong>{{ type.tooth_type|capfirst }}:</strong> {{ type.count }}</p>
                    {% endfor %}
                </div>
            </div>
            
            <div class="form-row">
                <div style="background: #fff3e0; padding: 15px; border-radius: 5px; min-width: 200px;">
                    <h3 style="margin: 0 0 10px 0; color: #333;">Par État</h3>
                    {% for status in tooth_stats.by_status %}
                        <p><strong>{{ status.status|capfirst }}:</strong> {{ status.count }}</p>
                    {% endfor %}
                </div>
            </div>
        </div>
        
        <div style="margin-top: 15px; padding: 10px; background: #f0f8ff; border-left: 4px solid #2196f3;">
            <h4 style="margin: 0 0 5px 0;">Système de Numérotation FDI</h4>
            <p style="margin: 0; font-size: 12px; color: #666;">
                <strong>Dents permanentes:</strong> 
                Quadrant sup. droit (11-18), sup. gauche (21-28), inf. gauche (31-38), inf. droit (41-48)<br>
                <strong>Dents primaires:</strong> 
                Quadrant sup. droit (51-55), sup. gauche (61-65), inf. gauche (71-75), inf. droit (81-85)
            </p>
        </div>
    </div>
    {% endif %}
{% endblock %}

{% block extrahead %}
    {{ block.super }}
    <style>
        .module h2 {
            background: #417690;
            color: white;
            padding: 10px 15px;
            margin: 0;
            font-size: 14px;
            font-weight: bold;
        }
        .form-row h3 {
            font-size: 13px;
            font-weight: bold;
        }
        .form-row p {
            margin: 5px 0;
            font-size: 12px;
        }
    </style>
{% endblock %}
