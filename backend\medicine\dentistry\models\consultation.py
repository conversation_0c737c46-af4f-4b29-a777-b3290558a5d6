"""
Dental consultation models for the healthcare service.
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from dentistry.models.base import DentistryBaseModel
from dentistry.models.doctor import DentistryDoctor
from dentistry.models.patient import DentistryPatient
from dentistry.models.appointment import DentistryAppointment

class DentalConsultation(DentistryBaseModel):
    """
    Dental consultation model.
    """
    appointment = models.OneToOneField(
        DentistryAppointment,
        on_delete=models.CASCADE,
        related_name="dental_consultation",
        verbose_name=_("Appointment")
    )
    doctor = models.ForeignKey(
        DentistryDoctor,
        on_delete=models.CASCADE,
        related_name="dental_consultations",
        verbose_name=_("Doctor")
    )
    patient = models.ForeignKey(
        DentistryPatient,
        on_delete=models.CASCADE,
        related_name="dental_consultations",
        verbose_name=_("Patient")
    )
    chief_complaint = models.TextField(
        null=True,
        blank=True,
        verbose_name=_("Chief complaint")
    )
    dental_history = models.TextField(
        null=True,
        blank=True,
        verbose_name=_("Dental history")
    )
    medical_history = models.TextField(
        null=True,
        blank=True,
        verbose_name=_("Medical history")
    )
    medications = models.TextField(
        null=True,
        blank=True,
        verbose_name=_("Medications")
    )
    allergies = models.TextField(
        null=True,
        blank=True,
        verbose_name=_("Allergies")
    )
    examination_notes = models.TextField(
        null=True,
        blank=True,
        verbose_name=_("Examination notes")
    )
    assessment = models.TextField(
        null=True,
        blank=True,
        verbose_name=_("Assessment")
    )
    treatment_plan = models.TextField(
        null=True,
        blank=True,
        verbose_name=_("Treatment plan")
    )
    notes = models.TextField(
        null=True,
        blank=True,
        verbose_name=_("Notes")
    )
    status = models.CharField(
        max_length=20,
        choices=[
            ('draft', _('Draft')),
            ('completed', _('Completed')),
            ('signed', _('Signed')),
        ],
        default='draft',
        verbose_name=_("Status")
    )
    completed_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_("Completed at")
    )
    signed_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_("Signed at")
    )

    class Meta:
        verbose_name = _("Dental Consultation")
        verbose_name_plural = _("Dental Consultations")
        ordering = ['-created_at']

    def __str__(self):
        return f"Dental consultation for {self.patient} by {self.doctor} on {self.appointment.appointment_date}"

class Tooth(DentistryBaseModel):
    """
    Enhanced Tooth model with SVG data integration from Tdantal.ts
    """
    TOOTH_NUMBERS = [
        (11, _('Upper Right Central Incisor')),
        (12, _('Upper Right Lateral Incisor')),
        (13, _('Upper Right Canine')),
        (14, _('Upper Right First Premolar')),
        (15, _('Upper Right Second Premolar')),
        (16, _('Upper Right First Molar')),
        (17, _('Upper Right Second Molar')),
        (18, _('Upper Right Third Molar')),
        (21, _('Upper Left Central Incisor')),
        (22, _('Upper Left Lateral Incisor')),
        (23, _('Upper Left Canine')),
        (24, _('Upper Left First Premolar')),
        (25, _('Upper Left Second Premolar')),
        (26, _('Upper Left First Molar')),
        (27, _('Upper Left Second Molar')),
        (28, _('Upper Left Third Molar')),
        (31, _('Lower Left Central Incisor')),
        (32, _('Lower Left Lateral Incisor')),
        (33, _('Lower Left Canine')),
        (34, _('Lower Left First Premolar')),
        (35, _('Lower Left Second Premolar')),
        (36, _('Lower Left First Molar')),
        (37, _('Lower Left Second Molar')),
        (38, _('Lower Left Third Molar')),
        (41, _('Lower Right Central Incisor')),
        (42, _('Lower Right Lateral Incisor')),
        (43, _('Lower Right Canine')),
        (44, _('Lower Right First Premolar')),
        (45, _('Lower Right Second Premolar')),
        (46, _('Lower Right First Molar')),
        (47, _('Lower Right Second Molar')),
        (48, _('Lower Right Third Molar')),
        # Primary teeth
        (51, _('Upper Right Primary Central Incisor')),
        (52, _('Upper Right Primary Lateral Incisor')),
        (53, _('Upper Right Primary Canine')),
        (54, _('Upper Right Primary First Molar')),
        (55, _('Upper Right Primary Second Molar')),
        (61, _('Upper Left Primary Central Incisor')),
        (62, _('Upper Left Primary Lateral Incisor')),
        (63, _('Upper Left Primary Canine')),
        (64, _('Upper Left Primary First Molar')),
        (65, _('Upper Left Primary Second Molar')),
        (71, _('Lower Left Primary Central Incisor')),
        (72, _('Lower Left Primary Lateral Incisor')),
        (73, _('Lower Left Primary Canine')),
        (74, _('Lower Left Primary First Molar')),
        (75, _('Lower Left Primary Second Molar')),
        (81, _('Lower Right Primary Central Incisor')),
        (82, _('Lower Right Primary Lateral Incisor')),
        (83, _('Lower Right Primary Canine')),
        (84, _('Lower Right Primary First Molar')),
        (85, _('Lower Right Primary Second Molar')),
    ]

    TOOTH_TYPE_CHOICES = [
        ('incisor', _('Incisor')),
        ('canine', _('Canine')),
        ('premolar', _('Premolar')),
        ('molar', _('Molar')),
        ('wisdom', _('Wisdom Tooth')),
        ('other', _('Other')),
    ]

    QUADRANT_CHOICES = [
        ('upper_right', _('Upper Right')),
        ('upper_left', _('Upper Left')),
        ('lower_left', _('Lower Left')),
        ('lower_right', _('Lower Right')),
    ]

    patient = models.ForeignKey(
        DentistryPatient,
        on_delete=models.CASCADE,
        related_name="teeth",
        verbose_name=_("Patient")
    )
    tooth_number = models.PositiveSmallIntegerField(
        choices=TOOTH_NUMBERS,
        verbose_name=_("Tooth number")
    )
    name = models.CharField(
        max_length=100,
        verbose_name=_("Tooth name"),
        help_text=_("Name of the tooth (e.g., Upper Right Central Incisor)")
    )
    tooth_type = models.CharField(
        max_length=20,
        choices=TOOTH_TYPE_CHOICES,
        verbose_name=_("Tooth type")
    )
    quadrant = models.CharField(
        max_length=20,
        choices=QUADRANT_CHOICES,
        verbose_name=_("Quadrant")
    )
    position = models.PositiveSmallIntegerField(
        verbose_name=_("Position in quadrant"),
        help_text=_("Position of the tooth within its quadrant (1-8)")
    )
    is_permanent = models.BooleanField(
        default=True,
        verbose_name=_("Is permanent tooth"),
        help_text=_("True for permanent teeth, False for primary teeth")
    )
    description = models.TextField(
        null=True,
        blank=True,
        verbose_name=_("Description"),
        help_text=_("Additional description or notes about the tooth")
    )
    status = models.CharField(
        max_length=50,
        choices=[
            ('healthy', _('Healthy')),
            ('decayed', _('Decayed')),
            ('filled', _('Filled')),
            ('missing', _('Missing')),
            ('crown', _('Crown')),
            ('implant', _('Implant')),
            ('bridge', _('Bridge')),
            ('root_canal', _('Root Canal')),
            ('other', _('Other')),
        ],
        default='healthy',
        verbose_name=_("Status")
    )
    notes = models.TextField(
        null=True,
        blank=True,
        verbose_name=_("Notes")
    )

    # =============================================================================
    # SVG DATA FIELDS (from Tdantal.ts)
    # =============================================================================
    svg_id = models.CharField(
        max_length=10,
        null=True,
        blank=True,
        verbose_name=_("SVG ID"),
        help_text=_("SVG identifier from Tdantal.ts")
    )
    svg_width = models.CharField(
        max_length=20,
        null=True,
        blank=True,
        verbose_name=_("SVG Width"),
        help_text=_("SVG width (e.g., '59.8625px')")
    )
    svg_position = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name=_("SVG Position"),
        help_text=_("SVG viewBox position (e.g., '0 0 50.8 172')")
    )

    # =============================================================================
    # TREATMENT BUTTONS STATUS (from Tdantal.ts)
    # =============================================================================
    # Basic treatments
    cleaning_applied = models.BooleanField(
        default=False,
        verbose_name=_("Cleaning Applied"),
        help_text=_("Nettoyage - ID 17")
    )
    fluoride_applied = models.BooleanField(
        default=False,
        verbose_name=_("Fluoride Applied"),
        help_text=_("Fluorure - ID 18")
    )
    sealant_applied = models.BooleanField(
        default=False,
        verbose_name=_("Sealant Applied"),
        help_text=_("Scellant - ID 19")
    )

    # Whitening (toggle between IDs 20, 21, 22, 23)
    whitening_applied = models.BooleanField(
        default=False,
        verbose_name=_("Whitening Applied"),
        help_text=_("Blanchiment - IDs 20, 21, 22, 23")
    )

    # Restorations
    restoration_temporary_applied = models.BooleanField(
        default=False,
        verbose_name=_("Temporary Restoration Applied"),
        help_text=_("Restauration Temporaire - IDs 24, 25")
    )
    restoration_amalgam_applied = models.BooleanField(
        default=False,
        verbose_name=_("Amalgam Restoration Applied"),
        help_text=_("Restauration Amalgame - ID 26")
    )
    restoration_glass_ionomer_applied = models.BooleanField(
        default=False,
        verbose_name=_("Glass Ionomer Restoration Applied"),
        help_text=_("Restauration Verre Ionomère - ID 27")
    )

    # Root treatments
    root_temporary_applied = models.BooleanField(
        default=False,
        verbose_name=_("Root Temporary Applied"),
        help_text=_("Root Temporaire - IDs 28, 29")
    )
    root_calcium_applied = models.BooleanField(
        default=False,
        verbose_name=_("Root Calcium Applied"),
        help_text=_("Root Calcium - IDs 30-33")
    )
    root_gutta_percha_applied = models.BooleanField(
        default=False,
        verbose_name=_("Root Gutta Percha Applied"),
        help_text=_("Root Gutta Percha Mode - IDs 34, 35")
    )

    # Advanced treatments
    post_care_applied = models.BooleanField(
        default=False,
        verbose_name=_("Post Care Applied"),
        help_text=_("Post Care - ID 36")
    )
    veneer_applied = models.BooleanField(
        default=False,
        verbose_name=_("Veneer Applied"),
        help_text=_("Facettes - ID 37")
    )
    onlay_applied = models.BooleanField(
        default=False,
        verbose_name=_("Onlay Applied"),
        help_text=_("Onlay - IDs 38, 39")
    )
    crown_applied = models.BooleanField(
        default=False,
        verbose_name=_("Crown Applied"),
        help_text=_("Couronne - IDs 40, 41")
    )
    crown_gold_applied = models.BooleanField(
        default=False,
        verbose_name=_("Gold Crown Applied"),
        help_text=_("Couronne Or - IDs 43, 44")
    )
    crown_zirconia_applied = models.BooleanField(
        default=False,
        verbose_name=_("Zirconia Crown Applied"),
        help_text=_("Couronne Zirconia - IDs 45, 46, 47")
    )
    denture_applied = models.BooleanField(
        default=False,
        verbose_name=_("Denture Applied"),
        help_text=_("Prothèse - IDs 48, 49, 50")
    )
    bridge_applied = models.BooleanField(
        default=False,
        verbose_name=_("Bridge Applied"),
        help_text=_("Bridge - IDs 51, 52")
    )
    implant_applied = models.BooleanField(
        default=False,
        verbose_name=_("Implant Applied"),
        help_text=_("Implant - IDs 54-60")
    )
    bone_applied = models.BooleanField(
        default=False,
        verbose_name=_("Bone Applied"),
        help_text=_("Bone - IDs 61, 62, 63")
    )
    resection_applied = models.BooleanField(
        default=False,
        verbose_name=_("Resection Applied"),
        help_text=_("Résection - Various IDs")
    )

    # =============================================================================
    # AGE-BASED VISIBILITY SETTINGS
    # =============================================================================
    visible_under_6_years = models.BooleanField(
        default=True,
        verbose_name=_("Visible Under 6 Years"),
        help_text=_("Show this tooth for patients under 6 years")
    )
    visible_under_7_5_years = models.BooleanField(
        default=True,
        verbose_name=_("Visible Under 7.5 Years"),
        help_text=_("Show this tooth for patients under 7.5 years")
    )
    visible_under_12_years = models.BooleanField(
        default=True,
        verbose_name=_("Visible Under 12 Years"),
        help_text=_("Show this tooth for patients under 12 years")
    )
    visible_under_13_5_years = models.BooleanField(
        default=True,
        verbose_name=_("Visible Under 13.5 Years"),
        help_text=_("Show this tooth for patients under 13.5 years")
    )
    visible_adult = models.BooleanField(
        default=True,
        verbose_name=_("Visible Adult"),
        help_text=_("Show this tooth for adult patients")
    )

    # =============================================================================
    # COLOR AND STYLING
    # =============================================================================
    current_color = models.CharField(
        max_length=20,
        null=True,
        blank=True,
        verbose_name=_("Current Color"),
        help_text=_("Current applied color for the tooth")
    )
    is_hidden = models.BooleanField(
        default=False,
        verbose_name=_("Is Hidden"),
        help_text=_("Hide this tooth from display")
    )

    class Meta:
        verbose_name = _("Tooth")
        verbose_name_plural = _("Teeth")
        unique_together = ('patient', 'tooth_number')

    def save(self, *args, **kwargs):
        """
        Override save method to automatically populate fields based on tooth_number.
        """
        if self.tooth_number:
            # Auto-populate name from TOOTH_NUMBERS choices
            if not self.name:
                self.name = self.get_tooth_number_display()

            # Auto-populate tooth_type based on tooth number
            if not self.tooth_type:
                self.tooth_type = self._get_tooth_type()

            # Auto-populate quadrant based on tooth number
            if not self.quadrant:
                self.quadrant = self._get_quadrant()

            # Auto-populate position based on tooth number
            if not self.position:
                self.position = self._get_position()

            # Auto-populate is_permanent based on tooth number
            if self.is_permanent is None:
                self.is_permanent = self._get_is_permanent()

        super().save(*args, **kwargs)

    def _get_tooth_type(self):
        """Determine tooth type based on tooth number."""
        tooth_num = self.tooth_number
        last_digit = tooth_num % 10

        if last_digit in [1, 2]:
            return 'incisor'
        elif last_digit == 3:
            return 'canine'
        elif last_digit in [4, 5]:
            return 'premolar'
        elif last_digit in [6, 7]:
            return 'molar'
        elif last_digit == 8:
            return 'wisdom'
        else:
            return 'other'

    def _get_quadrant(self):
        """Determine quadrant based on tooth number."""
        tooth_num = self.tooth_number
        first_digit = tooth_num // 10

        if first_digit == 1:
            return 'upper_right'
        elif first_digit == 2:
            return 'upper_left'
        elif first_digit == 3:
            return 'lower_left'
        elif first_digit == 4:
            return 'lower_right'
        else:
            return 'upper_right'  # Default fallback

    def _get_position(self):
        """Determine position within quadrant based on tooth number."""
        return self.tooth_number % 10

    def _get_is_permanent(self):
        """Determine if tooth is permanent based on tooth number."""
        tooth_num = self.tooth_number
        first_digit = tooth_num // 10
        # Primary teeth have first digits 5, 6, 7, 8
        return first_digit not in [5, 6, 7, 8]

    def __str__(self):
        return f"Tooth {self.tooth_number} ({self.name}) of {self.patient}"
