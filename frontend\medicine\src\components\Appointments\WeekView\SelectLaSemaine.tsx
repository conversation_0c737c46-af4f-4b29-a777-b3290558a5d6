import { Select } from "@mantine/core";
import { useState, useEffect } from "react";
import moment from "moment";
import "moment/locale/en-gb"; // Import the English locale for date formatting
moment.locale("fr"); // Assurer que les jours sont affichés en français

interface SelectLaSemaineProps {
  date: Date;
  setter: (newDate: Date) => void;
  label: string;
}

const SelectLaSemaine: React.FC<SelectLaSemaineProps> = ({
  date,
  setter,
  label,
}) => {
  const startOfWeek = moment(date).startOf("isoWeek"); // Assurer un début le lundi

  // Générer les 7 jours de la semaine
  const weekDays = Array.from({ length: 7 }, (_, i) => {
    const day = startOfWeek.clone().add(i, "days");
    return {
      value: day.format("YYYY-MM-DD"), // Valeur en ISO format
      label: day.format("dddd DD/MM"), // Ex: "Lundi 24/02"
    };
  });

  const [selected, setSelected] = useState<string>(moment(date).format("YYYY-MM-DD"));

  useEffect(() => {
    setSelected(moment(label, "DD MMMM YYYY").format("YYYY-MM-DD"));
  }, [label]);

  const handleDateChange = (value: string | null) => {
    if (value) {
      setSelected(value);
      setter(moment(value, "YYYY-MM-DD").toDate());
    }
  };

  return (
    <Select
      placeholder="Choisissez un jour"
      data={weekDays}
      value={selected}
      onChange={handleDateChange}
      className="leading5 text-sm font-medium"
    />
  );
};


export default SelectLaSemaine;
