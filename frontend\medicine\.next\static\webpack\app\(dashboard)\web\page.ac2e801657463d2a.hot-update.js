"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/web/page",{

/***/ "(app-pages-browser)/./src/components/Appointments/overview/AjouterUnRendezVous.tsx":
/*!**********************************************************************!*\
  !*** ./src/components/Appointments/overview/AjouterUnRendezVous.tsx ***!
  \**********************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Text/Text.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Select/Select.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/NumberInput/NumberInput.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Button/Button.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Modal/Modal.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Group/Group.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Switch/Switch.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Menu/Menu.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Avatar/Avatar.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/TextInput/TextInput.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ActionIcon/ActionIcon.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Radio/Radio.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/InputBase/InputBase.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ColorPicker/ColorPicker.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Textarea/Textarea.mjs\");\n/* harmony import */ var simplebar_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! simplebar-react */ \"(app-pages-browser)/./node_modules/simplebar-react/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCheck_IconColorPicker_IconHexagonPlusFilled_tabler_icons_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=IconCheck,IconColorPicker,IconHexagonPlusFilled!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconHexagonPlusFilled.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCheck_IconColorPicker_IconHexagonPlusFilled_tabler_icons_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=IconCheck,IconColorPicker,IconHexagonPlusFilled!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconColorPicker.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCheck_IconColorPicker_IconHexagonPlusFilled_tabler_icons_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=IconCheck,IconColorPicker,IconHexagonPlusFilled!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconCheck.mjs\");\n/* harmony import */ var _barrel_optimize_names_FaCalendarPlus_FaMicrophone_FaMicrophoneLines_FaUserDoctor_react_icons_fa6__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=FaCalendarPlus,FaMicrophone,FaMicrophoneLines,FaUserDoctor!=!react-icons/fa6 */ \"(app-pages-browser)/./node_modules/react-icons/fa6/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_RiUserFollowLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=RiUserFollowLine!=!react-icons/ri */ \"(app-pages-browser)/./node_modules/react-icons/ri/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_LiaAddressCardSolid_LiaBirthdayCakeSolid_react_icons_lia__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=LiaAddressCardSolid,LiaBirthdayCakeSolid!=!react-icons/lia */ \"(app-pages-browser)/./node_modules/react-icons/lia/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_TbNumber_react_icons_tb__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=TbNumber!=!react-icons/tb */ \"(app-pages-browser)/./node_modules/react-icons/tb/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FiPhone_react_icons_fi__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=FiPhone!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_CiAt_react_icons_ci__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=CiAt!=!react-icons/ci */ \"(app-pages-browser)/./node_modules/react-icons/ci/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_MdOutlineBedroomChild_MdOutlineContentPasteSearch_MdOutlineSocialDistance_react_icons_md__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=MdOutlineBedroomChild,MdOutlineContentPasteSearch,MdOutlineSocialDistance!=!react-icons/md */ \"(app-pages-browser)/./node_modules/react-icons/md/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ListPlus_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=ListPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/list-plus.js\");\n/* harmony import */ var react_imask__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-imask */ \"(app-pages-browser)/./node_modules/react-imask/esm/index.js\");\n/* harmony import */ var _mantine_notifications__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @mantine/notifications */ \"(app-pages-browser)/./node_modules/@mantine/notifications/esm/notifications.store.mjs\");\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! moment */ \"(app-pages-browser)/./node_modules/moment/moment.js\");\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(moment__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst RendezVousSelector = (param)=>{\n    let { onClose } = param;\n    _s();\n    const [selectedPeriod, setSelectedPeriod] = react__WEBPACK_IMPORTED_MODULE_1___default().useState('15days');\n    const [, setStartDate] = react__WEBPACK_IMPORTED_MODULE_1___default().useState('12/06/2025');\n    const [duration, setDuration] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(30);\n    const [numberOfDays, setNumberOfDays] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(3);\n    const [selectedSlots, setSelectedSlots] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(new Set());\n    // Générer les créneaux horaires\n    const generateTimeSlots = ()=>{\n        const slots = [];\n        const startHour = 8;\n        const endHour = 14;\n        for(let hour = startHour; hour < endHour; hour++){\n            for(let minute = 0; minute < 60; minute += 30){\n                const startTime = \"\".concat(hour.toString().padStart(2, '0'), \":\").concat(minute.toString().padStart(2, '0'));\n                const endMinute = minute + 30;\n                const endHour = endMinute >= 60 ? hour + 1 : hour;\n                const adjustedEndMinute = endMinute >= 60 ? endMinute - 60 : endMinute;\n                const endTime = \"\".concat(endHour.toString().padStart(2, '0'), \":\").concat(adjustedEndMinute.toString().padStart(2, '0'));\n                slots.push({\n                    id: \"\".concat(hour, \"-\").concat(minute),\n                    startTime,\n                    endTime\n                });\n            }\n        }\n        return slots;\n    };\n    // Calculer la date selon la période sélectionnée\n    const getDateForPeriod = ()=>{\n        switch(selectedPeriod){\n            case '15days':\n                return '12 juin 2025';\n            case '1month':\n                return '26 juin 2025';\n            case '3months':\n                return '10 juillet 2025';\n            default:\n                return '12 juin 2025';\n        }\n    };\n    // Calculer la date de début selon la période\n    const getStartDateForPeriod = ()=>{\n        switch(selectedPeriod){\n            case '15days':\n                return '12/06/2025';\n            case '1month':\n                return '25/06/2025';\n            case '3months':\n                return '10/07/2025';\n            default:\n                return '12/06/2025';\n        }\n    };\n    // Calculer la date formatée selon la période\n    const getFormattedDateForPeriod = ()=>{\n        switch(selectedPeriod){\n            case '15days':\n                return '12/06/2025';\n            case '1month':\n                return '26/06/2025';\n            case '3months':\n                return '10/07/2025';\n            default:\n                return '12/06/2025';\n        }\n    };\n    const timeSlots = generateTimeSlots();\n    const handleSlotToggle = (slotId)=>{\n        const newSelectedSlots = new Set(selectedSlots);\n        if (newSelectedSlots.has(slotId)) {\n            newSelectedSlots.delete(slotId);\n        } else {\n            newSelectedSlots.add(slotId);\n        }\n        setSelectedSlots(newSelectedSlots);\n    };\n    const isValidateEnabled = selectedSlots.size > 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-12 gap-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"col-span-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 bg-gray-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                        size: \"sm\",\n                                        fw: 500,\n                                        mb: \"xs\",\n                                        children: \"\\xc0 partir de\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                        value: getStartDateForPeriod(),\n                                        onChange: (value)=>setStartDate(value || ''),\n                                        data: [\n                                            {\n                                                value: '12/06/2025',\n                                                label: '12/06/2025'\n                                            },\n                                            {\n                                                value: '25/06/2025',\n                                                label: '25/06/2025'\n                                            },\n                                            {\n                                                value: '10/07/2025',\n                                                label: '10/07/2025'\n                                            },\n                                            {\n                                                value: '10/09/2025',\n                                                label: '10/09/2025'\n                                            }\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                        size: \"sm\",\n                                        fw: 500,\n                                        mb: \"xs\",\n                                        children: \"Dur\\xe9e (min)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.NumberInput, {\n                                        value: duration,\n                                        onChange: (value)=>setDuration(Number(value)),\n                                        min: 15,\n                                        max: 120,\n                                        step: 15\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                        size: \"sm\",\n                                        fw: 500,\n                                        mb: \"xs\",\n                                        children: \"Nbre des jours\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.NumberInput, {\n                                        value: numberOfDays,\n                                        onChange: (value)=>setNumberOfDays(Number(value)),\n                                        min: 1,\n                                        max: 30\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                lineNumber: 228,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"col-span-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                    size: \"lg\",\n                                    fw: 600,\n                                    children: getDateForPeriod()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                    size: \"sm\",\n                                    color: \"dimmed\",\n                                    children: \"24\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2 max-h-96 overflow-y-auto\",\n                            children: timeSlots.map((slot)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between p-2 hover:bg-gray-50 rounded\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                    size: \"sm\",\n                                                    children: \"le\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                    size: \"sm\",\n                                                    color: \"blue\",\n                                                    fw: 500,\n                                                    children: getFormattedDateForPeriod()\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                    size: \"sm\",\n                                                    children: \"de\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                    size: \"sm\",\n                                                    color: \"red\",\n                                                    fw: 500,\n                                                    children: slot.startTime\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                    lineNumber: 285,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                    size: \"sm\",\n                                                    children: \"\\xe0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                    lineNumber: 288,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                    size: \"sm\",\n                                                    color: \"green\",\n                                                    fw: 500,\n                                                    children: slot.endTime\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: selectedSlots.has(slot.id),\n                                            onChange: ()=>handleSlotToggle(slot.id),\n                                            className: \"form-checkbox h-4 w-4 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, slot.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                            lineNumber: 276,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center mt-6 pt-4 border-t\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                            variant: selectedPeriod === '15days' ? 'filled' : 'outline',\n                                            onClick: ()=>setSelectedPeriod('15days'),\n                                            size: \"sm\",\n                                            children: \"15 jours\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                            variant: selectedPeriod === '1month' ? 'filled' : 'outline',\n                                            onClick: ()=>setSelectedPeriod('1month'),\n                                            size: \"sm\",\n                                            children: \"1 Mois\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                            variant: selectedPeriod === '3months' ? 'filled' : 'outline',\n                                            onClick: ()=>setSelectedPeriod('3months'),\n                                            size: \"sm\",\n                                            children: \"3 Mois\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                            color: isValidateEnabled ? 'blue' : 'gray',\n                                            disabled: !isValidateEnabled,\n                                            onClick: ()=>{\n                                                // Logique de validation ici\n                                                onClose();\n                                            },\n                                            children: \"Valider\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                            variant: \"outline\",\n                                            color: \"red\",\n                                            onClick: onClose,\n                                            children: \"Annuler\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                    lineNumber: 328,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                            lineNumber: 303,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                    lineNumber: 270,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                lineNumber: 269,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n        lineNumber: 227,\n        columnNumber: 5\n    }, undefined);\n};\n_s(RendezVousSelector, \"JA7kajyxwsY0NPzKL2AkP/6ymxQ=\");\n_c = RendezVousSelector;\nconst AjouterUnRendezVous = (props)=>{\n    const { opened, onClose, appointmentForm, handleSubmit, eventTitle, setEventTitle, titleOptions, setTitleOptions, newTitle, setNewTitle, patientName, setPatientName, patientlastName, setPatientlastName, openListDesPatient, eventDateDeNaissance, handleDateChange, eventAge, genderOption, handleOptionChange, eventEtatCivil, setEventEtatCivil, eventCin, setEventCin, address, setAddress, eventTelephone, setEventTelephone, email, setEmail, patientdoctor, setPatientDocteur, patientsocialSecurity, setSocialSecurity, consultationTypes, setConsultationTypes, patienttypeConsultation, setPatientTypeConsultation, setEventType, searchValue, setSearchValue, dureeDeLexamen, getEventTypeColor, newConsultationType, setNewConsultationType, newConsultationColor, setNewConsultationColor, ColorPickeropened, openedColorPicker, closeColorPicker, changeEndValue, setChangeEndValue, setDureeDeLexamen, eventAganda, setEventAganda, agendaTypes, setAgendaTypes, newAgendaType, setNewAgendaType, isWaitingList, eventDate, setEventDate, eventTime, setEventTime, eventConsultation, openListRendezVous, ListRendezVousOpened, closeListRendezVous, patientcomment, setPatientcomment, patientnotes, setPatientNotes, patientcommentairelistedattente, setPatientCommentairelistedattente, eventResourceId, setEventResourceId, eventType, checkedAppelvideo, handleAppelvideoChange, checkedRappelSms, handleRappelSmsChange, checkedRappelEmail, handleRappelEmailChange, currentPatient, waitingList, setWaitingList, setPatientModalOpen, // New props for Edit Modal\n    showEditModal, setShowEditModal, selectedEvent, setSelectedEvent, resetForm, handleEditSubmit, closeRendezVous, initialConsultationTypes } = props;\n    var _eventAge_toString;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Modal.Root, {\n                opened: opened,\n                onClose: onClose,\n                size: \"70%\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Modal.Overlay, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                        lineNumber: 461,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Modal.Content, {\n                        className: \"overflow-y-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Modal.Header, {\n                                style: {\n                                    height: '60px',\n                                    background: \"#3799CE\",\n                                    padding: \"11px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Modal.Title, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                fw: 600,\n                                                c: \"var(--mantine-color-white)\",\n                                                className: \"mb-2 flex gap-2 text-lg font-semibold leading-none tracking-tight\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        width: \"1em\",\n                                                        height: \"1em\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M21 7.5V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h3.5M16 2v4M8 2v4m-5 4h5m9.5 7.5L16 16.3V14\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 479,\n                                                                    columnNumber: 19\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                    cx: 16,\n                                                                    cy: 16,\n                                                                    r: 6\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 480,\n                                                                    columnNumber: 19\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 472,\n                                                            columnNumber: 17\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                        lineNumber: 466,\n                                                        columnNumber: 15\n                                                    }, undefined),\n                                                    \"Ajouter un rendez-vous\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                lineNumber: 465,\n                                                columnNumber: 13\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-muted-foreground text-sm text-[var(--mantine-color-white)]\",\n                                                children: \"Remplissez les d\\xe9tails ci-dessous pour ajouter un nouvel \\xe9v\\xe9nement.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 13\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                        lineNumber: 464,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Group, {\n                                        justify: \"flex-end\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Switch, {\n                                                defaultChecked: true,\n                                                color: \"teal\",\n                                                size: \"xs\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                lineNumber: 490,\n                                                columnNumber: 13\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-muted-foreground text-sm text-[var(--mantine-color-white)]\",\n                                                children: \"Pause\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                lineNumber: 495,\n                                                columnNumber: 13\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Modal.CloseButton, {\n                                                className: \"mantine-focus-always\",\n                                                style: {\n                                                    color: \"white\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                lineNumber: 498,\n                                                columnNumber: 13\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                        lineNumber: 489,\n                                        columnNumber: 11\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                lineNumber: 463,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Modal.Body, {\n                                style: {\n                                    padding: '0px'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"py-2 pl-4 h-[600px]\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(simplebar_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"simplebar-scrollable-y h-[calc(100%)]\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"pr-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                onSubmit: (e)=>{\n                                                    e.preventDefault();\n                                                    handleSubmit(appointmentForm.values);\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid gap-3 py-2 pr-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-4 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                                    value: eventTitle,\n                                                                    onChange: (value)=>setEventTitle(value !== null && value !== void 0 ? value : \"\"),\n                                                                    placeholder: \"Titre\",\n                                                                    data: titleOptions,\n                                                                    className: \"w-full\",\n                                                                    leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarPlus_FaMicrophone_FaMicrophoneLines_FaUserDoctor_react_icons_fa6__WEBPACK_IMPORTED_MODULE_12__.FaUserDoctor, {\n                                                                        size: 16\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 516,\n                                                                        columnNumber: 38\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 510,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Menu, {\n                                                                    width: 200,\n                                                                    shadow: \"md\",\n                                                                    closeOnItemClick: false,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Menu.Target, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Avatar, {\n                                                                                color: \"#4BA3D3\",\n                                                                                radius: \"sm\",\n                                                                                h: 36,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCheck_IconColorPicker_IconHexagonPlusFilled_tabler_icons_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                    size: 20\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                    lineNumber: 521,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                lineNumber: 520,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                            lineNumber: 519,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Menu.Dropdown, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.TextInput, {\n                                                                                leftSectionPointerEvents: \"none\",\n                                                                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarPlus_FaMicrophone_FaMicrophoneLines_FaUserDoctor_react_icons_fa6__WEBPACK_IMPORTED_MODULE_12__.FaUserDoctor, {\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                    lineNumber: 527,\n                                                                                    columnNumber: 42\n                                                                                }, void 0),\n                                                                                placeholder: \"Ajouter des titres\",\n                                                                                value: newTitle,\n                                                                                onChange: (e)=>setNewTitle(e.target.value),\n                                                                                rightSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.ActionIcon, {\n                                                                                    size: \"sm\",\n                                                                                    onClick: ()=>{\n                                                                                        if (newTitle.trim()) {\n                                                                                            const newTitleOption = {\n                                                                                                value: newTitle,\n                                                                                                label: newTitle\n                                                                                            };\n                                                                                            setTitleOptions([\n                                                                                                ...titleOptions,\n                                                                                                newTitleOption\n                                                                                            ]);\n                                                                                            setEventTitle(newTitle);\n                                                                                            setNewTitle(\"\");\n                                                                                            _mantine_notifications__WEBPACK_IMPORTED_MODULE_18__.notifications.show({\n                                                                                                title: 'Titre ajouté',\n                                                                                                message: '\"'.concat(newTitle, '\" a \\xe9t\\xe9 ajout\\xe9 \\xe0 la liste des titres'),\n                                                                                                color: 'green',\n                                                                                                autoClose: 2000\n                                                                                            });\n                                                                                        }\n                                                                                    },\n                                                                                    disabled: !newTitle.trim(),\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarPlus_FaMicrophone_FaMicrophoneLines_FaUserDoctor_react_icons_fa6__WEBPACK_IMPORTED_MODULE_12__.FaCalendarPlus, {\n                                                                                        size: 16\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                        lineNumber: 550,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                    lineNumber: 532,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                onKeyDown: (e)=>{\n                                                                                    if (e.key === 'Enter' && newTitle.trim()) {\n                                                                                        const newTitleOption = {\n                                                                                            value: newTitle,\n                                                                                            label: newTitle\n                                                                                        };\n                                                                                        setTitleOptions([\n                                                                                            ...titleOptions,\n                                                                                            newTitleOption\n                                                                                        ]);\n                                                                                        setEventTitle(newTitle);\n                                                                                        setNewTitle(\"\");\n                                                                                        _mantine_notifications__WEBPACK_IMPORTED_MODULE_18__.notifications.show({\n                                                                                            title: 'Titre ajouté',\n                                                                                            message: '\"'.concat(newTitle, '\" a \\xe9t\\xe9 ajout\\xe9 \\xe0 la liste des titres'),\n                                                                                            color: 'green',\n                                                                                            autoClose: 2000\n                                                                                        });\n                                                                                    }\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                lineNumber: 525,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                            lineNumber: 524,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 518,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.TextInput, {\n                                                                    id: \"event-nom\",\n                                                                    placeholder: \"Nom *\",\n                                                                    type: \"text\",\n                                                                    value: patientName,\n                                                                    onChange: (e)=>setPatientName(e.target.value),\n                                                                    required: true,\n                                                                    className: \"input input-bordered w-full\",\n                                                                    leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiUserFollowLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_19__.RiUserFollowLine, {\n                                                                        size: 16\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 578,\n                                                                        columnNumber: 38\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 570,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.TextInput, {\n                                                                    id: \"event-prenom\",\n                                                                    placeholder: \"Pr\\xe9nom *\",\n                                                                    type: \"text\",\n                                                                    value: patientlastName,\n                                                                    onChange: (e)=>setPatientlastName(e.target.value),\n                                                                    required: true,\n                                                                    className: \"input input-bordered w-full\",\n                                                                    leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiUserFollowLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_19__.RiUserFollowLine, {\n                                                                        size: 16\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 588,\n                                                                        columnNumber: 38\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 580,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Avatar, {\n                                                                    color: \"#4BA3D3\",\n                                                                    radius: \"sm\",\n                                                                    h: 36,\n                                                                    onClick: openListDesPatient,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MdOutlineBedroomChild_MdOutlineContentPasteSearch_MdOutlineSocialDistance_react_icons_md__WEBPACK_IMPORTED_MODULE_20__.MdOutlineContentPasteSearch, {\n                                                                        size: 20\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 591,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 590,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 509,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-4 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.TextInput, {\n                                                                    type: \"date\",\n                                                                    placeholder: \"Date de Naissance...\",\n                                                                    id: \"event-dateDeNaissance\",\n                                                                    value: eventDateDeNaissance,\n                                                                    onChange: handleDateChange,\n                                                                    required: true,\n                                                                    className: \"input input-bordered max-w-[278px] w-full\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 597,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.TextInput, {\n                                                                    type: \"text\",\n                                                                    id: \"event-age\",\n                                                                    value: (_eventAge_toString = eventAge === null || eventAge === void 0 ? void 0 : eventAge.toString()) !== null && _eventAge_toString !== void 0 ? _eventAge_toString : \"\",\n                                                                    placeholder: eventAge !== null ? eventAge.toString() : \"Veuillez entrer votre date de naissance\",\n                                                                    readOnly: true,\n                                                                    className: \"input input-bordered max-w-[278px] w-full\",\n                                                                    leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LiaAddressCardSolid_LiaBirthdayCakeSolid_react_icons_lia__WEBPACK_IMPORTED_MODULE_21__.LiaBirthdayCakeSolid, {\n                                                                        size: 16\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 617,\n                                                                        columnNumber: 38\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 606,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Radio.Group, {\n                                                                        value: genderOption,\n                                                                        onChange: handleOptionChange,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Radio, {\n                                                                                    value: \"Homme\",\n                                                                                    label: \"Homme\"\n                                                                                }, \"homme\", false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                    lineNumber: 625,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Radio, {\n                                                                                    value: \"Femme\",\n                                                                                    label: \"Femme\"\n                                                                                }, \"femme\", false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                    lineNumber: 626,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Radio, {\n                                                                                    value: \"Enfant\",\n                                                                                    label: \"Enfant\"\n                                                                                }, \"enfant\", false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                    lineNumber: 627,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                            lineNumber: 624,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 620,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 619,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 596,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                                    value: eventEtatCivil,\n                                                                    onChange: (value)=>setEventEtatCivil(value !== null && value !== void 0 ? value : \"\"),\n                                                                    placeholder: \"\\xc9tat civil\",\n                                                                    data: [\n                                                                        {\n                                                                            value: \"Célibataire\",\n                                                                            label: \"Célibataire\"\n                                                                        },\n                                                                        {\n                                                                            value: \"Marié(e)\",\n                                                                            label: \"Marié(e)\"\n                                                                        },\n                                                                        {\n                                                                            value: \"Divorcé(e)\",\n                                                                            label: \"Divorcé(e)\"\n                                                                        },\n                                                                        {\n                                                                            value: \"Veuf(ve)\",\n                                                                            label: \"Veuf(ve)\"\n                                                                        },\n                                                                        {\n                                                                            value: \"Autre chose\",\n                                                                            label: \"Autre chose\"\n                                                                        }\n                                                                    ],\n                                                                    className: \"select w-full max-w-xs\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 635,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.TextInput, {\n                                                                    placeholder: \"CIN\",\n                                                                    disabled: genderOption === 'Enfant',\n                                                                    value: eventCin,\n                                                                    onChange: (e)=>setEventCin(e.target.value),\n                                                                    styles: {\n                                                                        input: {\n                                                                            backgroundColor: genderOption === 'Enfant' ? '#f5f5f5' : undefined,\n                                                                            color: genderOption === 'Enfant' ? '#999' : undefined\n                                                                        }\n                                                                    },\n                                                                    className: \"input input-bordered mb-2 w-full\",\n                                                                    leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TbNumber_react_icons_tb__WEBPACK_IMPORTED_MODULE_23__.TbNumber, {\n                                                                        size: 16\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 660,\n                                                                        columnNumber: 38\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 648,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.TextInput, {\n                                                                    id: \"Adresse\",\n                                                                    placeholder: \"Adress\\xe9 par\",\n                                                                    value: address,\n                                                                    onChange: (e)=>setAddress(e.target.value),\n                                                                    className: \"input input-bordered mb-2 w-full\",\n                                                                    leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LiaAddressCardSolid_LiaBirthdayCakeSolid_react_icons_lia__WEBPACK_IMPORTED_MODULE_21__.LiaAddressCardSolid, {\n                                                                        size: 16\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 668,\n                                                                        columnNumber: 38\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 662,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 634,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.InputBase, {\n                                                                    id: \"T\\xe9l\\xe9phone\",\n                                                                    component: react_imask__WEBPACK_IMPORTED_MODULE_2__.IMaskInput,\n                                                                    mask: \"00-00-00-00-00\",\n                                                                    placeholder: \"T\\xe9l\\xe9phone\",\n                                                                    value: eventTelephone,\n                                                                    onAccept: (value)=>setEventTelephone(value),\n                                                                    unmask: true,\n                                                                    className: \"input input-bordered mb-2 w-full\",\n                                                                    leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiPhone_react_icons_fi__WEBPACK_IMPORTED_MODULE_25__.FiPhone, {\n                                                                        size: 16\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 683,\n                                                                        columnNumber: 38\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 674,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.TextInput, {\n                                                                    id: \"Email\",\n                                                                    placeholder: \"Email\",\n                                                                    value: email,\n                                                                    onChange: (e)=>setEmail(e.target.value),\n                                                                    className: \"input input-bordered mb-2 w-full\",\n                                                                    leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CiAt_react_icons_ci__WEBPACK_IMPORTED_MODULE_26__.CiAt, {\n                                                                        size: 16\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 691,\n                                                                        columnNumber: 38\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 685,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 673,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                                    value: patientdoctor,\n                                                                    onChange: (value)=>setPatientDocteur(value !== null && value !== void 0 ? value : \"\"),\n                                                                    placeholder: \"Docteur\",\n                                                                    data: [\n                                                                        {\n                                                                            value: \"Docteur\",\n                                                                            label: \"Docteur\"\n                                                                        },\n                                                                        {\n                                                                            value: \"dr.Kader\",\n                                                                            label: \"dr.Kader\"\n                                                                        },\n                                                                        {\n                                                                            value: \"dr.Kaders\",\n                                                                            label: \"dr.Kaders\"\n                                                                        }\n                                                                    ],\n                                                                    className: \"w-full\",\n                                                                    leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarPlus_FaMicrophone_FaMicrophoneLines_FaUserDoctor_react_icons_fa6__WEBPACK_IMPORTED_MODULE_12__.FaUserDoctor, {\n                                                                        size: 16\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 707,\n                                                                        columnNumber: 38\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 697,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                                    value: patientsocialSecurity || 'Aucune',\n                                                                    onChange: (value)=>setSocialSecurity(value || 'Aucune'),\n                                                                    placeholder: \"S\\xe9curit\\xe9 sociale\",\n                                                                    data: [\n                                                                        {\n                                                                            value: \"Aucune\",\n                                                                            label: \"Aucune\"\n                                                                        },\n                                                                        {\n                                                                            value: \"CNSS\",\n                                                                            label: \"CNSS\"\n                                                                        },\n                                                                        {\n                                                                            value: \"AMO\",\n                                                                            label: \"AMO\"\n                                                                        }\n                                                                    ],\n                                                                    className: \"w-full\",\n                                                                    leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MdOutlineBedroomChild_MdOutlineContentPasteSearch_MdOutlineSocialDistance_react_icons_md__WEBPACK_IMPORTED_MODULE_20__.MdOutlineSocialDistance, {\n                                                                        size: 16\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 719,\n                                                                        columnNumber: 38\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 709,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 696,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-4 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                                    label: \"Type de consultation\",\n                                                                    placeholder: \"Rechercher ou saisir...\",\n                                                                    data: consultationTypes,\n                                                                    value: patienttypeConsultation,\n                                                                    onChange: (value)=>{\n                                                                        setPatientTypeConsultation(value !== null && value !== void 0 ? value : \"\");\n                                                                        const selectedLabel = [\n                                                                            {\n                                                                                value: \"Visite de malade\",\n                                                                                eventType: \"visit\"\n                                                                            },\n                                                                            {\n                                                                                value: \"Visitor Counter\",\n                                                                                eventType: \"visitor-counter\"\n                                                                            },\n                                                                            {\n                                                                                value: \"Completed\",\n                                                                                eventType: \"completed\"\n                                                                            }\n                                                                        ].find((item)=>item.value === value);\n                                                                        if (selectedLabel) {\n                                                                            setEventType(selectedLabel.eventType);\n                                                                        }\n                                                                    },\n                                                                    searchable: true,\n                                                                    searchValue: searchValue,\n                                                                    onSearchChange: setSearchValue,\n                                                                    clearable: true,\n                                                                    maxDropdownHeight: 280,\n                                                                    rightSectionWidth: 70,\n                                                                    required: true,\n                                                                    rightSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"bg-[#4CAF50] text-white px-2 py-1 rounded text-xs\",\n                                                                        children: dureeDeLexamen\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 750,\n                                                                        columnNumber: 27\n                                                                    }, void 0),\n                                                                    allowDeselect: true,\n                                                                    className: \"w-full\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 725,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Menu, {\n                                                                    width: 260,\n                                                                    shadow: \"md\",\n                                                                    closeOnItemClick: false,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Menu.Target, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Avatar, {\n                                                                                color: \"#4BA3D3\",\n                                                                                radius: \"sm\",\n                                                                                h: 36,\n                                                                                mt: \"24\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCheck_IconColorPicker_IconHexagonPlusFilled_tabler_icons_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                    size: 20\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                    lineNumber: 758,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                lineNumber: 757,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                            lineNumber: 756,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Menu.Dropdown, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.TextInput, {\n                                                                                        leftSectionPointerEvents: \"none\",\n                                                                                        leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarPlus_FaMicrophone_FaMicrophoneLines_FaUserDoctor_react_icons_fa6__WEBPACK_IMPORTED_MODULE_12__.FaUserDoctor, {\n                                                                                            size: 16\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                            lineNumber: 765,\n                                                                                            columnNumber: 44\n                                                                                        }, void 0),\n                                                                                        placeholder: \"Ajouter des Consultation\",\n                                                                                        value: newConsultationType,\n                                                                                        onChange: (e)=>setNewConsultationType(e.target.value),\n                                                                                        rightSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.ActionIcon, {\n                                                                                            size: \"sm\",\n                                                                                            onClick: ()=>{\n                                                                                                if (newConsultationType.trim()) {\n                                                                                                    const newType = {\n                                                                                                        value: newConsultationType,\n                                                                                                        label: newConsultationType,\n                                                                                                        duration: dureeDeLexamen || \"15 min\"\n                                                                                                    };\n                                                                                                    setConsultationTypes([\n                                                                                                        ...consultationTypes,\n                                                                                                        newType\n                                                                                                    ]);\n                                                                                                    setPatientTypeConsultation(newConsultationType);\n                                                                                                    setNewConsultationType(\"\");\n                                                                                                    _mantine_notifications__WEBPACK_IMPORTED_MODULE_18__.notifications.show({\n                                                                                                        title: 'Type de consultation ajouté',\n                                                                                                        message: '\"'.concat(newConsultationType, '\" a \\xe9t\\xe9 ajout\\xe9'),\n                                                                                                        color: 'green',\n                                                                                                        autoClose: 2000\n                                                                                                    });\n                                                                                                }\n                                                                                            },\n                                                                                            disabled: !newConsultationType.trim(),\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarPlus_FaMicrophone_FaMicrophoneLines_FaUserDoctor_react_icons_fa6__WEBPACK_IMPORTED_MODULE_12__.FaCalendarPlus, {\n                                                                                                size: 16\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                                lineNumber: 792,\n                                                                                                columnNumber: 35\n                                                                                            }, void 0)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                            lineNumber: 770,\n                                                                                            columnNumber: 33\n                                                                                        }, void 0),\n                                                                                        onKeyDown: (e)=>{\n                                                                                            if (e.key === 'Enter' && newConsultationType.trim()) {\n                                                                                                const newType = {\n                                                                                                    value: newConsultationType,\n                                                                                                    label: newConsultationType,\n                                                                                                    duration: dureeDeLexamen || \"15 min\"\n                                                                                                };\n                                                                                                setConsultationTypes([\n                                                                                                    ...consultationTypes,\n                                                                                                    newType\n                                                                                                ]);\n                                                                                                setPatientTypeConsultation(newConsultationType);\n                                                                                                setNewConsultationType(\"\");\n                                                                                                _mantine_notifications__WEBPACK_IMPORTED_MODULE_18__.notifications.show({\n                                                                                                    title: 'Type de consultation ajouté',\n                                                                                                    message: '\"'.concat(newConsultationType, '\" a \\xe9t\\xe9 ajout\\xe9'),\n                                                                                                    color: 'green',\n                                                                                                    autoClose: 2000\n                                                                                                });\n                                                                                            }\n                                                                                        }\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                        lineNumber: 763,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Avatar, {\n                                                                                        color: newConsultationColor,\n                                                                                        radius: \"sm\",\n                                                                                        ml: 4,\n                                                                                        h: 36,\n                                                                                        onClick: openedColorPicker,\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                            fill: \"none\",\n                                                                                            viewBox: \"0 0 200 200\",\n                                                                                            style: {\n                                                                                                width: \"26px\",\n                                                                                                height: \"26px\"\n                                                                                            },\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                fill: \"#FF5178\",\n                                                                                                d: \"M100 0a100 100 0 00-50 13.398l30 51.961A40 40 0 01100 60V0z\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                                lineNumber: 824,\n                                                                                                columnNumber: 33\n                                                                                            }, undefined)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                            lineNumber: 821,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                        lineNumber: 814,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                lineNumber: 762,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                            lineNumber: 761,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 755,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Modal, {\n                                                                    opened: ColorPickeropened,\n                                                                    onClose: closeColorPicker,\n                                                                    size: \"auto\",\n                                                                    yOffset: \"18vh\",\n                                                                    xOffset: 30,\n                                                                    withCloseButton: false,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_27__.ColorPicker, {\n                                                                            defaultValue: newConsultationColor,\n                                                                            value: newConsultationColor,\n                                                                            onChange: setNewConsultationColor,\n                                                                            onChangeEnd: setChangeEndValue,\n                                                                            format: \"hex\",\n                                                                            swatches: [\n                                                                                '#2e2e2e',\n                                                                                '#868e96',\n                                                                                '#fa5252',\n                                                                                '#e64980',\n                                                                                '#be4bdb',\n                                                                                '#7950f2',\n                                                                                '#4c6ef5',\n                                                                                '#228be6',\n                                                                                '#15aabf',\n                                                                                '#12b886',\n                                                                                '#40c057',\n                                                                                '#82c91e',\n                                                                                '#fab005',\n                                                                                '#fd7e14'\n                                                                            ]\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                            lineNumber: 831,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Group, {\n                                                                            justify: \"center\",\n                                                                            mt: 8,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                                                variant: \"filled\",\n                                                                                w: \"100%\",\n                                                                                color: \"\".concat(newConsultationColor),\n                                                                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCheck_IconColorPicker_IconHexagonPlusFilled_tabler_icons_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                                    stroke: 1,\n                                                                                    size: 18\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                    lineNumber: 844,\n                                                                                    columnNumber: 42\n                                                                                }, void 0),\n                                                                                onClick: ()=>{\n                                                                                    setNewConsultationColor(changeEndValue);\n                                                                                    closeColorPicker();\n                                                                                },\n                                                                                children: \"S\\xe9lectionner cette couleur\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                lineNumber: 840,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                            lineNumber: 839,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 830,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                                    label: \"Dur\\xe9e\",\n                                                                    value: dureeDeLexamen,\n                                                                    onChange: (value)=>setDureeDeLexamen(value !== null && value !== void 0 ? value : \"\"),\n                                                                    placeholder: \"15 min\",\n                                                                    data: [\n                                                                        \"10 min\",\n                                                                        \"15 min\",\n                                                                        \"20 min\",\n                                                                        \"25 min\",\n                                                                        \"30 min\",\n                                                                        \"35 min\",\n                                                                        \"40 min\",\n                                                                        \"45 min\"\n                                                                    ],\n                                                                    className: \"select w-full max-w-xs\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 855,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                                    label: \"Agenda\",\n                                                                    value: eventAganda,\n                                                                    onChange: (value)=>setEventAganda(value !== null && value !== void 0 ? value : \"\"),\n                                                                    placeholder: \"Ajouter des Agenda\",\n                                                                    data: agendaTypes,\n                                                                    className: \"w-full\",\n                                                                    leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarPlus_FaMicrophone_FaMicrophoneLines_FaUserDoctor_react_icons_fa6__WEBPACK_IMPORTED_MODULE_12__.FaUserDoctor, {\n                                                                        size: 16\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 870,\n                                                                        columnNumber: 38\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 863,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Menu, {\n                                                                    width: 200,\n                                                                    shadow: \"md\",\n                                                                    closeOnItemClick: false,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Menu.Target, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Avatar, {\n                                                                                color: \"#4BA3D3\",\n                                                                                radius: \"sm\",\n                                                                                h: 36,\n                                                                                mt: \"24\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCheck_IconColorPicker_IconHexagonPlusFilled_tabler_icons_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                    size: 20\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                    lineNumber: 875,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                lineNumber: 874,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                            lineNumber: 873,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Menu.Dropdown, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.TextInput, {\n                                                                                leftSectionPointerEvents: \"none\",\n                                                                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarPlus_FaMicrophone_FaMicrophoneLines_FaUserDoctor_react_icons_fa6__WEBPACK_IMPORTED_MODULE_12__.FaUserDoctor, {\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                    lineNumber: 881,\n                                                                                    columnNumber: 42\n                                                                                }, void 0),\n                                                                                placeholder: \"Ajouter des Agenda\",\n                                                                                value: newAgendaType,\n                                                                                onChange: (e)=>setNewAgendaType(e.target.value),\n                                                                                rightSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.ActionIcon, {\n                                                                                    size: \"sm\",\n                                                                                    onClick: ()=>{\n                                                                                        if (newAgendaType.trim()) {\n                                                                                            const newAgendaOption = {\n                                                                                                value: newAgendaType,\n                                                                                                label: newAgendaType\n                                                                                            };\n                                                                                            setAgendaTypes([\n                                                                                                ...agendaTypes,\n                                                                                                newAgendaOption\n                                                                                            ]);\n                                                                                            setEventAganda(newAgendaType);\n                                                                                            setNewAgendaType(\"\");\n                                                                                            _mantine_notifications__WEBPACK_IMPORTED_MODULE_18__.notifications.show({\n                                                                                                title: 'Agenda ajouté',\n                                                                                                message: '\"'.concat(newAgendaType, '\" a \\xe9t\\xe9 ajout\\xe9 \\xe0 la liste des agendas'),\n                                                                                                color: 'green',\n                                                                                                autoClose: 2000\n                                                                                            });\n                                                                                        }\n                                                                                    },\n                                                                                    disabled: !newAgendaType.trim(),\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarPlus_FaMicrophone_FaMicrophoneLines_FaUserDoctor_react_icons_fa6__WEBPACK_IMPORTED_MODULE_12__.FaCalendarPlus, {\n                                                                                        size: 16\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                        lineNumber: 904,\n                                                                                        columnNumber: 33\n                                                                                    }, void 0)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                    lineNumber: 886,\n                                                                                    columnNumber: 31\n                                                                                }, void 0),\n                                                                                onKeyDown: (e)=>{\n                                                                                    if (e.key === 'Enter' && newAgendaType.trim()) {\n                                                                                        const newAgendaOption = {\n                                                                                            value: newAgendaType,\n                                                                                            label: newAgendaType\n                                                                                        };\n                                                                                        setAgendaTypes([\n                                                                                            ...agendaTypes,\n                                                                                            newAgendaOption\n                                                                                        ]);\n                                                                                        setEventAganda(newAgendaType);\n                                                                                        setNewAgendaType(\"\");\n                                                                                        _mantine_notifications__WEBPACK_IMPORTED_MODULE_18__.notifications.show({\n                                                                                            title: 'Agenda ajouté',\n                                                                                            message: '\"'.concat(newAgendaType, '\" a \\xe9t\\xe9 ajout\\xe9 \\xe0 la liste des agendas'),\n                                                                                            color: 'green',\n                                                                                            autoClose: 2000\n                                                                                        });\n                                                                                    }\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                lineNumber: 879,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                            lineNumber: 878,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 872,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 724,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        !isWaitingList && !appointmentForm.values.addToWaitingList && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mx-auto flex gap-4 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                                    size: \"12px\",\n                                                                    className: \"label\",\n                                                                    style: {\n                                                                        marginTop: \"10px\"\n                                                                    },\n                                                                    children: \"Date du RDV\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 929,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.TextInput, {\n                                                                    id: \"event-date\",\n                                                                    type: \"date\",\n                                                                    value: eventDate,\n                                                                    onChange: (e)=>setEventDate(e.target.value),\n                                                                    className: \"input input-bordered mb-2 w-64 max-w-64\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 930,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                                    size: \"12px\",\n                                                                    className: \"label\",\n                                                                    style: {\n                                                                        marginTop: \"10px\"\n                                                                    },\n                                                                    children: \"De*\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 937,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.TextInput, {\n                                                                    id: \"event-time\",\n                                                                    type: \"time\",\n                                                                    value: eventTime,\n                                                                    onChange: (e)=>setEventTime(e.target.value),\n                                                                    className: \"input input-bordered mb-2 w-64 max-w-64\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 938,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                                    size: \"12px\",\n                                                                    className: \"label\",\n                                                                    style: {\n                                                                        marginTop: \"10px\"\n                                                                    },\n                                                                    children: \"\\xe0*\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 945,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.TextInput, {\n                                                                    id: \"event-time-end\",\n                                                                    type: \"text\",\n                                                                    placeholder: eventTime !== null ? moment__WEBPACK_IMPORTED_MODULE_3___default()(eventTime, \"HH:mm\").add(parseInt(eventConsultation), \"minutes\").format(\"HH:mm\") : \"Please enter your date of birth\",\n                                                                    readOnly: true,\n                                                                    className: \"input input-bordered mb-2 w-40\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 946,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Avatar, {\n                                                                    color: \"#4BA3D3\",\n                                                                    radius: \"sm\",\n                                                                    h: 36,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ListPlus_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                                        size: 30,\n                                                                        className: \"text-[#3799CE] cursor-pointer\",\n                                                                        onClick: openListRendezVous\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 960,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 959,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Modal, {\n                                                                    opened: ListRendezVousOpened,\n                                                                    onClose: closeListRendezVous,\n                                                                    size: \"xl\",\n                                                                    centered: true,\n                                                                    withCloseButton: false,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RendezVousSelector, {\n                                                                        onClose: closeListRendezVous\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 972,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 965,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 928,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-4 mb-2 -mt-2 pr-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_30__.Textarea, {\n                                                                    id: \"event-Commentaire\",\n                                                                    value: patientcomment,\n                                                                    onChange: (event)=>{\n                                                                        var _event_currentTarget_value;\n                                                                        return setPatientcomment((_event_currentTarget_value = event.currentTarget.value) !== null && _event_currentTarget_value !== void 0 ? _event_currentTarget_value : \"\");\n                                                                    },\n                                                                    placeholder: \"Commentaire ...\",\n                                                                    className: \"w-full\",\n                                                                    rightSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarPlus_FaMicrophone_FaMicrophoneLines_FaUserDoctor_react_icons_fa6__WEBPACK_IMPORTED_MODULE_12__.FaMicrophoneLines, {\n                                                                        size: 18\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 985,\n                                                                        columnNumber: 39\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 979,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_30__.Textarea, {\n                                                                    id: \"event-Notes\",\n                                                                    value: patientnotes,\n                                                                    onChange: (event)=>{\n                                                                        var _event_currentTarget_value;\n                                                                        return setPatientNotes((_event_currentTarget_value = event.currentTarget.value) !== null && _event_currentTarget_value !== void 0 ? _event_currentTarget_value : \"\");\n                                                                    },\n                                                                    placeholder: \"Notes ...\",\n                                                                    className: \"w-full\",\n                                                                    rightSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarPlus_FaMicrophone_FaMicrophoneLines_FaUserDoctor_react_icons_fa6__WEBPACK_IMPORTED_MODULE_12__.FaMicrophoneLines, {\n                                                                        size: 18\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 993,\n                                                                        columnNumber: 39\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 987,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_30__.Textarea, {\n                                                                    id: \"event-Commentairelistedattente\",\n                                                                    value: patientcommentairelistedattente,\n                                                                    onChange: (event)=>{\n                                                                        var _event_currentTarget_value;\n                                                                        return setPatientCommentairelistedattente((_event_currentTarget_value = event.currentTarget.value) !== null && _event_currentTarget_value !== void 0 ? _event_currentTarget_value : \"\");\n                                                                    },\n                                                                    placeholder: \"Commentaire (liste d'attente)...\",\n                                                                    className: \"w-full\",\n                                                                    rightSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarPlus_FaMicrophone_FaMicrophoneLines_FaUserDoctor_react_icons_fa6__WEBPACK_IMPORTED_MODULE_12__.FaMicrophone, {\n                                                                        size: 18\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 1001,\n                                                                        columnNumber: 39\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 995,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 978,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-base-100 px-[4px] pt-[8px]\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                className: \"text-daisy flex flex-wrap gap-x-4 gap-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-1\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                                            value: eventResourceId ? eventResourceId.toString() : \"\",\n                                                                            onChange: (value)=>{\n                                                                                setEventResourceId(Number(value) || 1);\n                                                                            },\n                                                                            name: \"resourceId\",\n                                                                            placeholder: \"Room\",\n                                                                            data: [\n                                                                                {\n                                                                                    value: \"1\",\n                                                                                    label: \"Room A\"\n                                                                                },\n                                                                                {\n                                                                                    value: \"2\",\n                                                                                    label: \"Room B\"\n                                                                                }\n                                                                            ],\n                                                                            required: true,\n                                                                            className: \"select w-full max-w-xs\",\n                                                                            leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MdOutlineBedroomChild_MdOutlineContentPasteSearch_MdOutlineSocialDistance_react_icons_md__WEBPACK_IMPORTED_MODULE_20__.MdOutlineBedroomChild, {\n                                                                                size: 16\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                lineNumber: 1022,\n                                                                                columnNumber: 42\n                                                                            }, void 0)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                            lineNumber: 1009,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 1008,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                id: \"visit\",\n                                                                                type: \"radio\",\n                                                                                name: \"eventType\",\n                                                                                value: \"visit\",\n                                                                                className: \"peer hidden\",\n                                                                                checked: eventType === \"visit\",\n                                                                                onChange: (e)=>setEventType(e.target.value)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                lineNumber: 1027,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                htmlFor: \"visit\",\n                                                                                className: \"\".concat(eventType === \"visit\" ? \"peer-checked:text-[#34D1BF]\" : \"text-[var(--mantine-color-dark-0)]\", \" inline-flex h-10 w-full items-center justify-center whitespace-nowrap rounded-md px-4 py-2 text-sm font-medium uppercase\"),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    className: \"flex items-center gap-2 text-xs uppercase\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"disk-teal relative z-10 block h-[12px] w-[12px] rounded-full border-2 border-white\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                            lineNumber: 1045,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined),\n                                                                                        \"Visite de malade\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                    lineNumber: 1044,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                lineNumber: 1036,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 1026,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                id: \"visitor-counter\",\n                                                                                type: \"radio\",\n                                                                                name: \"eventType\",\n                                                                                value: \"visitor-counter\",\n                                                                                className: \"peer hidden\",\n                                                                                checked: eventType === \"visitor-counter\",\n                                                                                onChange: (e)=>setEventType(e.target.value)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                lineNumber: 1052,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                htmlFor: \"visitor-counter\",\n                                                                                className: \"\".concat(eventType === \"visitor-counter\" ? \"peer-checked:text-[#F17105]\" : \"text-[var(--mantine-color-dark-0)]\", \" inline-flex h-10 w-full items-center justify-center whitespace-nowrap rounded-md px-4 py-2 text-sm font-medium uppercase\"),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    className: \"flex items-center gap-2 text-xs uppercase\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"disk-orange relative z-10 block h-[12px] w-[12px] rounded-full border-2 border-white\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                            lineNumber: 1070,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined),\n                                                                                        \"Visitor Counter\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                    lineNumber: 1069,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                lineNumber: 1061,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 1051,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                id: \"completed\",\n                                                                                type: \"radio\",\n                                                                                name: \"eventType\",\n                                                                                value: \"completed\",\n                                                                                className: \"peer hidden\",\n                                                                                checked: eventType === \"completed\",\n                                                                                onChange: (e)=>setEventType(e.target.value)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                lineNumber: 1077,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                htmlFor: \"completed\",\n                                                                                className: \"\".concat(eventType === \"completed\" ? \"peer-checked:text-[#3799CE]\" : \"text-[var(--mantine-color-dark-0)]\", \" inline-flex h-10 w-full items-center justify-center whitespace-nowrap px-4 py-2 text-sm font-medium uppercase\"),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    className: \"flex items-center gap-2 text-xs uppercase\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"disk-azure relative z-10 block h-[12px] w-[12px] rounded-full border-2 border-white\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                            lineNumber: 1095,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined),\n                                                                                        \"Completed\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                    lineNumber: 1094,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                lineNumber: 1086,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 1076,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                id: \"diagnosis\",\n                                                                                type: \"radio\",\n                                                                                name: \"eventType\",\n                                                                                value: \"diagnosis\",\n                                                                                checked: eventType === \"diagnosis\",\n                                                                                className: \"peer hidden\",\n                                                                                onChange: (e)=>setEventType(e.target.value)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                lineNumber: 1102,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                htmlFor: \"diagnosis\",\n                                                                                className: \"\".concat(eventType === \"diagnosis\" ? \"peer-checked:text-[#F3124E]\" : \"text-[var(--mantine-color-dark-0)]\", \" inline-flex h-10 w-full items-center justify-center whitespace-nowrap px-4 py-2 text-sm font-medium uppercase\"),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    className: \"flex items-center gap-2 text-xs uppercase\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"disk-red relative z-10 block h-[12px] w-[12px] rounded-full border-2 border-white\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                            lineNumber: 1120,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined),\n                                                                                        \"Re-diagnose\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                    lineNumber: 1119,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                lineNumber: 1111,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                        lineNumber: 1101,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                lineNumber: 1007,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 1006,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 pr-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Group, {\n                                                                    gap: \"xs\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Switch, {\n                                                                            color: \"teal\",\n                                                                            size: \"xs\",\n                                                                            label: \"Add to Waiting List\",\n                                                                            checked: appointmentForm.values.addToWaitingList,\n                                                                            onChange: (event)=>{\n                                                                                appointmentForm.setFieldValue('addToWaitingList', event.currentTarget.checked);\n                                                                                appointmentForm.setFieldValue('removeFromCalendar', event.currentTarget.checked);\n                                                                            },\n                                                                            thumbIcon: appointmentForm.values.addToWaitingList ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCheck_IconColorPicker_IconHexagonPlusFilled_tabler_icons_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                                                size: 12,\n                                                                                color: \"var(--mantine-color-teal-6)\",\n                                                                                stroke: 3\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                lineNumber: 1142,\n                                                                                columnNumber: 31\n                                                                            }, void 0) : null\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                            lineNumber: 1131,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Switch, {\n                                                                            checked: checkedAppelvideo,\n                                                                            onChange: handleAppelvideoChange,\n                                                                            color: \"teal\",\n                                                                            size: \"xs\",\n                                                                            label: \"Appel video\",\n                                                                            thumbIcon: checkedAppelvideo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCheck_IconColorPicker_IconHexagonPlusFilled_tabler_icons_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                                                size: 12,\n                                                                                color: \"var(--mantine-color-teal-6)\",\n                                                                                stroke: 3\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                lineNumber: 1157,\n                                                                                columnNumber: 31\n                                                                            }, void 0) : null\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                            lineNumber: 1149,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Switch, {\n                                                                            checked: checkedRappelSms,\n                                                                            onChange: handleRappelSmsChange,\n                                                                            color: \"teal\",\n                                                                            size: \"xs\",\n                                                                            label: \"Rappel Sms\",\n                                                                            thumbIcon: checkedRappelSms ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCheck_IconColorPicker_IconHexagonPlusFilled_tabler_icons_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                                                size: 12,\n                                                                                color: \"var(--mantine-color-teal-6)\",\n                                                                                stroke: 3\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                lineNumber: 1171,\n                                                                                columnNumber: 31\n                                                                            }, void 0) : null\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                            lineNumber: 1163,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Switch, {\n                                                                            checked: checkedRappelEmail,\n                                                                            onChange: handleRappelEmailChange,\n                                                                            color: \"teal\",\n                                                                            size: \"xs\",\n                                                                            label: \"Rappel e-mail\",\n                                                                            thumbIcon: checkedRappelEmail ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCheck_IconColorPicker_IconHexagonPlusFilled_tabler_icons_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                                                size: 12,\n                                                                                color: \"var(--mantine-color-teal-6)\",\n                                                                                stroke: 3\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                                lineNumber: 1185,\n                                                                                columnNumber: 31\n                                                                            }, void 0) : null\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                            lineNumber: 1177,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 1130,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                                    type: \"submit\",\n                                                                    className: \"btn mb-2 bg-[#03A684] text-[var(--mantine-Button-label-MB)] hover:bg-[#03A684]/90\",\n                                                                    onClick: ()=>{\n                                                                        onClose();\n                                                                    },\n                                                                    children: currentPatient ? \"Enregistrer\" : \"Ajouter\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 1193,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                currentPatient && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                                    color: \"red\",\n                                                                    onClick: ()=>{\n                                                                        if (currentPatient) {\n                                                                            setWaitingList(waitingList.filter((p)=>p.id !== currentPatient.id));\n                                                                            setPatientModalOpen(false);\n                                                                        }\n                                                                    },\n                                                                    className: \"btn mb-2 bg-[#F3124E] text-[var(--mantine-Button-label-MB)] hover:bg-[#F3124E]/90\",\n                                                                    children: \"Supprimer\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 1203,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                                    onClick: ()=>{\n                                                                        onClose();\n                                                                    },\n                                                                    className: \"btn mb-2 bg-[#F5A524] text-[var(--mantine-Button-label-MB)] hover:bg-[#F5A524]/90\",\n                                                                    children: \"Annuler\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 1216,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 1129,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                    lineNumber: 507,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                lineNumber: 506,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                            lineNumber: 505,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                        lineNumber: 504,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                    lineNumber: 503,\n                                    columnNumber: 11\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                lineNumber: 502,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                        lineNumber: 462,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                lineNumber: 456,\n                columnNumber: 5\n            }, undefined),\n            showEditModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Modal.Root, {\n                opened: true,\n                onClose: ()=>{\n                    resetForm();\n                    setShowEditModal(false);\n                },\n                size: \"70%\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Modal.Overlay, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                        lineNumber: 1244,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Modal.Content, {\n                        className: \"overflow-y-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Modal.Header, {\n                                style: {\n                                    height: '60px',\n                                    background: \"#3799CE\",\n                                    padding: \"11px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Modal.Title, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                fw: 600,\n                                                c: \"var(--mantine-color-white)\",\n                                                className: \"mb-2 flex gap-2 text-lg font-semibold leading-none tracking-tight\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        width: \"1em\",\n                                                        height: \"1em\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M21 7.5V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h3.5M16 2v4M8 2v4m-5 4h5m9.5 7.5L16 16.3V14\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 1262,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                    cx: 16,\n                                                                    cy: 16,\n                                                                    r: 6\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 1263,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                            lineNumber: 1255,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                        lineNumber: 1249,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"Modifier rendez-vous\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                lineNumber: 1248,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-muted-foreground text-sm text-[var(--mantine-color-white)]\",\n                                                children: \"Modifiez les d\\xe9tails de l'\\xe9v\\xe9nement ci-dessous\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                lineNumber: 1268,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                        lineNumber: 1247,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Modal.CloseButton, {\n                                        className: \"mantine-focus-always text-[var(--mantine-color-white)] hover:text-[#868e96]\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                        lineNumber: 1272,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                lineNumber: 1246,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Modal.Body, {\n                                style: {\n                                    padding: '0px'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"py-2 pl-4 h-[600px]\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(simplebar_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"simplebar-scrollable-y h-[calc(100%)]\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"pr-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                onSubmit: handleEditSubmit,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid gap-3 py-2 pr-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-4 mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                                value: eventTitle,\n                                                                onChange: (value)=>setEventTitle(value !== null && value !== void 0 ? value : \"\"),\n                                                                placeholder: \"Titre\",\n                                                                data: [\n                                                                    {\n                                                                        value: \"Titre1\",\n                                                                        label: \"Titre1\"\n                                                                    },\n                                                                    {\n                                                                        value: \"Titre2\",\n                                                                        label: \"Titre2\"\n                                                                    },\n                                                                    {\n                                                                        value: \"Titre3\",\n                                                                        label: \"Titre3\"\n                                                                    }\n                                                                ],\n                                                                className: \"w-full\",\n                                                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarPlus_FaMicrophone_FaMicrophoneLines_FaUserDoctor_react_icons_fa6__WEBPACK_IMPORTED_MODULE_12__.FaUserDoctor, {\n                                                                    size: 16\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 1291,\n                                                                    columnNumber: 40\n                                                                }, void 0)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                lineNumber: 1281,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.TextInput, {\n                                                                id: \"event-nom\",\n                                                                placeholder: \"Nom *\",\n                                                                type: \"text\",\n                                                                value: patientName,\n                                                                onChange: (e)=>setPatientName(e.target.value),\n                                                                required: true,\n                                                                className: \"input input-bordered w-full\",\n                                                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiUserFollowLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_19__.RiUserFollowLine, {\n                                                                    size: 16\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 1301,\n                                                                    columnNumber: 40\n                                                                }, void 0)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                lineNumber: 1293,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.TextInput, {\n                                                                id: \"event-prenom\",\n                                                                placeholder: \"Pr\\xe9nom *\",\n                                                                type: \"text\",\n                                                                value: patientlastName,\n                                                                onChange: (e)=>setPatientlastName(e.target.value),\n                                                                required: true,\n                                                                className: \"input input-bordered w-full\",\n                                                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiUserFollowLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_19__.RiUserFollowLine, {\n                                                                    size: 16\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 1311,\n                                                                    columnNumber: 40\n                                                                }, void 0)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                lineNumber: 1303,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Avatar, {\n                                                                color: \"#4BA3D3\",\n                                                                radius: \"sm\",\n                                                                h: 36,\n                                                                onClick: openListDesPatient,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MdOutlineBedroomChild_MdOutlineContentPasteSearch_MdOutlineSocialDistance_react_icons_md__WEBPACK_IMPORTED_MODULE_20__.MdOutlineContentPasteSearch, {\n                                                                    size: 20\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                    lineNumber: 1314,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                                lineNumber: 1313,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                        lineNumber: 1280,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                    lineNumber: 1279,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                                lineNumber: 1278,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                            lineNumber: 1277,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                        lineNumber: 1276,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                    lineNumber: 1275,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                                lineNumber: 1274,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                        lineNumber: 1245,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\components\\\\Appointments\\\\overview\\\\AjouterUnRendezVous.tsx\",\n                lineNumber: 1236,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_c1 = AjouterUnRendezVous;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AjouterUnRendezVous);\nvar _c, _c1;\n$RefreshReg$(_c, \"RendezVousSelector\");\n$RefreshReg$(_c1, \"AjouterUnRendezVous\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Appointments/overview/AjouterUnRendezVous.tsx\n"));

/***/ })

});