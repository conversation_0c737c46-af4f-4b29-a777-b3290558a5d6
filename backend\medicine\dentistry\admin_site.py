"""
Custom admin site for the dentistry application.
"""
from django.contrib.admin import AdminSite
from django.utils.translation import gettext_lazy as _
from dentistry.models import (
    DentistryPatient, DentistryMedicalRecord, DentalImaging,
    DentistryAppointment, DentistryDoctor, DentistryDoctorSettings,
    DentalTreatment, DentalProcedure, DentalLaboratory, LabWorkOrder,
    DentistryRole, DentistryStaffProfile, DentistryBillingCode,
    DentistryInvoice, DentistryPatientInsurance, DentistryNotification,
    DentalReminderSetting, DentistryConfiguration
)

class DentistryAdminSite(AdminSite):
    """
    Custom admin site for the dentistry application.
    """
    site_header = _("Dentistry Administration")
    site_title = _("Dentistry Admin Portal")
    index_title = _("Welcome to Dentistry Admin Portal")
    site_url = "/dentistry/"

# Create an instance of the dentistry admin site
dentistry_admin_site = DentistryAdminSite(name='dentistry_admin')

# Register models with the dentistry admin site
dentistry_admin_site.register(DentistryPatient)
dentistry_admin_site.register(DentistryMedicalRecord)
dentistry_admin_site.register(DentalImaging)
dentistry_admin_site.register(DentistryAppointment)
dentistry_admin_site.register(DentistryDoctor)
dentistry_admin_site.register(DentistryDoctorSettings)
dentistry_admin_site.register(DentalTreatment)
dentistry_admin_site.register(DentalProcedure)
dentistry_admin_site.register(DentalLaboratory)
dentistry_admin_site.register(LabWorkOrder)
dentistry_admin_site.register(DentistryRole)
dentistry_admin_site.register(DentistryStaffProfile)
dentistry_admin_site.register(DentistryBillingCode)
dentistry_admin_site.register(DentistryInvoice)
dentistry_admin_site.register(DentistryPatientInsurance)
dentistry_admin_site.register(DentistryNotification)
dentistry_admin_site.register(DentalReminderSetting)
dentistry_admin_site.register(DentistryConfiguration)
