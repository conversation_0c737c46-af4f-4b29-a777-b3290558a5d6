# backend/dental_medicine/dentistry/views_estimates.py

from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, AllowAny
from django.shortcuts import get_object_or_404
from django.db.models import Count, Q
from django.utils import timezone
from datetime import timed<PERSON><PERSON>

def get_user_or_none(request):
    """Utilitaire pour obtenir l'utilisateur ou None si non authentifié"""
    return getattr(request, 'user', None) if hasattr(request, 'user') and request.user.is_authenticated else None

from .models_estimates import (
    EstimateSession,
    ToothModificationEstimate,
    EstimateSessionHistory,
    EstimateTemplate,
    EstimateStatistics
)
from .serializers_estimates import (
    EstimateSessionSerializer,
    ToothModificationEstimateSerializer,
    EstimateSessionHistorySerializer,
    EstimateTemplateSerializer,
    EstimateStatisticsSerializer
)

class EstimateSessionViewSet(viewsets.ModelViewSet):
    """
    ViewSet pour gérer les sessions d'estimation
    """
    queryset = EstimateSession.objects.all()
    serializer_class = EstimateSessionSerializer
    permission_classes = [AllowAny]  # Temporaire : permettre l'accès sans authentification

    def get_queryset(self):
        queryset = super().get_queryset()

        # Filtrer par patient si spécifié
        patient_id = self.request.query_params.get('patient_id')
        if patient_id:
            queryset = queryset.filter(patient__id=patient_id)

        # Filtrer par statut actif
        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')

        return queryset

    def perform_create(self, serializer):
        # Gérer le cas où il n'y a pas d'utilisateur authentifié
        user = get_user_or_none(self.request)
        session = serializer.save(created_by=user)

        # Créer les statistiques initiales
        EstimateStatistics.objects.create(session=session)

        # Enregistrer l'historique
        EstimateSessionHistory.objects.create(
            session=session,
            action='created',
            description=f"Session créée: {session.session_name}",
            performed_by=user,
            new_data={'session_name': session.session_name, 'patient_id': session.patient.id if session.patient else None}
        )

    def perform_update(self, serializer):
        old_instance = self.get_object()
        old_data = {
            'session_name': old_instance.session_name,
            'patient_id': old_instance.patient.id if old_instance.patient else None,
            'is_active': old_instance.is_active
        }

        session = serializer.save()

        # Enregistrer l'historique
        user = get_user_or_none(self.request)
        EstimateSessionHistory.objects.create(
            session=session,
            action='modified',
            description=f"Session modifiée: {session.session_name}",
            performed_by=user,
            previous_data=old_data,
            new_data={
                'session_name': session.session_name,
                'patient_id': session.patient.id if session.patient else None,
                'is_active': session.is_active
            }
        )

        # Recalculer les statistiques
        if hasattr(session, 'statistics'):
            session.statistics.calculate_statistics()

    @action(detail=True, methods=['post'])
    def complete(self, request, pk=None):
        """Marquer une session comme terminée"""
        session = self.get_object()
        session.is_completed = True
        session.save()

        # Enregistrer l'historique
        EstimateSessionHistory.objects.create(
            session=session,
            action='completed',
            description=f"Session terminée: {session.session_name}",
            performed_by=request.user
        )

        return Response({'status': 'Session terminée'})

    @action(detail=True, methods=['post'])
    def reset(self, request, pk=None):
        """Réinitialiser une session (supprimer toutes les modifications)"""
        session = self.get_object()

        # Sauvegarder les données avant suppression
        modifications_data = list(session.modifications.values())

        # Supprimer toutes les modifications
        session.modifications.all().delete()

        # Enregistrer l'historique
        EstimateSessionHistory.objects.create(
            session=session,
            action='reset',
            description=f"Session réinitialisée: {session.session_name}",
            performed_by=request.user,
            previous_data={'modifications': modifications_data}
        )

        # Recalculer les statistiques
        if hasattr(session, 'statistics'):
            session.statistics.calculate_statistics()

        return Response({'status': 'Session réinitialisée'})

    @action(detail=True, methods=['get'])
    def statistics(self, request, pk=None):
        """Obtenir les statistiques d'une session"""
        session = self.get_object()

        if hasattr(session, 'statistics'):
            # Recalculer les statistiques avant de les retourner
            stats = session.statistics.calculate_statistics()
            serializer = EstimateStatisticsSerializer(stats)
            return Response(serializer.data)
        else:
            # Créer les statistiques si elles n'existent pas
            stats = EstimateStatistics.objects.create(session=session)
            stats.calculate_statistics()
            serializer = EstimateStatisticsSerializer(stats)
            return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def export_data(self, request, pk=None):
        """Exporter les données de la session"""
        session = self.get_object()

        # Préparer les données d'export
        export_data = {
            'session': EstimateSessionSerializer(session).data,
            'modifications': ToothModificationEstimateSerializer(
                session.modifications.all(), many=True
            ).data,
            'statistics': EstimateStatisticsSerializer(
                session.statistics if hasattr(session, 'statistics') else None
            ).data,
            'export_date': timezone.now().isoformat()
        }

        return Response(export_data)


class ToothModificationEstimateViewSet(viewsets.ModelViewSet):
    """
    ViewSet pour gérer les modifications dentaires dans les estimations
    """
    queryset = ToothModificationEstimate.objects.all()
    serializer_class = ToothModificationEstimateSerializer
    permission_classes = [AllowAny]  # Temporaire : permettre l'accès sans authentification

    def get_queryset(self):
        queryset = super().get_queryset()

        # Filtrer par session
        session_id = self.request.query_params.get('session_id')
        if session_id:
            queryset = queryset.filter(session_id=session_id)

        # Filtrer par dent
        tooth_number = self.request.query_params.get('tooth_number')
        if tooth_number:
            queryset = queryset.filter(tooth_number=tooth_number)

        # Filtrer par type de modification
        modification_type = self.request.query_params.get('modification_type')
        if modification_type:
            queryset = queryset.filter(modification_type=modification_type)

        # Filtrer par statut
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        return queryset

    def perform_create(self, serializer):
        modification = serializer.save()

        # Enregistrer l'historique
        EstimateSessionHistory.objects.create(
            session=modification.session,
            modification=modification,
            action='created',
            description=f"Modification créée: Dent {modification.tooth_number} - {modification.modification_type}",
            performed_by=self.request.user,
            new_data=ToothModificationEstimateSerializer(modification).data
        )

        # Recalculer les statistiques
        if hasattr(modification.session, 'statistics'):
            modification.session.statistics.calculate_statistics()

    def perform_update(self, serializer):
        old_instance = self.get_object()
        old_data = ToothModificationEstimateSerializer(old_instance).data

        modification = serializer.save()

        # Enregistrer l'historique
        EstimateSessionHistory.objects.create(
            session=modification.session,
            modification=modification,
            action='modified',
            description=f"Modification mise à jour: Dent {modification.tooth_number} - {modification.modification_type}",
            performed_by=self.request.user,
            previous_data=old_data,
            new_data=ToothModificationEstimateSerializer(modification).data
        )

        # Recalculer les statistiques
        if hasattr(modification.session, 'statistics'):
            modification.session.statistics.calculate_statistics()

    def perform_destroy(self, instance):
        old_data = ToothModificationEstimateSerializer(instance).data

        # Enregistrer l'historique avant suppression
        EstimateSessionHistory.objects.create(
            session=instance.session,
            action='deleted',
            description=f"Modification supprimée: Dent {instance.tooth_number} - {instance.modification_type}",
            performed_by=self.request.user,
            previous_data=old_data
        )

        session = instance.session
        super().perform_destroy(instance)

        # Recalculer les statistiques
        if hasattr(session, 'statistics'):
            session.statistics.calculate_statistics()

    @action(detail=False, methods=['post'])
    def bulk_update(self, request):
        """Mise à jour en lot des modifications"""
        modifications_data = request.data.get('modifications', [])
        session_id = request.data.get('session_id')

        if not session_id:
            return Response(
                {'error': 'session_id requis'},
                status=status.HTTP_400_BAD_REQUEST
            )

        session = get_object_or_404(EstimateSession, id=session_id)
        updated_modifications = []

        for mod_data in modifications_data:
            # Chercher ou créer la modification
            modification, created = ToothModificationEstimate.objects.update_or_create(
                session=session,
                svg_id=mod_data.get('svg_id'),
                path_id=mod_data.get('path_id'),
                defaults={
                    'tooth_number': mod_data.get('tooth_number'),
                    'modification_type': mod_data.get('modification_type'),
                    'status': mod_data.get('status', 'not_applied'),
                    'is_visible': mod_data.get('is_visible', False),
                    'color': mod_data.get('color'),
                    'stroke': mod_data.get('stroke'),
                    'metadata': mod_data.get('metadata', {})
                }
            )

            updated_modifications.append(modification)

            # Enregistrer l'historique
            action = 'created' if created else 'modified'
            user = get_user_or_none(request)
            EstimateSessionHistory.objects.create(
                session=session,
                modification=modification,
                action=action,
                description=f"Modification {action}: Dent {modification.tooth_number} - {modification.modification_type}",
                performed_by=user,
                new_data=ToothModificationEstimateSerializer(modification).data
            )

        # Recalculer les statistiques
        if hasattr(session, 'statistics'):
            session.statistics.calculate_statistics()

        serializer = ToothModificationEstimateSerializer(updated_modifications, many=True)
        return Response({
            'updated_count': len(updated_modifications),
            'modifications': serializer.data
        })


class EstimateTemplateViewSet(viewsets.ModelViewSet):
    """
    ViewSet pour gérer les modèles d'estimation
    """
    queryset = EstimateTemplate.objects.filter(is_active=True)
    serializer_class = EstimateTemplateSerializer
    permission_classes = [AllowAny]  # Temporaire : permettre l'accès sans authentification

    def get_queryset(self):
        queryset = super().get_queryset()

        # Filtrer par spécialité
        specialty = self.request.query_params.get('specialty')
        if specialty:
            queryset = queryset.filter(specialty=specialty)

        # Filtrer par visibilité (public ou créé par l'utilisateur)
        user = get_user_or_none(self.request)
        if user:
            queryset = queryset.filter(
                Q(is_public=True) | Q(created_by=user)
            )
        else:
            # Si pas d'utilisateur authentifié, ne montrer que les modèles publics
            queryset = queryset.filter(is_public=True)

        return queryset

    def perform_create(self, serializer):
        user = get_user_or_none(self.request)
        serializer.save(created_by=user)

    @action(detail=True, methods=['post'])
    def apply_to_session(self, request, pk=None):
        """Appliquer un modèle à une session"""
        template = self.get_object()
        session_id = request.data.get('session_id')

        if not session_id:
            return Response(
                {'error': 'session_id requis'},
                status=status.HTTP_400_BAD_REQUEST
            )

        session = get_object_or_404(EstimateSession, id=session_id)

        # Appliquer les modifications du modèle
        template_modifications = template.template_data.get('modifications', [])
        applied_count = 0

        for mod_data in template_modifications:
            modification, created = ToothModificationEstimate.objects.update_or_create(
                session=session,
                svg_id=mod_data.get('svg_id'),
                path_id=mod_data.get('path_id'),
                defaults=mod_data
            )
            if created:
                applied_count += 1

        # Enregistrer l'historique
        user = get_user_or_none(request)
        EstimateSessionHistory.objects.create(
            session=session,
            action='modified',
            description=f"Modèle appliqué: {template.name} ({applied_count} modifications)",
            performed_by=user,
            new_data={'template_id': str(template.id), 'template_name': template.name}
        )

        # Recalculer les statistiques
        if hasattr(session, 'statistics'):
            session.statistics.calculate_statistics()

        return Response({
            'status': 'Modèle appliqué',
            'applied_modifications': applied_count
        })
