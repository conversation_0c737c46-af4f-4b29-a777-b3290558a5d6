"""
Signal handlers for the dentistry application.
"""
from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver
from django.utils import timezone
from datetime import timedelta
from dentistry.models.appointment import DentistryAppointment
from dentistry.models.notification import DentistryNotification

# Store the initial status of the appointment before save
@receiver(pre_save, sender=DentistryAppointment)
def store_initial_status(sender, instance, **kwargs):
    """
    Store the initial status of the appointment before save.
    """
    if instance.pk:
        try:
            instance._initial_status = DentistryAppointment.objects.get(pk=instance.pk).status
        except DentistryAppointment.DoesNotExist:
            instance._initial_status = None
    else:
        instance._initial_status = None

@receiver(post_save, sender=DentistryAppointment)
def appointment_notification(sender, instance, created, **kwargs):
    """
    Create notifications when an appointment is created or updated.
    """
    if created:
        # Create notification for the patient
        if instance.patient:
            DentistryNotification.objects.create(
                patient=instance.patient,
                doctor=instance.doctor,
                title=f"New Appointment: {instance.get_appointment_type_display()}",
                message=(
                    f"You have a new dental appointment scheduled for {instance.appointment_date} "
                    f"at {instance.appointment_time}. "
                    f"Type: {instance.get_appointment_type_display()}. "
                    f"{instance.pre_appointment_instructions or ''}"
                ),
                notification_type='appointment',
                priority='medium',
                delivery_method='in_app',
                related_object_type='appointment',
                related_object_id=instance.id,
                action_url=f"/dentistry/appointments/{instance.id}"
            )

            # Create a reminder notification to be sent 24 hours before the appointment
            appointment_datetime = timezone.make_aware(
                timezone.datetime.combine(instance.appointment_date, instance.appointment_time)
            )
            reminder_datetime = appointment_datetime - timedelta(days=1)

            # Only create reminder if appointment is more than 24 hours in the future
            if reminder_datetime > timezone.now():
                DentistryNotification.objects.create(
                    patient=instance.patient,
                    doctor=instance.doctor,
                    title=f"Appointment Reminder: {instance.get_appointment_type_display()}",
                    message=(
                        f"Reminder: You have a dental appointment scheduled for tomorrow, "
                        f"{instance.appointment_date} at {instance.appointment_time}. "
                        f"Type: {instance.get_appointment_type_display()}. "
                        f"{instance.pre_appointment_instructions or ''}"
                    ),
                    notification_type='appointment',
                    priority='high',
                    delivery_method='in_app',
                    related_object_type='appointment',
                    related_object_id=instance.id,
                    action_url=f"/dentistry/appointments/{instance.id}",
                    is_sent=False
                )

        # Create notification for the doctor
        if instance.doctor:
            DentistryNotification.objects.create(
                doctor=instance.doctor,
                patient=instance.patient,
                title=f"New Appointment: {instance.patient.full_name}",
                message=(
                    f"You have a new dental appointment scheduled with {instance.patient.full_name} "
                    f"on {instance.appointment_date} at {instance.appointment_time}. "
                    f"Type: {instance.get_appointment_type_display()}."
                ),
                notification_type='appointment',
                priority='medium',
                delivery_method='in_app',
                related_object_type='appointment',
                related_object_id=instance.id,
                action_url=f"/dentistry/appointments/{instance.id}"
            )

    elif hasattr(instance, '_initial_status') and instance.status == 'cancelled' and instance._initial_status != 'cancelled':
        # Create cancellation notification for the patient
        if instance.patient:
            DentistryNotification.objects.create(
                patient=instance.patient,
                doctor=instance.doctor,
                title="Appointment Cancelled",
                message=(
                    f"Your dental appointment scheduled for {instance.appointment_date} "
                    f"at {instance.appointment_time} has been cancelled. "
                    f"Please contact us if you need to reschedule."
                ),
                notification_type='appointment',
                priority='high',
                delivery_method='in_app',
                related_object_type='appointment',
                related_object_id=instance.id,
                action_url=f"/dentistry/appointments"
            )

        # Create cancellation notification for the doctor
        if instance.doctor:
            DentistryNotification.objects.create(
                doctor=instance.doctor,
                patient=instance.patient,
                title=f"Appointment Cancelled: {instance.patient.full_name}",
                message=(
                    f"The dental appointment with {instance.patient.full_name} scheduled for "
                    f"{instance.appointment_date} at {instance.appointment_time} "
                    f"has been cancelled."
                ),
                notification_type='appointment',
                priority='medium',
                delivery_method='in_app',
                related_object_type='appointment',
                related_object_id=instance.id,
                action_url=f"/dentistry/appointments"
            )

    elif hasattr(instance, '_initial_status') and instance.status == 'rescheduled' and instance._initial_status != 'rescheduled':
        # Create rescheduling notification for the patient
        if instance.patient:
            DentistryNotification.objects.create(
                patient=instance.patient,
                doctor=instance.doctor,
                title="Appointment Rescheduled",
                message=(
                    f"Your dental appointment has been rescheduled to {instance.appointment_date} "
                    f"at {instance.appointment_time}. "
                    f"Type: {instance.get_appointment_type_display()}. "
                    f"{instance.pre_appointment_instructions or ''}"
                ),
                notification_type='appointment',
                priority='high',
                delivery_method='in_app',
                related_object_type='appointment',
                related_object_id=instance.id,
                action_url=f"/dentistry/appointments/{instance.id}"
            )

        # Create rescheduling notification for the doctor
        if instance.doctor:
            DentistryNotification.objects.create(
                doctor=instance.doctor,
                patient=instance.patient,
                title=f"Appointment Rescheduled: {instance.patient.full_name}",
                message=(
                    f"The dental appointment with {instance.patient.full_name} has been rescheduled to "
                    f"{instance.appointment_date} at {instance.appointment_time}. "
                    f"Type: {instance.get_appointment_type_display()}."
                ),
                notification_type='appointment',
                priority='medium',
                delivery_method='in_app',
                related_object_type='appointment',
                related_object_id=instance.id,
                action_url=f"/dentistry/appointments/{instance.id}"
            )
