{% extends "admin/change_form.html" %}
{% load i18n admin_urls static %}

{% block extrastyle %}
  {{ block.super }}
  <style>
    .license-info-box {
      margin-bottom: 20px;
      padding: 15px;
      border-radius: 5px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }
    
    .transaction-id-box {
      background-color: #f8f9fa;
      border-left: 4px solid #007bff;
    }
    
    .days-remaining-box {
      background-color: #f8f9fa;
      border-left: 4px solid #28a745;
    }
    
    .days-critical {
      background-color: #fff8f8;
      border-left: 4px solid #dc3545;
    }
    
    .box-title {
      margin-top: 0;
      font-size: 16px;
      font-weight: bold;
      color: #495057;
    }
    
    .box-content {
      font-size: 24px;
      font-weight: bold;
      margin: 10px 0;
    }
    
    .transaction-id {
      color: #007bff;
    }
    
    .days-remaining {
      color: #28a745;
    }
    
    .days-critical .days-remaining {
      color: #dc3545;
    }
    
    .box-footer {
      font-size: 12px;
      color: #6c757d;
      margin-bottom: 0;
    }
    
    .license-info-container {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      margin-bottom: 20px;
    }
    
    .license-info-box {
      flex: 1;
      min-width: 250px;
    }
    
    .reminder-text {
      margin-top: 10px;
      font-weight: bold;
    }
    
    .reminder-critical {
      color: #dc3545;
    }
    
    .reminder-normal {
      color: #28a745;
    }
    
    .email-button {
      display: inline-block;
      padding: 8px 16px;
      background-color: #007bff;
      color: white;
      text-decoration: none;
      border-radius: 4px;
      margin-top: 10px;
      font-size: 14px;
    }
    
    .email-button:hover {
      background-color: #0069d9;
      color: white;
    }
  </style>
{% endblock %}

{% block content %}
  {% if original %}
    <div class="license-info-container">
      <div class="license-info-box transaction-id-box">
        <h3 class="box-title">Transaction ID</h3>
        <div class="box-content">
          <span class="transaction-id">{{ original.transaction_id|default:"Not Available" }}</span>
        </div>
        <p class="box-footer">Used for payment tracking and verification</p>
      </div>
      
      <div class="license-info-box {% if original.days_remaining <= 10 %}days-critical{% else %}days-remaining-box{% endif %}">
        <h3 class="box-title">Days Remaining</h3>
        <div class="box-content">
          <span class="days-remaining">{{ original.days_remaining }}</span>
        </div>
        <p class="box-footer">
          {% if original.days_remaining <= 10 %}
            <span class="reminder-text reminder-critical">License will expire soon! Please renew.</span>
          {% else %}
            <span class="reminder-text reminder-normal">License is active and valid.</span>
          {% endif %}
        </p>
        <a href="{% url 'admin:send-license-email' original.id %}" class="email-button">
          Send License Details to Email
        </a>
      </div>
    </div>
  {% endif %}
  
  {{ block.super }}
{% endblock %}
