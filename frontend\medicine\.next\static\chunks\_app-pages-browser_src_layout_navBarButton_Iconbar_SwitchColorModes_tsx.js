"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_layout_navBarButton_Iconbar_SwitchColorModes_tsx"],{

/***/ "(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconCircleHalf2.mjs":
/*!*****************************************************************************!*\
  !*** ./node_modules/@tabler/icons-react/dist/esm/icons/IconCircleHalf2.mjs ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ IconCircleHalf2)\n/* harmony export */ });\n/* harmony import */ var _createReactComponent_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createReactComponent.mjs */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/createReactComponent.mjs\");\n/**\n * @license @tabler/icons-react v3.33.0 - MIT\n *\n * This source code is licensed under the MIT license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nvar IconCircleHalf2 = (0,_createReactComponent_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"outline\", \"circle-half-2\", \"IconCircleHalf2\", [\n    [\n        \"path\",\n        {\n            \"d\": \"M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0\",\n            \"key\": \"svg-0\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            \"d\": \"M12 3v18\",\n            \"key\": \"svg-1\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            \"d\": \"M12 14l7 -7\",\n            \"key\": \"svg-2\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            \"d\": \"M12 19l8.5 -8.5\",\n            \"key\": \"svg-3\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            \"d\": \"M12 9l4.5 -4.5\",\n            \"key\": \"svg-4\"\n        }\n    ]\n]);\n //# sourceMappingURL=IconCircleHalf2.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AdGFibGVyL2ljb25zLXJlYWN0L2Rpc3QvZXNtL2ljb25zL0ljb25DaXJjbGVIYWxmMi5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFDQSxzQkFBZSxzRUFBcUIsVUFBVyxnQkFBaUIscUJBQW1CO0lBQUM7UUFBQyxPQUFPO1FBQUE7WUFBQyxLQUFJLDRDQUE2QztZQUFBLE1BQU07UUFBQSxDQUFRO0tBQUE7SUFBRTtRQUFDO1FBQU8sQ0FBQztZQUFBLElBQUk7WUFBVyxDQUFNO1FBQVE7S0FBQTtJQUFFO1FBQUMsT0FBTztRQUFBO1lBQUMsR0FBSSxnQkFBYztZQUFBLE9BQU0sQ0FBTztRQUFBLENBQUM7S0FBQTtJQUFFO1FBQUM7UUFBTztZQUFDLEtBQUksaUJBQWtCO1lBQUEsTUFBTTtRQUFBLENBQVE7S0FBQTtJQUFFO1FBQUM7UUFBTyxDQUFDO1lBQUEsSUFBSTtZQUFpQixDQUFNO1FBQVE7S0FBQztDQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNvcnJ5XFxEZXNrdG9wXFxzcmNcXGljb25zXFxJY29uQ2lyY2xlSGFsZjIudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNyZWF0ZVJlYWN0Q29tcG9uZW50IGZyb20gJy4uL2NyZWF0ZVJlYWN0Q29tcG9uZW50JztcbmV4cG9ydCBkZWZhdWx0IGNyZWF0ZVJlYWN0Q29tcG9uZW50KCdvdXRsaW5lJywgJ2NpcmNsZS1oYWxmLTInLCAnSWNvbkNpcmNsZUhhbGYyJywgW1tcInBhdGhcIix7XCJkXCI6XCJNMTIgMTJtLTkgMGE5IDkgMCAxIDAgMTggMGE5IDkgMCAxIDAgLTE4IDBcIixcImtleVwiOlwic3ZnLTBcIn1dLFtcInBhdGhcIix7XCJkXCI6XCJNMTIgM3YxOFwiLFwia2V5XCI6XCJzdmctMVwifV0sW1wicGF0aFwiLHtcImRcIjpcIk0xMiAxNGw3IC03XCIsXCJrZXlcIjpcInN2Zy0yXCJ9XSxbXCJwYXRoXCIse1wiZFwiOlwiTTEyIDE5bDguNSAtOC41XCIsXCJrZXlcIjpcInN2Zy0zXCJ9XSxbXCJwYXRoXCIse1wiZFwiOlwiTTEyIDlsNC41IC00LjVcIixcImtleVwiOlwic3ZnLTRcIn1dXSk7Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconCircleHalf2.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconMoonStars.mjs":
/*!***************************************************************************!*\
  !*** ./node_modules/@tabler/icons-react/dist/esm/icons/IconMoonStars.mjs ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ IconMoonStars)\n/* harmony export */ });\n/* harmony import */ var _createReactComponent_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createReactComponent.mjs */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/createReactComponent.mjs\");\n/**\n * @license @tabler/icons-react v3.33.0 - MIT\n *\n * This source code is licensed under the MIT license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nvar IconMoonStars = (0,_createReactComponent_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"outline\", \"moon-stars\", \"IconMoonStars\", [\n    [\n        \"path\",\n        {\n            \"d\": \"M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z\",\n            \"key\": \"svg-0\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            \"d\": \"M17 4a2 2 0 0 0 2 2a2 2 0 0 0 -2 2a2 2 0 0 0 -2 -2a2 2 0 0 0 2 -2\",\n            \"key\": \"svg-1\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            \"d\": \"M19 11h2m-1 -1v2\",\n            \"key\": \"svg-2\"\n        }\n    ]\n]);\n //# sourceMappingURL=IconMoonStars.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AdGFibGVyL2ljb25zLXJlYWN0L2Rpc3QvZXNtL2ljb25zL0ljb25Nb29uU3RhcnMubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQ0EsQ0FBZSx3RkFBcUIsWUFBVyxZQUFjLGtCQUFpQjtJQUFDO1FBQUM7UUFBTyxDQUFDO1lBQUEsSUFBSSxpRkFBaUY7WUFBQSxPQUFNLENBQU87UUFBQSxDQUFDO0tBQUE7SUFBRTtRQUFDO1FBQU8sQ0FBQztZQUFBLElBQUksb0VBQW9FO1lBQUEsT0FBTSxDQUFPO1FBQUEsQ0FBQztLQUFBO0lBQUU7UUFBQztRQUFPLENBQUM7WUFBQSxJQUFJLG1CQUFtQjtZQUFBLE9BQU0sQ0FBTztRQUFBLENBQUM7S0FBQztDQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNvcnJ5XFxEZXNrdG9wXFxzcmNcXGljb25zXFxJY29uTW9vblN0YXJzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjcmVhdGVSZWFjdENvbXBvbmVudCBmcm9tICcuLi9jcmVhdGVSZWFjdENvbXBvbmVudCc7XG5leHBvcnQgZGVmYXVsdCBjcmVhdGVSZWFjdENvbXBvbmVudCgnb3V0bGluZScsICdtb29uLXN0YXJzJywgJ0ljb25Nb29uU3RhcnMnLCBbW1wicGF0aFwiLHtcImRcIjpcIk0xMiAzYy4xMzIgMCAuMjYzIDAgLjM5MyAwYTcuNSA3LjUgMCAwIDAgNy45MiAxMi40NDZhOSA5IDAgMSAxIC04LjMxMyAtMTIuNDU0elwiLFwia2V5XCI6XCJzdmctMFwifV0sW1wicGF0aFwiLHtcImRcIjpcIk0xNyA0YTIgMiAwIDAgMCAyIDJhMiAyIDAgMCAwIC0yIDJhMiAyIDAgMCAwIC0yIC0yYTIgMiAwIDAgMCAyIC0yXCIsXCJrZXlcIjpcInN2Zy0xXCJ9XSxbXCJwYXRoXCIse1wiZFwiOlwiTTE5IDExaDJtLTEgLTF2MlwiLFwia2V5XCI6XCJzdmctMlwifV1dKTsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconMoonStars.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconSunHigh.mjs":
/*!*************************************************************************!*\
  !*** ./node_modules/@tabler/icons-react/dist/esm/icons/IconSunHigh.mjs ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ IconSunHigh)\n/* harmony export */ });\n/* harmony import */ var _createReactComponent_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createReactComponent.mjs */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/createReactComponent.mjs\");\n/**\n * @license @tabler/icons-react v3.33.0 - MIT\n *\n * This source code is licensed under the MIT license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nvar IconSunHigh = (0,_createReactComponent_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"outline\", \"sun-high\", \"IconSunHigh\", [\n    [\n        \"path\",\n        {\n            \"d\": \"M14.828 14.828a4 4 0 1 0 -5.656 -5.656a4 4 0 0 0 5.656 5.656z\",\n            \"key\": \"svg-0\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            \"d\": \"M6.343 17.657l-1.414 1.414\",\n            \"key\": \"svg-1\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            \"d\": \"M6.343 6.343l-1.414 -1.414\",\n            \"key\": \"svg-2\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            \"d\": \"M17.657 6.343l1.414 -1.414\",\n            \"key\": \"svg-3\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            \"d\": \"M17.657 17.657l1.414 1.414\",\n            \"key\": \"svg-4\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            \"d\": \"M4 12h-2\",\n            \"key\": \"svg-5\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            \"d\": \"M12 4v-2\",\n            \"key\": \"svg-6\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            \"d\": \"M20 12h2\",\n            \"key\": \"svg-7\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            \"d\": \"M12 20v2\",\n            \"key\": \"svg-8\"\n        }\n    ]\n]);\n //# sourceMappingURL=IconSunHigh.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconSunHigh.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/layout/navBarButton/Iconbar/SwitchColorModes.tsx":
/*!**************************************************************!*\
  !*** ./src/layout/navBarButton/Iconbar/SwitchColorModes.tsx ***!
  \**************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _i18n_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ~/i18n/client */ \"(app-pages-browser)/./src/i18n/client.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/use-mantine-color-scheme/use-mantine-color-scheme.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Menu/Menu.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Tooltip/Tooltip.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ActionIcon/ActionIcon.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCircleHalf2_IconMoonStars_IconSunHigh_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=IconCircleHalf2,IconMoonStars,IconSunHigh!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconCircleHalf2.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCircleHalf2_IconMoonStars_IconSunHigh_tabler_icons_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=IconCircleHalf2,IconMoonStars,IconSunHigh!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconMoonStars.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCircleHalf2_IconMoonStars_IconSunHigh_tabler_icons_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=IconCircleHalf2,IconMoonStars,IconSunHigh!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconSunHigh.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst ICON_SIZE = 20;\nconst SwitchColorModes = ()=>{\n    _s();\n    const { colorScheme, setColorScheme } = (0,_mantine_core__WEBPACK_IMPORTED_MODULE_3__.useMantineColorScheme)();\n    const { t } = (0,_i18n_client__WEBPACK_IMPORTED_MODULE_1__.useTranslation)('menu');\n    const [isChanging, setIsChanging] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const handleColorSchemeChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)({\n        \"SwitchColorModes.useCallback[handleColorSchemeChange]\": async (newScheme)=>{\n            if (isChanging) return; // Prevent multiple rapid clicks\n            setIsChanging(true);\n            try {\n                setColorScheme(newScheme);\n                // Save to localStorage for persistence\n                localStorage.setItem('theme', newScheme);\n            } finally{\n                // Reset after a short delay\n                setTimeout({\n                    \"SwitchColorModes.useCallback[handleColorSchemeChange]\": ()=>setIsChanging(false)\n                }[\"SwitchColorModes.useCallback[handleColorSchemeChange]\"], 300);\n            }\n        }\n    }[\"SwitchColorModes.useCallback[handleColorSchemeChange]\"], [\n        setColorScheme,\n        isChanging\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Menu, {\n            shadow: \"lg\",\n            width: 200,\n            zIndex: 1000010,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Menu.Target, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tooltip, {\n                        label: t(\"Switch-color-modes\"),\n                        withArrow: true,\n                        style: {\n                            color: \"var(--mantine-color-text)\"\n                        },\n                        className: \"h-10   navBarButtonicon\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.ActionIcon, {\n                            //variant=\"light\"\n                            className: \"h-10 bg-[var(--bg-SwitchColor)]  navBarButtonicon\",\n                            style: {\n                                color: \" light-dark(var(--mantine-color-gray-6), var(--mantine-color-dark-0))\"\n                            },\n                            children: colorScheme === \"auto\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCircleHalf2_IconMoonStars_IconSunHigh_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                size: ICON_SIZE,\n                                className: \" navBarButtonicon \"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\layout\\\\navBarButton\\\\Iconbar\\\\SwitchColorModes.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 17\n                            }, undefined) : colorScheme === \"dark\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCircleHalf2_IconMoonStars_IconSunHigh_tabler_icons_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                size: ICON_SIZE,\n                                className: \" navBarButtonicon \"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\layout\\\\navBarButton\\\\Iconbar\\\\SwitchColorModes.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 17\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCircleHalf2_IconMoonStars_IconSunHigh_tabler_icons_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                size: ICON_SIZE,\n                                className: \" navBarButtonicon \"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\layout\\\\navBarButton\\\\Iconbar\\\\SwitchColorModes.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\layout\\\\navBarButton\\\\Iconbar\\\\SwitchColorModes.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\layout\\\\navBarButton\\\\Iconbar\\\\SwitchColorModes.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\layout\\\\navBarButton\\\\Iconbar\\\\SwitchColorModes.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Menu.Dropdown, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Menu.Label, {\n                            tt: \"uppercase\",\n                            ta: \"center\",\n                            fw: 600,\n                            children: t('color-modes')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\layout\\\\navBarButton\\\\Iconbar\\\\SwitchColorModes.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Menu.Item, {\n                            leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCircleHalf2_IconMoonStars_IconSunHigh_tabler_icons_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                size: 16\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\layout\\\\navBarButton\\\\Iconbar\\\\SwitchColorModes.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 26\n                            }, void 0),\n                            onClick: ()=>handleColorSchemeChange(\"light\"),\n                            children: t('Light-modes')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\layout\\\\navBarButton\\\\Iconbar\\\\SwitchColorModes.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Menu.Item, {\n                            leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCircleHalf2_IconMoonStars_IconSunHigh_tabler_icons_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                size: 16\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\layout\\\\navBarButton\\\\Iconbar\\\\SwitchColorModes.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 26\n                            }, void 0),\n                            onClick: ()=>handleColorSchemeChange(\"dark\"),\n                            children: t('Dark-modes')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\layout\\\\navBarButton\\\\Iconbar\\\\SwitchColorModes.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Menu.Item, {\n                            leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCircleHalf2_IconMoonStars_IconSunHigh_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                size: 16\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\layout\\\\navBarButton\\\\Iconbar\\\\SwitchColorModes.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 26\n                            }, void 0),\n                            onClick: ()=>handleColorSchemeChange(\"auto\"),\n                            children: \"Auto\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\layout\\\\navBarButton\\\\Iconbar\\\\SwitchColorModes.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\layout\\\\navBarButton\\\\Iconbar\\\\SwitchColorModes.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\layout\\\\navBarButton\\\\Iconbar\\\\SwitchColorModes.tsx\",\n            lineNumber: 41,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false);\n};\n_s(SwitchColorModes, \"XmcA0dGGnQsoam8oJaTouyg8xLI=\", false, function() {\n    return [\n        _mantine_core__WEBPACK_IMPORTED_MODULE_3__.useMantineColorScheme,\n        _i18n_client__WEBPACK_IMPORTED_MODULE_1__.useTranslation\n    ];\n});\n_c = SwitchColorModes;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SwitchColorModes);\nvar _c;\n$RefreshReg$(_c, \"SwitchColorModes\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/layout/navBarButton/Iconbar/SwitchColorModes.tsx\n"));

/***/ })

}]);