"""
This file contains copies of the serializers from serializers.py to avoid circular imports.
"""

from rest_framework import serializers
from django.contrib.auth import get_user_model
from django.contrib.auth.password_validation import validate_password

User = get_user_model()

class UserSerializer(serializers.ModelSerializer):
    trial_days_remaining = serializers.IntegerField(read_only=True)
    is_trial_active = serializers.BooleanField(read_only=True)
    has_pending_trial_request = serializers.BooleanField(read_only=True)

    class Meta:
        model = User
        fields = [
            # Basic user info
            'id', 'email', 'first_name', 'last_name', 'user_type',
            'phone_number', 'landline_number', 'address',

            # Profile images
            'profile_image', 'profile_image_medium', 'profile_image_large',

            # Location
            'country', 'country_name', 'region', 'region_name', 'city', 'city_name',

            # Doctor specific fields
            'specialization', 'license_number', 'bio', 'years_of_experience',
            'education', 'certifications',

            # Doctor social media fields
            'facebook_url', 'twitter_url', 'linkedin_url', 'youtube_url',
            'telegram_url', 'whatsapp_number',

            # Doctor clinic photos
            'clinic_photo_1', 'clinic_photo_2', 'clinic_photo_3',

            # Trial fields
            'is_trial', 'trial_start_date', 'trial_end_date', 'trial_duration_months',
            'is_trial_active', 'trial_days_remaining', 'trial_requested',
            'trial_request_date', 'trial_request_approved', 'trial_request_duration_months',
            'has_pending_trial_request'
        ]
        read_only_fields = [
            'id', 'trial_days_remaining', 'is_trial_active',
            'trial_request_approved', 'has_pending_trial_request'
        ]

class DoctorSerializer(serializers.ModelSerializer):
    trial_days_remaining = serializers.IntegerField(read_only=True)
    is_trial_active = serializers.BooleanField(read_only=True)
    has_pending_trial_request = serializers.BooleanField(read_only=True)

    class Meta:
        model = User
        fields = [
            # Basic user info
            'id', 'email', 'first_name', 'last_name', 'phone_number', 'landline_number',
            'address',

            # Profile images
            'profile_image', 'profile_image_medium', 'profile_image_large',

            # Location
            'country', 'country_name', 'region', 'region_name', 'city', 'city_name',

            # Doctor specific fields
            'specialization', 'license_number', 'bio', 'years_of_experience',
            'education', 'certifications',

            # Doctor social media fields
            'facebook_url', 'twitter_url', 'linkedin_url', 'youtube_url',
            'telegram_url', 'whatsapp_number',

            # Doctor clinic photos
            'clinic_photo_1', 'clinic_photo_2', 'clinic_photo_3',

            # Trial fields
            'is_trial', 'trial_start_date', 'trial_end_date',
            'trial_duration_months', 'is_trial_active', 'trial_days_remaining',
            'trial_requested', 'trial_request_date', 'trial_request_approved',
            'trial_request_duration_months', 'has_pending_trial_request'
        ]
        read_only_fields = [
            'id', 'trial_days_remaining', 'is_trial_active',
            'trial_request_approved', 'has_pending_trial_request'
        ]

class PatientSerializer(serializers.ModelSerializer):
    assigned_doctor_name = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = User
        fields = [
            # Basic user info
            'id', 'email', 'first_name', 'last_name', 'title', 'phone_number', 'landline_number',
            'address', 'date_of_birth', 'age', 'gender', 'medical_history',

            # Profile images
            'profile_image', 'profile_image_medium', 'profile_image_large',

            # Location
            'country', 'region', 'city',

            # Additional patient info
            'social_security', 'cin', 'etat_civil', 'notes',

            # Appointment related
            'appointment_date', 'appointment_time', 'appointment_end_time',
            'consultation_duration', 'etat_agenda', 'assigned_doctor', 'assigned_doctor_name',
            'event_title', 'event_type', 'resource_id', 'type_consultation',
            'commentaire_liste_attente', 'visitor_count'
        ]
        read_only_fields = ['id', 'assigned_doctor_name']

    def get_assigned_doctor_name(self, obj):
        if obj.assigned_doctor:
            return f"{obj.assigned_doctor.first_name} {obj.assigned_doctor.last_name}"
        return None

class AssistantSerializer(serializers.ModelSerializer):
    assigned_doctor_email = serializers.EmailField(source='assigned_doctor.email', read_only=True)
    date_joined = serializers.DateTimeField(read_only=True, format="%Y-%m-%d %H:%M:%S")
    profile_image_url = serializers.SerializerMethodField(read_only=True)
    is_pending = serializers.SerializerMethodField(read_only=True)
    password = serializers.CharField(write_only=True, required=False)
    password2 = serializers.CharField(write_only=True, required=False)

    class Meta:
        model = User
        fields = [
            'id', 'email', 'first_name', 'last_name', 'phone_number', 'address',
            'assigned_doctor', 'assigned_doctor_email',
            # Add profile image fields
            'profile_image', 'profile_image_url', 'profile_image_medium', 'profile_image_large',
            # Add status fields
            'is_active', 'is_pending',
            # Add date fields
            'date_joined',
            # Add password fields
            'password', 'password2'
        ]
        read_only_fields = ['id', 'date_joined', 'profile_image_url', 'is_pending']

    def get_profile_image_url(self, obj):
        """
        Return the complete URL of the profile image if it exists.
        """
        from django.conf import settings

        if obj.profile_image and obj.profile_image.name:
            # Get the base URL from settings or use a default
            base_url = getattr(settings, 'BASE_URL', 'http://localhost:8000')

            # Make sure the URL doesn't have double slashes
            image_url = obj.profile_image.url
            if image_url.startswith('/'):
                image_url = image_url[1:]

            # Construct the full URL
            full_url = f"{base_url}/{image_url}"

            # Log the URL construction for debugging
            print(f"Constructed profile_image_url for {obj.email}: {full_url}")

            return full_url

        # Try medium and large images as fallbacks
        elif obj.profile_image_medium and obj.profile_image_medium.name:
            base_url = getattr(settings, 'BASE_URL', 'http://localhost:8000')
            image_url = obj.profile_image_medium.url
            if image_url.startswith('/'):
                image_url = image_url[1:]
            return f"{base_url}/{image_url}"

        elif obj.profile_image_large and obj.profile_image_large.name:
            base_url = getattr(settings, 'BASE_URL', 'http://localhost:8000')
            image_url = obj.profile_image_large.url
            if image_url.startswith('/'):
                image_url = image_url[1:]
            return f"{base_url}/{image_url}"

        return None

    def get_is_pending(self, obj):
        """
        Return whether the assistant is pending.
        For now, we'll return False by default since there's no pending status in the model.
        This can be enhanced later to check for a specific condition.
        """
        # In the future, this could be based on a status field or other criteria
        return not obj.is_active

    def validate(self, attrs):
        # Check if password fields are provided and match
        if 'password' in attrs and 'password2' in attrs:
            if attrs['password'] != attrs['password2']:
                raise serializers.ValidationError({"password": "Password fields didn't match."})
        return attrs

    def create(self, validated_data):
        # Remove password2 field as it's not a field in the User model
        password2 = validated_data.pop('password2', None)

        # Create the user instance
        user = super().create(validated_data)

        # Set the password if provided
        if 'password' in validated_data:
            user.set_password(validated_data['password'])
            user.save()

        return user

    def update(self, instance, validated_data):
        # Handle password update
        if 'password' in validated_data:
            password = validated_data.pop('password')
            # Remove password2 field as it's not needed for user update
            validated_data.pop('password2', None)

            # Set the new password
            instance.set_password(password)

            # Log the password update
            print(f"Setting new password for assistant {instance.email}")

        # Update the instance with the remaining validated data
        return super().update(instance, validated_data)

class TrialRequestSerializer(serializers.Serializer):
    duration_months = serializers.ChoiceField(
        choices=[(1, '1 Month'), (2, '2 Months'), (3, '3 Months')],
        default=1
    )

    def validate_duration_months(self, value):
        try:
            value = int(value)
            if value not in [1, 2, 3]:
                raise serializers.ValidationError("Trial duration must be 1, 2, or 3 months")
            return value
        except (ValueError, TypeError):
            raise serializers.ValidationError("Invalid duration value")

    def save(self, user):
        duration_months = self.validated_data.get('duration_months')
        user.request_trial(duration_months=duration_months)
        return user

class RegisterSerializer(serializers.ModelSerializer):
    password = serializers.CharField(write_only=True, required=True, validators=[validate_password])
    password2 = serializers.CharField(write_only=True, required=True)

    # Additional fields for doctor registration
    specialization = serializers.CharField(required=False, allow_blank=True)
    license_number = serializers.CharField(required=False, allow_blank=True)

    # Subscription package fields - these will be handled separately
    subscription_period = serializers.CharField(required=False, allow_blank=True, write_only=True)
    package_id = serializers.CharField(required=False, allow_blank=True, write_only=True)
    package_name = serializers.CharField(required=False, allow_blank=True, write_only=True)
    package_price = serializers.CharField(required=False, allow_blank=True, write_only=True)
    billing_cycle = serializers.CharField(required=False, allow_blank=True, write_only=True)

    # Payment information fields
    transaction_id = serializers.CharField(required=False, allow_blank=True, write_only=True)
    payment_method = serializers.CharField(required=False, allow_blank=True, write_only=True)
    payment_status = serializers.CharField(required=False, allow_blank=True, write_only=True)
    payment_reference = serializers.CharField(required=False, allow_blank=True, write_only=True)
    payment_bank = serializers.CharField(required=False, allow_blank=True, write_only=True)
    payment_account = serializers.CharField(required=False, allow_blank=True, write_only=True)
    payment_amount = serializers.CharField(required=False, allow_blank=True, write_only=True)

    class Meta:
        model = User
        fields = [
            'email', 'password', 'password2', 'first_name', 'last_name', 'user_type',
            'specialization', 'license_number', 'subscription_period', 'package_id',
            'package_name', 'package_price', 'billing_cycle', 'transaction_id',
            'payment_method', 'payment_status', 'payment_reference', 'payment_bank',
            'payment_account', 'payment_amount'
        ]

    def validate(self, attrs):
        if attrs['password'] != attrs['password2']:
            raise serializers.ValidationError({"password": "Password fields didn't match."})
        return attrs

    def create(self, validated_data):
        # Remove password2 field as it's not needed for user creation
        validated_data.pop('password2')

        # Ensure the user is active by default
        validated_data['is_active'] = True

        # Extract subscription package fields
        subscription_period = validated_data.pop('subscription_period', None)
        package_id = validated_data.pop('package_id', None)
        package_name = validated_data.pop('package_name', None)
        package_price = validated_data.pop('package_price', None)
        billing_cycle = validated_data.pop('billing_cycle', None)

        # Extract payment information (will be added to license)
        transaction_id = validated_data.pop('transaction_id', None)
        payment_method = validated_data.pop('payment_method', None)
        payment_status = validated_data.pop('payment_status', None)
        payment_reference = validated_data.pop('payment_reference', None)
        payment_bank = validated_data.pop('payment_bank', None)
        payment_account = validated_data.pop('payment_account', None)
        payment_amount = validated_data.pop('payment_amount', None)

        # Log the subscription information for debugging
        if any([subscription_period, package_id, package_name, package_price, billing_cycle]):
            print("Subscription information received:")
            print(f"  subscription_period: {subscription_period}")
            print(f"  package_id: {package_id}")
            print(f"  package_name: {package_name}")
            print(f"  package_price: {package_price}")
            print(f"  billing_cycle: {billing_cycle}")

        # Log payment information
        if transaction_id:
            print("Payment information received:")
            print(f"  transaction_id: {transaction_id}")
            print(f"  payment_method: {payment_method}")
            print(f"  payment_status: {payment_status}")
            print(f"  payment_reference: {payment_reference}")
            print(f"  payment_bank: {payment_bank}")
            print(f"  payment_account: {payment_account}")
            print(f"  payment_amount: {payment_amount}")

        # Create the user without the subscription fields
        user = User.objects.create_user(**validated_data)

        # If this is a doctor, generate a secure license number
        if user.user_type == 'doctor':
            from users.utils import LicenseGenerator
            from users.utils.json_utils import UUIDEncoder, dumps
            from users.models import DoctorLicense, Specialty
            import datetime
            import json

            # Generate a secure license number
            license_number, raw_data = LicenseGenerator.generate_license(
                user_id=user.id,
                specialization=user.specialization,
                expiry_days=365  # Default to 1 year
            )

            # Generate an activation code
            activation_code = LicenseGenerator.generate_activation_code()

            # Determine expiry date based on subscription period
            expiry_days = 180  # Default to 6 months
            if subscription_period == '1year':
                expiry_days = 365  # 1 year

            expiry_date = datetime.datetime.now() + datetime.timedelta(days=expiry_days)

            # Convert payment_amount to float if it's not None or empty
            payment_amount_float = None
            if payment_amount:
                try:
                    payment_amount_float = float(payment_amount)
                    print(f"Converted payment_amount '{payment_amount}' to float: {payment_amount_float}")
                except (ValueError, TypeError) as e:
                    print(f"Error converting payment_amount '{payment_amount}' to float: {e}")
                    payment_amount_float = None

            # Convert raw_data to JSON using our custom encoder
            serialized_data = dumps(raw_data)
            parsed_data = json.loads(serialized_data)

            # Check if a license already exists for this user
            existing_license = DoctorLicense.objects.filter(user=user).first()

            if existing_license:
                print(f"Found existing license for doctor {user.email}, updating it")

                # Update the existing license
                existing_license.license_number = license_number
                existing_license.license_data = parsed_data
                existing_license.expiry_date = expiry_date
                existing_license.activation_code = activation_code
                existing_license.subscription_period = subscription_period or '6months'

                # Update payment information if provided
                if transaction_id:
                    existing_license.transaction_id = transaction_id
                if payment_method:
                    existing_license.payment_method = payment_method
                if payment_status:
                    existing_license.payment_status = payment_status
                    if payment_status == 'completed':
                        existing_license.payment_date = datetime.datetime.now()
                if payment_reference:
                    existing_license.payment_reference = payment_reference
                if payment_bank:
                    existing_license.payment_bank = payment_bank
                if payment_account:
                    existing_license.payment_account = payment_account
                if payment_amount_float is not None:
                    existing_license.payment_amount = payment_amount_float

                # Save the updated license
                existing_license.save()
                license_obj = existing_license
                print(f"Updated existing license for doctor {user.email}")
            else:
                # Create a new license
                try:
                    license_obj = DoctorLicense.objects.create(
                        user=user,
                        license_number=license_number,
                        status='pending',
                        license_data=parsed_data,
                        expiry_date=expiry_date,
                        activation_code=activation_code,
                        subscription_period=subscription_period or '6months',
                        transaction_id=transaction_id,
                        payment_method=payment_method,
                        payment_status=payment_status,
                        payment_date=datetime.datetime.now() if payment_status == 'completed' else None,
                        payment_reference=payment_reference,
                        payment_bank=payment_bank,
                        payment_account=payment_account,
                        payment_amount=payment_amount_float
                    )
                    print(f"Created new license for doctor {user.email}")
                except Exception as e:
                    print(f"Error creating license record: {e}")
                    # Create license without payment information as fallback
                    license_obj = DoctorLicense.objects.create(
                        user=user,
                        license_number=license_number,
                        status='pending',
                        license_data=parsed_data,
                        expiry_date=expiry_date,
                        activation_code=activation_code,
                        subscription_period=subscription_period or '6months'
                    )
                    print(f"Created fallback license record without payment information")

            # Update the user's license number
            user.license_number = license_number

            # Associate the doctor with the specialty
            if user.specialization:
                try:
                    # Try to find the specialty by slug first
                    specialty = Specialty.objects.filter(slug=user.specialization.lower()).first()

                    # If not found by slug, try by name
                    if not specialty:
                        specialty = Specialty.objects.filter(name__iexact=user.specialization).first()

                    # If still not found, create a new specialty
                    if not specialty:
                        from django.utils.text import slugify
                        specialty = Specialty.objects.create(
                            name=user.specialization.capitalize(),
                            slug=slugify(user.specialization),
                            description=f"Specialty for {user.specialization}",
                            is_active=True
                        )

                    # Add the doctor to the specialty
                    user.specialties.add(specialty)
                    print(f"Added doctor to specialty: {specialty.name}")
                    print(f"Doctor count in specialty: {specialty.doctor_count}")
                except Exception as e:
                    print(f"Error associating doctor with specialty: {e}")

            user.save()

            # In a real system, you would send the activation code to the user via email
            print(f"Generated license {license_number} with activation code {activation_code}")

            # Store the license number on the user object
            user.license_number = license_number
            # Return the user object directly
            return user

        # No automatic trial period - doctors need to request it
        return user
