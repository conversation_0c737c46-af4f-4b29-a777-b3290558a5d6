from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='User',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('first_name', models.CharField(blank=True, max_length=150, verbose_name='first name')),
                ('last_name', models.Char<PERSON>ield(blank=True, max_length=150, verbose_name='last name')),
                ('is_staff', models.<PERSON><PERSON>an<PERSON>ield(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('email', models.EmailField(max_length=254, unique=True)),
                ('user_type', models.CharField(choices=[('doctor', 'Doctor'), ('patient', 'Patient'), ('assistant', 'Assistant'), ('admin', 'Admin')], default='patient', max_length=10)),
                ('phone_number', models.CharField(blank=True, max_length=15, null=True)),
                ('address', models.TextField(blank=True, null=True)),
                ('country', models.CharField(blank=True, max_length=100, null=True)),
                ('region', models.CharField(blank=True, max_length=100, null=True)),
                ('city', models.CharField(blank=True, max_length=100, null=True)),
                ('specialization', models.CharField(blank=True, max_length=100, null=True)),
                ('license_number', models.CharField(blank=True, max_length=100, null=True)),
                ('license_verified', models.BooleanField(default=False, help_text='Whether the license has been verified')),
                ('date_of_birth', models.DateField(blank=True, null=True)),
                ('medical_history', models.TextField(blank=True, null=True)),
                ('assigned_doctor', models.ForeignKey(blank=True, null=True, on_delete=models.deletion.SET_NULL, related_name='assistants', to='users.user')),
                ('is_trial', models.BooleanField(default=False)),
                ('trial_start_date', models.DateTimeField(blank=True, null=True)),
                ('trial_end_date', models.DateTimeField(blank=True, null=True)),
                ('trial_duration_months', models.IntegerField(blank=True, null=True)),
                ('trial_notification_sent', models.BooleanField(default=False)),
                ('subscription_period', models.CharField(blank=True, max_length=20, null=True)),
                ('package_id', models.CharField(blank=True, max_length=100, null=True)),
                ('package_name', models.CharField(blank=True, max_length=100, null=True)),
                ('package_price', models.CharField(blank=True, max_length=20, null=True)),
                ('billing_cycle', models.CharField(blank=True, max_length=20, null=True)),
                ('trial_requested', models.BooleanField(default=False)),
                ('trial_request_date', models.DateTimeField(blank=True, null=True)),
                ('trial_request_approved', models.BooleanField(default=False)),
                ('trial_request_approved_by', models.CharField(blank=True, max_length=255, null=True)),
                ('trial_request_approved_date', models.DateTimeField(blank=True, null=True)),
                ('trial_request_notes', models.TextField(blank=True, null=True)),
                ('trial_request_duration_months', models.IntegerField(blank=True, null=True)),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions')),
            ],
            options={
                'verbose_name': 'user',
                'verbose_name_plural': 'users',
                'abstract': False,
            },
        ),
    ]
