from django.db import models
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator
from decimal import Decimal

User = get_user_model()


class BasePharmacyModel(models.Model):
    """Base model for all pharmacy models"""
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        abstract = True


class Supplier(BasePharmacyModel):
    """Model for pharmacy suppliers (Fournisseurs)"""
    raison_sociale = models.CharField(max_length=255, verbose_name=_('Raison sociale'))
    email = models.EmailField(verbose_name=_('Email'))
    ville = models.CharField(max_length=100, verbose_name=_('Ville'), blank=True)
    tel_gestionnaire = models.CharField(max_length=20, verbose_name=_('Tél. Gestionnaire'))
    tel_fixe = models.CharField(max_length=20, verbose_name=_('Tél. Fixe'), blank=True)
    fax = models.CharField(max_length=20, verbose_name=_('Fax'), blank=True)
    adresse = models.TextField(verbose_name=_('Adresse'), blank=True)
    commentaire = models.TextField(verbose_name=_('Commentaire'), blank=True)
    mode_paiement = models.CharField(max_length=50, verbose_name=_('Mode de paiement'), blank=True)
    condition_paiement = models.CharField(max_length=50, verbose_name=_('Condition de paiement'), blank=True)
    ice = models.CharField(max_length=50, verbose_name=_('ICE'), blank=True)
    rc = models.CharField(max_length=50, verbose_name=_('RC'), blank=True)
    directeur_commercial = models.CharField(max_length=255, verbose_name=_('Directeur commercial'), blank=True)
    gestionnaire_vente = models.CharField(max_length=255, verbose_name=_('Gestionnaire vente'), blank=True)

    class Meta:
        verbose_name = _('Supplier')
        verbose_name_plural = _('Suppliers')
        ordering = ['raison_sociale']

    def __str__(self):
        return self.raison_sociale


class Depot(BasePharmacyModel):
    """Model for pharmacy depots (Dépôts)"""
    name = models.CharField(max_length=255, verbose_name=_('Nom du dépôt'))
    code = models.CharField(max_length=20, unique=True, verbose_name=_('Code'))
    address = models.TextField(verbose_name=_('Adresse'), blank=True)
    manager = models.CharField(max_length=255, verbose_name=_('Responsable'), blank=True)
    phone = models.CharField(max_length=20, verbose_name=_('Téléphone'), blank=True)

    class Meta:
        verbose_name = _('Depot')
        verbose_name_plural = _('Depots')
        ordering = ['name']

    def __str__(self):
        return self.name


class ProductCategory(BasePharmacyModel):
    """Model for product categories (Familles)"""
    name = models.CharField(max_length=255, verbose_name=_('Nom de la famille'))
    code = models.CharField(max_length=20, unique=True, verbose_name=_('Code'))
    description = models.TextField(verbose_name=_('Description'), blank=True)

    class Meta:
        verbose_name = _('Product Category')
        verbose_name_plural = _('Product Categories')
        ordering = ['name']

    def __str__(self):
        return self.name


class Product(BasePharmacyModel):
    """Model for pharmacy products (Articles/Médicaments)"""
    code = models.CharField(max_length=50, unique=True, verbose_name=_('Code'))
    designation = models.CharField(max_length=255, verbose_name=_('Désignation'))
    category = models.ForeignKey(ProductCategory, on_delete=models.SET_NULL, null=True, blank=True, verbose_name=_('Famille'))
    unit = models.CharField(max_length=20, verbose_name=_('Unité'), default='pièce')
    price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name=_('Prix'), validators=[MinValueValidator(Decimal('0.00'))])
    barcode = models.CharField(max_length=50, verbose_name=_('Code à barres'), blank=True)
    description = models.TextField(verbose_name=_('Description'), blank=True)
    min_stock = models.PositiveIntegerField(verbose_name=_('Stock minimum'), default=0)
    max_stock = models.PositiveIntegerField(verbose_name=_('Stock maximum'), default=1000)

    class Meta:
        verbose_name = _('Product')
        verbose_name_plural = _('Products')
        ordering = ['designation']

    def __str__(self):
        return f"{self.code} - {self.designation}"


class PurchaseRequest(BasePharmacyModel):
    """Model for purchase requests (Demandes d'achat)"""
    STATUS_CHOICES = [
        ('draft', _('Brouillon')),
        ('pending', _('En attente')),
        ('approved', _('Approuvée')),
        ('rejected', _('Rejetée')),
        ('completed', _('Terminée')),
    ]

    numero = models.CharField(max_length=50, unique=True, verbose_name=_('Numéro'))
    date = models.DateField(verbose_name=_('Date'))
    date_echeance = models.DateField(verbose_name=_('Date d\'échéance'), null=True, blank=True)
    urgent = models.BooleanField(default=False, verbose_name=_('Urgent'))
    supplier = models.ForeignKey(Supplier, on_delete=models.CASCADE, verbose_name=_('Fournisseur'))
    commentaire = models.TextField(verbose_name=_('Commentaire'), blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft', verbose_name=_('Statut'))

    class Meta:
        verbose_name = _('Purchase Request')
        verbose_name_plural = _('Purchase Requests')
        ordering = ['-date', '-numero']

    def __str__(self):
        return f"Demande {self.numero} - {self.supplier.raison_sociale}"


class PurchaseRequestItem(BasePharmacyModel):
    """Model for purchase request items (Articles de demande d'achat)"""
    purchase_request = models.ForeignKey(PurchaseRequest, on_delete=models.CASCADE, related_name='items', verbose_name=_('Demande d\'achat'))
    product = models.ForeignKey(Product, on_delete=models.CASCADE, verbose_name=_('Article'))
    depot = models.ForeignKey(Depot, on_delete=models.CASCADE, verbose_name=_('Dépôt'))
    quantity = models.PositiveIntegerField(verbose_name=_('Quantité'), validators=[MinValueValidator(1)])
    unit_price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name=_('Prix unitaire'), null=True, blank=True)
    total_price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name=_('Prix total'), null=True, blank=True)

    class Meta:
        verbose_name = _('Purchase Request Item')
        verbose_name_plural = _('Purchase Request Items')
        unique_together = ['purchase_request', 'product', 'depot']

    def save(self, *args, **kwargs):
        if self.unit_price and self.quantity:
            self.total_price = self.unit_price * self.quantity
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.product.designation} - Qté: {self.quantity}"


class Inventory(BasePharmacyModel):
    """Model for inventory management"""
    product = models.ForeignKey(Product, on_delete=models.CASCADE, verbose_name=_('Article'))
    depot = models.ForeignKey(Depot, on_delete=models.CASCADE, verbose_name=_('Dépôt'))
    quantity = models.PositiveIntegerField(verbose_name=_('Quantité en stock'), default=0)
    reserved_quantity = models.PositiveIntegerField(verbose_name=_('Quantité réservée'), default=0)
    last_movement_date = models.DateTimeField(auto_now=True, verbose_name=_('Dernière modification'))

    class Meta:
        verbose_name = _('Inventory')
        verbose_name_plural = _('Inventories')
        unique_together = ['product', 'depot']

    @property
    def available_quantity(self):
        return self.quantity - self.reserved_quantity

    def __str__(self):
        return f"{self.product.designation} - {self.depot.name}: {self.quantity}"


class StockMovement(BasePharmacyModel):
    """Model for stock movements (Mouvements de stock)"""
    MOVEMENT_TYPES = [
        ('in', _('Entrée')),
        ('out', _('Sortie')),
        ('transfer', _('Transfert')),
        ('adjustment', _('Ajustement')),
    ]

    product = models.ForeignKey(Product, on_delete=models.CASCADE, verbose_name=_('Article'))
    depot = models.ForeignKey(Depot, on_delete=models.CASCADE, verbose_name=_('Dépôt'))
    movement_type = models.CharField(max_length=20, choices=MOVEMENT_TYPES, verbose_name=_('Type de mouvement'))
    quantity = models.IntegerField(verbose_name=_('Quantité'))  # Can be negative for out movements
    reference = models.CharField(max_length=100, verbose_name=_('Référence'), blank=True)
    reason = models.TextField(verbose_name=_('Motif'), blank=True)
    date = models.DateTimeField(auto_now_add=True, verbose_name=_('Date'))

    class Meta:
        verbose_name = _('Stock Movement')
        verbose_name_plural = _('Stock Movements')
        ordering = ['-date']

    def __str__(self):
        return f"{self.movement_type} - {self.product.designation}: {self.quantity}"
