from django.contrib import admin
from django.utils.html import format_html
from .models import SubscriptionPackage, DoctorSubscription, Coupon, SubscriptionTransaction

@admin.register(SubscriptionPackage)
class SubscriptionPackageAdmin(admin.ModelAdmin):
    list_display = ('name', 'price_monthly', 'price_yearly', 'max_assistants', 'max_users', 'is_active')
    list_filter = ('is_active',)
    search_fields = ('name', 'description')
    fieldsets = (
        ('Package Details', {
            'fields': ('name', 'description', 'is_active')
        }),
        ('Limits', {
            'fields': ('max_assistants', 'max_users', 'max_specialties')
        }),
        ('Pricing', {
            'fields': ('price_monthly', 'price_yearly')
        }),
        ('Features', {
            'fields': ('features',)
        }),
    )

@admin.register(DoctorSubscription)
class DoctorSubscriptionAdmin(admin.ModelAdmin):
    list_display = ('doctor', 'package', 'status', 'billing_cycle', 'start_date', 'end_date', 'days_remaining_colored')
    list_filter = ('status', 'billing_cycle', 'package', 'start_date', 'end_date')
    search_fields = ('doctor__email', 'doctor__first_name', 'doctor__last_name')
    readonly_fields = ('days_remaining_colored',)

    def days_remaining_colored(self, obj):
        """Display days remaining with color coding based on urgency."""
        days = obj.days_remaining()

        if days <= 0:
            # Expired
            return format_html(
                '<span style="color: #dc3545; font-weight: bold; background-color: #f8d7da; padding: 2px 6px; border-radius: 3px;">EXPIRÉ</span>'
            )
        elif days < 30:
            # Critical - Red
            return format_html(
                '<span style="color: #dc3545; font-weight: bold;">{} jours</span>',
                days
            )
        elif days < 60:
            # Warning - Orange
            return format_html(
                '<span style="color: #fd7e14; font-weight: bold;">{} jours</span>',
                days
            )
        else:
            # Good - Green
            return format_html(
                '<span style="color: #28a745;">{} jours</span>',
                days
            )

    days_remaining_colored.short_description = 'Jours restants'
    days_remaining_colored.admin_order_field = 'end_date'

@admin.register(Coupon)
class CouponAdmin(admin.ModelAdmin):
    list_display = ('code', 'discount_type', 'discount_value', 'valid_from', 'valid_until', 'current_uses', 'max_uses', 'is_active', 'is_valid')
    list_filter = ('discount_type', 'is_active')
    search_fields = ('code', 'description')
    readonly_fields = ('current_uses', 'is_valid')
    filter_horizontal = ('applicable_packages',)

    def is_valid(self, obj):
        return obj.is_valid()
    is_valid.boolean = True
    is_valid.short_description = 'Is Valid'

@admin.register(SubscriptionTransaction)
class SubscriptionTransactionAdmin(admin.ModelAdmin):
    list_display = ('subscription', 'amount', 'status', 'payment_method', 'coupon', 'discount_amount', 'created_at')
    list_filter = ('status', 'payment_method', 'created_at')
    search_fields = ('subscription__doctor__email', 'transaction_id')
    readonly_fields = ('created_at', 'updated_at')
