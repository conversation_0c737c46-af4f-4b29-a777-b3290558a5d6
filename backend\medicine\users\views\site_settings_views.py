from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IsAdminUser, AllowAny
from users.models import SiteSettings
from users.serializers import SiteSettingsSerializer


class SiteSettingsView(APIView):
    """
    View to get and update site settings.
    GET: Anyone can retrieve site settings
    PUT/PATCH: Only admin users can update site settings
    """

    def get_permissions(self):
        """
        Return different permissions depending on the HTTP method.
        """
        if self.request.method == 'GET':
            return [AllowAny()]
        return [IsAdminUser()]

    def get(self, request, *args, **kwargs):
        """
        Get the site settings.
        """
        settings = SiteSettings.get_settings()
        serializer = SiteSettingsSerializer(settings)
        return Response(serializer.data)

    def put(self, request, *args, **kwargs):
        """
        Update the site settings.
        """
        settings = SiteSettings.get_settings()
        serializer = SiteSettingsSerializer(settings, data=request.data)

        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def patch(self, request, *args, **kwargs):
        """
        Partially update the site settings.
        """
        settings = SiteSettings.get_settings()
        serializer = SiteSettingsSerializer(settings, data=request.data, partial=True)

        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
