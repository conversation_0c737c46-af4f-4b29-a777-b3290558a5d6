"""
Billing models for the dentistry application.
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from dentistry.models.base import DentistryBaseModel
from django.db.models import Model
from dentistry.models.patient import DentistryPatient
from dentistry.models.doctor import DentistryDoctor

# Simplified base models for billing
class BillingCode(Model):
    """Simplified BillingCode model"""
    code = models.CharField(max_length=20)
    description = models.CharField(max_length=255)

    def __str__(self):
        return f"{self.code} - {self.description}"

class Invoice(Model):
    """Simplified Invoice model"""
    date_issued = models.DateField()

    def __str__(self):
        return f"Invoice {self.id}"

class PatientInsurance(Model):
    """Simplified PatientInsurance model"""
    is_primary = models.BooleanField(default=True)

    def __str__(self):
        return f"Insurance {self.id}"

class DentistryBillingCode(DentistryBaseModel):
    """
    Dentistry-specific billing code model.
    """
    base_code = models.ForeignKey(
        BillingCode,
        on_delete=models.CASCADE,
        related_name="dentistry_codes",
        verbose_name=_("Base Billing Code")
    )
    ada_code = models.CharField(
        max_length=20,
        blank=True,
        verbose_name=_("ADA Code")
    )
    dentistry_specific_details = models.TextField(
        blank=True,
        verbose_name=_("Dentistry-Specific Details")
    )
    is_preventive = models.BooleanField(
        default=False,
        verbose_name=_("Is Preventive")
    )
    is_restorative = models.BooleanField(
        default=False,
        verbose_name=_("Is Restorative")
    )
    is_endodontic = models.BooleanField(
        default=False,
        verbose_name=_("Is Endodontic")
    )
    is_periodontic = models.BooleanField(
        default=False,
        verbose_name=_("Is Periodontic")
    )
    is_prosthodontic = models.BooleanField(
        default=False,
        verbose_name=_("Is Prosthodontic")
    )
    is_orthodontic = models.BooleanField(
        default=False,
        verbose_name=_("Is Orthodontic")
    )
    is_surgical = models.BooleanField(
        default=False,
        verbose_name=_("Is Surgical")
    )

    class Meta:
        verbose_name = _("Dentistry Billing Code")
        verbose_name_plural = _("Dentistry Billing Codes")
        ordering = ['ada_code', 'base_code__code']

    def __str__(self):
        ada = f"ADA: {self.ada_code} - " if self.ada_code else ""
        return f"{ada}{self.base_code.code} - {self.base_code.description}"

class DentistryInvoice(DentistryBaseModel):
    """
    Dentistry-specific invoice model.
    """
    base_invoice = models.OneToOneField(
        Invoice,
        on_delete=models.CASCADE,
        related_name="dentistry_invoice",
        verbose_name=_("Base Invoice")
    )
    dentistry_patient = models.ForeignKey(
        DentistryPatient,
        on_delete=models.CASCADE,
        related_name="dentistry_invoices",
        verbose_name=_("Dentistry Patient")
    )
    teeth_treated = models.CharField(
        max_length=200,
        blank=True,
        verbose_name=_("Teeth Treated"),
        help_text=_("Comma-separated list of tooth numbers")
    )
    x_rays_taken = models.BooleanField(
        default=False,
        verbose_name=_("X-Rays Taken")
    )
    cleaning_performed = models.BooleanField(
        default=False,
        verbose_name=_("Cleaning Performed")
    )
    lab_work_required = models.BooleanField(
        default=False,
        verbose_name=_("Lab Work Required")
    )
    lab_work_description = models.TextField(
        blank=True,
        verbose_name=_("Lab Work Description")
    )

    class Meta:
        verbose_name = _("Dentistry Invoice")
        verbose_name_plural = _("Dentistry Invoices")
        ordering = ['-base_invoice__date_issued']

    def __str__(self):
        return f"Dentistry Invoice for {self.dentistry_patient.patient.full_name}"

class DentistryPatientInsurance(DentistryBaseModel):
    """
    Dentistry-specific patient insurance model.
    """
    base_insurance = models.OneToOneField(
        PatientInsurance,
        on_delete=models.CASCADE,
        related_name="dentistry_insurance",
        verbose_name=_("Base Patient Insurance")
    )
    dentistry_patient = models.ForeignKey(
        DentistryPatient,
        on_delete=models.CASCADE,
        related_name="dentistry_insurances",
        verbose_name=_("Dentistry Patient")
    )
    annual_maximum = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name=_("Annual Maximum")
    )
    remaining_benefit = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name=_("Remaining Benefit")
    )
    covers_preventive = models.BooleanField(
        default=True,
        verbose_name=_("Covers Preventive")
    )
    preventive_coverage_percent = models.PositiveIntegerField(
        default=100,
        verbose_name=_("Preventive Coverage Percent")
    )
    covers_basic = models.BooleanField(
        default=True,
        verbose_name=_("Covers Basic")
    )
    basic_coverage_percent = models.PositiveIntegerField(
        default=80,
        verbose_name=_("Basic Coverage Percent")
    )
    covers_major = models.BooleanField(
        default=True,
        verbose_name=_("Covers Major")
    )
    major_coverage_percent = models.PositiveIntegerField(
        default=50,
        verbose_name=_("Major Coverage Percent")
    )
    covers_orthodontic = models.BooleanField(
        default=False,
        verbose_name=_("Covers Orthodontic")
    )
    orthodontic_coverage_percent = models.PositiveIntegerField(
        default=0,
        verbose_name=_("Orthodontic Coverage Percent")
    )
    orthodontic_lifetime_maximum = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name=_("Orthodontic Lifetime Maximum")
    )
    waiting_period_details = models.TextField(
        blank=True,
        verbose_name=_("Waiting Period Details")
    )

    class Meta:
        verbose_name = _("Dentistry Patient Insurance")
        verbose_name_plural = _("Dentistry Patient Insurances")
        ordering = ['-base_insurance__is_primary']

    def __str__(self):
        return f"Dental Insurance for {self.dentistry_patient.patient.full_name}"
