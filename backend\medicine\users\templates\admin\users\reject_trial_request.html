{% extends "admin/base_site.html" %}
{% load i18n admin_urls %}

{% block breadcrumbs %}
<div class="breadcrumbs">
<a href="{% url 'admin:index' %}">{% trans 'Home' %}</a>
&rsaquo; <a href="{% url 'admin:app_list' app_label=opts.app_label %}">{{ opts.app_config.verbose_name }}</a>
&rsaquo; <a href="{% url 'admin:users_user_changelist' %}">{{ opts.verbose_name_plural|capfirst }}</a>
&rsaquo; <a href="{% url 'admin:trial-requests' %}">{% trans 'Trial Requests' %}</a>
&rsaquo; {% trans 'Reject Trial Request' %}
</div>
{% endblock %}

{% block content %}
<div id="content-main">
  <div class="module">
    <h2>{% trans 'Reject Trial Request' %}</h2>
    
    <div class="form-row">
      <h3>User Information</h3>
      <table>
        <tr>
          <th style="text-align: left; padding-right: 20px;">Email:</th>
          <td>{{ user.email }}</td>
        </tr>
        <tr>
          <th style="text-align: left; padding-right: 20px;">Name:</th>
          <td>{{ user.first_name }} {{ user.last_name }}</td>
        </tr>
        <tr>
          <th style="text-align: left; padding-right: 20px;">User Type:</th>
          <td>{{ user.get_user_type_display }}</td>
        </tr>
        <tr>
          <th style="text-align: left; padding-right: 20px;">Requested On:</th>
          <td>{{ user.trial_request_date|date:"Y-m-d H:i" }}</td>
        </tr>
        <tr>
          <th style="text-align: left; padding-right: 20px;">Requested Duration:</th>
          <td>
            {% if user.trial_request_duration_months %}
              {{ user.trial_request_duration_months }} month{{ user.trial_request_duration_months|pluralize }}
            {% else %}
              Not specified
            {% endif %}
          </td>
        </tr>
      </table>
    </div>
    
    <form method="post">
      {% csrf_token %}
      
      <div class="form-row">
        <h3>{% trans 'Rejection Reason' %}</h3>
        <p>{% trans 'Please provide a reason for rejecting this trial request.' %}</p>
        {{ form.notes.errors }}
        {{ form.notes }}
      </div>
      
      <div class="submit-row">
        <input type="submit" value="{% trans 'Reject Trial Request' %}" class="default" style="background-color: #ba2121; border-color: #ba2121;" />
        <a href="{% url 'admin:trial-requests' %}" class="button cancel-link">{% trans "Cancel" %}</a>
      </div>
    </form>
  </div>
</div>
{% endblock %}
