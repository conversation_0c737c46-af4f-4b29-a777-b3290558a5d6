from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    SupplierViewSet, DepotViewSet, ProductCategoryViewSet,
    ProductViewSet, PurchaseRequestViewSet, PurchaseRequestItemViewSet,
    InventoryViewSet, StockMovementViewSet
)

# Create a router and register our viewsets with it
router = DefaultRouter()
router.register(r'suppliers', SupplierViewSet, basename='supplier')
router.register(r'depots', DepotViewSet, basename='depot')
router.register(r'categories', ProductCategoryViewSet, basename='productcategory')
router.register(r'products', ProductViewSet, basename='product')
router.register(r'purchase-requests', PurchaseRequestViewSet, basename='purchaserequest')
router.register(r'purchase-request-items', PurchaseRequestItemViewSet, basename='purchaserequestitem')
router.register(r'inventory', InventoryViewSet, basename='inventory')
router.register(r'stock-movements', StockMovementViewSet, basename='stockmovement')

# The API URLs are now determined automatically by the router
urlpatterns = [
    path('', include(router.urls)),
]
