from rest_framework import serializers
from django.utils import timezone
from .models import SubscriptionPackage, DoctorSubscription, Coupon, SubscriptionTransaction

class SubscriptionPackageSerializer(serializers.ModelSerializer):
    class Meta:
        model = SubscriptionPackage
        fields = ['id', 'name', 'description', 'max_assistants', 'max_users', 'max_specialties',
                 'price_monthly', 'price_yearly', 'features', 'is_active']

class DoctorSubscriptionSerializer(serializers.ModelSerializer):
    package_details = SubscriptionPackageSerializer(source='package', read_only=True)
    days_remaining = serializers.SerializerMethodField()
    is_six_month = serializers.BooleanField(write_only=True, required=False, default=False)

    class Meta:
        model = DoctorSubscription
        fields = ['id', 'doctor', 'package', 'package_details', 'status', 'billing_cycle',
                 'start_date', 'end_date', 'current_assistant_count', 'current_user_count',
                 'current_specialty_count', 'days_remaining', 'is_six_month']
        read_only_fields = ['current_assistant_count', 'current_user_count', 'current_specialty_count']

    def get_days_remaining(self, obj):
        return obj.days_remaining()

    def create(self, validated_data):
        # Calculate end date based on billing cycle
        start_date = validated_data.get('start_date') or timezone.now()
        billing_cycle = validated_data.get('billing_cycle', 'monthly')

        # Check if this is explicitly marked as a 6-month package
        is_six_month = validated_data.pop('is_six_month', False)

        # If not explicitly marked, try to detect if it's a 6-month package
        if not is_six_month:
            package = validated_data.get('package')
            if package:
                # Check if price_monthly is significantly different from price_yearly / 12
                # This indicates it's likely a 6-month package
                try:
                    monthly_price = float(package.price_monthly)
                    yearly_price = float(package.price_yearly)
                    monthly_from_yearly = yearly_price / 12

                    # If monthly price is at least 50% higher than monthly_from_yearly,
                    # it's likely a 6-month package
                    if monthly_price >= (monthly_from_yearly * 1.5):
                        is_six_month = True
                        print(f"Detected 6-month package: {package.name}")
                except (AttributeError, TypeError, ValueError) as e:
                    print(f"Error checking package prices: {e}")

        if billing_cycle == 'annual':
            end_date = start_date + timezone.timedelta(days=365)
        elif is_six_month and billing_cycle == 'monthly':
            # This is a 6-month package
            end_date = start_date + timezone.timedelta(days=180)
            print(f"Setting 6-month duration (180 days) for subscription")
        else:  # regular monthly
            end_date = start_date + timezone.timedelta(days=30)

        validated_data['end_date'] = end_date

        # Store the fact that this is a 6-month package in the subscription metadata
        if 'metadata' not in validated_data:
            validated_data['metadata'] = {}

        if is_six_month and billing_cycle == 'monthly':
            validated_data['metadata']['is_six_month'] = True
            print("Marking subscription as 6-month package in metadata")

        return super().create(validated_data)

class CouponSerializer(serializers.ModelSerializer):
    is_valid = serializers.SerializerMethodField()

    class Meta:
        model = Coupon
        fields = ['id', 'code', 'description', 'discount_type', 'discount_value',
                 'valid_from', 'valid_until', 'max_uses', 'current_uses',
                 'minimum_purchase', 'is_valid']

    def get_is_valid(self, obj):
        return obj.is_valid()

class ApplyCouponSerializer(serializers.Serializer):
    code = serializers.CharField(max_length=50)
    package_id = serializers.IntegerField(required=False)

class SubscriptionTransactionSerializer(serializers.ModelSerializer):
    class Meta:
        model = SubscriptionTransaction
        fields = ['id', 'subscription', 'amount', 'status', 'payment_method',
                 'transaction_id', 'coupon', 'discount_amount', 'created_at']
        read_only_fields = ['created_at']
