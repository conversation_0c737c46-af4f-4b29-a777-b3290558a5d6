# Subscription Management System

This module provides a comprehensive subscription management system for the medical appointment application. It allows doctors to subscribe to different packages, manage their subscriptions, and apply coupons for discounts.

## Features

- **Dynamic Package Management**: Create and manage subscription packages with different features and pricing.
- **Subscription Tracking**: Track subscription status, billing cycles, and expiration dates.
- **Coupon System**: Create and apply discount coupons with various restrictions.
- **Transaction History**: Record all subscription-related transactions.
- **API Integration**: Well-defined API endpoints for frontend integration.

## Setup

1. **Apply Migrations**:
   ```bash
   python manage.py migrate subscriptions
   ```

2. **Seed Initial Packages**:
   ```bash
   python manage.py seed_subscriptions
   ```

3. **Access Admin Interface**:
   - Go to `/admin/subscriptions/` to manage packages, subscriptions, and coupons.

## API Endpoints

### Packages

- `GET /api/subscriptions/packages/`: List all active packages
- `GET /api/subscriptions/packages/{id}/`: Get details of a specific package

### Subscriptions

- `GET /api/subscriptions/subscriptions/`: List user's subscriptions
- `GET /api/subscriptions/subscriptions/current/`: Get current active subscription
- `POST /api/subscriptions/subscriptions/`: Create a new subscription
- `POST /api/subscriptions/subscriptions/{id}/cancel/`: Cancel a subscription
- `POST /api/subscriptions/subscriptions/{id}/renew/`: Renew a subscription

### Coupons

- `POST /api/subscriptions/coupons/validate/`: Validate a coupon code

### Transactions

- `GET /api/subscriptions/transactions/`: List user's transactions

## Models

### SubscriptionPackage

Defines the available subscription packages with their features and pricing.

- `name`: Package name
- `description`: Package description
- `max_assistants`: Maximum number of assistants allowed
- `max_users`: Maximum number of users allowed
- `max_specialties`: Maximum number of specialties allowed
- `price_monthly`: Monthly price
- `price_yearly`: Yearly price
- `features`: List of features included in this package
- `is_active`: Whether this package is available for purchase

### DoctorSubscription

Tracks a doctor's subscription to a package.

- `doctor`: Reference to the doctor user
- `package`: Reference to the subscription package
- `status`: Subscription status (active, expired, cancelled, pending)
- `billing_cycle`: Billing cycle (monthly, annual)
- `start_date`: Subscription start date
- `end_date`: Subscription end date
- `current_assistant_count`: Current number of assistants used
- `current_user_count`: Current number of users used
- `current_specialty_count`: Current number of specialties used

### Coupon

Defines discount coupons that can be applied to subscriptions.

- `code`: Coupon code
- `description`: Coupon description
- `discount_type`: Type of discount (percentage, fixed)
- `discount_value`: Discount value
- `valid_from`: Validity start date
- `valid_until`: Validity end date
- `max_uses`: Maximum number of times this coupon can be used
- `current_uses`: Current number of uses
- `applicable_packages`: Packages this coupon can be applied to
- `minimum_purchase`: Minimum purchase amount required

### SubscriptionTransaction

Records all subscription-related transactions.

- `subscription`: Reference to the subscription
- `amount`: Transaction amount
- `status`: Transaction status (pending, completed, failed, refunded)
- `payment_method`: Payment method used
- `transaction_id`: External transaction ID
- `coupon`: Reference to the coupon used
- `discount_amount`: Discount amount applied
