from rest_framework import generics, permissions, status
from rest_framework.response import Response
from rest_framework.views import APIView
from users.models import PaymentConfiguration
from users.serializers.payment_serializers import (
    PaymentConfigurationSerializer,
    PaymentConfigurationPublicSerializer
)

class PaymentConfigurationListCreateView(generics.ListCreateAPIView):
    """
    View to list all payment configurations or create a new one.
    Admin only for creation.
    """
    queryset = PaymentConfiguration.objects.all().order_by('-updated_at')

    def get_serializer_class(self):
        if self.request.method == 'POST' or self.request.user.is_staff:
            return PaymentConfigurationSerializer
        return PaymentConfigurationPublicSerializer

    def get_permissions(self):
        if self.request.method == 'POST':
            return [permissions.IsAdminUser()]
        return [permissions.AllowAny()]

class PaymentConfigurationDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    View to retrieve, update or delete a payment configuration.
    Admin only for update and delete.
    """
    queryset = PaymentConfiguration.objects.all()

    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH'] or self.request.user.is_staff:
            return PaymentConfigurationSerializer
        return PaymentConfigurationPublicSerializer

    def get_permissions(self):
        if self.request.method in ['PUT', 'PATCH', 'DELETE']:
            return [permissions.IsAdminUser()]
        return [permissions.AllowAny()]

class ActivePaymentConfigurationView(APIView):
    """
    View to get the active payment configuration.
    """
    permission_classes = [permissions.AllowAny]

    def get(self, request, format=None):
        config = PaymentConfiguration.get_active()
        serializer = PaymentConfigurationPublicSerializer(config)
        return Response(serializer.data)

class SetActivePaymentConfigurationView(APIView):
    """
    View to set a payment configuration as active.
    Admin only.
    """
    permission_classes = [permissions.IsAdminUser]

    def post(self, request, pk, format=None):
        try:
            config = PaymentConfiguration.objects.get(pk=pk)
            config.is_active = True
            config.save()
            serializer = PaymentConfigurationSerializer(config)
            return Response(serializer.data)
        except PaymentConfiguration.DoesNotExist:
            return Response(
                {"detail": "Payment configuration not found."},
                status=status.HTTP_404_NOT_FOUND
            )
