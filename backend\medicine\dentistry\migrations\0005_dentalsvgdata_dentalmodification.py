# Generated by Django 4.2.7 on 2025-05-28 10:59

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('dentistry', '0004_dentalpatient_remove_estimatesession_patient_id_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='DentalSvgData',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('dental_svg_upper', models.TextField(blank=True, help_text='Données SVG complètes pour la mâchoire supérieure', verbose_name='SVG Mâchoire Supérieure')),
                ('dental_svg_lower', models.TextField(blank=True, help_text='Données SVG complètes pour la mâchoire inférieure', verbose_name='SVG Mâchoire Inférieure')),
                ('current_age_restriction', models.CharField(choices=[('none', 'Aucune restriction'), ('under_6', 'Moins de 6 ans'), ('under_7_5', 'Moins de 7 ans et demi'), ('under_12', 'Moins de 12 ans'), ('under_13_5', 'Moins de 13 ans et demi')], default='none', max_length=20, verbose_name="Restriction d'âge actuelle")),
                ('dental_view_level', models.CharField(choices=[('full', 'Vue complète'), ('partial', 'Vue partielle'), ('limited', 'Vue limitée'), ('minimal', 'Vue minimale'), ('hidden', 'Masqué')], default='full', max_length=20, verbose_name='Niveau de vue dentaire')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('last_modification_date', models.DateTimeField(blank=True, null=True, verbose_name='Dernière modification dentaire')),
                ('patient', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='dental_svg_data', to=settings.AUTH_USER_MODEL, verbose_name='Patient')),
            ],
            options={
                'verbose_name': 'Données SVG Dentaires',
                'verbose_name_plural': 'Données SVG Dentaires',
                'ordering': ['-updated_at'],
            },
        ),
        migrations.CreateModel(
            name='DentalModification',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('tooth_number', models.IntegerField(validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(32)], verbose_name='Numéro de dent')),
                ('path_id', models.CharField(help_text='Identifiant de la partie de la dent modifiée', max_length=50, verbose_name='ID du chemin SVG')),
                ('modification_type', models.CharField(choices=[('color', 'Coloration'), ('replacement', 'Remplacement'), ('addition', 'Ajout'), ('removal', 'Suppression'), ('restoration', 'Restauration')], max_length=20, verbose_name='Type de modification')),
                ('value', models.TextField(help_text='Couleur hex, SVG de remplacement, ou autre valeur', verbose_name='Valeur')),
                ('specialty', models.CharField(choices=[('therapeutic', 'Dentisterie Thérapeutique'), ('esthetic', 'Dentisterie Esthétique'), ('prosthetic', 'Prosthodontie'), ('orthodontic', 'Orthodontie'), ('surgery', 'Chirurgie')], max_length=20, verbose_name='Spécialité')),
                ('applied_by_button', models.CharField(help_text='Nom du bouton qui a appliqué cette modification', max_length=100, verbose_name='Appliqué par le bouton')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True, verbose_name='Actif')),
                ('dental_svg_data', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='modifications', to='dentistry.dentalsvgdata', verbose_name='Données SVG Dentaires')),
            ],
            options={
                'verbose_name': 'Modification Dentaire',
                'verbose_name_plural': 'Modifications Dentaires',
                'ordering': ['-created_at'],
                'unique_together': {('dental_svg_data', 'tooth_number', 'path_id', 'modification_type')},
            },
        ),
    ]
