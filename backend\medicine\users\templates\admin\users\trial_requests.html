{% extends "admin/base_site.html" %}
{% load i18n admin_urls static admin_list %}

{% block extrastyle %}
  {{ block.super }}
  <link rel="stylesheet" type="text/css" href="{% static "admin/css/changelists.css" %}">
  <style>
    .trial-request-table {
      width: 100%;
      border-collapse: collapse;
    }
    .trial-request-table th, .trial-request-table td {
      padding: 8px;
      text-align: left;
      border-bottom: 1px solid #ddd;
    }
    .trial-request-table th {
      background-color: #f2f2f2;
    }
    .trial-request-table tr:hover {
      background-color: #f5f5f5;
    }
    .action-buttons {
      white-space: nowrap;
    }
    .action-buttons a {
      margin-right: 5px;
    }
  </style>
{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
<a href="{% url 'admin:index' %}">{% trans 'Home' %}</a>
&rsaquo; <a href="{% url 'admin:app_list' app_label=opts.app_label %}">{{ opts.app_config.verbose_name }}</a>
&rsaquo; <a href="{% url 'admin:users_user_changelist' %}">{{ opts.verbose_name_plural|capfirst }}</a>
&rsaquo; {% trans 'Trial Requests' %}
</div>
{% endblock %}

{% block content %}
<div id="content-main">
  <div class="module" id="changelist">
    <div class="results">
      <h1>{% trans 'Pending Trial Requests' %}</h1>
      
      {% if pending_requests %}
        <table class="trial-request-table">
          <thead>
            <tr>
              <th>{% trans 'Email' %}</th>
              <th>{% trans 'Name' %}</th>
              <th>{% trans 'Requested On' %}</th>
              <th>{% trans 'Requested Duration' %}</th>
              <th>{% trans 'Actions' %}</th>
            </tr>
          </thead>
          <tbody>
            {% for user in pending_requests %}
              <tr>
                <td>{{ user.email }}</td>
                <td>{{ user.first_name }} {{ user.last_name }}</td>
                <td>{{ user.trial_request_date|date:"Y-m-d H:i" }}</td>
                <td>
                  {% if user.trial_request_duration_months %}
                    {{ user.trial_request_duration_months }} month{{ user.trial_request_duration_months|pluralize }}
                  {% else %}
                    Not specified
                  {% endif %}
                </td>
                <td class="action-buttons">
                  <a href="{% url 'admin:approve-trial-request' user.id %}" class="button">{% trans 'Approve' %}</a>
                  <a href="{% url 'admin:reject-trial-request' user.id %}" class="button">{% trans 'Reject' %}</a>
                  <a href="{% url 'admin:users_user_change' user.id %}" class="button">{% trans 'View Details' %}</a>
                </td>
              </tr>
            {% endfor %}
          </tbody>
        </table>
      {% else %}
        <p>{% trans 'No pending trial requests.' %}</p>
      {% endif %}
    </div>
  </div>
</div>
{% endblock %}
