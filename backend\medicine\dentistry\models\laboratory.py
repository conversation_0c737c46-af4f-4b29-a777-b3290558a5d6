"""
Laboratory models for the dentistry application.
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from dentistry.models.base import DentistryBaseModel
from dentistry.models.patient import DentistryPatient
from dentistry.models.doctor import DentistryDoctor

class DentalLaboratory(DentistryBaseModel):
    """
    Dental laboratory model.
    """
    name = models.CharField(
        max_length=200,
        verbose_name=_("Laboratory Name")
    )
    address = models.TextField(
        verbose_name=_("Address")
    )
    phone = models.CharField(
        max_length=20,
        verbose_name=_("Phone Number")
    )
    email = models.EmailField(
        verbose_name=_("Email"),
        blank=True
    )
    website = models.URLField(
        verbose_name=_("Website"),
        blank=True
    )
    contact_person = models.CharField(
        max_length=100,
        verbose_name=_("Contact Person"),
        blank=True
    )

    # Specializations
    SPECIALIZATION_CHOICES = (
        ('general', _('General')),
        ('crowns_bridges', _('Crowns & Bridges')),
        ('dentures', _('Dentures')),
        ('implants', _('Implants')),
        ('orthodontics', _('Orthodontics')),
        ('ceramics', _('Ceramics')),
    )
    specializations = models.JSONField(
        default=list,
        verbose_name=_("Specializations"),
        help_text=_("List of laboratory specializations")
    )

    # Business relationship
    is_preferred = models.BooleanField(
        default=False,
        verbose_name=_("Preferred Laboratory")
    )
    contract_start_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_("Contract Start Date")
    )
    contract_end_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_("Contract End Date")
    )
    pricing_agreement = models.TextField(
        blank=True,
        verbose_name=_("Pricing Agreement")
    )

    # Quality metrics
    average_turnaround_days = models.FloatField(
        null=True,
        blank=True,
        verbose_name=_("Average Turnaround (Days)")
    )
    quality_rating = models.FloatField(
        null=True,
        blank=True,
        verbose_name=_("Quality Rating (1-5)")
    )

    notes = models.TextField(
        blank=True,
        verbose_name=_("Notes")
    )

    class Meta:
        verbose_name = _("Dental Laboratory")
        verbose_name_plural = _("Dental Laboratories")
        ordering = ['name']

    def __str__(self):
        return self.name

class LabWorkOrder(DentistryBaseModel):
    """
    Laboratory work order model.
    """
    laboratory = models.ForeignKey(
        DentalLaboratory,
        on_delete=models.PROTECT,
        related_name="work_orders",
        verbose_name=_("Laboratory")
    )
    patient = models.ForeignKey(
        DentistryPatient,
        on_delete=models.CASCADE,
        related_name="lab_work_orders",
        verbose_name=_("Patient")
    )
    doctor = models.ForeignKey(
        DentistryDoctor,
        on_delete=models.SET_NULL,
        null=True,
        related_name="lab_work_orders",
        verbose_name=_("Doctor")
    )

    # Order details
    order_number = models.CharField(
        max_length=50,
        verbose_name=_("Order Number"),
        unique=True
    )

    WORK_TYPE_CHOICES = (
        ('crown', _('Crown')),
        ('bridge', _('Bridge')),
        ('denture', _('Denture')),
        ('partial_denture', _('Partial Denture')),
        ('implant', _('Implant')),
        ('veneer', _('Veneer')),
        ('inlay', _('Inlay')),
        ('onlay', _('Onlay')),
        ('night_guard', _('Night Guard')),
        ('retainer', _('Retainer')),
        ('other', _('Other')),
    )
    work_type = models.CharField(
        max_length=20,
        choices=WORK_TYPE_CHOICES,
        verbose_name=_("Work Type")
    )

    description = models.TextField(
        verbose_name=_("Description")
    )
    tooth_numbers = models.CharField(
        max_length=100,
        blank=True,
        verbose_name=_("Tooth Numbers"),
        help_text=_("Comma-separated list of tooth numbers")
    )

    # Materials
    MATERIAL_CHOICES = (
        ('porcelain', _('Porcelain')),
        ('ceramic', _('Ceramic')),
        ('zirconia', _('Zirconia')),
        ('metal', _('Metal')),
        ('acrylic', _('Acrylic')),
        ('composite', _('Composite')),
        ('other', _('Other')),
    )
    material = models.CharField(
        max_length=20,
        choices=MATERIAL_CHOICES,
        verbose_name=_("Material")
    )

    shade = models.CharField(
        max_length=50,
        blank=True,
        verbose_name=_("Shade")
    )

    # Dates and status
    date_sent = models.DateField(
        verbose_name=_("Date Sent")
    )
    requested_return_date = models.DateField(
        verbose_name=_("Requested Return Date")
    )
    actual_return_date = models.DateField(
        null=True,
        blank=True,
        verbose_name=_("Actual Return Date")
    )

    STATUS_CHOICES = (
        ('draft', _('Draft')),
        ('sent', _('Sent to Lab')),
        ('received_by_lab', _('Received by Lab')),
        ('in_progress', _('In Progress')),
        ('completed_by_lab', _('Completed by Lab')),
        ('returned', _('Returned to Office')),
        ('fitted', _('Fitted to Patient')),
        ('rejected', _('Rejected')),
        ('remake', _('Remake Required')),
    )
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='draft',
        verbose_name=_("Status")
    )

    # Financial information
    cost = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name=_("Cost")
    )

    invoice_number = models.CharField(
        max_length=50,
        blank=True,
        verbose_name=_("Invoice Number")
    )

    is_invoiced = models.BooleanField(
        default=False,
        verbose_name=_("Is Invoiced")
    )

    is_paid = models.BooleanField(
        default=False,
        verbose_name=_("Is Paid")
    )

    # Additional information
    attachments = models.JSONField(
        default=list,
        blank=True,
        verbose_name=_("Attachments"),
        help_text=_("List of file paths or URLs")
    )

    special_instructions = models.TextField(
        blank=True,
        verbose_name=_("Special Instructions")
    )

    lab_notes = models.TextField(
        blank=True,
        verbose_name=_("Laboratory Notes")
    )

    internal_notes = models.TextField(
        blank=True,
        verbose_name=_("Internal Notes")
    )

    class Meta:
        verbose_name = _("Lab Work Order")
        verbose_name_plural = _("Lab Work Orders")
        ordering = ['-date_sent']

    def __str__(self):
        return f"{self.order_number} - {self.get_work_type_display()} for {self.patient.full_name}"
