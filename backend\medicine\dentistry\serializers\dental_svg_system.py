"""
Serializers pour le système SVG dentaire dynamique
"""
from rest_framework import serializers
from ..models.dental_svg_system import (
    DentalSvgConfiguration, DentalSvgPath, DentalTreatmentTemplate
)


class DentalSvgPathSerializer(serializers.ModelSerializer):
    """
    Serializer pour les paths SVG individuels
    """
    class Meta:
        model = DentalSvgPath
        fields = [
            'id', 'path_id', 'code', 'path', 'style', 'transform',
            'treatment_type', 'display_order', 'is_active',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def validate_style(self, value):
        """
        Valide que le style est un dictionnaire valide
        """
        if not isinstance(value, dict):
            raise serializers.ValidationError("Style must be a valid JSON object")
        return value


class DentalSvgConfigurationSerializer(serializers.ModelSerializer):
    """
    Serializer pour la configuration SVG des dents
    """
    svg_paths = DentalSvgPathSerializer(many=True, read_only=True)
    is_upper_jaw = serializers.ReadOnlyField()
    is_lower_jaw = serializers.ReadOnlyField()
    display_tooth_number = serializers.ReadOnlyField()
    patient_name = serializers.CharField(source='patient.patient.get_full_name', read_only=True)

    class Meta:
        model = DentalSvgConfiguration
        fields = [
            'id', 'patient', 'patient_name', 'tooth_id', 'width', 'position',
            'age_restriction', 'is_upper_jaw', 'is_lower_jaw', 'display_tooth_number',
            
            # États des traitements thérapeutiques
            'is_cleaning_active', 'is_fluoride_active', 'is_sealant_active',
            'is_whitening_active', 'is_restoration_amalgam_active',
            'is_restoration_glass_ionomer_active', 'is_restoration_temporary_active',
            
            # États des traitements prosthodontiques
            'is_crown_zirconia_active', 'is_crown_gold_active', 'is_veneer_active',
            'is_onlay_active', 'is_bridge_active',
            
            # États des traitements chirurgicaux
            'is_implant_active', 'is_extraction_active', 'is_bone_graft_active',
            
            'svg_paths', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'patient_name', 'is_upper_jaw', 'is_lower_jaw',
            'display_tooth_number', 'created_at', 'updated_at'
        ]

    def validate_tooth_id(self, value):
        """
        Valide que l'ID de la dent est dans la plage valide (1-32)
        """
        if not 1 <= value <= 32:
            raise serializers.ValidationError("Tooth ID must be between 1 and 32")
        return value

    def validate_age_restriction(self, value):
        """
        Valide la restriction d'âge
        """
        if value is not None and (value < 0 or value > 120):
            raise serializers.ValidationError("Age restriction must be between 0 and 120")
        return value


class DentalSvgConfigurationCreateSerializer(serializers.ModelSerializer):
    """
    Serializer pour la création de configuration SVG avec paths de base
    """
    base_paths = serializers.ListField(
        child=serializers.DictField(),
        write_only=True,
        required=False,
        help_text="List of base paths to create with the configuration"
    )

    class Meta:
        model = DentalSvgConfiguration
        fields = [
            'patient', 'tooth_id', 'width', 'position', 'age_restriction',
            'base_paths'
        ]

    def create(self, validated_data):
        """
        Crée une configuration avec les paths de base
        """
        base_paths_data = validated_data.pop('base_paths', [])
        configuration = DentalSvgConfiguration.objects.create(**validated_data)
        
        # Créer les paths de base si fournis
        for path_data in base_paths_data:
            DentalSvgPath.objects.create(
                dental_svg_config=configuration,
                **path_data
            )
        
        return configuration


class DentalTreatmentTemplateSerializer(serializers.ModelSerializer):
    """
    Serializer pour les templates de traitement
    """
    class Meta:
        model = DentalTreatmentTemplate
        fields = [
            'id', 'name', 'description', 'template_paths', 'paths_to_remove',
            'min_age', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def validate_template_paths(self, value):
        """
        Valide la structure des paths du template
        """
        if not isinstance(value, list):
            raise serializers.ValidationError("Template paths must be a list")
        
        for path_data in value:
            if not isinstance(path_data, dict):
                raise serializers.ValidationError("Each path must be a dictionary")
            
            required_fields = ['path_id', 'code', 'path']
            for field in required_fields:
                if field not in path_data:
                    raise serializers.ValidationError(f"Path must contain '{field}' field")
        
        return value

    def validate_paths_to_remove(self, value):
        """
        Valide la liste des paths à supprimer
        """
        if not isinstance(value, list):
            raise serializers.ValidationError("Paths to remove must be a list")
        return value


class DentalTreatmentApplicationSerializer(serializers.Serializer):
    """
    Serializer pour l'application d'un traitement
    """
    treatment_name = serializers.CharField(
        max_length=100,
        help_text="Name of the treatment to apply"
    )
    
    tooth_ids = serializers.ListField(
        child=serializers.IntegerField(min_value=1, max_value=32),
        required=False,
        help_text="List of tooth IDs to apply the treatment to (optional)"
    )
    
    custom_paths = serializers.ListField(
        child=serializers.DictField(),
        required=False,
        help_text="Custom paths to add (optional)"
    )

    def validate_treatment_name(self, value):
        """
        Valide que le template de traitement existe
        """
        if not DentalTreatmentTemplate.objects.filter(name=value).exists():
            raise serializers.ValidationError(f"Treatment template '{value}' does not exist")
        return value


class PatientTeethSummarySerializer(serializers.Serializer):
    """
    Serializer pour le résumé des dents d'un patient
    """
    patient_id = serializers.IntegerField(read_only=True)
    patient_name = serializers.CharField(read_only=True)
    total_teeth = serializers.IntegerField(read_only=True)
    upper_jaw_teeth = serializers.IntegerField(read_only=True)
    lower_jaw_teeth = serializers.IntegerField(read_only=True)
    
    # Compteurs de traitements
    cleaning_count = serializers.IntegerField(read_only=True)
    fluoride_count = serializers.IntegerField(read_only=True)
    sealant_count = serializers.IntegerField(read_only=True)
    whitening_count = serializers.IntegerField(read_only=True)
    restoration_count = serializers.IntegerField(read_only=True)
    crown_count = serializers.IntegerField(read_only=True)
    implant_count = serializers.IntegerField(read_only=True)
    
    # Restrictions d'âge
    age_restricted_teeth = serializers.ListField(
        child=serializers.IntegerField(),
        read_only=True
    )


class BulkTreatmentApplicationSerializer(serializers.Serializer):
    """
    Serializer pour l'application en masse de traitements
    """
    treatment_name = serializers.CharField(max_length=100)
    tooth_ids = serializers.ListField(
        child=serializers.IntegerField(min_value=1, max_value=32)
    )
    
    def validate_treatment_name(self, value):
        """
        Valide que le template de traitement existe
        """
        if not DentalTreatmentTemplate.objects.filter(name=value).exists():
            raise serializers.ValidationError(f"Treatment template '{value}' does not exist")
        return value

    def validate_tooth_ids(self, value):
        """
        Valide que les IDs de dents sont uniques
        """
        if len(value) != len(set(value)):
            raise serializers.ValidationError("Tooth IDs must be unique")
        return value
