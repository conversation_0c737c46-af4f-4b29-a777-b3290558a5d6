from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0003_specialty_user_specialties'),
    ]

    operations = [
        migrations.CreateModel(
            name='Country',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('code', models.CharField(help_text='ISO 3166-1 alpha-2 country code', max_length=2, unique=True)),
                ('phone_code', models.CharField(blank=True, help_text='International dialing code', max_length=10, null=True)),
                ('flag_image', models.ImageField(blank=True, help_text='Country flag image', null=True, upload_to='flags/')),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'verbose_name': 'Country',
                'verbose_name_plural': 'Countries',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Region',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('code', models.CharField(blank=True, help_text='Region code', max_length=10, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('country', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='regions', to='users.country')),
            ],
            options={
                'verbose_name': 'Region',
                'verbose_name_plural': 'Regions',
                'ordering': ['country', 'name'],
                'unique_together': {('name', 'country')},
            },
        ),
        migrations.CreateModel(
            name='City',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('is_active', models.BooleanField(default=True)),
                ('latitude', models.DecimalField(blank=True, decimal_places=6, max_digits=9, null=True)),
                ('longitude', models.DecimalField(blank=True, decimal_places=6, max_digits=9, null=True)),
                ('country', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='cities', to='users.country')),
                ('region', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='cities', to='users.region')),
            ],
            options={
                'verbose_name': 'City',
                'verbose_name_plural': 'Cities',
                'ordering': ['region', 'name'],
                'unique_together': {('name', 'region')},
            },
        ),
        migrations.RenameField(
            model_name='user',
            old_name='country',
            new_name='country_name',
        ),
        migrations.RenameField(
            model_name='user',
            old_name='region',
            new_name='region_name',
        ),
        migrations.RenameField(
            model_name='user',
            old_name='city',
            new_name='city_name',
        ),
        migrations.AddField(
            model_name='user',
            name='landline_number',
            field=models.CharField(blank=True, help_text='Landline phone number (optional)', max_length=15, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='profile_image',
            field=models.ImageField(blank=True, help_text='Profile image (thumbnail)', null=True, upload_to='profile_images/'),
        ),
        migrations.AddField(
            model_name='user',
            name='profile_image_large',
            field=models.ImageField(blank=True, help_text='Large profile image for doctor detail page', null=True, upload_to='profile_images/large/'),
        ),
        migrations.AddField(
            model_name='user',
            name='profile_image_medium',
            field=models.ImageField(blank=True, help_text='Medium-sized profile image for home page', null=True, upload_to='profile_images/medium/'),
        ),
        migrations.AlterField(
            model_name='user',
            name='phone_number',
            field=models.CharField(blank=True, help_text='Mobile phone number', max_length=15, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='city',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='users', to='users.city'),
        ),
        migrations.AddField(
            model_name='user',
            name='country',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='users', to='users.country'),
        ),
        migrations.AddField(
            model_name='user',
            name='region',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='users', to='users.region'),
        ),
    ]
