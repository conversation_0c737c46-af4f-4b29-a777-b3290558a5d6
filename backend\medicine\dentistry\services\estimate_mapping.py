# backend/dental_medicine/dentistry/services/estimate_mapping.py

"""
Mapping des path IDs vers les types de modifications dentaires
"""

# Mapping des path IDs vers les types de modifications
PATH_MODIFICATION_MAPPING = {
    # Préventif
    '17': 'cleaning',
    '18': 'fluoride',
    '19': 'sealant',
    
    # Esthétique
    '20': 'whitening_1',
    '21': 'whitening_2', 
    '22': 'whitening_3',
    '23': 'whitening_4',
    '37': 'veneer',
    
    # Restaurations
    '24': 'restoration_temporary_1',
    '25': 'restoration_temporary_2',
    '26': 'restoration_amalgam',
    '27': 'restoration_glass_ionomer',
    
    # Endodontie
    '28': 'root_temporary_1',
    '29': 'root_temporary_2',
    '30': 'root_calcium_1',
    '31': 'root_calcium_2',
    '32': 'root_calcium_3',
    '33': 'root_calcium_4',
    '34': 'root_gutta_percha_1',
    '35': 'root_gutta_percha_2',
    '36': 'post_care',
    
    # Prothèses
    '38': 'onlay_1',
    '39': 'onlay_2',
    '40': 'crown_permanent_1',
    '41': 'crown_permanent_2',
    '42': 'crown_temporary',
    '43': 'crown_gold_1',
    '44': 'crown_gold_2',
    '45': 'crown_zirconia_1',
    '46': 'crown_zirconia_2',
    '47': 'crown_zirconia_3',
    '48': 'denture_1',
    '49': 'denture_2',
    '50': 'denture_3',
    '51': 'bridge_1',
    '52': 'bridge_2',
    
    # Chirurgie et Implants
    '53': 'extraction',
    '54': 'implant_1',
    '55': 'implant_2',
    '56': 'implant_3',
    '57': 'implant_4',
    '58': 'implant_5',
    '59': 'implant_6',
    '60': 'implant_7',
    '61': 'bone_1',
    '62': 'bone_2',
    '63': 'bone_3',
    '64': 'bone_4',
    '65': 'bone_5',
    '66': 'bone_6',
    '67': 'bone_7',
    '68': 'resection',
    '69': 'teeth_crown',
}

# Mapping inverse : type de modification vers path ID
MODIFICATION_PATH_MAPPING = {v: k for k, v in PATH_MODIFICATION_MAPPING.items()}

# Groupes de modifications par spécialité
SPECIALTY_MODIFICATIONS = {
    'esthetic': [
        'cleaning', 'fluoride', 'sealant', 
        'whitening_1', 'whitening_2', 'whitening_3', 'whitening_4',
        'veneer'
    ],
    'prosthetic': [
        'restoration_temporary_1', 'restoration_temporary_2',
        'restoration_amalgam', 'restoration_glass_ionomer',
        'root_temporary_1', 'root_temporary_2',
        'root_calcium_1', 'root_calcium_2', 'root_calcium_3', 'root_calcium_4',
        'root_gutta_percha_1', 'root_gutta_percha_2',
        'post_care', 'onlay_1', 'onlay_2',
        'crown_permanent_1', 'crown_permanent_2', 'crown_temporary',
        'crown_gold_1', 'crown_gold_2',
        'crown_zirconia_1', 'crown_zirconia_2', 'crown_zirconia_3',
        'denture_1', 'denture_2', 'denture_3',
        'bridge_1', 'bridge_2'
    ],
    'surgery': [
        'extraction', 'resection',
        'bone_1', 'bone_2', 'bone_3', 'bone_4', 'bone_5', 'bone_6', 'bone_7'
    ],
    'orthodontics': [
        'teeth_crown',
        'implant_1', 'implant_2', 'implant_3', 'implant_4', 
        'implant_5', 'implant_6', 'implant_7'
    ]
}

def get_modification_type(path_id: str) -> str:
    """
    Obtenir le type de modification à partir du path ID
    
    Args:
        path_id: ID du path SVG (ex: '17', '18', etc.)
        
    Returns:
        Type de modification (ex: 'cleaning', 'whitening_1', etc.)
    """
    return PATH_MODIFICATION_MAPPING.get(path_id, f'path_{path_id}')

def get_path_id(modification_type: str) -> str:
    """
    Obtenir le path ID à partir du type de modification
    
    Args:
        modification_type: Type de modification (ex: 'cleaning', 'whitening_1', etc.)
        
    Returns:
        ID du path SVG (ex: '17', '18', etc.)
    """
    return MODIFICATION_PATH_MAPPING.get(modification_type, '')

def get_specialty_for_modification(modification_type: str) -> str:
    """
    Obtenir la spécialité pour un type de modification
    
    Args:
        modification_type: Type de modification
        
    Returns:
        Spécialité ('esthetic', 'prosthetic', 'surgery', 'orthodontics')
    """
    for specialty, modifications in SPECIALTY_MODIFICATIONS.items():
        if modification_type in modifications:
            return specialty
    return 'general'

def get_modifications_for_specialty(specialty: str) -> list:
    """
    Obtenir toutes les modifications pour une spécialité
    
    Args:
        specialty: Nom de la spécialité
        
    Returns:
        Liste des types de modifications
    """
    return SPECIALTY_MODIFICATIONS.get(specialty, [])

def validate_path_id(path_id: str) -> bool:
    """
    Valider qu'un path ID est supporté
    
    Args:
        path_id: ID du path à valider
        
    Returns:
        True si le path ID est valide
    """
    return path_id in PATH_MODIFICATION_MAPPING

def get_all_path_ids() -> list:
    """
    Obtenir tous les path IDs supportés
    
    Returns:
        Liste de tous les path IDs
    """
    return list(PATH_MODIFICATION_MAPPING.keys())

def get_all_modification_types() -> list:
    """
    Obtenir tous les types de modifications supportés
    
    Returns:
        Liste de tous les types de modifications
    """
    return list(PATH_MODIFICATION_MAPPING.values())

def get_mapping_stats() -> dict:
    """
    Obtenir les statistiques du mapping
    
    Returns:
        Dictionnaire avec les statistiques
    """
    return {
        'total_paths': len(PATH_MODIFICATION_MAPPING),
        'total_modifications': len(set(PATH_MODIFICATION_MAPPING.values())),
        'specialties': len(SPECIALTY_MODIFICATIONS),
        'modifications_by_specialty': {
            specialty: len(modifications) 
            for specialty, modifications in SPECIALTY_MODIFICATIONS.items()
        }
    }
