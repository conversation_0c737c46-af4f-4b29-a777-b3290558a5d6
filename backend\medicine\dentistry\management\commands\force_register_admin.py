"""
Commande pour forcer l'enregistrement de tous les modèles dans l'administration
"""
from django.core.management.base import BaseCommand
from django.contrib import admin
from django.apps import apps


class Command(BaseCommand):
    help = 'Force register all models in Django admin'

    def handle(self, *args, **options):
        """
        Force l'enregistrement de tous les modèles dans l'administration
        """
        self.stdout.write(self.style.SUCCESS('Force registering all models in Django admin...'))
        
        # Obtenir tous les modèles de l'application dentistry
        dentistry_models = list(apps.get_app_config('dentistry').get_models())
        
        registered_count = 0
        already_registered_count = 0
        error_count = 0
        
        for model in dentistry_models:
            try:
                if not admin.site.is_registered(model):
                    admin.site.register(model)
                    registered_count += 1
                    self.stdout.write(
                        self.style.SUCCESS(f'✅ Registered: {model.__name__}')
                    )
                else:
                    already_registered_count += 1
                    self.stdout.write(
                        self.style.WARNING(f'⚠️  Already registered: {model.__name__}')
                    )
            except Exception as e:
                error_count += 1
                self.stdout.write(
                    self.style.ERROR(f'❌ Error registering {model.__name__}: {str(e)}')
                )
        
        self.stdout.write(f'\n📊 Summary:')
        self.stdout.write(f'✅ Newly registered: {registered_count}')
        self.stdout.write(f'⚠️  Already registered: {already_registered_count}')
        self.stdout.write(f'❌ Errors: {error_count}')
        self.stdout.write(f'📋 Total models: {len(dentistry_models)}')
        
        # Vérifier l'état final
        final_registered = sum(1 for model in dentistry_models if admin.site.is_registered(model))
        self.stdout.write(f'🎯 Final registered count: {final_registered}/{len(dentistry_models)}')
        
        if final_registered == len(dentistry_models):
            self.stdout.write(
                self.style.SUCCESS('🎉 All models are now registered in Django admin!')
            )
        else:
            missing = len(dentistry_models) - final_registered
            self.stdout.write(
                self.style.WARNING(f'⚠️  {missing} models are still not registered')
            )
        
        self.stdout.write(self.style.SUCCESS('\n✨ Force registration completed!'))
