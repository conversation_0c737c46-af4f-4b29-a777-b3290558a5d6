# Generated by Django 4.2.7 on 2025-05-29 17:55

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('dentistry', '0009_tooth_bone_applied_tooth_bridge_applied_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='DentalSet',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_private', models.BooleanField(default=False, verbose_name='Is private')),
                ('privatized_by', models.UUIDField(blank=True, null=True, verbose_name='Privatized by')),
                ('owner', models.UUIDField(blank=True, null=True, verbose_name='Owner')),
                ('name', models.CharField(max_length=100, verbose_name="Nom de l'ensemble")),
                ('set_type', models.CharField(choices=[('general', 'Ensemble Général'), ('upper', 'Ensemble Supérieur'), ('lower', 'Ensemble Inférieur')], max_length=20, verbose_name="Type d'ensemble")),
                ('age_restriction', models.FloatField(choices=[(13.5, '13.5 ans et plus'), (12.0, '12 ans à 13.5 ans'), (7.5, '7.5 ans à 12 ans'), (6.0, '6 ans à 7.5 ans'), (0.0, 'Moins de 6 ans')], default=0.0, help_text="Tranche d'âge pour cet ensemble", verbose_name="Restriction d'âge")),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('is_active', models.BooleanField(default=True, verbose_name='Actif')),
            ],
            options={
                'verbose_name': 'Ensemble Dentaire',
                'verbose_name_plural': 'Ensembles Dentaires',
                'ordering': ['set_type', '-age_restriction'],
                'unique_together': {('set_type', 'age_restriction')},
            },
        ),
        migrations.CreateModel(
            name='ToothButton',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_private', models.BooleanField(default=False, verbose_name='Is private')),
                ('privatized_by', models.UUIDField(blank=True, null=True, verbose_name='Privatized by')),
                ('owner', models.UUIDField(blank=True, null=True, verbose_name='Owner')),
                ('tooth_number', models.PositiveSmallIntegerField(help_text='Numéro FDI: 11-18, 21-28 (supérieur), 31-38, 41-48 (inférieur)', validators=[django.core.validators.MinValueValidator(11), django.core.validators.MaxValueValidator(48)], verbose_name='Numéro de la dent')),
                ('tooth_name', models.CharField(help_text='Ex: Incisive centrale supérieure droite', max_length=200, verbose_name='Nom de la dent')),
                ('quadrant_position', models.PositiveSmallIntegerField(validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(8)], verbose_name='Position dans le quadrant')),
                ('quadrant', models.CharField(choices=[('upper_right', 'Supérieur droit'), ('upper_left', 'Supérieur gauche'), ('lower_left', 'Inférieur gauche'), ('lower_right', 'Inférieur droit')], max_length=20, verbose_name='Quadrant')),
                ('is_expanded', models.BooleanField(default=False, help_text='True si les options sont affichées', verbose_name='Bouton développé')),
                ('is_visible', models.BooleanField(default=True, verbose_name='Visible')),
                ('dental_set', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tooth_buttons', to='dentistry.dentalset', verbose_name='Ensemble dentaire')),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tooth_buttons', to='dentistry.dentistrypatient', verbose_name='Patient')),
            ],
            options={
                'verbose_name': 'Bouton de Dent',
                'verbose_name_plural': 'Boutons de Dents',
                'ordering': ['tooth_number'],
                'unique_together': {('dental_set', 'patient', 'tooth_number')},
            },
        ),
        migrations.CreateModel(
            name='SpecialtyField',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_private', models.BooleanField(default=False, verbose_name='Is private')),
                ('privatized_by', models.UUIDField(blank=True, null=True, verbose_name='Privatized by')),
                ('owner', models.UUIDField(blank=True, null=True, verbose_name='Owner')),
                ('specialty_type', models.CharField(choices=[('esthetic', 'Dentisterie Esthétique'), ('therapeutic', 'Thérapeutique'), ('prosthodontics', 'Prothèses'), ('surgery', 'Chirurgie'), ('orthodontics', 'Orthodontie')], max_length=20, verbose_name='Type de spécialisation')),
                ('min_age_required', models.FloatField(default=6.0, help_text='Âge minimum pour cette spécialisation', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)], verbose_name='Âge minimum requis')),
                ('color_value', models.CharField(default='#FFFFFF', help_text='Couleur hexadécimale (ex: #FFFFFF)', max_length=7, verbose_name='Couleur')),
                ('is_hidden', models.BooleanField(default=False, verbose_name='Masqué')),
                ('orthodontic_complete', models.BooleanField(default=False, help_text='Disponible à partir de 13.5 ans', verbose_name='Traitement orthodontique complet')),
                ('maxillofacial_surgery', models.BooleanField(default=False, help_text='Disponible à partir de 13.5 ans', verbose_name='Chirurgie maxillo-faciale')),
                ('dental_implants', models.BooleanField(default=False, help_text='Disponible à partir de 13.5 ans', verbose_name='Implants dentaires')),
                ('preventive_orthodontics', models.BooleanField(default=False, help_text='Disponible de 12 à 13.5 ans', verbose_name='Orthodontie préventive')),
                ('groove_sealants', models.BooleanField(default=False, help_text='Disponible de 12 à 13.5 ans', verbose_name='Scellants de sillons')),
                ('scheduled_extractions', models.BooleanField(default=False, help_text='Disponible de 12 à 13.5 ans', verbose_name='Extractions programmées')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('priority', models.PositiveSmallIntegerField(default=1, help_text='Priorité de traitement (1-5)', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)], verbose_name='Priorité')),
                ('tooth_button', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='specialty_fields', to='dentistry.toothbutton', verbose_name='Bouton de dent')),
            ],
            options={
                'verbose_name': 'Champ de Spécialisation',
                'verbose_name_plural': 'Champs de Spécialisations',
                'ordering': ['specialty_type'],
                'unique_together': {('tooth_button', 'specialty_type')},
            },
        ),
        migrations.CreateModel(
            name='ReplacementButton',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_private', models.BooleanField(default=False, verbose_name='Is private')),
                ('privatized_by', models.UUIDField(blank=True, null=True, verbose_name='Privatized by')),
                ('owner', models.UUIDField(blank=True, null=True, verbose_name='Owner')),
                ('button_name', models.CharField(max_length=100, verbose_name='Nom du bouton')),
                ('button_action', models.CharField(help_text='Action à effectuer lors du clic', max_length=50, verbose_name='Action du bouton')),
                ('button_color', models.CharField(default='#007BFF', max_length=7, verbose_name='Couleur du bouton')),
                ('display_order', models.PositiveSmallIntegerField(default=0, verbose_name="Ordre d'affichage")),
                ('is_active', models.BooleanField(default=True, verbose_name='Actif')),
                ('specialty_field', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='replacement_buttons', to='dentistry.specialtyfield', verbose_name='Champ de spécialisation')),
            ],
            options={
                'verbose_name': 'Bouton de Remplacement',
                'verbose_name_plural': 'Boutons de Remplacement',
                'ordering': ['display_order', 'button_name'],
            },
        ),
        migrations.CreateModel(
            name='DentalSetConfiguration',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_private', models.BooleanField(default=False, verbose_name='Is private')),
                ('privatized_by', models.UUIDField(blank=True, null=True, verbose_name='Privatized by')),
                ('owner', models.UUIDField(blank=True, null=True, verbose_name='Owner')),
                ('active_set_type', models.CharField(choices=[('general', 'Ensemble Général'), ('upper', 'Ensemble Supérieur'), ('lower', 'Ensemble Inférieur')], default='general', max_length=20, verbose_name='Ensemble actif')),
                ('patient_age', models.FloatField(help_text='Âge en années (ex: 12.5)', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)], verbose_name='Âge du patient')),
                ('last_modified', models.DateTimeField(auto_now=True, verbose_name='Dernière modification')),
                ('patient', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='dental_set_config', to='dentistry.dentistrypatient', verbose_name='Patient')),
            ],
            options={
                'verbose_name': "Configuration d'Ensemble Dentaire",
                'verbose_name_plural': "Configurations d'Ensembles Dentaires",
            },
        ),
    ]
