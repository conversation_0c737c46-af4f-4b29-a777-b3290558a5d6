"""
Notification models for the dentistry application.
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from dentistry.models.base import DentistryBaseModel
from dentistry.models.patient import DentistryPatient
from dentistry.models.doctor import DentistryDoctor

class DentistryNotification(DentistryBaseModel):
    """
    Dentistry-specific notification model.
    """
    patient = models.ForeignKey(
        DentistryPatient,
        on_delete=models.CASCADE,
        related_name="dentistry_notifications",
        verbose_name=_("Patient"),
        null=True,
        blank=True
    )
    doctor = models.ForeignKey(
        DentistryDoctor,
        on_delete=models.CASCADE,
        related_name="dentistry_notifications",
        verbose_name=_("Doctor"),
        null=True,
        blank=True
    )
    title = models.CharField(
        max_length=200,
        verbose_name=_("Title")
    )
    message = models.TextField(
        verbose_name=_("Message")
    )
    TYPE_CHOICES = (
        ('appointment', _('Appointment')),
        ('treatment', _('Treatment')),
        ('procedure', _('Procedure')),
        ('lab_work', _('Lab Work')),
        ('cleaning_reminder', _('Cleaning Reminder')),
        ('follow_up', _('Follow-up Reminder')),
        ('payment', _('Payment')),
        ('system', _('System Notification')),
    )
    notification_type = models.CharField(
        max_length=20,
        choices=TYPE_CHOICES,
        default='system',
        verbose_name=_("Notification Type")
    )
    PRIORITY_CHOICES = (
        ('low', _('Low')),
        ('medium', _('Medium')),
        ('high', _('High')),
        ('urgent', _('Urgent')),
    )
    priority = models.CharField(
        max_length=10,
        choices=PRIORITY_CHOICES,
        default='medium',
        verbose_name=_("Priority")
    )
    is_read = models.BooleanField(
        default=False,
        verbose_name=_("Is Read")
    )
    read_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_("Read At")
    )
    is_sent = models.BooleanField(
        default=False,
        verbose_name=_("Is Sent")
    )
    sent_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_("Sent At")
    )
    DELIVERY_METHOD_CHOICES = (
        ('in_app', _('In-App')),
        ('email', _('Email')),
        ('sms', _('SMS')),
        ('push', _('Push Notification')),
    )
    delivery_method = models.CharField(
        max_length=10,
        choices=DELIVERY_METHOD_CHOICES,
        default='in_app',
        verbose_name=_("Delivery Method")
    )
    related_object_type = models.CharField(
        max_length=100,
        blank=True,
        verbose_name=_("Related Object Type")
    )
    related_object_id = models.PositiveIntegerField(
        null=True,
        blank=True,
        verbose_name=_("Related Object ID")
    )
    action_url = models.CharField(
        max_length=255,
        blank=True,
        verbose_name=_("Action URL")
    )

    class Meta:
        verbose_name = _("Dentistry Notification")
        verbose_name_plural = _("Dentistry Notifications")
        ordering = ['-created_at']

    def __str__(self):
        return self.title

    def mark_as_read(self):
        """
        Mark the notification as read.
        """
        from django.utils import timezone
        self.is_read = True
        self.read_at = timezone.now()
        self.save()

    def mark_as_sent(self):
        """
        Mark the notification as sent.
        """
        from django.utils import timezone
        self.is_sent = True
        self.sent_at = timezone.now()
        self.save()

class DentalReminderSetting(DentistryBaseModel):
    """
    Settings for dental reminders.
    """
    patient = models.OneToOneField(
        DentistryPatient,
        on_delete=models.CASCADE,
        related_name="dental_reminder_settings",
        verbose_name=_("Patient")
    )
    # Cleaning reminder settings
    cleaning_reminder_enabled = models.BooleanField(
        default=True,
        verbose_name=_("Cleaning Reminder Enabled")
    )
    cleaning_interval_months = models.PositiveIntegerField(
        default=6,
        verbose_name=_("Cleaning Interval (months)")
    )
    days_before_cleaning_reminder = models.PositiveIntegerField(
        default=14,
        verbose_name=_("Days Before Cleaning Reminder")
    )

    # Checkup reminder settings
    checkup_reminder_enabled = models.BooleanField(
        default=True,
        verbose_name=_("Checkup Reminder Enabled")
    )
    checkup_interval_months = models.PositiveIntegerField(
        default=12,
        verbose_name=_("Checkup Interval (months)")
    )
    days_before_checkup_reminder = models.PositiveIntegerField(
        default=21,
        verbose_name=_("Days Before Checkup Reminder")
    )

    # Treatment follow-up settings
    treatment_followup_enabled = models.BooleanField(
        default=True,
        verbose_name=_("Treatment Follow-up Enabled")
    )
    days_after_treatment_followup = models.PositiveIntegerField(
        default=7,
        verbose_name=_("Days After Treatment Follow-up")
    )

    # Notification settings
    NOTIFICATION_METHOD_CHOICES = (
        ('in_app', _('In-App')),
        ('email', _('Email')),
        ('sms', _('SMS')),
        ('push', _('Push Notification')),
        ('all', _('All Methods')),
    )
    notification_method = models.CharField(
        max_length=10,
        choices=NOTIFICATION_METHOD_CHOICES,
        default='all',
        verbose_name=_("Notification Method")
    )

    class Meta:
        verbose_name = _("Dental Reminder Setting")
        verbose_name_plural = _("Dental Reminder Settings")

    def __str__(self):
        return f"Reminder Settings for {self.patient.patient.full_name}"
