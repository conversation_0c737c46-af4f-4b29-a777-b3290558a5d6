from rest_framework import serializers
from ..models import Country, Region, City

class CountrySerializer(serializers.ModelSerializer):
    """
    Serializer for the Country model.
    """
    class Meta:
        model = Country
        fields = [
            'id', 'name', 'code', 'phone_code', 'flag_image', 'is_active'
        ]
        read_only_fields = ['id']

class RegionSerializer(serializers.ModelSerializer):
    """
    Serializer for the Region model.
    """
    country_name = serializers.CharField(source='country.name', read_only=True)
    
    class Meta:
        model = Region
        fields = [
            'id', 'name', 'code', 'country', 'country_name', 'is_active'
        ]
        read_only_fields = ['id', 'country_name']

class CitySerializer(serializers.ModelSerializer):
    """
    Serializer for the City model.
    """
    country_name = serializers.CharField(source='country.name', read_only=True)
    region_name = serializers.CharField(source='region.name', read_only=True)
    
    class Meta:
        model = City
        fields = [
            'id', 'name', 'region', 'region_name', 'country', 'country_name', 
            'latitude', 'longitude', 'is_active'
        ]
        read_only_fields = ['id', 'country_name', 'region_name']

class CountryWithRegionsSerializer(serializers.ModelSerializer):
    """
    Serializer for the Country model with its regions.
    """
    regions = RegionSerializer(many=True, read_only=True)
    
    class Meta:
        model = Country
        fields = [
            'id', 'name', 'code', 'phone_code', 'flag_image', 'is_active', 'regions'
        ]
        read_only_fields = ['id', 'regions']

class RegionWithCitiesSerializer(serializers.ModelSerializer):
    """
    Serializer for the Region model with its cities.
    """
    country_name = serializers.CharField(source='country.name', read_only=True)
    cities = CitySerializer(many=True, read_only=True)
    
    class Meta:
        model = Region
        fields = [
            'id', 'name', 'code', 'country', 'country_name', 'is_active', 'cities'
        ]
        read_only_fields = ['id', 'country_name', 'cities']
