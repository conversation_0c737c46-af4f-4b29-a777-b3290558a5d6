from rest_framework import generics, permissions, status, filters
from rest_framework.response import Response
from rest_framework.views import APIView
from django.shortcuts import get_object_or_404

from ..models import Country, Region, City
from ..serializers.location_serializers import (
    CountrySerializer,
    RegionSerializer,
    CitySerializer,
    CountryWithRegionsSerializer,
    RegionWithCitiesSerializer
)

class CountryListView(generics.ListAPIView):
    """
    View to list all active countries.
    """
    queryset = Country.objects.filter(is_active=True)
    serializer_class = CountrySerializer
    permission_classes = [permissions.AllowAny]
    filter_backends = [filters.SearchFilter]
    search_fields = ['name', 'code']

class CountryDetailView(generics.RetrieveAPIView):
    """
    View to retrieve a specific country.
    """
    queryset = Country.objects.filter(is_active=True)
    serializer_class = CountryWithRegionsSerializer
    permission_classes = [permissions.AllowAny]
    lookup_field = 'code'

class RegionListView(generics.ListAPIView):
    """
    View to list all active regions, optionally filtered by country.
    """
    serializer_class = RegionSerializer
    permission_classes = [permissions.AllowAny]
    filter_backends = [filters.SearchFilter]
    search_fields = ['name', 'code']
    
    def get_queryset(self):
        queryset = Region.objects.filter(is_active=True)
        country_code = self.request.query_params.get('country_code', None)
        if country_code:
            queryset = queryset.filter(country__code=country_code)
        return queryset

class RegionDetailView(generics.RetrieveAPIView):
    """
    View to retrieve a specific region with its cities.
    """
    queryset = Region.objects.filter(is_active=True)
    serializer_class = RegionWithCitiesSerializer
    permission_classes = [permissions.AllowAny]
    
    def get_object(self):
        country_code = self.kwargs.get('country_code')
        region_id = self.kwargs.get('pk')
        return get_object_or_404(
            Region, 
            id=region_id, 
            country__code=country_code,
            is_active=True
        )

class CityListView(generics.ListAPIView):
    """
    View to list all active cities, optionally filtered by region and country.
    """
    serializer_class = CitySerializer
    permission_classes = [permissions.AllowAny]
    filter_backends = [filters.SearchFilter]
    search_fields = ['name']
    
    def get_queryset(self):
        queryset = City.objects.filter(is_active=True)
        country_code = self.request.query_params.get('country_code', None)
        region_id = self.request.query_params.get('region_id', None)
        
        if country_code:
            queryset = queryset.filter(country__code=country_code)
        if region_id:
            queryset = queryset.filter(region_id=region_id)
            
        return queryset

class CityDetailView(generics.RetrieveAPIView):
    """
    View to retrieve a specific city.
    """
    queryset = City.objects.filter(is_active=True)
    serializer_class = CitySerializer
    permission_classes = [permissions.AllowAny]
    
    def get_object(self):
        country_code = self.kwargs.get('country_code')
        region_id = self.kwargs.get('region_id')
        city_id = self.kwargs.get('pk')
        
        return get_object_or_404(
            City, 
            id=city_id, 
            region_id=region_id,
            country__code=country_code,
            is_active=True
        )
