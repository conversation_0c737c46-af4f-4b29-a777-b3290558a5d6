"""
Website models for the dentistry application.
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from dentistry.models.base import DentistryBaseModel
from dentistry.models.doctor import DentistryDoctor

class DentistryWebsiteSettings(DentistryBaseModel):
    """
    Dentistry-specific website settings model.
    """
    doctor = models.OneToOneField(
       DentistryDoctor,
        on_delete=models.CASCADE,
        related_name="dentistry_website_settings",
        verbose_name=_("Doctor")
    )
    
    # General settings
    subdomain = models.CharField(
        max_length=100,
        blank=True,
        verbose_name=_("Subdomain")
    )
    site_title = models.CharField(
        max_length=200,
        blank=True,
        verbose_name=_("Site Title")
    )
    site_description = models.TextField(
        blank=True,
        verbose_name=_("Site Description")
    )
    logo = models.ImageField(
        upload_to='dentistry/website/logos/',
        null=True,
        blank=True,
        verbose_name=_("Logo")
    )
    favicon = models.ImageField(
        upload_to='dentistry/website/favicons/',
        null=True,
        blank=True,
        verbose_name=_("Favicon")
    )
    
    # Theme settings
    primary_color = models.CharField(
        max_length=20,
        default="#00BFFF",
        verbose_name=_("Primary Color")
    )
    secondary_color = models.CharField(
        max_length=20,
        default="#FFFFFF",
        verbose_name=_("Secondary Color")
    )
    font_family = models.CharField(
        max_length=100,
        default="Arial, sans-serif",
        verbose_name=_("Font Family")
    )
    
    # SEO settings
    meta_keywords = models.TextField(
        blank=True,
        verbose_name=_("Meta Keywords")
    )
    meta_description = models.TextField(
        blank=True,
        verbose_name=_("Meta Description")
    )
    google_analytics_id = models.CharField(
        max_length=50,
        blank=True,
        verbose_name=_("Google Analytics ID")
    )
    
    # Contact information
    display_phone = models.CharField(
        max_length=20,
        blank=True,
        verbose_name=_("Display Phone")
    )
    display_email = models.EmailField(
        blank=True,
        verbose_name=_("Display Email")
    )
    display_address = models.TextField(
        blank=True,
        verbose_name=_("Display Address")
    )
    google_maps_embed = models.TextField(
        blank=True,
        verbose_name=_("Google Maps Embed Code")
    )
    
    # Social media
    facebook_url = models.URLField(
        blank=True,
        verbose_name=_("Facebook URL")
    )
    twitter_url = models.URLField(
        blank=True,
        verbose_name=_("Twitter URL")
    )
    instagram_url = models.URLField(
        blank=True,
        verbose_name=_("Instagram URL")
    )
    linkedin_url = models.URLField(
        blank=True,
        verbose_name=_("LinkedIn URL")
    )
    youtube_url = models.URLField(
        blank=True,
        verbose_name=_("YouTube URL")
    )
    
    # Feature flags
    enable_appointment_booking = models.BooleanField(
        default=True,
        verbose_name=_("Enable Appointment Booking")
    )
    enable_patient_testimonials = models.BooleanField(
        default=True,
        verbose_name=_("Enable Patient Testimonials")
    )
    enable_blog = models.BooleanField(
        default=True,
        verbose_name=_("Enable Blog")
    )
    enable_before_after_gallery = models.BooleanField(
        default=True,
        verbose_name=_("Enable Before/After Gallery")
    )
    enable_smile_simulator = models.BooleanField(
        default=True,
        verbose_name=_("Enable Smile Simulator")
    )
    enable_patient_education = models.BooleanField(
        default=True,
        verbose_name=_("Enable Patient Education")
    )
    
    class Meta:
        verbose_name = _("Dentistry Website Settings")
        verbose_name_plural = _("Dentistry Website Settings")
    
    def __str__(self):
        return f"Website Settings for {self.doctor}"

class DentistryPage(DentistryBaseModel):
    """
    Dentistry-specific website page model.
    """
    doctor = models.ForeignKey(
       DentistryDoctor,
        on_delete=models.CASCADE,
        related_name="dentistry_pages",
        verbose_name=_("Doctor")
    )
    title = models.CharField(
        max_length=200,
        verbose_name=_("Page Title")
    )
    slug = models.SlugField(
        max_length=200,
        verbose_name=_("Page Slug")
    )
    content = models.TextField(
        verbose_name=_("Page Content")
    )
    meta_description = models.TextField(
        blank=True,
        verbose_name=_("Meta Description")
    )
    is_published = models.BooleanField(
        default=True,
        verbose_name=_("Is Published")
    )
    display_in_menu = models.BooleanField(
        default=True,
        verbose_name=_("Display in Menu")
    )
    menu_order = models.PositiveIntegerField(
        default=0,
        verbose_name=_("Menu Order")
    )
    
    class Meta:
        verbose_name = _("Dentistry Page")
        verbose_name_plural = _("Dentistry Pages")
        ordering = ['menu_order', 'title']
        unique_together = ['doctor', 'slug']
    
    def __str__(self):
        return self.title

class DentistryBeforeAfterCase(DentistryBaseModel):
    """
    Dentistry-specific before/after case model.
    """
    doctor = models.ForeignKey(
       DentistryDoctor,
        on_delete=models.CASCADE,
        related_name="dentistry_before_after_cases",
        verbose_name=_("Doctor")
    )
    title = models.CharField(
        max_length=200,
        verbose_name=_("Case Title")
    )
    description = models.TextField(
        blank=True,
        verbose_name=_("Description")
    )
    CATEGORY_CHOICES = (
        ('cosmetic', _('Cosmetic')),
        ('restorative', _('Restorative')),
        ('orthodontic', _('Orthodontic')),
        ('implant', _('Implant')),
        ('other', _('Other')),
    )
    category = models.CharField(
        max_length=20,
        choices=CATEGORY_CHOICES,
        default='cosmetic',
        verbose_name=_("Category")
    )
    before_image = models.ImageField(
        upload_to='dentistry/before_after/before/',
        verbose_name=_("Before Image")
    )
    after_image = models.ImageField(
        upload_to='dentistry/before_after/after/',
        verbose_name=_("After Image")
    )
    treatment_duration = models.CharField(
        max_length=100,
        blank=True,
        verbose_name=_("Treatment Duration")
    )
    procedures_performed = models.TextField(
        blank=True,
        verbose_name=_("Procedures Performed")
    )
    is_published = models.BooleanField(
        default=True,
        verbose_name=_("Is Published")
    )
    display_order = models.PositiveIntegerField(
        default=0,
        verbose_name=_("Display Order")
    )
    
    class Meta:
        verbose_name = _("Dentistry Before/After Case")
        verbose_name_plural = _("Dentistry Before/After Cases")
        ordering = ['display_order', 'title']
    
    def __str__(self):
        return self.title
