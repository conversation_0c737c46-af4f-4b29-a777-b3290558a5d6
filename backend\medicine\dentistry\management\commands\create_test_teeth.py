"""
Management command to create test teeth data for dentistry patients.
"""
from django.core.management.base import BaseCommand
from django.utils import timezone
import random

from dentistry.models import DentistryPatient, Tooth
from users.models import User


class Command(BaseCommand):
    help = 'Create test teeth data for dentistry patients'

    def add_arguments(self, parser):
        parser.add_argument(
            '--patients',
            type=int,
            default=5,
            help='Number of patients to create teeth for'
        )
        parser.add_argument(
            '--full-set',
            action='store_true',
            help='Create full set of 32 teeth for each patient'
        )

    def handle(self, *args, **options):
        self.stdout.write('Creating test teeth data...')

        # Get existing patients
        patients = list(DentistryPatient.objects.all()[:options['patients']])

        if not patients:
            self.stdout.write(self.style.WARNING('No patients found. Creating some patients first...'))
            # Create some test patients
            for i in range(options['patients']):
                # First create a User
                user = User.objects.create_user(
                    email=f'patient{i+1}@test.com',
                    first_name=f'Patient{i+1}',
                    last_name='Test',
                    password='testpass123',
                    user_type='patient'
                )

                # Then create the DentistryPatient profile
                patient = DentistryPatient.objects.create(
                    patient=user,
                    dental_history=f'Test dental history for patient {i+1}',
                    dental_insurance_provider='Test Insurance Co.'
                )
                patients.append(patient)
                self.stdout.write(f'Created patient: {patient.full_name}')

        # Standard adult teeth numbers (FDI notation)
        adult_teeth = [
            # Upper right (1st quadrant)
            11, 12, 13, 14, 15, 16, 17, 18,
            # Upper left (2nd quadrant)
            21, 22, 23, 24, 25, 26, 27, 28,
            # Lower left (3rd quadrant)
            31, 32, 33, 34, 35, 36, 37, 38,
            # Lower right (4th quadrant)
            41, 42, 43, 44, 45, 46, 47, 48
        ]

        # Tooth status options
        status_options = ['healthy', 'caries', 'filled', 'crowned', 'missing', 'extracted']
        status_weights = [60, 15, 15, 5, 3, 2]  # Weighted towards healthy teeth

        total_teeth_created = 0

        for patient in patients:
            self.stdout.write(f'\nCreating teeth for {patient.full_name}...')

            # Delete existing teeth for this patient
            existing_count = Tooth.objects.filter(patient=patient).count()
            if existing_count > 0:
                Tooth.objects.filter(patient=patient).delete()
                self.stdout.write(f'Deleted {existing_count} existing teeth')

            if options['full_set']:
                # Create full set of 32 teeth
                teeth_to_create = adult_teeth
            else:
                # Create random subset of teeth (20-30 teeth)
                num_teeth = random.randint(20, 30)
                teeth_to_create = random.sample(adult_teeth, num_teeth)

            patient_teeth_created = 0

            for tooth_number in sorted(teeth_to_create):
                # Choose status based on weights
                status = random.choices(status_options, weights=status_weights)[0]

                # Create notes based on status
                notes = self.generate_tooth_notes(tooth_number, status)

                tooth = Tooth.objects.create(
                    patient=patient,
                    tooth_number=tooth_number,
                    status=status,
                    notes=notes
                )

                patient_teeth_created += 1
                total_teeth_created += 1

            self.stdout.write(
                self.style.SUCCESS(f'✓ Created {patient_teeth_created} teeth for {patient.full_name}')
            )

        self.stdout.write(
            self.style.SUCCESS(
                f'\n🦷 Successfully created {total_teeth_created} teeth for {len(patients)} patients!'
            )
        )

        # Show summary statistics
        self.show_statistics()

    def generate_tooth_notes(self, tooth_number, status):
        """Generate appropriate notes based on tooth number and status."""
        # Get tooth name from FDI notation
        tooth_names = {
            1: 'Central Incisor', 2: 'Lateral Incisor', 3: 'Canine',
            4: 'First Premolar', 5: 'Second Premolar',
            6: 'First Molar', 7: 'Second Molar', 8: 'Third Molar (Wisdom)'
        }

        last_digit = tooth_number % 10
        tooth_name = tooth_names.get(last_digit, 'Unknown')

        # Generate notes based on status
        if status == 'healthy':
            return f'{tooth_name} in good condition. No treatment required.'
        elif status == 'caries':
            return f'{tooth_name} has dental caries. Requires filling.'
        elif status == 'filled':
            return f'{tooth_name} has been filled. Composite restoration.'
        elif status == 'crowned':
            return f'{tooth_name} has a crown. Porcelain restoration.'
        elif status == 'missing':
            return f'{tooth_name} is missing. Consider implant or bridge.'
        elif status == 'extracted':
            return f'{tooth_name} was extracted. Healing well.'
        else:
            return f'{tooth_name} - Status: {status}'

    def show_statistics(self):
        """Show statistics about created teeth."""
        self.stdout.write('\n' + '='*50)
        self.stdout.write('TEETH STATISTICS')
        self.stdout.write('='*50)

        total_teeth = Tooth.objects.count()
        total_patients = DentistryPatient.objects.count()

        self.stdout.write(f'Total Patients: {total_patients}')
        self.stdout.write(f'Total Teeth: {total_teeth}')
        self.stdout.write(f'Average Teeth per Patient: {total_teeth/total_patients:.1f}')

        # Status distribution
        self.stdout.write('\nStatus Distribution:')
        for status_code, status_name in Tooth._meta.get_field('status').choices:
            count = Tooth.objects.filter(status=status_code).count()
            percentage = (count / total_teeth * 100) if total_teeth > 0 else 0
            self.stdout.write(f'  {status_name}: {count} ({percentage:.1f}%)')

        # Quadrant distribution
        self.stdout.write('\nQuadrant Distribution:')
        quadrants = {
            'Upper Right (1x)': (10, 19),
            'Upper Left (2x)': (20, 29),
            'Lower Left (3x)': (30, 39),
            'Lower Right (4x)': (40, 49)
        }

        for quadrant_name, (start, end) in quadrants.items():
            count = Tooth.objects.filter(
                tooth_number__gte=start,
                tooth_number__lte=end
            ).count()
            percentage = (count / total_teeth * 100) if total_teeth > 0 else 0
            self.stdout.write(f'  {quadrant_name}: {count} ({percentage:.1f}%)')

        self.stdout.write('\n✅ Test data creation completed!')
        self.stdout.write('🌐 You can now test the API at: http://127.0.0.1:8000/api/teeth/')
        self.stdout.write('🔧 Admin panel: http://127.0.0.1:8000/admin/dentistry/tooth/')
